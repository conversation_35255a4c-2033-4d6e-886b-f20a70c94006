#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص مبسط لبيانات MySQL
"""

import mysql.connector

def simple_mysql_check():
    """فحص مبسط لـ MySQL"""
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager'
        }
        
        print("🔗 الاتصال بـ MySQL...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # فحص الجداول
        cursor.execute("SHOW TABLES")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📋 الجداول: {', '.join(tables)}")
        
        # فحص البيانات الأساسية
        basic_tables = ['users', 'currencies', 'accounts', 'account_balances']
        
        for table in basic_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} سجل")
        
        # فحص المستخدمين
        if 'users' in tables:
            cursor.execute("SELECT username, role FROM users")
            users = cursor.fetchall()
            print("👥 المستخدمين:")
            for user in users:
                print(f"   - {user[0]} ({user[1]})")
        
        # فحص العملات
        if 'currencies' in tables:
            cursor.execute("SELECT code, name FROM currencies")
            currencies = cursor.fetchall()
            print("💰 العملات:")
            for currency in currencies:
                print(f"   - {currency[0]}: {currency[1]}")
        
        cursor.close()
        connection.close()
        
        print("✅ فحص MySQL مكتمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص MySQL: {e}")
        return False

if __name__ == "__main__":
    simple_mysql_check()
