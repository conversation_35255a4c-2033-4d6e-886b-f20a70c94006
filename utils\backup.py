"""
نظام النسخ الاحتياطي للبيانات
"""

import os
import shutil
import zipfile
import json
from datetime import datetime
import threading
import schedule
import time
import logging
from database.connection import db
from utils.auth import auth_manager
from config.settings import BACKUP_CONFIG, PATHS

class BackupManager:
    """مدير النسخ الاحتياطي"""
    
    def __init__(self):
        self.backup_dir = PATHS['backup_dir']
        self.is_auto_backup_running = False
        self.backup_thread = None
        
        # إنشاء مجلد النسخ الاحتياطي
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # بدء النسخ الاحتياطي التلقائي إذا كان مفعلاً
        if BACKUP_CONFIG['auto_backup']:
            self.start_auto_backup()
    
    def create_backup(self, backup_type='manual', include_uploads=True):
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"money_manager_backup_{timestamp}.zip"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            logging.info(f"بدء إنشاء النسخة الاحتياطية: {backup_filename}")
            
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as backup_zip:
                # نسخ احتياطي لقاعدة البيانات
                self._backup_database(backup_zip)
                
                # نسخ احتياطي للملفات المرفوعة
                if include_uploads:
                    self._backup_uploads(backup_zip)
                
                # نسخ احتياطي لملفات الإعدادات
                self._backup_config(backup_zip)
                
                # إضافة معلومات النسخة الاحتياطية
                self._add_backup_info(backup_zip, backup_type)
            
            # حفظ معلومات النسخة الاحتياطية في قاعدة البيانات
            backup_size = os.path.getsize(backup_path)
            self._save_backup_record(backup_filename, backup_path, backup_size, backup_type)
            
            # تنظيف النسخ القديمة
            self._cleanup_old_backups()
            
            logging.info(f"تم إنشاء النسخة الاحتياطية بنجاح: {backup_filename}")
            return True, backup_path
            
        except Exception as e:
            logging.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False, str(e)
    
    def _backup_database(self, backup_zip):
        """نسخ احتياطي لقاعدة البيانات"""
        try:
            # الحصول على جميع البيانات من الجداول الرئيسية
            tables_data = {}
            
            tables = [
                'users', 'currencies', 'account_types', 'accounts',
                'income_categories', 'expense_categories', 'transactions',
                'transfers', 'attachments'
            ]
            
            for table in tables:
                query = f"SELECT * FROM {table}"
                data = db.execute_query(query)
                tables_data[table] = data if data else []
            
            # حفظ البيانات في ملف JSON
            db_backup = json.dumps(tables_data, ensure_ascii=False, indent=2, default=str)
            backup_zip.writestr('database_backup.json', db_backup.encode('utf-8'))
            
            logging.info("تم نسخ قاعدة البيانات احتياطياً")
            
        except Exception as e:
            logging.error(f"خطأ في نسخ قاعدة البيانات: {e}")
            raise
    
    def _backup_uploads(self, backup_zip):
        """نسخ احتياطي للملفات المرفوعة"""
        try:
            uploads_dir = PATHS['uploads_dir']
            if os.path.exists(uploads_dir):
                for root, dirs, files in os.walk(uploads_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, os.path.dirname(uploads_dir))
                        backup_zip.write(file_path, arc_path)
                        
                logging.info("تم نسخ الملفات المرفوعة احتياطياً")
                
        except Exception as e:
            logging.error(f"خطأ في نسخ الملفات المرفوعة: {e}")
    
    def _backup_config(self, backup_zip):
        """نسخ احتياطي لملفات الإعدادات"""
        try:
            config_files = [
                'config/settings.py',
                'config/colors.py'
            ]
            
            for config_file in config_files:
                if os.path.exists(config_file):
                    backup_zip.write(config_file, f"config/{os.path.basename(config_file)}")
                    
            logging.info("تم نسخ ملفات الإعدادات احتياطياً")
            
        except Exception as e:
            logging.error(f"خطأ في نسخ ملفات الإعدادات: {e}")
    
    def _add_backup_info(self, backup_zip, backup_type):
        """إضافة معلومات النسخة الاحتياطية"""
        try:
            backup_info = {
                'created_at': datetime.now().isoformat(),
                'backup_type': backup_type,
                'created_by': auth_manager.current_user['username'] if auth_manager.current_user else 'system',
                'version': '1.0.0',
                'database_tables': [
                    'users', 'currencies', 'account_types', 'accounts',
                    'income_categories', 'expense_categories', 'transactions',
                    'transfers', 'attachments'
                ]
            }
            
            info_json = json.dumps(backup_info, ensure_ascii=False, indent=2)
            backup_zip.writestr('backup_info.json', info_json.encode('utf-8'))
            
        except Exception as e:
            logging.error(f"خطأ في إضافة معلومات النسخة الاحتياطية: {e}")
    
    def _save_backup_record(self, filename, filepath, filesize, backup_type):
        """حفظ سجل النسخة الاحتياطية في قاعدة البيانات"""
        try:
            query = """
                INSERT INTO backups (file_name, file_path, file_size, backup_type, created_by)
                VALUES (%s, %s, %s, %s, %s)
            """
            created_by = auth_manager.current_user['id'] if auth_manager.current_user else None
            db.execute_insert(query, (filename, filepath, filesize, backup_type, created_by))
            
        except Exception as e:
            logging.error(f"خطأ في حفظ سجل النسخة الاحتياطية: {e}")
    
    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            max_backups = BACKUP_CONFIG['max_backup_files']
            
            # الحصول على قائمة النسخ الاحتياطية مرتبة حسب التاريخ
            query = """
                SELECT file_name, file_path 
                FROM backups 
                ORDER BY created_at DESC
            """
            backups = db.execute_query(query)
            
            if len(backups) > max_backups:
                # حذف النسخ الزائدة
                old_backups = backups[max_backups:]
                
                for backup in old_backups:
                    try:
                        # حذف الملف
                        if os.path.exists(backup['file_path']):
                            os.remove(backup['file_path'])
                        
                        # حذف السجل من قاعدة البيانات
                        delete_query = "DELETE FROM backups WHERE file_name = %s"
                        db.execute_update(delete_query, (backup['file_name'],))
                        
                        logging.info(f"تم حذف النسخة الاحتياطية القديمة: {backup['file_name']}")
                        
                    except Exception as e:
                        logging.error(f"خطأ في حذف النسخة الاحتياطية {backup['file_name']}: {e}")
                        
        except Exception as e:
            logging.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                return False, "ملف النسخة الاحتياطية غير موجود"
            
            logging.info(f"بدء استعادة النسخة الاحتياطية: {backup_path}")
            
            with zipfile.ZipFile(backup_path, 'r') as backup_zip:
                # التحقق من صحة النسخة الاحتياطية
                if not self._validate_backup(backup_zip):
                    return False, "النسخة الاحتياطية غير صالحة أو تالفة"
                
                # استعادة قاعدة البيانات
                self._restore_database(backup_zip)
                
                # استعادة الملفات المرفوعة
                self._restore_uploads(backup_zip)
            
            logging.info("تم استعادة النسخة الاحتياطية بنجاح")
            return True, "تم استعادة النسخة الاحتياطية بنجاح"
            
        except Exception as e:
            logging.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False, f"خطأ في الاستعادة: {str(e)}"
    
    def _validate_backup(self, backup_zip):
        """التحقق من صحة النسخة الاحتياطية"""
        try:
            # التحقق من وجود الملفات الأساسية
            required_files = ['backup_info.json', 'database_backup.json']
            
            for required_file in required_files:
                if required_file not in backup_zip.namelist():
                    return False
            
            # التحقق من معلومات النسخة الاحتياطية
            info_data = backup_zip.read('backup_info.json').decode('utf-8')
            backup_info = json.loads(info_data)
            
            if 'created_at' not in backup_info or 'version' not in backup_info:
                return False
                
            return True
            
        except Exception as e:
            logging.error(f"خطأ في التحقق من النسخة الاحتياطية: {e}")
            return False
    
    def _restore_database(self, backup_zip):
        """استعادة قاعدة البيانات"""
        try:
            # قراءة بيانات قاعدة البيانات
            db_data = backup_zip.read('database_backup.json').decode('utf-8')
            tables_data = json.loads(db_data)
            
            # تعطيل فحص المفاتيح الخارجية مؤقتاً
            db.execute_update("SET FOREIGN_KEY_CHECKS = 0")
            
            try:
                # مسح البيانات الحالية (باستثناء المستخدمين)
                tables_to_clear = [
                    'transfers', 'attachments', 'transactions', 'accounts'
                ]
                
                for table in tables_to_clear:
                    db.execute_update(f"DELETE FROM {table}")
                
                # استعادة البيانات
                for table_name, table_data in tables_data.items():
                    if table_name in tables_to_clear and table_data:
                        self._restore_table_data(table_name, table_data)
                        
            finally:
                # إعادة تفعيل فحص المفاتيح الخارجية
                db.execute_update("SET FOREIGN_KEY_CHECKS = 1")
                
            logging.info("تم استعادة قاعدة البيانات")
            
        except Exception as e:
            logging.error(f"خطأ في استعادة قاعدة البيانات: {e}")
            raise
    
    def _restore_table_data(self, table_name, table_data):
        """استعادة بيانات جدول"""
        try:
            if not table_data:
                return
                
            # الحصول على أعمدة الجدول
            columns = list(table_data[0].keys())
            columns_str = ', '.join(columns)
            placeholders = ', '.join(['%s'] * len(columns))
            
            query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
            
            # إعداد البيانات للإدراج
            rows_data = []
            for row in table_data:
                row_values = [row[col] for col in columns]
                rows_data.append(row_values)
            
            # إدراج البيانات
            db.execute_many(query, rows_data)
            
            logging.info(f"تم استعادة بيانات جدول {table_name}")
            
        except Exception as e:
            logging.error(f"خطأ في استعادة بيانات جدول {table_name}: {e}")
            raise
    
    def _restore_uploads(self, backup_zip):
        """استعادة الملفات المرفوعة"""
        try:
            uploads_dir = PATHS['uploads_dir']
            
            # إنشاء مجلد الملفات المرفوعة إذا لم يكن موجوداً
            os.makedirs(uploads_dir, exist_ok=True)
            
            # استخراج الملفات المرفوعة
            for file_info in backup_zip.infolist():
                if file_info.filename.startswith('uploads/'):
                    backup_zip.extract(file_info, '.')
                    
            logging.info("تم استعادة الملفات المرفوعة")
            
        except Exception as e:
            logging.error(f"خطأ في استعادة الملفات المرفوعة: {e}")
    
    def get_backup_list(self):
        """الحصول على قائمة النسخ الاحتياطية"""
        try:
            query = """
                SELECT b.*, u.username as created_by_username
                FROM backups b
                LEFT JOIN users u ON b.created_by = u.id
                ORDER BY b.created_at DESC
            """
            return db.execute_query(query)
            
        except Exception as e:
            logging.error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []
    
    def start_auto_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        if self.is_auto_backup_running:
            return
            
        self.is_auto_backup_running = True
        
        # جدولة النسخ الاحتياطي
        schedule.every(BACKUP_CONFIG['backup_interval_hours']).hours.do(
            lambda: self.create_backup('automatic', include_uploads=True)
        )
        
        # بدء خيط النسخ الاحتياطي التلقائي
        self.backup_thread = threading.Thread(target=self._auto_backup_worker, daemon=True)
        self.backup_thread.start()
        
        logging.info("تم بدء النسخ الاحتياطي التلقائي")
    
    def stop_auto_backup(self):
        """إيقاف النسخ الاحتياطي التلقائي"""
        self.is_auto_backup_running = False
        schedule.clear()
        logging.info("تم إيقاف النسخ الاحتياطي التلقائي")
    
    def _auto_backup_worker(self):
        """عامل النسخ الاحتياطي التلقائي"""
        while self.is_auto_backup_running:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة

    def test_database_connection(self, config=None):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            if config:
                # اختبار إعدادات جديدة
                import mysql.connector
                from mysql.connector import Error

                test_connection = mysql.connector.connect(**config)
                if test_connection.is_connected():
                    test_connection.close()
                    return True, "تم الاتصال بقاعدة البيانات بنجاح"
                else:
                    return False, "فشل الاتصال بقاعدة البيانات"
            else:
                # اختبار الاتصال الحالي
                if db.is_connected() or db.connect():
                    return True, "تم الاتصال بقاعدة البيانات بنجاح"
                else:
                    return False, "فشل الاتصال بقاعدة البيانات"

        except Exception as e:
            error_message = f"خطأ في اختبار الاتصال: {str(e)}"
            logging.error(error_message)
            return False, error_message

    def get_backup_files(self):
        """الحصول على قائمة ملفات النسخ الاحتياطية"""
        try:
            backup_files = []

            if not os.path.exists(self.backup_dir):
                return backup_files

            for filename in os.listdir(self.backup_dir):
                if filename.endswith('.zip'):
                    file_path = os.path.join(self.backup_dir, filename)
                    file_stats = os.stat(file_path)

                    backup_files.append({
                        'name': filename,
                        'path': file_path,
                        'size': file_stats.st_size,
                        'size_mb': round(file_stats.st_size / (1024 * 1024), 2),
                        'created': datetime.fromtimestamp(file_stats.st_mtime)
                    })

            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backup_files.sort(key=lambda x: x['created'], reverse=True)
            return backup_files

        except Exception as e:
            logging.error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []

    def update_settings(self, new_settings):
        """تحديث إعدادات النسخ الاحتياطي"""
        try:
            # هذه دالة مؤقتة - يمكن تطويرها لاحقاً لحفظ الإعدادات في ملف
            logging.info(f"تحديث إعدادات النسخ الاحتياطي: {new_settings}")
            return True
        except Exception as e:
            logging.error(f"خطأ في تحديث الإعدادات: {e}")
            return False

# إنشاء مثيل عام لمدير النسخ الاحتياطي
backup_manager = BackupManager()
