#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة عرض المستخدمين في ميزة إدارة المستخدمين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔍 اختبار الاتصال بقاعدة البيانات...")
    try:
        from database.connection import db
        if db.is_connected() or db.connect():
            print("✅ الاتصال بقاعدة البيانات ناجح")
            return True
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_users_table():
    """اختبار جدول المستخدمين"""
    print("\n🔍 اختبار جدول المستخدمين...")
    try:
        from database.connection import db
        
        # فحص وجود الجدول
        tables = db.execute_query("SHOW TABLES LIKE 'users'")
        if not tables:
            print("❌ جدول المستخدمين غير موجود!")
            return False
        
        print("✅ جدول المستخدمين موجود")
        
        # فحص بنية الجدول
        structure = db.execute_query("DESCRIBE users")
        print("📋 بنية جدول المستخدمين:")
        for column in structure:
            print(f"   - {column['Field']}: {column['Type']}")
        
        # عد المستخدمين
        count_result = db.execute_query("SELECT COUNT(*) as count FROM users")
        user_count = count_result[0]['count'] if count_result else 0
        print(f"📊 عدد المستخدمين: {user_count}")
        
        if user_count == 0:
            print("⚠️ لا توجد مستخدمين في قاعدة البيانات")
            return False
        
        # عرض المستخدمين
        users = db.execute_query("SELECT id, username, full_name, role, is_active FROM users")
        print("👥 المستخدمين الموجودين:")
        for user in users:
            status = "نشط" if user['is_active'] else "معطل"
            print(f"   - {user['username']} ({user['full_name']}) - {user['role']} - {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص جدول المستخدمين: {e}")
        return False

def test_user_model():
    """اختبار نموذج المستخدم"""
    print("\n🔍 اختبار نموذج المستخدم...")
    try:
        from database.models import User
        
        # اختبار get_all()
        print("   اختبار User.get_all()...")
        users = User.get_all()
        
        if users is None:
            print("❌ User.get_all() أرجع None")
            return False
        
        if not isinstance(users, list):
            print(f"❌ User.get_all() أرجع نوع غير متوقع: {type(users)}")
            return False
        
        print(f"✅ User.get_all() أرجع قائمة بـ {len(users)} مستخدم")
        
        # عرض تفاصيل المستخدمين
        for i, user in enumerate(users):
            print(f"   المستخدم {i+1}:")
            print(f"      - ID: {user.get('id', 'غير محدد')}")
            print(f"      - اسم المستخدم: {user.get('username', 'غير محدد')}")
            print(f"      - الاسم الكامل: {user.get('full_name', 'غير محدد')}")
            print(f"      - الدور: {user.get('role', 'غير محدد')}")
            print(f"      - نشط: {user.get('is_active', 'غير محدد')}")
            print(f"      - منشئ بواسطة: {user.get('created_by_username', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نموذج المستخدم: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auth_system():
    """اختبار نظام المصادقة"""
    print("\n🔍 اختبار نظام المصادقة...")
    try:
        from utils.auth import auth_manager
        
        # محاولة تسجيل الدخول
        success, message = auth_manager.login("admin2", "123456")
        
        if success:
            print("✅ تسجيل الدخول ناجح")
            print(f"   المستخدم الحالي: {auth_manager.current_user}")
            
            # اختبار الصلاحيات
            if auth_manager.can_manage_users():
                print("✅ المستخدم يمكنه إدارة المستخدمين")
                return True
            else:
                print("❌ المستخدم لا يمكنه إدارة المستخدمين")
                return False
        else:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام المصادقة: {e}")
        return False

def test_gui_components():
    """اختبار مكونات واجهة المستخدم"""
    print("\n🔍 اختبار مكونات واجهة المستخدم...")
    try:
        # اختبار استيراد المكونات
        from config.colors import COLORS, BUTTON_STYLES, CARD_STYLES, ARABIC_TEXT_STYLES
        print("✅ تم استيراد الألوان والأنماط")
        
        from config.fonts import create_rtl_label, create_rtl_button, create_rtl_entry
        print("✅ تم استيراد مكونات الخطوط RTL")
        
        from gui.user_management_windows import AddUserWindow, EditUserWindow, ResetPasswordWindow
        print("✅ تم استيراد نوافذ إدارة المستخدمين")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد مكونات واجهة المستخدم: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_functions():
    """اختبار وظائف النافذة الرئيسية"""
    print("\n🔍 اختبار وظائف النافذة الرئيسية...")
    try:
        # محاكاة اختبار load_users_list
        from database.models import User
        
        print("   محاكاة دالة load_users_list...")
        
        # جلب المستخدمين
        users = User.get_all()
        
        if not users:
            print("❌ لا توجد مستخدمين للعرض")
            return False
        
        print(f"✅ تم جلب {len(users)} مستخدم للعرض")
        
        # محاكاة إنشاء بطاقات المستخدمين
        for user in users:
            print(f"   📋 بطاقة المستخدم: {user['username']}")
            print(f"      - الاسم الكامل: {user['full_name']}")
            print(f"      - الدور: {user['role']}")
            print(f"      - الحالة: {'نشط' if user['is_active'] else 'معطل'}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف النافذة الرئيسية: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_activity_log_table():
    """اختبار جدول سجل الأنشطة"""
    print("\n🔍 اختبار جدول سجل الأنشطة...")
    try:
        from database.connection import db
        
        # فحص وجود الجدول
        tables = db.execute_query("SHOW TABLES LIKE 'activity_log'")
        if not tables:
            print("⚠️ جدول سجل الأنشطة غير موجود - سيتم إنشاؤه")
            
            # إنشاء الجدول
            create_table_query = """
                CREATE TABLE activity_log (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    action_type VARCHAR(50) NOT NULL,
                    table_name VARCHAR(50) NOT NULL,
                    record_id INT NULL,
                    description TEXT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            """
            db.execute_update(create_table_query, ())
            print("✅ تم إنشاء جدول سجل الأنشطة")
        else:
            print("✅ جدول سجل الأنشطة موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص جدول سجل الأنشطة: {e}")
        return False

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🚀 تشخيص مشكلة عرض المستخدمين في ميزة إدارة المستخدمين")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 7
    
    # 1. اختبار الاتصال بقاعدة البيانات
    if test_database_connection():
        tests_passed += 1
    
    # 2. اختبار جدول المستخدمين
    if test_users_table():
        tests_passed += 1
    
    # 3. اختبار نموذج المستخدم
    if test_user_model():
        tests_passed += 1
    
    # 4. اختبار نظام المصادقة
    if test_auth_system():
        tests_passed += 1
    
    # 5. اختبار مكونات واجهة المستخدم
    if test_gui_components():
        tests_passed += 1
    
    # 6. اختبار وظائف النافذة الرئيسية
    if test_main_window_functions():
        tests_passed += 1
    
    # 7. اختبار جدول سجل الأنشطة
    if test_activity_log_table():
        tests_passed += 1
    
    # النتائج
    print("\n" + "=" * 70)
    print(f"📊 نتائج التشخيص: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 جميع المكونات تعمل بشكل صحيح!")
        print("\n📋 التوصيات:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول باستخدام: admin2 / 123456")
        print("3. انقر على '👥 إدارة المستخدمين'")
        print("4. يجب أن تظهر جميع المستخدمين بشكل صحيح")
        return True
    else:
        print("⚠️ تم العثور على مشاكل. راجع الأخطاء أعلاه.")
        
        # اقتراحات الإصلاح
        print("\n🔧 اقتراحات الإصلاح:")
        if tests_passed < 1:
            print("- تحقق من إعدادات قاعدة البيانات في config/settings.py")
            print("- تأكد من تشغيل MySQL Server")
        if tests_passed < 2:
            print("- تحقق من وجود جدول المستخدمين")
            print("- شغل سكريبت إنشاء قاعدة البيانات")
        if tests_passed < 4:
            print("- تحقق من بيانات اعتماد المدير admin2")
            print("- شغل سكريبت تحديث المستخدمين")
        
        return False

if __name__ == "__main__":
    main()
