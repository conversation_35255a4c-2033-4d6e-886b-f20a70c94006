#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص متطلبات تشغيل برنامج إدارة الأموال
"""

import sys
import subprocess
import platform
import os
from datetime import datetime

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 70)
    print("🔍 فحص متطلبات تشغيل برنامج إدارة الأموال")
    print("=" * 70)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💻 نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"🏗️  المعمارية: {platform.machine()}")
    print("=" * 70)

def check_python():
    """فحص إصدار Python"""
    print("\n🐍 فحص Python:")
    print("-" * 30)
    
    try:
        version = sys.version_info
        version_str = f"{version.major}.{version.minor}.{version.micro}"
        print(f"✅ Python مثبت: الإصدار {version_str}")
        
        if version.major == 3 and version.minor >= 8:
            print("✅ إصدار Python متوافق (3.8+)")
            return True
        else:
            print("❌ إصدار Python غير متوافق (يتطلب 3.8+)")
            print(f"💡 الإصدار الحالي: {version_str}")
            print("💡 يرجى تحديث Python من: https://python.org")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص Python: {e}")
        return False

def check_pip():
    """فحص pip"""
    print("\n📦 فحص pip:")
    print("-" * 30)
    
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            pip_version = result.stdout.strip()
            print(f"✅ pip مثبت: {pip_version}")
            return True
        else:
            print("❌ pip غير مثبت أو لا يعمل بشكل صحيح")
            print("💡 لتثبيت pip:")
            print("   curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py")
            print("   python get-pip.py")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ انتهت مهلة فحص pip")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص pip: {e}")
        return False

def check_mysql():
    """فحص MySQL"""
    print("\n🗄️  فحص MySQL:")
    print("-" * 30)
    
    mysql_commands = ['mysql', 'mysql.exe']
    mysql_found = False
    
    for cmd in mysql_commands:
        try:
            result = subprocess.run([cmd, "--version"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                mysql_version = result.stdout.strip()
                print(f"✅ MySQL مثبت: {mysql_version}")
                mysql_found = True
                break
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            continue
        except Exception:
            continue
    
    if not mysql_found:
        print("⚠️  MySQL غير موجود في PATH أو غير مثبت")
        print("💡 خيارات التثبيت:")
        print("   • MySQL Server: https://dev.mysql.com/downloads/mysql/")
        print("   • XAMPP: https://www.apachefriends.org/")
        print("   • WAMP: http://www.wampserver.com/")
        print("   • MAMP: https://www.mamp.info/")
        return False
    
    return True

def check_required_modules():
    """فحص المكتبات المطلوبة"""
    print("\n📚 فحص المكتبات المطلوبة:")
    print("-" * 30)
    
    required_modules = {
        'mysql.connector': 'mysql-connector-python',
        'customtkinter': 'customtkinter',
        'PIL': 'Pillow',
        'dateutil': 'python-dateutil',
        'bcrypt': 'bcrypt',
        'reportlab': 'reportlab',
        'matplotlib': 'matplotlib',
        'pandas': 'pandas',
        'openpyxl': 'openpyxl',
        'hijri_converter': 'hijri-converter',
        'schedule': 'schedule'
    }
    
    missing_modules = []
    installed_modules = []
    
    for module_name, package_name in required_modules.items():
        try:
            if module_name == 'mysql.connector':
                import mysql.connector
            elif module_name == 'PIL':
                from PIL import Image
            elif module_name == 'dateutil':
                import dateutil
            elif module_name == 'hijri_converter':
                try:
                    import hijri_converter
                except ImportError:
                    # هذه المكتبة اختيارية
                    print(f"⚠️  {package_name}: غير مثبت (اختياري)")
                    continue
            else:
                __import__(module_name)
            
            print(f"✅ {package_name}: مثبت")
            installed_modules.append(package_name)
            
        except ImportError:
            print(f"❌ {package_name}: غير مثبت")
            missing_modules.append(package_name)
        except Exception as e:
            print(f"⚠️  {package_name}: خطأ في الفحص - {e}")
            missing_modules.append(package_name)
    
    return missing_modules, installed_modules

def check_system_resources():
    """فحص موارد النظام"""
    print("\n💾 فحص موارد النظام:")
    print("-" * 30)
    
    try:
        # فحص مساحة القرص
        disk_usage = os.statvfs('.') if hasattr(os, 'statvfs') else None
        if disk_usage:
            free_space = disk_usage.f_bavail * disk_usage.f_frsize / (1024**3)  # GB
            print(f"💽 المساحة المتاحة: {free_space:.2f} GB")
            
            if free_space >= 1.0:
                print("✅ مساحة القرص كافية")
            else:
                print("⚠️  مساحة القرص قليلة (يُنصح بـ 1GB على الأقل)")
        else:
            print("ℹ️  لا يمكن فحص مساحة القرص على هذا النظام")
        
        # فحص الذاكرة (تقريبي)
        try:
            import psutil
            memory = psutil.virtual_memory()
            total_gb = memory.total / (1024**3)
            available_gb = memory.available / (1024**3)
            
            print(f"🧠 إجمالي الذاكرة: {total_gb:.2f} GB")
            print(f"🧠 الذاكرة المتاحة: {available_gb:.2f} GB")
            
            if total_gb >= 4.0:
                print("✅ الذاكرة كافية")
            else:
                print("⚠️  الذاكرة قليلة (يُنصح بـ 4GB على الأقل)")
                
        except ImportError:
            print("ℹ️  psutil غير مثبت - لا يمكن فحص الذاكرة")
            
    except Exception as e:
        print(f"⚠️  خطأ في فحص موارد النظام: {e}")

def generate_install_commands(missing_modules):
    """إنشاء أوامر التثبيت"""
    if not missing_modules:
        return
        
    print("\n🛠️  أوامر التثبيت المطلوبة:")
    print("-" * 30)
    
    print("📋 لتثبيت جميع المتطلبات:")
    print("pip install -r requirements.txt")
    print()
    
    print("📋 أو تثبيت المكتبات المفقودة منفصلة:")
    for module in missing_modules:
        print(f"pip install {module}")

def check_project_files():
    """فحص ملفات المشروع"""
    print("\n📁 فحص ملفات المشروع:")
    print("-" * 30)
    
    required_files = [
        'main.py',
        'requirements.txt',
        'config/settings.py',
        'config/colors.py',
        'database/connection.py',
        'database/schema.sql',
        'gui/login_window.py',
        'utils/auth.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  ملفات مفقودة: {len(missing_files)}")
        print("💡 تأكد من نسخ جميع ملفات المشروع")
    else:
        print("\n✅ جميع الملفات الأساسية موجودة")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص المتطلبات
    python_ok = check_python()
    pip_ok = check_pip()
    mysql_ok = check_mysql()
    missing_modules, installed_modules = check_required_modules()
    
    # فحص ملفات المشروع
    check_project_files()
    
    # فحص موارد النظام
    check_system_resources()
    
    # إنشاء أوامر التثبيت
    if missing_modules:
        generate_install_commands(missing_modules)
    
    # النتيجة النهائية
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print("=" * 70)
    
    if python_ok:
        print("✅ Python: جاهز")
    else:
        print("❌ Python: يحتاج تثبيت/تحديث")
    
    if pip_ok:
        print("✅ pip: جاهز")
    else:
        print("❌ pip: يحتاج تثبيت")
    
    if mysql_ok:
        print("✅ MySQL: جاهز")
    else:
        print("❌ MySQL: يحتاج تثبيت")
    
    print(f"✅ المكتبات المثبتة: {len(installed_modules)}")
    print(f"❌ المكتبات المفقودة: {len(missing_modules)}")
    
    if python_ok and pip_ok and len(missing_modules) == 0:
        print("\n🎉 جميع المتطلبات متوفرة! يمكنك تشغيل البرنامج الآن.")
        print("🚀 لتشغيل البرنامج: python main.py")
    else:
        print("\n⚠️  بعض المتطلبات مفقودة. يرجى تثبيتها أولاً.")
        
        if not mysql_ok:
            print("💡 MySQL مطلوب لتشغيل البرنامج")
        
        if missing_modules:
            print("💡 قم بتشغيل: pip install -r requirements.txt")
    
    print("=" * 70)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف الفحص بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    finally:
        input("\n👋 اضغط Enter للخروج...")
