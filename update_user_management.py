#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث قاعدة البيانات لدعم إدارة المستخدمين
إزالة المستخدم الحالي وإنشاء admin2 جديد
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import mysql.connector
import bcrypt
from datetime import datetime
from config.settings import DATABASE_CONFIG

def update_database_for_user_management():
    """تحديث قاعدة البيانات لدعم إدارة المستخدمين المتعددين"""
    
    try:
        print("🔄 الاتصال بقاعدة البيانات...")
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()
        
        print("🗑️ إزالة جميع المستخدمين الحاليين...")
        # تعطيل فحص القيود المرجعية مؤقتاً
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")

        # حذف جميع المستخدمين الحاليين
        cursor.execute("DELETE FROM users")

        # إعادة تفعيل فحص القيود المرجعية
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        print("🔧 تحديث بنية جدول المستخدمين...")
        # إضافة عمود created_by إذا لم يكن موجوداً
        try:
            cursor.execute("""
                ALTER TABLE users 
                ADD COLUMN created_by INT NULL,
                ADD FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            """)
            print("✅ تم إضافة عمود created_by")
        except mysql.connector.Error as e:
            if "Duplicate column name" in str(e):
                print("ℹ️ عمود created_by موجود مسبقاً")
            else:
                print(f"⚠️ خطأ في إضافة العمود: {e}")
        
        print("👤 إنشاء المستخدم الجديد admin2...")
        # تشفير كلمة المرور الجديدة
        password = "123456"
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        # إنشاء المستخدم الجديد
        insert_query = """
            INSERT INTO users (username, password_hash, full_name, role, is_active, created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        user_data = (
            'admin2',
            password_hash,
            'المدير الجديد',
            'admin',
            True,
            datetime.now()
        )
        
        cursor.execute(insert_query, user_data)
        user_id = cursor.lastrowid
        
        print(f"✅ تم إنشاء المستخدم admin2 بنجاح (ID: {user_id})")
        print(f"📝 اسم المستخدم: admin2")
        print(f"🔑 كلمة المرور: 123456")
        
        # حفظ التغييرات
        connection.commit()
        
        print("✅ تم تحديث قاعدة البيانات بنجاح!")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

def verify_new_user():
    """التحقق من إنشاء المستخدم الجديد"""
    try:
        print("\n🔍 التحقق من المستخدم الجديد...")
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute("SELECT * FROM users WHERE username = 'admin2'")
        user = cursor.fetchone()
        
        if user:
            print("✅ تم العثور على المستخدم admin2:")
            print(f"   - ID: {user['id']}")
            print(f"   - الاسم الكامل: {user['full_name']}")
            print(f"   - الدور: {user['role']}")
            print(f"   - نشط: {'نعم' if user['is_active'] else 'لا'}")
            print(f"   - تاريخ الإنشاء: {user['created_at']}")
            return True
        else:
            print("❌ لم يتم العثور على المستخدم admin2")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    print("🚀 بدء تحديث قاعدة البيانات لإدارة المستخدمين")
    print("=" * 50)
    
    if update_database_for_user_management():
        if verify_new_user():
            print("\n🎉 تم تحديث قاعدة البيانات بنجاح!")
            print("يمكنك الآن تسجيل الدخول باستخدام:")
            print("اسم المستخدم: admin2")
            print("كلمة المرور: 123456")
        else:
            print("\n⚠️ تم التحديث ولكن فشل التحقق")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات")
