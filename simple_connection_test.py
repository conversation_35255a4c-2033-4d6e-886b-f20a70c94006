#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🧪 اختبار بسيط لإصلاح زر 'اختبار الاتصال'")
print("=" * 50)

try:
    # 1. اختبار استيراد backup_manager
    print("1. اختبار استيراد backup_manager...")
    from utils.backup import backup_manager
    print("✅ تم استيراد backup_manager")
    
    # 2. فحص وجود الدالة
    print("\n2. فحص وجود دالة test_database_connection...")
    if hasattr(backup_manager, 'test_database_connection'):
        print("✅ دالة test_database_connection موجودة")
    else:
        print("❌ دالة test_database_connection مفقودة")
        exit(1)
    
    # 3. اختبار الدالة
    print("\n3. اختبار دالة test_database_connection...")
    success, message = backup_manager.test_database_connection()
    
    if success:
        print(f"✅ نجح الاختبار: {message}")
    else:
        print(f"⚠️ فشل الاختبار: {message}")
    
    print("\n🎉 تم إصلاح المشكلة بنجاح!")
    print("الآن يمكن استخدام زر 'اختبار الاتصال' في التطبيق")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
