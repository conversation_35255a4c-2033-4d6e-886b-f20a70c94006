import mysql.connector

try:
    # الاتصال بقاعدة البيانات
    connection = mysql.connector.connect(
        host='localhost',
        user='root',
        password='mohdam',
        database='money_manager'
    )
    cursor = connection.cursor()
    
    print("✅ متصل بقاعدة البيانات")
    
    # إنشاء الجداول الأساسية
    tables = [
        """CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            full_name VARCHAR(100),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )""",
        
        """CREATE TABLE IF NOT EXISTS currencies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            code VARCHAR(3) UNIQUE NOT NULL,
            name VARCHAR(50) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            exchange_rate DECIMAL(10, 4) DEFAULT 1.0000
        )""",
        
        """CREATE TABLE IF NOT EXISTS account_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            description TEXT
        )""",
        
        """CREATE TABLE IF NOT EXISTS accounts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            account_type_id INT NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )""",
        
        """CREATE TABLE IF NOT EXISTS account_balances (
            id INT AUTO_INCREMENT PRIMARY KEY,
            account_id INT NOT NULL,
            currency_id INT NOT NULL,
            balance DECIMAL(15, 2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_account_currency (account_id, currency_id)
        )""",
        
        """CREATE TABLE IF NOT EXISTS transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            account_id INT NOT NULL,
            type ENUM('income', 'expense', 'transfer') NOT NULL,
            amount DECIMAL(15, 2) NOT NULL,
            currency_id INT NOT NULL,
            description TEXT,
            transaction_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )""",
        
        """CREATE TABLE IF NOT EXISTS activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(100) NOT NULL,
            table_name VARCHAR(50) NOT NULL,
            record_id INT,
            details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )""",
        
        """CREATE TABLE IF NOT EXISTS transfers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            from_account_id INT NOT NULL,
            to_account_id INT NOT NULL,
            from_amount DECIMAL(15, 2) NOT NULL,
            to_amount DECIMAL(15, 2) NOT NULL,
            from_currency_id INT NOT NULL,
            to_currency_id INT NOT NULL,
            exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
            description TEXT,
            transfer_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )"""
    ]
    
    for i, table_sql in enumerate(tables, 1):
        cursor.execute(table_sql)
        print(f"✅ جدول {i} تم إنشاؤه")
    
    # إدراج البيانات الأساسية
    print("📥 إدراج البيانات...")
    
    # العملات
    cursor.execute("INSERT IGNORE INTO currencies (id, code, name, symbol) VALUES (1, 'SAR', 'ريال سعودي', 'ر.س')")
    cursor.execute("INSERT IGNORE INTO currencies (id, code, name, symbol) VALUES (2, 'YER', 'ريال يمني', 'ر.ي')")
    cursor.execute("INSERT IGNORE INTO currencies (id, code, name, symbol) VALUES (3, 'AED', 'درهم إماراتي', 'د.إ')")
    cursor.execute("INSERT IGNORE INTO currencies (id, code, name, symbol) VALUES (4, 'USD', 'دولار أمريكي', '$')")
    
    # أنواع الحسابات
    cursor.execute("INSERT IGNORE INTO account_types (id, name, description) VALUES (1, 'حساب عام', 'حساب عام')")
    cursor.execute("INSERT IGNORE INTO account_types (id, name, description) VALUES (2, 'نقدي', 'صندوق نقدي')")
    cursor.execute("INSERT IGNORE INTO account_types (id, name, description) VALUES (3, 'بنكي', 'حساب بنكي')")
    
    # مستخدم افتراضي
    cursor.execute("INSERT IGNORE INTO users (id, username, email, password_hash, full_name) VALUES (1, 'admin', '<EMAIL>', 'hashed_password', 'المدير')")
    
    connection.commit()
    
    print("🎉 تم إعداد قاعدة البيانات بنجاح!")
    
    cursor.close()
    connection.close()
    
except Exception as e:
    print(f"❌ خطأ: {e}")
