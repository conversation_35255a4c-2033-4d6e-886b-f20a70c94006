#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعداد قاعدة بيانات MySQL مع دعم متعدد العملات
Setup MySQL database with multi-currency support
"""

import mysql.connector
from mysql.connector import Error
import logging
from config.settings import DATABASE_CONFIG

def setup_mysql_database():
    """إعداد قاعدة بيانات MySQL مع الجداول الجديدة"""
    try:
        print("🚀 بدء إعداد قاعدة بيانات MySQL...")
        
        # الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # إنشاء الجداول الجديدة
        print("📊 إنشاء الجداول...")
        
        # جدول account_balances
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_balances (
                id INT AUTO_INCREMENT PRIMARY KEY,
                account_id INT NOT NULL,
                currency_id INT NOT NULL,
                balance DECIMAL(15, 2) DEFAULT 0.00,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (currency_id) REFERENCES currencies(id),
                UNIQUE KEY unique_account_currency (account_id, currency_id)
            )
        """)
        
        # إضافة عمود exchange_rate إلى جدول currencies إذا لم يكن موجوداً
        try:
            cursor.execute("ALTER TABLE currencies ADD COLUMN exchange_rate DECIMAL(10, 4) DEFAULT 1.0000")
            print("   - تم إضافة عمود exchange_rate إلى جدول currencies")
        except Error as e:
            if "Duplicate column" in str(e):
                print("   - عمود exchange_rate موجود بالفعل في جدول currencies")
            else:
                print(f"   - تحذير: {e}")

        # إزالة القيود الخارجية أولاً قبل حذف الأعمدة
        try:
            cursor.execute("ALTER TABLE accounts DROP FOREIGN KEY accounts_ibfk_3")
            print("   - تم إزالة القيد الخارجي accounts_ibfk_3")
        except Error as e:
            print(f"   - تحذير في إزالة القيد الخارجي: {e}")

        # الآن يمكن إزالة الأعمدة
        try:
            cursor.execute("ALTER TABLE accounts DROP COLUMN currency_id")
            print("   - تم إزالة عمود currency_id من جدول accounts")
        except Error as e:
            if "doesn't exist" in str(e):
                print("   - عمود currency_id غير موجود في جدول accounts")
            else:
                print(f"   - تحذير: {e}")

        try:
            cursor.execute("ALTER TABLE accounts DROP COLUMN initial_balance")
            print("   - تم إزالة عمود initial_balance من جدول accounts")
        except Error as e:
            if "doesn't exist" in str(e):
                print("   - عمود initial_balance غير موجود في جدول accounts")
            else:
                print(f"   - تحذير: {e}")

        try:
            cursor.execute("ALTER TABLE accounts DROP COLUMN current_balance")
            print("   - تم إزالة عمود current_balance من جدول accounts")
        except Error as e:
            if "doesn't exist" in str(e):
                print("   - عمود current_balance غير موجود في جدول accounts")
            else:
                print(f"   - تحذير: {e}")
        
        # تحديث العملات المدعومة
        print("💰 تحديث العملات المدعومة...")
        
        # حذف العملات غير المطلوبة
        cursor.execute("DELETE FROM currencies WHERE code NOT IN ('SAR', 'YER', 'AED', 'USD')")
        
        # إدراج العملات المطلوبة
        currencies = [
            ('SAR', 'ريال سعودي', 'ر.س'),
            ('YER', 'ريال يمني', 'ر.ي'),
            ('AED', 'درهم إماراتي', 'د.إ'),
            ('USD', 'دولار أمريكي', '$')
        ]
        
        for code, name, symbol in currencies:
            cursor.execute("""
                INSERT INTO currencies (code, name, symbol, is_active, exchange_rate)
                VALUES (%s, %s, %s, 1, 1.0)
                ON DUPLICATE KEY UPDATE 
                name = VALUES(name), 
                symbol = VALUES(symbol),
                is_active = 1
            """, (code, name, symbol))
        
        # إنشاء جدول activity_log إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                action VARCHAR(100) NOT NULL,
                table_name VARCHAR(50) NOT NULL,
                record_id INT,
                details TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        """)
        
        # إضافة عمود icon إلى جدول account_types إذا لم يكن موجوداً
        try:
            cursor.execute("ALTER TABLE account_types ADD COLUMN icon VARCHAR(50)")
            print("   - تم إضافة عمود icon إلى جدول account_types")
        except Error as e:
            if "Duplicate column" in str(e):
                print("   - عمود icon موجود بالفعل في جدول account_types")
            else:
                print(f"   - تحذير: {e}")

        # إنشاء جدول account_types إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_types (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                description TEXT,
                icon VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # إدراج أنواع الحسابات الافتراضية
        account_types = [
            ('حساب عام', 'حساب عام للاستخدام المتنوع'),
            ('نقدي', 'صندوق نقدي'),
            ('بنكي', 'حساب بنكي'),
            ('محفظة إلكترونية', 'محفظة إلكترونية')
        ]

        for name, description in account_types:
            cursor.execute("""
                INSERT IGNORE INTO account_types (name, description)
                VALUES (%s, %s)
            """, (name, description))
        
        # تحديث جدول transactions إذا لزم الأمر
        try:
            cursor.execute("ALTER TABLE transactions MODIFY COLUMN type ENUM('income', 'expense', 'transfer') NOT NULL")
            print("   - تم تحديث عمود type في جدول transactions")
        except Error as e:
            print(f"   - تحذير في تحديث جدول transactions: {e}")
        
        # إنشاء جدول transfers إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transfers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                from_account_id INT NOT NULL,
                to_account_id INT NOT NULL,
                from_amount DECIMAL(15, 2) NOT NULL,
                to_amount DECIMAL(15, 2) NOT NULL,
                from_currency_id INT NOT NULL,
                to_currency_id INT NOT NULL,
                exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
                description TEXT,
                transfer_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reference_number VARCHAR(50),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (from_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (to_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (from_currency_id) REFERENCES currencies(id),
                FOREIGN KEY (to_currency_id) REFERENCES currencies(id)
            )
        """)
        
        connection.commit()
        
        # عرض إحصائيات
        cursor.execute("SELECT COUNT(*) FROM accounts")
        accounts_count = cursor.fetchone()['count']

        cursor.execute("SELECT COUNT(*) FROM account_balances")
        balances_count = cursor.fetchone()['count']

        cursor.execute("SELECT COUNT(*) FROM currencies WHERE is_active = 1")
        currencies_count = cursor.fetchone()['count']
        
        print(f"\n📊 إحصائيات قاعدة البيانات:")
        print(f"   - عدد الحسابات: {accounts_count}")
        print(f"   - عدد أرصدة العملات: {balances_count}")
        print(f"   - عدد العملات النشطة: {currencies_count}")
        
        cursor.close()
        connection.close()
        
        print("\n✅ تم إعداد قاعدة البيانات بنجاح!")
        print("🎉 يمكنك الآن استخدام ميزة الحسابات متعددة العملات")
        
        return True
        
    except Error as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏦 إعداد قاعدة بيانات MySQL مع دعم متعدد العملات")
    print("=" * 60)
    
    success = setup_mysql_database()
    
    if success:
        print("\n🚀 يمكنك الآن تشغيل التطبيق:")
        print("   python main.py")
    else:
        print("\n❌ فشل في إعداد قاعدة البيانات")
        print("تأكد من:")
        print("   - تشغيل خادم MySQL")
        print("   - صحة إعدادات الاتصال في config/settings.py")
        print("   - وجود صلاحيات كافية للمستخدم")

if __name__ == "__main__":
    main()
