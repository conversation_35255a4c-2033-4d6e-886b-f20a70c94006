#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إصلاح مشكلة اختفاء البيانات من تطبيق إدارة الأموال
"""

import sys
import os
import mysql.connector
from mysql.connector import Error

def check_mysql_connection():
    """فحص الاتصال بـ MySQL"""
    print("🔍 فحص الاتصال بخادم MySQL...")
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        if connection.is_connected():
            print("✅ خادم MySQL يعمل بشكل طبيعي")
            connection.close()
            return True
        else:
            print("❌ خادم MySQL لا يستجيب")
            return False
    except Error as e:
        print(f"❌ خطأ في الاتصال بـ MySQL: {e}")
        return False

def check_database_exists():
    """فحص وجود قاعدة البيانات"""
    print("\n🔍 فحص وجود قاعدة البيانات...")
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        cursor.execute("SHOW DATABASES LIKE 'money_manager'")
        result = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        if result:
            print("✅ قاعدة البيانات 'money_manager' موجودة")
            return True
        else:
            print("❌ قاعدة البيانات 'money_manager' غير موجودة")
            return False
            
    except Error as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def check_tables_exist():
    """فحص وجود الجداول"""
    print("\n🔍 فحص وجود الجداول...")
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        if tables:
            table_names = [table[0] for table in tables]
            print(f"✅ تم العثور على {len(tables)} جدول: {', '.join(table_names)}")
            
            # فحص الجداول الأساسية
            essential_tables = ['users', 'accounts', 'transactions', 'currencies']
            missing_tables = [t for t in essential_tables if t not in table_names]
            
            if missing_tables:
                print(f"⚠️ جداول مفقودة: {', '.join(missing_tables)}")
                return False
            else:
                print("✅ جميع الجداول الأساسية موجودة")
                return True
        else:
            print("❌ لا توجد جداول في قاعدة البيانات")
            return False
            
    except Error as e:
        print(f"❌ خطأ في فحص الجداول: {e}")
        return False

def check_data_exists():
    """فحص وجود البيانات"""
    print("\n🔍 فحص وجود البيانات...")
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        
        # فحص المستخدمين
        cursor.execute("SELECT COUNT(*) as count FROM users")
        users_count = cursor.fetchone()['count']
        
        # فحص الحسابات
        cursor.execute("SELECT COUNT(*) as count FROM accounts")
        accounts_count = cursor.fetchone()['count']
        
        # فحص المعاملات
        cursor.execute("SELECT COUNT(*) as count FROM transactions")
        transactions_count = cursor.fetchone()['count']
        
        cursor.close()
        connection.close()
        
        print(f"📊 إحصائيات البيانات:")
        print(f"   - المستخدمين: {users_count}")
        print(f"   - الحسابات: {accounts_count}")
        print(f"   - المعاملات: {transactions_count}")
        
        if users_count == 0:
            print("❌ لا يوجد مستخدمين")
            return False
        elif accounts_count == 0:
            print("⚠️ لا توجد حسابات")
            return False
        elif transactions_count == 0:
            print("⚠️ لا توجد معاملات")
            return False
        else:
            print("✅ البيانات موجودة")
            return True
            
    except Error as e:
        print(f"❌ خطأ في فحص البيانات: {e}")
        return False

def create_database():
    """إنشاء قاعدة البيانات"""
    print("\n🔧 إنشاء قاعدة البيانات...")
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        cursor.execute("CREATE DATABASE IF NOT EXISTS money_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ تم إنشاء قاعدة البيانات")
        
        cursor.close()
        connection.close()
        return True
        
    except Error as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def run_init_script():
    """تشغيل سكريبت التهيئة"""
    print("\n🔧 تشغيل سكريبت تهيئة قاعدة البيانات...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "database/init_db.py"], 
                              capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ تم تشغيل سكريبت التهيئة بنجاح")
            print(result.stdout)
            return True
        else:
            print("❌ فشل في تشغيل سكريبت التهيئة")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل سكريبت التهيئة: {e}")
        return False

def main():
    """الدالة الرئيسية لإصلاح المشكلة"""
    print("🚨 أداة إصلاح مشكلة اختفاء البيانات")
    print("=" * 50)
    
    # 1. فحص خادم MySQL
    if not check_mysql_connection():
        print("\n❌ خادم MySQL لا يعمل")
        print("💡 الحلول:")
        print("   1. تأكد من تشغيل خادم MySQL")
        print("   2. راجع إعدادات الاتصال في config/settings.py")
        print("   3. تحقق من كلمة المرور")
        return
    
    # 2. فحص وجود قاعدة البيانات
    if not check_database_exists():
        print("\n🔧 محاولة إنشاء قاعدة البيانات...")
        if create_database():
            print("✅ تم إنشاء قاعدة البيانات")
        else:
            print("❌ فشل في إنشاء قاعدة البيانات")
            return
    
    # 3. فحص وجود الجداول
    if not check_tables_exist():
        print("\n🔧 محاولة إنشاء الجداول...")
        if run_init_script():
            print("✅ تم إنشاء الجداول")
        else:
            print("❌ فشل في إنشاء الجداول")
            return
    
    # 4. فحص وجود البيانات
    data_exists = check_data_exists()
    
    # 5. تقديم التوصيات
    print("\n" + "=" * 50)
    print("📋 التوصيات النهائية:")
    
    if data_exists:
        print("✅ البيانات موجودة في قاعدة البيانات")
        print("💡 المشكلة قد تكون في:")
        print("   1. عرض البيانات في الواجهة")
        print("   2. تسجيل الدخول بمستخدم خاطئ")
        print("   3. مشكلة في استعلامات البيانات")
        print("\n🚀 خطوات الإصلاح:")
        print("   1. أعد تشغيل التطبيق: python main.py")
        print("   2. سجل الدخول بحساب admin / 123456")
        print("   3. تحقق من ظهور البيانات")
    else:
        print("⚠️ البيانات مفقودة أو ناقصة")
        print("💡 الحلول:")
        print("   1. استعادة نسخة احتياطية")
        print("   2. إعادة إدخال البيانات يدوياً")
        print("   3. استيراد البيانات من ملف Excel")
    
    print("\n📞 للمساعدة الإضافية:")
    print("   1. احفظ نتائج هذا الفحص")
    print("   2. تحقق من ملفات النسخ الاحتياطية")
    print("   3. اطلب المساعدة التقنية")

if __name__ == "__main__":
    main()
