#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔧 إنشاء المستخدم admin2...")

try:
    import mysql.connector
    import bcrypt
    from datetime import datetime
    from config.settings import DATABASE_CONFIG
    
    connection = mysql.connector.connect(**DATABASE_CONFIG)
    cursor = connection.cursor(dictionary=True)
    
    # البحث عن admin2
    cursor.execute("SELECT * FROM users WHERE username = 'admin2'")
    admin2 = cursor.fetchone()
    
    if admin2:
        print("✅ admin2 موجود مسبقاً")
    else:
        print("⚠️ إنشاء admin2...")
        password_hash = bcrypt.hashpw("123456".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        cursor.execute("""
            INSERT INTO users (username, password_hash, full_name, role, is_active, created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, ('admin2', password_hash, 'المدير الجديد', 'admin', True, datetime.now()))
        
        print("✅ تم إنشاء admin2")
    
    # عرض جميع المستخدمين
    cursor.execute("SELECT username, full_name, role FROM users")
    users = cursor.fetchall()
    print("\n👥 المستخدمين:")
    for user in users:
        print(f"   - {user['username']} ({user['full_name']}) - {user['role']}")
    
    connection.commit()
    connection.close()
    
    print("\n🎉 تم بنجاح!")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
