import mysql.connector
from datetime import date

try:
    connection = mysql.connector.connect(
        host='localhost',
        user='root',
        password='mohdam',
        database='money_manager'
    )
    cursor = connection.cursor()
    
    print("✅ متصل بقاعدة البيانات")
    
    # 1. فحص الحسابات الموجودة
    cursor.execute("SELECT id, name FROM accounts WHERE user_id = 1")
    accounts = cursor.fetchall()
    
    if not accounts:
        print("⚠️ لا توجد حسابات، سأنشئ حساب اختبار...")
        cursor.execute("""
            INSERT INTO accounts (user_id, name, account_type_id, description)
            VALUES (1, 'حساب اختبار المعاملات', 1, 'حساب لاختبار المعاملات')
        """)
        account_id = cursor.lastrowid
        
        # إضافة رصيد ابتدائي
        cursor.execute("""
            INSERT INTO account_balances (account_id, currency_id, balance)
            VALUES (%s, 1, 1000.0)
        """, (account_id,))
        
        print(f"✅ تم إنشاء حساب جديد - ID: {account_id}")
    else:
        account_id = accounts[0][0]
        print(f"📋 استخدام الحساب الموجود - ID: {account_id}")
    
    # 2. اختبار إضافة وارد
    print("\n💰 اختبار إضافة وارد...")
    try:
        cursor.execute("""
            INSERT INTO transactions (user_id, account_id, type, amount, currency_id, description, transaction_date)
            VALUES (%s, %s, 'income', 500.0, 1, 'راتب شهري', %s)
        """, (1, account_id, date.today()))
        
        transaction_id = cursor.lastrowid
        print(f"✅ تم إضافة الوارد - ID: {transaction_id}")
        
        # تحديث الرصيد
        cursor.execute("""
            INSERT INTO account_balances (account_id, currency_id, balance)
            VALUES (%s, 1, 500.0)
            ON DUPLICATE KEY UPDATE balance = balance + 500.0
        """, (account_id,))
        
        print("✅ تم تحديث الرصيد")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الوارد: {e}")
    
    # 3. اختبار إضافة مصروف
    print("\n💸 اختبار إضافة مصروف...")
    try:
        cursor.execute("""
            INSERT INTO transactions (user_id, account_id, type, amount, currency_id, description, transaction_date)
            VALUES (%s, %s, 'expense', 200.0, 1, 'مصروفات متنوعة', %s)
        """, (1, account_id, date.today()))
        
        transaction_id = cursor.lastrowid
        print(f"✅ تم إضافة المصروف - ID: {transaction_id}")
        
        # تحديث الرصيد
        cursor.execute("""
            UPDATE account_balances 
            SET balance = balance - 200.0
            WHERE account_id = %s AND currency_id = 1
        """, (account_id,))
        
        print("✅ تم تحديث الرصيد")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المصروف: {e}")
    
    # 4. عرض الرصيد النهائي
    print("\n📊 الرصيد النهائي:")
    cursor.execute("""
        SELECT ab.balance, c.name, c.symbol
        FROM account_balances ab
        JOIN currencies c ON ab.currency_id = c.id
        WHERE ab.account_id = %s
    """, (account_id,))
    
    balances = cursor.fetchall()
    for balance in balances:
        print(f"   - {balance[1]}: {balance[0]} {balance[2]}")
    
    # 5. عرض المعاملات
    print("\n📋 المعاملات الأخيرة:")
    cursor.execute("""
        SELECT t.type, t.amount, t.description, c.symbol, t.transaction_date
        FROM transactions t
        JOIN currencies c ON t.currency_id = c.id
        WHERE t.account_id = %s
        ORDER BY t.created_at DESC
        LIMIT 5
    """, (account_id,))

    transactions = cursor.fetchall()
    for trans in transactions:
        trans_type = "وارد" if trans[0] == "income" else "مصروف"
        print(f"   - {trans_type}: {trans[1]} {trans[3]} - {trans[2]} ({trans[4]})")
    
    connection.commit()
    cursor.close()
    connection.close()
    
    print("\n🎉 الاختبار نجح!")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
