"""
حاسبة سعر الصرف للعملات المتعددة
"""

from decimal import Decimal
import json
import os
from datetime import datetime

class ExchangeCalculator:
    """حاسبة سعر الصرف للعملات"""
    
    def __init__(self):
        """تهيئة حاسبة سعر الصرف"""
        self.exchange_rates = {
            'SAR': {'USD': 0.2667, 'EUR': 0.2534, 'YER': 665.1, 'AED': 0.9798},
            'USD': {'SAR': 3.75, 'EUR': 0.95, 'YER': 2493.0, 'AED': 3.673},
            'EUR': {'SAR': 3.947, 'USD': 1.053, 'YER': 2624.8, 'AED': 3.867},
            'YER': {'SAR': 0.0015, 'USD': 0.0004, 'EUR': 0.00038, 'AED': 0.00147},
            'AED': {'SAR': 1.021, 'USD': 0.272, 'EUR': 0.259, 'YER': 678.9}
        }
        self.last_updated = datetime.now()
    
    def convert_currency(self, amount, from_currency_code, to_currency_code):
        """
        تحويل مبلغ من عملة إلى أخرى
        
        Args:
            amount (Decimal): المبلغ المراد تحويله
            from_currency_code (str): رمز العملة المصدر
            to_currency_code (str): رمز العملة المستهدفة
            
        Returns:
            Decimal: المبلغ المحول
        """
        if from_currency_code == to_currency_code:
            return amount
        
        # إذا كانت العملة المصدر متاحة في جدول أسعار الصرف
        if from_currency_code in self.exchange_rates:
            if to_currency_code in self.exchange_rates[from_currency_code]:
                rate = Decimal(str(self.exchange_rates[from_currency_code][to_currency_code]))
                return amount * rate
        
        # إذا لم نجد السعر المباشر، نحاول العكس
        if to_currency_code in self.exchange_rates:
            if from_currency_code in self.exchange_rates[to_currency_code]:
                rate = Decimal(str(self.exchange_rates[to_currency_code][from_currency_code]))
                return amount / rate
        
        # إذا لم نجد أي سعر، نعيد نفس المبلغ مع تحذير
        print(f"تحذير: لا يوجد سعر صرف من {from_currency_code} إلى {to_currency_code}")
        return amount
    
    def get_exchange_rate(self, from_currency_code, to_currency_code):
        """
        الحصول على سعر الصرف بين عملتين
        
        Args:
            from_currency_code (str): رمز العملة المصدر
            to_currency_code (str): رمز العملة المستهدفة
            
        Returns:
            Decimal: سعر الصرف
        """
        if from_currency_code == to_currency_code:
            return Decimal('1.0')
        
        if from_currency_code in self.exchange_rates:
            if to_currency_code in self.exchange_rates[from_currency_code]:
                return Decimal(str(self.exchange_rates[from_currency_code][to_currency_code]))
        
        if to_currency_code in self.exchange_rates:
            if from_currency_code in self.exchange_rates[to_currency_code]:
                rate = Decimal(str(self.exchange_rates[to_currency_code][from_currency_code]))
                return Decimal('1.0') / rate
        
        return Decimal('1.0')
    
    def update_exchange_rate(self, from_currency, to_currency, rate):
        """
        تحديث سعر الصرف بين عملتين
        
        Args:
            from_currency (str): رمز العملة المصدر
            to_currency (str): رمز العملة المستهدفة
            rate (float): سعر الصرف الجديد
        """
        if from_currency not in self.exchange_rates:
            self.exchange_rates[from_currency] = {}
        
        self.exchange_rates[from_currency][to_currency] = rate
        
        # تحديث العكس تلقائياً
        if to_currency not in self.exchange_rates:
            self.exchange_rates[to_currency] = {}
        
        self.exchange_rates[to_currency][from_currency] = 1.0 / rate
        self.last_updated = datetime.now()
    
    def get_supported_currencies(self):
        """الحصول على قائمة العملات المدعومة"""
        all_currencies = set(self.exchange_rates.keys())
        for rates in self.exchange_rates.values():
            all_currencies.update(rates.keys())
        return sorted(list(all_currencies))
    
    def save_rates_to_file(self, filepath):
        """حفظ أسعار الصرف إلى ملف"""
        data = {
            'exchange_rates': self.exchange_rates,
            'last_updated': self.last_updated.isoformat()
        }
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def load_rates_from_file(self, filepath):
        """تحميل أسعار الصرف من ملف"""
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.exchange_rates = data.get('exchange_rates', self.exchange_rates)
                last_updated_str = data.get('last_updated')
                if last_updated_str:
                    self.last_updated = datetime.fromisoformat(last_updated_str)
            except Exception as e:
                print(f"خطأ في تحميل أسعار الصرف: {e}")

# إنشاء instance عام للاستخدام
exchange_calculator = ExchangeCalculator()
