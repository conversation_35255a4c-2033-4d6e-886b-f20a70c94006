#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة عرض الأرصدة القديمة في لوحة التحكم
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from utils.auth import auth_manager

def diagnose_dashboard_issue():
    """تشخيص مشكلة لوحة التحكم"""
    print("🔍 تشخيص مشكلة عرض الأرصدة في لوحة التحكم")
    print("=" * 55)
    
    try:
        # الاتصال وتسجيل الدخول
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        user_id = auth_manager.current_user['id']
        print(f"✅ تم تسجيل الدخول (المستخدم ID: {user_id})")
        
        # 1. فحص جدول الحسابات
        print("\n1. فحص جدول الحسابات...")
        accounts = db.execute_query("SELECT * FROM accounts WHERE user_id = %s", (user_id,))
        
        if accounts:
            print(f"✅ تم العثور على {len(accounts)} حساب:")
            for account in accounts:
                print(f"   - {account['name']}: {account.get('balance', 'N/A')} {account.get('currency', 'N/A')}")
        else:
            print("⚠️ لا توجد حسابات في جدول accounts")
        
        # 2. فحص جدول أرصدة الحسابات
        print("\n2. فحص جدول أرصدة الحسابات...")
        try:
            account_balances = db.execute_query("""
                SELECT ab.*, a.name as account_name, c.code as currency_code, c.symbol
                FROM account_balances ab
                LEFT JOIN accounts a ON ab.account_id = a.id
                LEFT JOIN currencies c ON ab.currency_id = c.id
                WHERE a.user_id = %s OR a.user_id IS NULL
            """, (user_id,))
            
            if account_balances:
                print(f"✅ تم العثور على {len(account_balances)} رصيد في account_balances:")
                for balance in account_balances:
                    account_name = balance['account_name'] or f"حساب محذوف (ID: {balance['account_id']})"
                    currency = balance['currency_code'] or 'غير محدد'
                    print(f"   - {account_name}: {balance['balance']} {currency}")
            else:
                print("⚠️ لا توجد أرصدة في جدول account_balances")
        except Exception as e:
            print(f"❌ خطأ في فحص جدول account_balances: {e}")
        
        # 3. فحص الاستعلام المستخدم في لوحة التحكم
        print("\n3. فحص الاستعلام المستخدم في لوحة التحكم...")
        try:
            dashboard_query = """
                SELECT
                    c.code,
                    c.name,
                    c.symbol,
                    COALESCE(SUM(ab.balance), 0) as total_balance,
                    COUNT(DISTINCT a.id) as accounts_count
                FROM currencies c
                LEFT JOIN account_balances ab ON c.id = ab.currency_id
                LEFT JOIN accounts a ON ab.account_id = a.id AND a.user_id = %s AND a.is_active = TRUE
                WHERE c.is_active = TRUE
                GROUP BY c.id, c.code, c.name, c.symbol
                HAVING total_balance > 0 OR accounts_count > 0
                ORDER BY total_balance DESC
            """
            
            dashboard_results = db.execute_query(dashboard_query, (user_id,))
            
            if dashboard_results:
                print(f"✅ نتائج استعلام لوحة التحكم ({len(dashboard_results)} عملة):")
                for result in dashboard_results:
                    print(f"   - {result['name']} ({result['code']}): {result['total_balance']} {result['symbol']} ({result['accounts_count']} حساب)")
            else:
                print("⚠️ لا توجد نتائج من استعلام لوحة التحكم")
        except Exception as e:
            print(f"❌ خطأ في استعلام لوحة التحكم: {e}")
        
        # 4. فحص الأرصدة اليتيمة (بدون حسابات)
        print("\n4. فحص الأرصدة اليتيمة...")
        try:
            orphaned_balances = db.execute_query("""
                SELECT ab.*, c.code as currency_code, c.symbol
                FROM account_balances ab
                LEFT JOIN accounts a ON ab.account_id = a.id
                LEFT JOIN currencies c ON ab.currency_id = c.id
                WHERE a.id IS NULL
            """)
            
            if orphaned_balances:
                print(f"⚠️ تم العثور على {len(orphaned_balances)} رصيد يتيم (بدون حساب):")
                for balance in orphaned_balances:
                    currency = balance['currency_code'] or 'غير محدد'
                    print(f"   - حساب محذوف (ID: {balance['account_id']}): {balance['balance']} {currency}")
            else:
                print("✅ لا توجد أرصدة يتيمة")
        except Exception as e:
            print(f"❌ خطأ في فحص الأرصدة اليتيمة: {e}")
        
        # 5. فحص العملات النشطة
        print("\n5. فحص العملات النشطة...")
        try:
            currencies = db.execute_query("SELECT * FROM currencies WHERE is_active = TRUE")
            
            if currencies:
                print(f"✅ العملات النشطة ({len(currencies)}):")
                for currency in currencies:
                    print(f"   - {currency['name']} ({currency['code']}) - {currency['symbol']}")
            else:
                print("⚠️ لا توجد عملات نشطة")
        except Exception as e:
            print(f"❌ خطأ في فحص العملات: {e}")
        
        auth_manager.logout()
        
        print("\n" + "=" * 55)
        print("📊 ملخص التشخيص:")
        print("المشكلة المحتملة:")
        print("- وجود أرصدة في جدول account_balances مرتبطة بحسابات محذوفة")
        print("- استعلام لوحة التحكم يجمع هذه الأرصدة اليتيمة")
        print("- الحاجة لتنظيف البيانات أو تحديث الاستعلام")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()
        return False

def clean_orphaned_balances():
    """تنظيف الأرصدة اليتيمة"""
    print("\n🧹 تنظيف الأرصدة اليتيمة...")
    
    try:
        # حذف الأرصدة التي لا ترتبط بحسابات موجودة
        deleted_count = db.execute_update("""
            DELETE FROM account_balances 
            WHERE account_id NOT IN (SELECT id FROM accounts)
        """)
        
        print(f"✅ تم حذف {deleted_count} رصيد يتيم")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف الأرصدة اليتيمة: {e}")
        return False

def fix_dashboard_query():
    """إصلاح استعلام لوحة التحكم"""
    print("\n🔧 إصلاح استعلام لوحة التحكم...")
    
    # سيتم تطبيق الإصلاح في ملف main_window.py
    print("سيتم إصلاح الاستعلام في الكود...")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚨 تشخيص وإصلاح مشكلة لوحة التحكم")
    print("=" * 45)
    
    # تشخيص المشكلة
    if not diagnose_dashboard_issue():
        print("❌ فشل في التشخيص")
        return
    
    # اقتراح الحلول
    print("\n💡 الحلول المقترحة:")
    print("1. تنظيف الأرصدة اليتيمة")
    print("2. إصلاح استعلام لوحة التحكم")
    print("3. تحديث منطق عرض البيانات")
    
    response = input("\nهل تريد تنظيف الأرصدة اليتيمة؟ (y/N): ").strip().lower()
    if response in ['y', 'yes', 'نعم']:
        if clean_orphaned_balances():
            print("✅ تم تنظيف الأرصدة اليتيمة")
        else:
            print("❌ فشل في تنظيف الأرصدة اليتيمة")
    
    print("\n📋 الخطوات التالية:")
    print("1. سيتم إصلاح استعلام لوحة التحكم في الكود")
    print("2. إعادة تشغيل التطبيق للتحقق من الإصلاح")
    print("3. التأكد من عرض الأرصدة الصحيحة فقط")

if __name__ == "__main__":
    main()
