"""
أدوات التاريخ والتحويل بين الهجري والميلادي
"""

from datetime import datetime, date, timedelta
from dateutil.relativedelta import relativedelta
import calendar
try:
    from hijri_converter import Hijri, Gregorian
    HIJRI_AVAILABLE = True
except ImportError:
    HIJRI_AVAILABLE = False
    print("تحذير: مكتبة hijri-converter غير مثبتة. سيتم استخدام التاريخ الميلادي فقط.")

class DateUtils:
    """أدوات التاريخ والتحويل"""
    
    @staticmethod
    def get_current_date(date_format='gregorian'):
        """الحصول على التاريخ الحالي"""
        today = date.today()
        
        if date_format == 'hijri' and HIJRI_AVAILABLE:
            hijri_date = <PERSON><PERSON>(today.year, today.month, today.day).to_hijri()
            return {
                'date': today,
                'formatted': f"{hijri_date.day:02d}/{hijri_date.month:02d}/{hijri_date.year}",
                'year': hijri_date.year,
                'month': hijri_date.month,
                'day': hijri_date.day,
                'type': 'hijri'
            }
        else:
            return {
                'date': today,
                'formatted': today.strftime("%d/%m/%Y"),
                'year': today.year,
                'month': today.month,
                'day': today.day,
                'type': 'gregorian'
            }
    
    @staticmethod
    def format_date(date_obj, date_format='gregorian', format_string=None):
        """تنسيق التاريخ"""
        if not isinstance(date_obj, (date, datetime)):
            return str(date_obj)
            
        if isinstance(date_obj, datetime):
            date_obj = date_obj.date()
            
        if date_format == 'hijri' and HIJRI_AVAILABLE:
            try:
                hijri_date = Gregorian(date_obj.year, date_obj.month, date_obj.day).to_hijri()
                if format_string:
                    # تخصيص تنسيق التاريخ الهجري
                    formatted = format_string.replace('%Y', str(hijri_date.year))
                    formatted = formatted.replace('%m', f"{hijri_date.month:02d}")
                    formatted = formatted.replace('%d', f"{hijri_date.day:02d}")
                    return formatted
                else:
                    return f"{hijri_date.day:02d}/{hijri_date.month:02d}/{hijri_date.year} هـ"
            except:
                # في حالة فشل التحويل، استخدم الميلادي
                pass
                
        # التنسيق الميلادي
        if format_string:
            return date_obj.strftime(format_string)
        else:
            return date_obj.strftime("%d/%m/%Y م")
    
    @staticmethod
    def parse_date(date_string, date_format='gregorian'):
        """تحليل نص التاريخ وتحويله إلى كائن date"""
        try:
            if date_format == 'hijri' and HIJRI_AVAILABLE:
                # تحليل التاريخ الهجري
                parts = date_string.replace('هـ', '').strip().split('/')
                if len(parts) == 3:
                    day, month, year = map(int, parts)
                    hijri_date = Hijri(year, month, day)
                    gregorian_date = hijri_date.to_gregorian()
                    return date(gregorian_date.year, gregorian_date.month, gregorian_date.day)
            
            # تحليل التاريخ الميلادي
            date_string = date_string.replace('م', '').strip()
            
            # تجربة تنسيقات مختلفة
            formats = [
                "%d/%m/%Y",
                "%Y-%m-%d",
                "%d-%m-%Y",
                "%Y/%m/%d"
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(date_string, fmt).date()
                except ValueError:
                    continue
                    
        except Exception as e:
            print(f"خطأ في تحليل التاريخ: {e}")
            
        return None
    
    @staticmethod
    def get_month_name(month_number, date_format='gregorian', language='ar'):
        """الحصول على اسم الشهر"""
        if date_format == 'hijri':
            hijri_months_ar = [
                "محرم", "صفر", "ربيع الأول", "ربيع الثاني",
                "جمادى الأولى", "جمادى الثانية", "رجب", "شعبان",
                "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
            ]
            
            hijri_months_en = [
                "Muharram", "Safar", "Rabi' al-awwal", "Rabi' al-thani",
                "Jumada al-awwal", "Jumada al-thani", "Rajab", "Sha'ban",
                "Ramadan", "Shawwal", "Dhu al-Qi'dah", "Dhu al-Hijjah"
            ]
            
            if 1 <= month_number <= 12:
                return hijri_months_ar[month_number - 1] if language == 'ar' else hijri_months_en[month_number - 1]
        else:
            gregorian_months_ar = [
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            ]
            
            gregorian_months_en = [
                "January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"
            ]
            
            if 1 <= month_number <= 12:
                return gregorian_months_ar[month_number - 1] if language == 'ar' else gregorian_months_en[month_number - 1]
        
        return str(month_number)
    
    @staticmethod
    def get_date_range(period_type, date_format='gregorian'):
        """الحصول على نطاق تاريخ حسب النوع"""
        today = date.today()
        
        if period_type == 'today':
            return today, today
        elif period_type == 'yesterday':
            yesterday = today - timedelta(days=1)
            return yesterday, yesterday
        elif period_type == 'this_week':
            start_of_week = today - timedelta(days=today.weekday())
            end_of_week = start_of_week + timedelta(days=6)
            return start_of_week, end_of_week
        elif period_type == 'last_week':
            start_of_last_week = today - timedelta(days=today.weekday() + 7)
            end_of_last_week = start_of_last_week + timedelta(days=6)
            return start_of_last_week, end_of_last_week
        elif period_type == 'this_month':
            start_of_month = today.replace(day=1)
            next_month = start_of_month + relativedelta(months=1)
            end_of_month = next_month - timedelta(days=1)
            return start_of_month, end_of_month
        elif period_type == 'last_month':
            start_of_last_month = (today.replace(day=1) - relativedelta(months=1))
            end_of_last_month = today.replace(day=1) - timedelta(days=1)
            return start_of_last_month, end_of_last_month
        elif period_type == 'this_year':
            start_of_year = today.replace(month=1, day=1)
            end_of_year = today.replace(month=12, day=31)
            return start_of_year, end_of_year
        elif period_type == 'last_year':
            last_year = today.year - 1
            start_of_last_year = date(last_year, 1, 1)
            end_of_last_year = date(last_year, 12, 31)
            return start_of_last_year, end_of_last_year
        elif period_type == 'last_30_days':
            start_date = today - timedelta(days=30)
            return start_date, today
        elif period_type == 'last_90_days':
            start_date = today - timedelta(days=90)
            return start_date, today
        elif period_type == 'last_365_days':
            start_date = today - timedelta(days=365)
            return start_date, today
        
        return today, today
    
    @staticmethod
    def get_period_name(period_type, language='ar'):
        """الحصول على اسم الفترة"""
        period_names = {
            'ar': {
                'today': 'اليوم',
                'yesterday': 'أمس',
                'this_week': 'هذا الأسبوع',
                'last_week': 'الأسبوع الماضي',
                'this_month': 'هذا الشهر',
                'last_month': 'الشهر الماضي',
                'this_year': 'هذا العام',
                'last_year': 'العام الماضي',
                'last_30_days': 'آخر 30 يوم',
                'last_90_days': 'آخر 90 يوم',
                'last_365_days': 'آخر 365 يوم',
                'custom': 'فترة مخصصة'
            },
            'en': {
                'today': 'Today',
                'yesterday': 'Yesterday',
                'this_week': 'This Week',
                'last_week': 'Last Week',
                'this_month': 'This Month',
                'last_month': 'Last Month',
                'this_year': 'This Year',
                'last_year': 'Last Year',
                'last_30_days': 'Last 30 Days',
                'last_90_days': 'Last 90 Days',
                'last_365_days': 'Last 365 Days',
                'custom': 'Custom Period'
            }
        }
        
        return period_names.get(language, {}).get(period_type, period_type)
    
    @staticmethod
    def is_valid_date(year, month, day, date_format='gregorian'):
        """التحقق من صحة التاريخ"""
        try:
            if date_format == 'hijri' and HIJRI_AVAILABLE:
                hijri_date = Hijri(year, month, day)
                return True
            else:
                date(year, month, day)
                return True
        except:
            return False
    
    @staticmethod
    def get_days_between(start_date, end_date):
        """حساب عدد الأيام بين تاريخين"""
        if isinstance(start_date, str):
            start_date = DateUtils.parse_date(start_date)
        if isinstance(end_date, str):
            end_date = DateUtils.parse_date(end_date)
            
        if start_date and end_date:
            return (end_date - start_date).days
        return 0
    
    @staticmethod
    def add_days(date_obj, days):
        """إضافة أيام إلى تاريخ"""
        if isinstance(date_obj, str):
            date_obj = DateUtils.parse_date(date_obj)
        if date_obj:
            return date_obj + timedelta(days=days)
        return None
    
    @staticmethod
    def get_weekday_name(date_obj, language='ar'):
        """الحصول على اسم يوم الأسبوع"""
        if isinstance(date_obj, str):
            date_obj = DateUtils.parse_date(date_obj)
            
        if not date_obj:
            return ""
            
        weekday_names = {
            'ar': ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'],
            'en': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        }
        
        weekday_index = date_obj.weekday()
        return weekday_names.get(language, weekday_names['en'])[weekday_index]

# إنشاء مثيل عام لأدوات التاريخ
date_utils = DateUtils()
