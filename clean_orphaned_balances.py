#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيف الأرصدة اليتيمة من قاعدة البيانات
"""

import mysql.connector
from mysql.connector import Error

def clean_orphaned_balances():
    """تنظيف الأرصدة اليتيمة"""
    print("🧹 تنظيف الأرصدة اليتيمة من قاعدة البيانات...")
    
    # إعدادات الاتصال
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager',
        'charset': 'utf8mb4'
    }
    
    try:
        # الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # 1. فحص الأرصدة اليتيمة قبل التنظيف
        print("\n1. فحص الأرصدة اليتيمة...")
        cursor.execute("""
            SELECT ab.*, c.code as currency_code
            FROM account_balances ab
            LEFT JOIN accounts a ON ab.account_id = a.id
            LEFT JOIN currencies c ON ab.currency_id = c.id
            WHERE a.id IS NULL
        """)
        
        orphaned_balances = cursor.fetchall()
        
        if orphaned_balances:
            print(f"⚠️ تم العثور على {len(orphaned_balances)} رصيد يتيم:")
            total_orphaned = {}
            for balance in orphaned_balances:
                currency = balance['currency_code'] or 'غير محدد'
                amount = float(balance['balance'])
                if currency in total_orphaned:
                    total_orphaned[currency] += amount
                else:
                    total_orphaned[currency] = amount
                print(f"   - حساب محذوف (ID: {balance['account_id']}): {balance['balance']} {currency}")
            
            print("\n📊 إجمالي الأرصدة اليتيمة:")
            for currency, total in total_orphaned.items():
                print(f"   - {currency}: {total}")
        else:
            print("✅ لا توجد أرصدة يتيمة")
            return True
        
        # 2. تنظيف الأرصدة اليتيمة
        print("\n2. حذف الأرصدة اليتيمة...")
        cursor.execute("""
            DELETE FROM account_balances 
            WHERE account_id NOT IN (SELECT id FROM accounts)
        """)
        
        deleted_count = cursor.rowcount
        connection.commit()
        
        print(f"✅ تم حذف {deleted_count} رصيد يتيم")
        
        # 3. التحقق من النتيجة
        print("\n3. التحقق من النتيجة...")
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM account_balances ab
            LEFT JOIN accounts a ON ab.account_id = a.id
            WHERE a.id IS NULL
        """)
        
        remaining_orphaned = cursor.fetchone()['count']
        
        if remaining_orphaned == 0:
            print("✅ تم تنظيف جميع الأرصدة اليتيمة بنجاح")
        else:
            print(f"⚠️ لا تزال هناك {remaining_orphaned} أرصدة يتيمة")
        
        # 4. عرض الأرصدة المتبقية
        print("\n4. الأرصدة المتبقية...")
        cursor.execute("""
            SELECT 
                a.name as account_name,
                ab.balance,
                c.code as currency_code,
                c.symbol
            FROM account_balances ab
            INNER JOIN accounts a ON ab.account_id = a.id
            INNER JOIN currencies c ON ab.currency_id = c.id
            ORDER BY c.code, a.name
        """)
        
        remaining_balances = cursor.fetchall()
        
        if remaining_balances:
            print(f"✅ الأرصدة المتبقية الصحيحة ({len(remaining_balances)}):")
            for balance in remaining_balances:
                print(f"   - {balance['account_name']}: {balance['balance']} {balance['currency_code']}")
        else:
            print("⚠️ لا توجد أرصدة متبقية")
        
        cursor.close()
        connection.close()
        
        print("\n🎉 تم تنظيف قاعدة البيانات بنجاح!")
        return True
        
    except Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 أداة تنظيف الأرصدة اليتيمة")
    print("=" * 40)
    
    print("\n⚠️ تحذير:")
    print("سيتم حذف جميع الأرصدة المرتبطة بحسابات محذوفة")
    print("هذا سيحل مشكلة عرض الأرصدة القديمة في لوحة التحكم")
    
    response = input("\nهل تريد المتابعة؟ (y/N): ").strip().lower()
    if response not in ['y', 'yes', 'نعم']:
        print("تم إلغاء العملية")
        return
    
    if clean_orphaned_balances():
        print("\n✅ تم إصلاح مشكلة لوحة التحكم!")
        
        print("\n📋 ما تم إصلاحه:")
        print("1. ✅ حذف الأرصدة اليتيمة من قاعدة البيانات")
        print("2. ✅ تنظيف جدول account_balances")
        print("3. ✅ إزالة البيانات المرتبطة بحسابات محذوفة")
        
        print("\n🎯 النتائج المتوقعة:")
        print("- لوحة التحكم ستعرض الأرصدة الصحيحة فقط")
        print("- لن تظهر أرصدة الحسابات المحذوفة")
        print("- ملخص العملات سيكون دقيق ومحدث")
        
        print("\n🚀 للتحقق من الإصلاح:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول: admin / 123456")
        print("3. تحقق من لوحة التحكم")
        print("4. راجع ملخص الأرصدة بجميع العملات")
        
    else:
        print("\n❌ فشل في تنظيف قاعدة البيانات")
        print("💡 تحقق من:")
        print("- الاتصال بقاعدة البيانات")
        print("- صلاحيات المستخدم")
        print("- إعدادات قاعدة البيانات")

if __name__ == "__main__":
    main()
