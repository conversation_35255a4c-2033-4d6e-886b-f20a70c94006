# 📋 دليل التثبيت والتشغيل - برنامج إدارة الأموال

## 💿 تثبيت النسخة التنفيذية (Windows)

### الطريقة 1: استخدام ملف التثبيت

1. قم بتشغيل ملف `MoneyManagerSetup.exe` الموجود في مجلد `installer_output`
2. اتبع خطوات معالج التثبيت
3. اختر مكان التثبيت المناسب
4. حدد ما إذا كنت ترغب في إنشاء اختصار على سطح المكتب
5. انقر على "تثبيت" لإكمال عملية التثبيت
6. يمكنك تشغيل البرنامج مباشرة بعد التثبيت

### الطريقة 2: استخدام الملف التنفيذي مباشرة

1. انتقل إلى مجلد `dist\Money Manager`
2. قم بتشغيل ملف `Money Manager.exe`

### إلغاء التثبيت

1. افتح "لوحة التحكم" > "البرامج والميزات"
2. ابحث عن "Money Manager" في قائمة البرامج
3. انقر بزر الماوس الأيمن واختر "إلغاء التثبيت"
4. اتبع التعليمات لإكمال عملية إلغاء التثبيت

## 🖥️ متطلبات النظام

### الحد الأدنى للمتطلبات:
- **نظام التشغيل:** Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **المعالج:** Intel Core i3 أو AMD Ryzen 3 أو أحدث
- **الذاكرة:** 4 GB RAM
- **مساحة التخزين:** 500 MB مساحة فارغة
- **الشبكة:** اتصال إنترنت (للتحديثات الاختيارية)

### المتطلبات الموصى بها:
- **المعالج:** Intel Core i5 أو AMD Ryzen 5 أو أحدث
- **الذاكرة:** 8 GB RAM أو أكثر
- **مساحة التخزين:** 2 GB مساحة فارغة
- **الشاشة:** دقة 1366x768 أو أعلى

## 🛠️ المتطلبات التقنية

### 1. Python
- **الإصدار المطلوب:** Python 3.8 أو أحدث
- **التحميل:** [python.org](https://www.python.org/downloads/)

#### تحقق من إصدار Python:
```bash
python --version
# أو
python3 --version
```

### 2. MySQL Server
- **الإصدار المطلوب:** MySQL 8.0 أو أحدث
- **التحميل:** [mysql.com](https://dev.mysql.com/downloads/mysql/)

#### خيارات بديلة:
- **XAMPP:** [apachefriends.org](https://www.apachefriends.org/)
- **WAMP:** [wampserver.com](http://www.wampserver.com/)
- **MAMP:** [mamp.info](https://www.mamp.info/)

### 3. pip (مدير حزم Python)
- يأتي مع Python 3.4+ تلقائياً
- للتحقق: `pip --version`

## 📦 تثبيت المكتبات المطلوبة

### المكتبات الأساسية:
```
mysql-connector-python==8.2.0    # للاتصال بقاعدة البيانات
customtkinter==5.2.2             # واجهة المستخدم الحديثة
Pillow==10.1.0                   # معالجة الصور
python-dateutil==2.8.2           # أدوات التاريخ
bcrypt==4.1.2                    # تشفير كلمات المرور
```

### المكتبات الإضافية:
```
reportlab==4.0.7                 # إنشاء تقارير PDF
matplotlib==3.8.2               # الرسوم البيانية
pandas==2.1.4                   # تحليل البيانات
openpyxl==3.1.2                 # ملفات Excel
hijri-converter==2.3.1          # التحويل الهجري
schedule==1.2.0                 # جدولة المهام
```

## 🚀 خطوات التثبيت

### الخطوة 1: تثبيت Python

#### على Windows:
1. حمل Python من [python.org](https://www.python.org/downloads/)
2. شغل ملف التثبيت
3. ✅ تأكد من تحديد "Add Python to PATH"
4. اختر "Install Now"

#### على macOS:
```bash
# باستخدام Homebrew
brew install python3

# أو حمل من الموقع الرسمي
```

#### على Ubuntu/Debian:
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

### الخطوة 2: تثبيت MySQL

#### على Windows:
1. حمل MySQL Installer من [mysql.com](https://dev.mysql.com/downloads/installer/)
2. اختر "Developer Default"
3. اتبع خطوات التثبيت
4. احفظ كلمة مرور root

#### على macOS:
```bash
# باستخدام Homebrew
brew install mysql
brew services start mysql
```

#### على Ubuntu/Debian:
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

### الخطوة 3: تحضير المشروع

#### 1. إنشاء مجلد المشروع:
```bash
mkdir money-manager
cd money-manager
```

#### 2. إنشاء بيئة افتراضية (موصى به):
```bash
# إنشاء البيئة الافتراضية
python -m venv money_manager_env

# تفعيل البيئة الافتراضية
# على Windows:
money_manager_env\Scripts\activate

# على macOS/Linux:
source money_manager_env/bin/activate
```

#### 3. نسخ ملفات المشروع:
- انسخ جميع ملفات المشروع إلى المجلد

### الخطوة 4: تثبيت المتطلبات

```bash
# تثبيت جميع المتطلبات
pip install -r requirements.txt

# أو تثبيت كل مكتبة منفصلة:
pip install mysql-connector-python==8.2.0
pip install customtkinter==5.2.2
pip install Pillow==10.1.0
pip install python-dateutil==2.8.2
pip install bcrypt==4.1.2
pip install reportlab==4.0.7
pip install matplotlib==3.8.2
pip install pandas==2.1.4
pip install openpyxl==3.1.2
pip install hijri-converter==2.3.1
pip install schedule==1.2.0
```

## ⚙️ إعداد قاعدة البيانات

### الطريقة التلقائية (موصى بها):
البرنامج سينشئ قاعدة البيانات تلقائياً عند التشغيل الأول.

### الطريقة اليدوية:
```sql
-- الاتصال بـ MySQL
mysql -u root -p

-- إنشاء قاعدة البيانات
CREATE DATABASE money_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم (اختياري)
CREATE USER 'money_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON money_manager.* TO 'money_user'@'localhost';
FLUSH PRIVILEGES;
```

## 🎯 التشغيل الأول

### 1. تشغيل البرنامج:
```bash
# الطريقة الأولى
python main.py

# الطريقة الثانية (Windows)
run.bat

# الطريقة الثالثة (macOS/Linux)
./run.sh
```

### 2. الإعداد الأولي:
1. **كلمة مرور MySQL:** أدخل كلمة مرور root أو اتركها فارغة
2. **إنشاء المدير الأول:** أكمل بيانات حساب المدير
3. **بدء الاستخدام:** ستظهر لوحة التحكم الرئيسية

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### خطأ: "Python is not recognized"
**الحل:**
- تأكد من تثبيت Python وإضافته لـ PATH
- أعد تشغيل Command Prompt/Terminal

#### خطأ: "No module named 'customtkinter'"
**الحل:**
```bash
pip install customtkinter
# أو
pip install -r requirements.txt
```

#### خطأ: "Access denied for user 'root'"
**الحل:**
- تأكد من كلمة مرور MySQL
- تأكد من تشغيل MySQL Server

#### خطأ: "Can't connect to MySQL server"
**الحل:**
```bash
# تشغيل MySQL على Windows
net start mysql

# تشغيل MySQL على macOS
brew services start mysql

# تشغيل MySQL على Ubuntu
sudo systemctl start mysql
```

#### خطأ في الواجهة الرسومية:
**الحل:**
- تأكد من تثبيت Pillow
- على Linux: `sudo apt install python3-tk`

### فحص المتطلبات:
```bash
# فحص Python
python --version

# فحص pip
pip --version

# فحص MySQL
mysql --version

# فحص المكتبات
pip list | grep -E "(customtkinter|mysql-connector|bcrypt|Pillow)"
```

## 📁 هيكل الملفات بعد التثبيت

```
money-manager/
├── main.py                    # ملف التشغيل الرئيسي
├── requirements.txt           # قائمة المتطلبات
├── run.bat                   # تشغيل سريع (Windows)
├── run.sh                    # تشغيل سريع (Linux/Mac)
├── config/                   # ملفات الإعدادات
├── database/                 # قاعدة البيانات
├── gui/                      # واجهة المستخدم
├── utils/                    # أدوات مساعدة
├── logs/                     # ملفات السجلات (ينشأ تلقائياً)
├── backups/                  # النسخ الاحتياطية (ينشأ تلقائياً)
├── uploads/                  # الملفات المرفوعة (ينشأ تلقائياً)
├── reports/                  # التقارير (ينشأ تلقائياً)
└── assets/                   # الموارد (ينشأ تلقائياً)
```

## 🎉 التحقق من نجاح التثبيت

إذا ظهرت نافذة تسجيل الدخول بالتصميم البنفسجي الأنيق، فقد تم التثبيت بنجاح! 🎊

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:
1. راجع ملفات السجل في مجلد `logs/`
2. تأكد من تثبيت جميع المتطلبات
3. تحقق من إعدادات قاعدة البيانات
4. راجع هذا الدليل مرة أخرى

---

**مبروك! أنت الآن جاهز لاستخدام برنامج إدارة الأموال! 💰✨**
