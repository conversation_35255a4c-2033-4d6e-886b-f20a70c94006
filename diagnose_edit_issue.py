#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص شامل لمشكلة عدم حفظ التعديلات
"""

import sys
import os
import traceback

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_xampp_connection():
    """اختبار الاتصال بـ XAMPP"""
    print("🔌 اختبار الاتصال بـ XAMPP...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        # محاولة الاتصال
        print("📡 محاولة الاتصال...")
        if db.connect():
            print("✅ تم الاتصال بـ XAMPP بنجاح!")
            
            # اختبار استعلام بسيط
            result = db.execute_query("SELECT VERSION() as version, DATABASE() as db_name")
            if result:
                print(f"   إصدار MySQL: {result[0]['version']}")
                print(f"   قاعدة البيانات: {result[0]['db_name']}")
            
            # اختبار صلاحيات الكتابة
            try:
                db.execute_update("CREATE TEMPORARY TABLE test_write (id INT)")
                print("✅ صلاحيات الكتابة متاحة")
                return True
            except Exception as e:
                print(f"❌ مشكلة في صلاحيات الكتابة: {e}")
                return False
                
        else:
            print("❌ فشل في الاتصال بـ XAMPP!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        traceback.print_exc()
        return False

def check_transactions_table():
    """فحص جدول المعاملات"""
    print("\n📋 فحص جدول transactions...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        # فحص هيكل الجدول
        print("🔍 فحص هيكل الجدول...")
        columns = db.execute_query("DESCRIBE transactions")
        
        if not columns:
            print("❌ جدول transactions غير موجود!")
            return False
        
        column_names = [col['Field'] for col in columns]
        required_columns = ['id', 'amount', 'account_id', 'currency_id', 'category_name', 'description', 'transaction_date', 'transaction_type', 'updated_at']
        
        print("   الأعمدة الموجودة:")
        for col in columns:
            status = "✅" if col['Field'] in required_columns else "📋"
            print(f"   {status} {col['Field']}: {col['Type']} {'NULL' if col['Null'] == 'YES' else 'NOT NULL'}")
        
        # التحقق من الأعمدة المفقودة
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"\n⚠️ أعمدة مفقودة: {', '.join(missing_columns)}")
            
            # إضافة الأعمدة المفقودة
            for col in missing_columns:
                try:
                    if col == 'category_name':
                        print(f"🔧 إضافة عمود {col}...")
                        db.execute_update("""
                            ALTER TABLE transactions 
                            ADD COLUMN category_name VARCHAR(100) NULL 
                            COMMENT 'اسم التصنيف المدخل يدوياً'
                        """)
                        print(f"✅ تم إضافة عمود {col}")
                    elif col == 'updated_at':
                        print(f"🔧 إضافة عمود {col}...")
                        db.execute_update("""
                            ALTER TABLE transactions 
                            ADD COLUMN updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                            COMMENT 'تاريخ آخر تحديث'
                        """)
                        print(f"✅ تم إضافة عمود {col}")
                except Exception as e:
                    print(f"❌ فشل في إضافة عمود {col}: {e}")
        else:
            print("\n✅ جميع الأعمدة المطلوبة موجودة")
        
        # اختبار استعلام البيانات
        print("\n🔍 اختبار استعلام البيانات...")
        test_query = """
            SELECT t.id, t.amount, t.account_id, t.currency_id, t.category_name, 
                   t.description, t.transaction_date, t.transaction_type,
                   a.name as account_name, c.symbol as currency_symbol, c.name as currency_name
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            LIMIT 1
        """
        
        result = db.execute_query(test_query)
        if result:
            print("✅ استعلام البيانات يعمل بشكل صحيح")
            print(f"   عدد المعاملات: {len(result)}")
            return True
        else:
            print("⚠️ لا توجد معاملات للاختبار")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص الجدول: {e}")
        traceback.print_exc()
        return False

def test_transaction_update_detailed():
    """اختبار مفصل لدالة تحديث المعاملة"""
    print("\n🧪 اختبار مفصل لدالة تحديث المعاملة...")
    print("=" * 50)
    
    try:
        from database.models import Transaction
        from database.connection import db
        
        # التحقق من وجود دالة update
        if not hasattr(Transaction, 'update'):
            print("❌ دالة Transaction.update غير موجودة!")
            return False
        
        print("✅ دالة Transaction.update موجودة")
        
        # البحث عن معاملة للاختبار
        transactions = db.execute_query("""
            SELECT t.id, t.amount, t.account_id, t.currency_id, t.category_name,
                   t.description, t.transaction_date, t.transaction_type,
                   a.name as account_name, c.symbol as currency_symbol
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            LIMIT 1
        """)
        
        if not transactions:
            print("⚠️ لا توجد معاملات للاختبار")
            print("   سيتم إنشاء معاملة تجريبية...")
            
            # إنشاء معاملة تجريبية
            from utils.auth import auth_manager
            
            # تعيين مستخدم تجريبي
            users = db.execute_query("SELECT id FROM users LIMIT 1")
            if users:
                auth_manager.current_user = {'id': users[0]['id']}
                user_id = users[0]['id']
                
                # البحث عن حساب
                accounts = db.execute_query("SELECT id FROM accounts WHERE user_id = %s LIMIT 1", (user_id,))
                if accounts:
                    account_id = accounts[0]['id']
                    
                    # إنشاء معاملة تجريبية
                    db.execute_update("""
                        INSERT INTO transactions (user_id, account_id, currency_id, transaction_type, amount, description, transaction_date)
                        VALUES (%s, %s, 1, 'income', 1000.00, 'معاملة تجريبية للاختبار', CURDATE())
                    """, (user_id, account_id))
                    
                    # إعادة البحث عن المعاملة
                    transactions = db.execute_query("""
                        SELECT t.id, t.amount, t.account_id, t.currency_id, t.category_name,
                               t.description, t.transaction_date, t.transaction_type,
                               a.name as account_name, c.symbol as currency_symbol
                        FROM transactions t
                        JOIN accounts a ON t.account_id = a.id
                        JOIN currencies c ON t.currency_id = c.id
                        WHERE t.description = 'معاملة تجريبية للاختبار'
                        LIMIT 1
                    """)
        
        if not transactions:
            print("❌ لا يمكن إنشاء معاملة للاختبار")
            return False
        
        test_transaction = transactions[0]
        transaction_id = test_transaction['id']
        
        print(f"📝 اختبار المعاملة رقم: {transaction_id}")
        print(f"   المبلغ الحالي: {test_transaction['amount']}")
        print(f"   الوصف الحالي: '{test_transaction['description']}'")
        print(f"   التصنيف الحالي: '{test_transaction['category_name']}'")
        
        # اختبار 1: تحديث الوصف فقط
        print("\n   🔄 اختبار 1: تحديث الوصف...")
        original_description = test_transaction['description']
        test_description = f"تم التحديث - {original_description}"
        
        print(f"      الوصف الجديد: '{test_description}'")
        
        success = Transaction.update(
            transaction_id=transaction_id,
            description=test_description
        )
        
        print(f"      نتيجة التحديث: {success}")
        
        if success:
            # التحقق من التحديث في قاعدة البيانات
            updated = db.execute_query(
                "SELECT description FROM transactions WHERE id = %s",
                (transaction_id,)
            )
            
            if updated and updated[0]['description'] == test_description:
                print("      ✅ تم حفظ التحديث في قاعدة البيانات")
                
                # إعادة الوصف الأصلي
                Transaction.update(transaction_id=transaction_id, description=original_description)
                print("      ✅ تم إعادة الوصف الأصلي")
            else:
                print("      ❌ لم يتم حفظ التحديث في قاعدة البيانات")
                print(f"      المتوقع: '{test_description}'")
                print(f"      الموجود: '{updated[0]['description'] if updated else 'لا شيء'}'")
                return False
        else:
            print("      ❌ فشل في تحديث الوصف")
            return False
        
        # اختبار 2: تحديث متعدد الحقول
        print("\n   🔄 اختبار 2: تحديث متعدد الحقول...")
        
        new_amount = float(test_transaction['amount']) + 100
        new_category = "تصنيف اختبار"
        new_description = "وصف اختبار متعدد"
        
        print(f"      المبلغ الجديد: {new_amount}")
        print(f"      التصنيف الجديد: '{new_category}'")
        print(f"      الوصف الجديد: '{new_description}'")
        
        success = Transaction.update(
            transaction_id=transaction_id,
            amount=new_amount,
            category_name=new_category,
            description=new_description
        )
        
        print(f"      نتيجة التحديث: {success}")
        
        if success:
            # التحقق من التحديث
            updated = db.execute_query(
                "SELECT amount, category_name, description FROM transactions WHERE id = %s",
                (transaction_id,)
            )
            
            if updated:
                result = updated[0]
                amount_ok = float(result['amount']) == new_amount
                category_ok = result['category_name'] == new_category
                description_ok = result['description'] == new_description
                
                print(f"      المبلغ: {result['amount']} {'✅' if amount_ok else '❌'}")
                print(f"      التصنيف: '{result['category_name']}' {'✅' if category_ok else '❌'}")
                print(f"      الوصف: '{result['description']}' {'✅' if description_ok else '❌'}")
                
                if amount_ok and category_ok and description_ok:
                    print("      ✅ جميع التحديثات تم حفظها بنجاح")
                    
                    # إعادة القيم الأصلية
                    Transaction.update(
                        transaction_id=transaction_id,
                        amount=test_transaction['amount'],
                        category_name=test_transaction['category_name'],
                        description=original_description
                    )
                    print("      ✅ تم إعادة القيم الأصلية")
                    
                    return True
                else:
                    print("      ❌ بعض التحديثات لم يتم حفظها")
                    return False
            else:
                print("      ❌ لا يمكن قراءة البيانات المحدثة")
                return False
        else:
            print("      ❌ فشل في التحديث متعدد الحقول")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحديث: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص شامل لمشكلة عدم حفظ التعديلات")
    print("=" * 60)
    
    # 1. اختبار XAMPP
    xampp_ok = test_xampp_connection()
    
    # 2. فحص جدول المعاملات
    table_ok = check_transactions_table()
    
    # 3. اختبار دالة التحديث
    update_ok = test_transaction_update_detailed()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص التشخيص:")
    print(f"   🔌 XAMPP: {'✅ يعمل' if xampp_ok else '❌ مشكلة'}")
    print(f"   📋 جدول المعاملات: {'✅ صحيح' if table_ok else '❌ مشكلة'}")
    print(f"   🔄 دالة التحديث: {'✅ تعمل' if update_ok else '❌ مشكلة'}")
    
    if xampp_ok and table_ok and update_ok:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("   المشكلة قد تكون في واجهة المستخدم أو استدعاء الدالة")
        print("\n🔧 الخطوات التالية:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. فتح نافذة التعديل")
        print("3. مراقبة رسائل وحدة التحكم")
        print("4. إذا ظهرت أخطاء، انسخها وأرسلها")
    else:
        print("\n❌ توجد مشاكل تحتاج إصلاح!")
        if not xampp_ok:
            print("   🔧 تأكد من تشغيل XAMPP وخدمة MySQL")
        if not table_ok:
            print("   🔧 أعد تشغيل هذا الملف لإصلاح الجدول")
        if not update_ok:
            print("   🔧 راجع ملف database/models.py")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
