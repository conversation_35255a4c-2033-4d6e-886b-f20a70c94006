# ملفات Excel النموذجية لاختبار الاستيراد مع RTL

## 📋 محتويات المجلد:

### 1. income_sample.xlsx (نموذج الواردات)
- يحتوي على 7 معاملات واردة تجريبية
- البيانات تشمل: راتب، مكافآت، عمولات، استثمارات
- العملات المستخدمة: SAR, USD, AED
- التواريخ: يناير 2024

### 2. expenses_sample.xlsx (نموذج المصروفات)  
- يحتوي على 7 معاملات مصروفات تجريبية
- البيانات تشمل: فواتير، تسوق، إيجار، صيانة
- العملات المستخدمة: SAR
- التواريخ: يناير 2024

## 🏦 الحسابات المستخدمة:
- الصندوق النقدي
- حساب الراجحي
- حساب التوفير
- محفظة PayPal
- بطاقة فيزا

## 💱 العملات المدعومة:
- SAR (ريال سعودي)
- USD (دولار أمريكي)
- AED (درهم إماراتي)
- YER (ريال يمني)

## 📊 هيكل الأعمدة (بالترتيب):
1. المبلغ - رقم عشري
2. الحساب - اسم الحساب الموجود في النظام
3. العملة - رمز العملة (SAR, USD, AED, YER)
4. التاريخ - تنسيق YYYY-MM-DD
5. الوصف - نص عربي يصف المعاملة

## 🧪 كيفية الاختبار:
1. شغل التطبيق: python main.py
2. سجل الدخول (admin / 123456)
3. انتقل إلى قسم الواردات أو المصروفات
4. اضغط على "📂 استيراد من Excel"
5. اختر أحد الملفات النموذجية
6. راجع نوافذ المعاينة والمعالجة والنتائج
7. تأكد من عرض النصوص العربية بالاتجاه الصحيح (RTL)

## ✅ نقاط التحقق من RTL:
- [ ] نافذة معاينة البيانات تعرض النصوص بـ RTL
- [ ] نافذة "جاري الاستيراد..." تعرض النصوص بـ RTL  
- [ ] نافذة نتائج الاستيراد تعرض النصوص بـ RTL
- [ ] منطقة تفاصيل الأخطاء تعرض النصوص بـ RTL
- [ ] جميع الأزرار والتسميات محاذاة بشكل صحيح
- [ ] البيانات تستورد بنجاح إلى قاعدة البيانات
- [ ] أرصدة الحسابات تتحدث بشكل صحيح

## 🔧 في حالة وجود مشاكل:
1. تأكد من أن الحسابات المذكورة موجودة في النظام
2. تأكد من أن العملات مدعومة
3. راجع ملفات السجلات في مجلد logs/
4. شغل diagnose_excel_import_error.py للتشخيص

---
تم إنشاء هذه الملفات بواسطة create_simple_excel_samples.py
التاريخ: 2025-07-16 18:18:53