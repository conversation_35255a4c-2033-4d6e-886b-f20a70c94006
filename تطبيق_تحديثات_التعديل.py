#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق تحديثات التعديل الشامل للمعاملات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def apply_updates():
    """تطبيق جميع التحديثات المطلوبة"""
    print("🔧 تطبيق تحديثات التعديل الشامل للمعاملات")
    print("=" * 60)
    
    try:
        from database.connection import db
        
        # التحقق من الاتصال
        print("🔌 التحقق من اتصال قاعدة البيانات...")
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                print("تأكد من تشغيل XAMPP وإعداد قاعدة البيانات بشكل صحيح")
                return False
        
        print("✅ الاتصال بقاعدة البيانات نشط")
        
        # 1. إضافة عمود category_name
        print("\n📋 الخطوة 1: إضافة عمود category_name...")
        try:
            db.execute_update("""
                ALTER TABLE transactions 
                ADD COLUMN category_name VARCHAR(100) NULL 
                COMMENT 'اسم التصنيف المدخل يدوياً'
            """)
            print("✅ تم إضافة عمود category_name")
        except Exception as e:
            if "Duplicate column name" in str(e) or "already exists" in str(e):
                print("✅ عمود category_name موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إضافة عمود category_name: {e}")
        
        # 2. إنشاء فهرس للبحث السريع
        print("\n📊 الخطوة 2: إنشاء فهرس للبحث السريع...")
        try:
            db.execute_update("""
                CREATE INDEX idx_category_name ON transactions(category_name)
            """)
            print("✅ تم إنشاء فهرس category_name")
        except Exception as e:
            if "already exists" in str(e) or "Duplicate key name" in str(e):
                print("✅ فهرس category_name موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إنشاء الفهرس: {e}")
        
        # 3. التحقق من العملات المطلوبة
        print("\n💱 الخطوة 3: التحقق من العملات...")
        try:
            currencies = db.execute_query("SELECT code, name, is_active FROM currencies WHERE is_active = TRUE")
            active_currencies = [c['code'] for c in currencies]
            required_currencies = ['SAR', 'YER', 'AED', 'USD']
            
            missing_currencies = [c for c in required_currencies if c not in active_currencies]
            
            if missing_currencies:
                print(f"⚠️ العملات المفقودة: {', '.join(missing_currencies)}")
                print("يرجى تشغيل update_currencies.py لإضافة العملات المطلوبة")
            else:
                print("✅ جميع العملات المطلوبة متوفرة")
                
        except Exception as e:
            print(f"⚠️ خطأ في التحقق من العملات: {e}")
        
        # 4. اختبار دالة التحديث
        print("\n🧪 الخطوة 4: اختبار دالة التحديث...")
        try:
            from database.models import Transaction
            
            # التحقق من وجود دالة update
            if hasattr(Transaction, 'update'):
                print("✅ دالة Transaction.update متوفرة")
            else:
                print("❌ دالة Transaction.update غير موجودة")
                return False
                
        except Exception as e:
            print(f"⚠️ خطأ في اختبار دالة التحديث: {e}")
        
        # 5. عرض هيكل الجدول النهائي
        print("\n📋 الخطوة 5: عرض هيكل جدول المعاملات:")
        try:
            columns = db.execute_query("DESCRIBE transactions")
            print("   الأعمدة المتوفرة:")
            for col in columns:
                status = "✅" if col['Field'] in ['amount', 'account_id', 'currency_id', 'category_name', 'description', 'transaction_date'] else "📋"
                print(f"   {status} {col['Field']}: {col['Type']} {'NULL' if col['Null'] == 'YES' else 'NOT NULL'}")
        except Exception as e:
            print(f"⚠️ خطأ في عرض هيكل الجدول: {e}")
        
        print("\n🎉 تم تطبيق جميع التحديثات بنجاح!")
        print("\nالميزات الجديدة المتاحة:")
        print("✅ تعديل المبلغ")
        print("✅ تغيير الحساب") 
        print("✅ تغيير العملة")
        print("✅ تعديل التصنيف")
        print("✅ تعديل التاريخ")
        print("✅ تعديل الوصف")
        print("✅ إدارة تلقائية للأرصدة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في تطبيق التحديثات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🏦 برنامج تطبيق تحديثات التعديل الشامل")
    print("=" * 60)
    
    # تطبيق التحديثات
    success = apply_updates()
    
    if success:
        print("\n🎊 تم تطبيق جميع التحديثات بنجاح!")
        print("\nيمكنك الآن:")
        print("1. تشغيل البرنامج الرئيسي: python main.py")
        print("2. الذهاب لقسم الواردات أو المصروفات")
        print("3. تجربة ميزة التعديل الشاملة الجديدة")
        print("\nراجع ملف 'دليل_التعديل_الشامل_للمعاملات.md' للتفاصيل الكاملة")
    else:
        print("\n❌ فشل في تطبيق بعض التحديثات!")
        print("يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة")
        print("\nنصائح لحل المشاكل:")
        print("1. تأكد من تشغيل XAMPP")
        print("2. تأكد من وجود قاعدة البيانات money_manager")
        print("3. تأكد من صحة إعدادات الاتصال")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
