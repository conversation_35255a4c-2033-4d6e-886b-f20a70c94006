#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من أن جميع التعديلات تم تطبيقها بشكل صحيح
"""

import os
import sys

def check_file_exists(file_path, description):
    """التحقق من وجود ملف"""
    if os.path.exists(file_path):
        print(f"✅ {description}: موجود")
        return True
    else:
        print(f"❌ {description}: غير موجود")
        return False

def check_file_content(file_path, search_text, description):
    """التحقق من محتوى ملف"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if search_text in content:
                print(f"✅ {description}: تم التطبيق")
                return True
            else:
                print(f"❌ {description}: لم يتم التطبيق")
                return False
    except Exception as e:
        print(f"❌ {description}: خطأ في القراءة - {e}")
        return False

def main():
    """فحص جميع التعديلات"""
    
    print("=" * 60)
    print("🔍 التحقق من التعديلات المطبقة")
    print("=" * 60)
    
    all_good = True
    
    # 1. فحص الملفات الأساسية
    print("\n📁 فحص الملفات الأساسية:")
    files_to_check = [
        ("main.py", "الملف الرئيسي"),
        ("setup_database.py", "إعداد قاعدة البيانات"),
        ("add_demo_accounts.py", "البيانات التجريبية"),
        ("quick_demo.py", "التشغيل السريع"),
        ("README_FIXED.md", "دليل الاستخدام المحدث"),
        ("database/models.py", "نماذج البيانات"),
        ("gui/main_window.py", "الواجهة الرئيسية"),
    ]
    
    for file_path, description in files_to_check:
        if not check_file_exists(file_path, description):
            all_good = False
    
    # 2. فحص إصلاحات قاعدة البيانات
    print("\n🗃️ فحص إصلاحات قاعدة البيانات:")
    
    db_fixes = [
        ("setup_database.py", "transaction_type ENUM('income', 'expense')", "نوع المعاملة في setup_database"),
        ("database/models.py", "transaction_type", "استخدام transaction_type في النماذج"),
        ("gui/main_window.py", "transaction_type", "استخدام transaction_type في الواجهة"),
    ]
    
    for file_path, search_text, description in db_fixes:
        if not check_file_content(file_path, search_text, description):
            all_good = False
    
    # 3. فحص البيانات التجريبية
    print("\n💾 فحص البيانات التجريبية:")
    
    demo_checks = [
        ("add_demo_accounts.py", "الحساب الشخصي - الراجحي", "حساب الراجحي"),
        ("add_demo_accounts.py", "حساب الأعمال - الأهلي", "حساب الأهلي"),
        ("add_demo_accounts.py", "محفظة دولارية", "المحفظة الدولارية"),
        ("add_demo_accounts.py", "income_transactions", "معاملات الواردات"),
        ("add_demo_accounts.py", "expense_transactions", "معاملات المصروفات"),
        ("add_demo_accounts.py", "transfer_transactions", "التحويلات"),
    ]
    
    for file_path, search_text, description in demo_checks:
        if not check_file_content(file_path, search_text, description):
            all_good = False
    
    # 4. فحص الواجهة
    print("\n🖥️ فحص تحديثات الواجهة:")
    
    ui_fixes = [
        ("gui/main_window.py", "show_dashboard", "لوحة التحكم"),
        ("gui/main_window.py", "show_income", "صفحة الواردات"),
        ("gui/main_window.py", "show_expense", "صفحة المصروفات"),
        ("gui/main_window.py", "show_accounts", "صفحة الحسابات"),
        ("gui/main_window.py", "show_transfers", "صفحة التحويلات"),
        ("gui/main_window.py", "show_reports", "صفحة التقارير"),
    ]
    
    for file_path, search_text, description in ui_fixes:
        if not check_file_content(file_path, search_text, description):
            all_good = False
    
    # 5. فحص متطلبات النظام
    print("\n📦 فحص المتطلبات:")
    
    try:
        import customtkinter
        print("✅ customtkinter: متوفر")
    except ImportError:
        print("❌ customtkinter: غير متوفر")
        all_good = False
    
    try:
        import mysql.connector
        print("✅ mysql-connector-python: متوفر")
    except ImportError:
        print("❌ mysql-connector-python: غير متوفر")
        all_good = False
    
    try:
        import bcrypt
        print("✅ bcrypt: متوفر")
    except ImportError:
        print("❌ bcrypt: غير متوفر")
        all_good = False
    
    try:
        from PIL import Image
        print("✅ Pillow: متوفر")
    except ImportError:
        print("❌ Pillow: غير متوفر")
        all_good = False
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if all_good:
        print("🎉 جميع التعديلات تم تطبيقها بنجاح!")
        print("✅ البرنامج جاهز للتشغيل")
        print("\n🚀 لتشغيل البرنامج:")
        print("   python main.py")
        print("\n🚀 أو للتشغيل السريع:")
        print("   python quick_demo.py")
    else:
        print("⚠️ هناك بعض المشاكل التي تحتاج إلى إصلاح")
        print("💡 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    
    # ملخص الميزات
    print("\n📋 ملخص الميزات المتاحة:")
    print("   • 5 حسابات تجريبية مختلفة")
    print("   • أكثر من 25 معاملة متنوعة")
    print("   • 5 تحويلات بين الحسابات")
    print("   • واجهة مستخدم حديثة")
    print("   • تقارير مالية شاملة")
    print("   • دعم عملات متعددة")
    
    print("\n🔑 بيانات تسجيل الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: 123456")

if __name__ == "__main__":
    main()
    input("\n🔄 اضغط Enter للخروج...")
