#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريپت رئيسي لفحص وإصلاح جميع مشاكل قاعدة البيانات في تطبيق إدارة الأموال
"""

import os
import sys
import json
import subprocess
from datetime import datetime

def run_script(script_name, description):
    """تشغيل سكريپت وعرض النتائج"""
    print(f"\n{'='*60}")
    print(f"تشغيل: {description}")
    print(f"السكريپت: {script_name}")
    print(f"{'='*60}")
    
    try:
        # تشغيل السكريپت
        result = subprocess.run([
            sys.executable, script_name
        ], capture_output=True, text=True, encoding='utf-8')
        
        # عرض النتائج
        if result.stdout:
            print("النتائج:")
            print(result.stdout)
        
        if result.stderr:
            print("الأخطاء:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✓ تم تشغيل {description} بنجاح")
            return True
        else:
            print(f"✗ فشل في تشغيل {description}")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في تشغيل {script_name}: {e}")
        return False

def check_mysql_service():
    """فحص خدمة MySQL"""
    print("فحص خدمة MySQL...")
    
    try:
        result = subprocess.run(['sc', 'query', 'mysql'], 
                              capture_output=True, text=True, shell=True)
        
        if result.returncode == 0:
            if "RUNNING" in result.stdout:
                print("✓ خدمة MySQL تعمل")
                return True
            else:
                print("✗ خدمة MySQL متوقفة")
                print("محاولة تشغيل الخدمة...")
                
                start_result = subprocess.run(['sc', 'start', 'mysql'], 
                                            capture_output=True, text=True, shell=True)
                if start_result.returncode == 0:
                    print("✓ تم تشغيل خدمة MySQL")
                    return True
                else:
                    print("✗ فشل في تشغيل خدمة MySQL")
                    return False
        else:
            print("✗ خدمة MySQL غير مثبتة")
            return False
            
    except Exception as e:
        print(f"خطأ في فحص خدمة MySQL: {e}")
        return False

def check_config_file():
    """فحص ملف إعدادات قاعدة البيانات"""
    config_file = "config/database_settings.json"
    
    if not os.path.exists(config_file):
        print("إنشاء ملف إعدادات قاعدة البيانات...")
        
        # إنشاء مجلد config إذا لم يكن موجوداً
        os.makedirs("config", exist_ok=True)
        
        # إنشاء ملف الإعدادات
        config = {
            "host": "localhost",
            "port": 3306,
            "user": "root",
            "password": "mohdam",
            "database": "money_manager"
        }
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            print(f"✓ تم إنشاء ملف الإعدادات: {config_file}")
            return True
        except Exception as e:
            print(f"✗ خطأ في إنشاء ملف الإعدادات: {e}")
            return False
    else:
        print(f"✓ ملف الإعدادات موجود: {config_file}")
        return True

def main():
    """الدالة الرئيسية"""
    print("=" * 80)
    print("فحص وإصلاح شامل لقاعدة البيانات - تطبيق إدارة الأموال")
    print("=" * 80)
    print(f"التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"المجلد الحالي: {os.getcwd()}")
    
    # قائمة السكريپتات والمهام
    tasks = [
        {
            'name': 'فحص خدمة MySQL',
            'function': check_mysql_service,
            'required': True
        },
        {
            'name': 'فحص ملف الإعدادات',
            'function': check_config_file,
            'required': True
        },
        {
            'name': 'فحص شامل لقاعدة البيانات',
            'script': 'comprehensive_database_audit.py',
            'required': False
        },
        {
            'name': 'إصلاح شامل لقاعدة البيانات',
            'script': 'comprehensive_database_fix.py',
            'required': True
        },
        {
            'name': 'إصلاح مشكلة إضافة الحسابات',
            'script': 'fix_account_creation_issue.py',
            'required': True
        }
    ]
    
    results = []
    
    for task in tasks:
        print(f"\n{'='*60}")
        print(f"المهمة: {task['name']}")
        print(f"{'='*60}")
        
        success = False
        
        if 'function' in task:
            # تشغيل دالة
            try:
                success = task['function']()
            except Exception as e:
                print(f"✗ خطأ في تشغيل {task['name']}: {e}")
                success = False
        
        elif 'script' in task:
            # تشغيل سكريپت
            if os.path.exists(task['script']):
                success = run_script(task['script'], task['name'])
            else:
                print(f"✗ السكريپت غير موجود: {task['script']}")
                success = False
        
        results.append({
            'name': task['name'],
            'success': success,
            'required': task['required']
        })
        
        if task['required'] and not success:
            print(f"\n✗ فشلت مهمة مطلوبة: {task['name']}")
            print("توقف التنفيذ")
            break
    
    # ملخص النتائج
    print(f"\n{'='*80}")
    print("ملخص النتائج")
    print(f"{'='*80}")
    
    total_tasks = len(results)
    successful_tasks = sum(1 for r in results if r['success'])
    failed_required = sum(1 for r in results if r['required'] and not r['success'])
    
    print(f"إجمالي المهام: {total_tasks}")
    print(f"المهام الناجحة: {successful_tasks}")
    print(f"المهام الفاشلة: {total_tasks - successful_tasks}")
    print(f"المهام المطلوبة الفاشلة: {failed_required}")
    
    print("\nتفاصيل النتائج:")
    for result in results:
        status = "✓ نجح" if result['success'] else "✗ فشل"
        required = " (مطلوب)" if result['required'] else " (اختياري)"
        print(f"  {status} {result['name']}{required}")
    
    # التوصيات النهائية
    print(f"\n{'='*80}")
    print("التوصيات النهائية")
    print(f"{'='*80}")
    
    if failed_required > 0:
        print("✗ فشلت مهام مطلوبة - يحتاج التطبيق إلى إصلاح إضافي")
        print("\nخطوات الإصلاح المقترحة:")
        print("1. تأكد من تشغيل خدمة MySQL")
        print("2. تحقق من صحة كلمة مرور قاعدة البيانات في config/database_settings.json")
        print("3. شغل السكريپتات يدوياً لمزيد من التفاصيل")
        print("4. راجع ملفات السجلات للأخطاء")
        
    elif successful_tasks == total_tasks:
        print("✓ تم إصلاح جميع المشاكل بنجاح!")
        print("\nيمكنك الآن:")
        print("1. تشغيل التطبيق")
        print("2. إضافة حسابات مالية جديدة")
        print("3. إدخال المعاملات")
        print("4. عرض التقارير")
        
    else:
        print("⚠ تم إصلاح المشاكل الأساسية")
        print("بعض المهام الاختيارية فشلت لكن التطبيق يجب أن يعمل")
    
    # حفظ تقرير النتائج
    report = {
        'timestamp': datetime.now().isoformat(),
        'total_tasks': total_tasks,
        'successful_tasks': successful_tasks,
        'failed_tasks': total_tasks - successful_tasks,
        'failed_required': failed_required,
        'results': results
    }
    
    report_file = f"database_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"\n✓ تم حفظ تقرير النتائج في: {report_file}")
    except Exception as e:
        print(f"✗ خطأ في حفظ التقرير: {e}")
    
    return failed_required == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
