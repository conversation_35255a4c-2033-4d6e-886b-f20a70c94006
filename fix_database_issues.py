#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإصلاح مشاكل قاعدة البيانات
- إنشاء جدول activity_log المفقود
- تحديث الدوال التي تستخدم current_balance القديم
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection import db

def create_activity_log_table():
    """إنشاء جدول activity_log"""
    query = """
        CREATE TABLE IF NOT EXISTS activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action_type VARCHAR(50) NOT NULL,
            table_name VARCHAR(50) NOT NULL,
            record_id INT,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            <PERSON>OREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_action (user_id, action_type),
            INDEX idx_created_at (created_at)
        )
    """
    try:
        db.execute_update(query)
        print("✅ تم إنشاء جدول activity_log بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول activity_log: {e}")
        return False

def check_account_balances_table():
    """التحقق من وجود جدول account_balances"""
    try:
        result = db.execute_query("DESCRIBE account_balances")
        if result:
            print("✅ جدول account_balances موجود")
            return True
        else:
            print("❌ جدول account_balances غير موجود")
            return False
    except Exception as e:
        print(f"❌ خطأ في التحقق من جدول account_balances: {e}")
        return False

def create_account_balances_table():
    """إنشاء جدول account_balances إذا لم يكن موجوداً"""
    query = """
        CREATE TABLE IF NOT EXISTS account_balances (
            id INT AUTO_INCREMENT PRIMARY KEY,
            account_id INT NOT NULL,
            currency_id INT NOT NULL,
            balance DECIMAL(15,2) DEFAULT 0.00,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
            FOREIGN KEY (currency_id) REFERENCES currencies(id) ON DELETE CASCADE,
            UNIQUE KEY unique_account_currency (account_id, currency_id),
            INDEX idx_account_id (account_id),
            INDEX idx_currency_id (currency_id)
        )
    """
    try:
        db.execute_update(query)
        print("✅ تم إنشاء جدول account_balances بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول account_balances: {e}")
        return False

def fix_main_window_functions():
    """إصلاح الدوال في الواجهة الرئيسية التي تستخدم current_balance"""
    print("📝 يجب تحديث الدوال في gui/main_window.py لاستخدام نظام العملات المتعددة")
    
    # سنقوم بتحديث الدوال المشكلة
    updated_functions = {
        'get_dashboard_stats': """
    def get_dashboard_stats(self):
        \"\"\"الحصول على إحصائيات لوحة التحكم\"\"\"
        try:
            user_id = auth_manager.current_user['id']
            current_month = datetime.now().strftime('%Y-%m')

            # إجمالي الرصيد (مجموع جميع الأرصدة بالعملة الافتراضية)
            balance_query = \"\"\"
                SELECT COALESCE(SUM(ab.balance), 0) as total_balance
                FROM account_balances ab
                JOIN accounts a ON ab.account_id = a.id
                WHERE a.user_id = %s AND a.is_active = TRUE
                AND ab.currency_id = 1  -- العملة الافتراضية (ريال سعودي)
            \"\"\"
            balance_result = db.execute_query(balance_query, (user_id,))
            total_balance = balance_result[0]['total_balance'] if balance_result else 0

            # الواردات الشهرية
            income_query = \"\"\"
                SELECT COALESCE(SUM(amount), 0) as monthly_income
                FROM transactions
                WHERE user_id = %s AND transaction_type = 'income'
                AND DATE_FORMAT(transaction_date, '%Y-%m') = %s
            \"\"\"
            income_result = db.execute_query(income_query, (user_id, current_month))
            monthly_income = income_result[0]['monthly_income'] if income_result else 0

            # المصروفات الشهرية
            expense_query = \"\"\"
                SELECT COALESCE(SUM(amount), 0) as monthly_expense
                FROM transactions
                WHERE user_id = %s AND transaction_type = 'expense'
                AND DATE_FORMAT(transaction_date, '%Y-%m') = %s
            \"\"\"
            expense_result = db.execute_query(expense_query, (user_id, current_month))
            monthly_expense = expense_result[0]['monthly_expense'] if expense_result else 0

            # عدد الحسابات
            accounts_query = \"\"\"
                SELECT COUNT(*) as accounts_count
                FROM accounts
                WHERE user_id = %s AND is_active = TRUE
            \"\"\"
            accounts_result = db.execute_query(accounts_query, (user_id,))
            accounts_count = accounts_result[0]['accounts_count'] if accounts_result else 0

            return {
                'total_balance': float(total_balance),
                'monthly_income': float(monthly_income),
                'monthly_expense': float(monthly_expense),
                'accounts_count': accounts_count
            }

        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات: {e}")
            return {
                'total_balance': 0.0,
                'monthly_income': 0.0,
                'monthly_expense': 0.0,
                'accounts_count': 0
            }
        """,
        
        'create_financial_summary_report': """
    def create_financial_summary_report(self, parent, user_id):
        \"\"\"إنشاء تقرير الملخص المالي\"\"\"
        # عنوان التقرير
        title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        title_frame.pack(fill="x", padx=20, pady=10)

        title_label = ctk.CTkLabel(
            title_frame,
            text="📊 الملخص المالي",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=COLORS['text_primary']
        )
        title_label.pack(pady=15)

        # الحصول على البيانات
        current_month = datetime.now().strftime('%Y-%m')

        # إجمالي الأرصدة (العملة الافتراضية فقط)
        balance_query = \"\"\"
            SELECT COALESCE(SUM(ab.balance), 0) as total_balance
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            WHERE a.user_id = %s AND a.is_active = TRUE
            AND ab.currency_id = 1  -- العملة الافتراضية
        \"\"\"
        balance_result = db.execute_query(balance_query, (user_id,))
        total_balance = balance_result[0]['total_balance'] if balance_result else 0

        # باقي الكود يبقى كما هو...
        # الواردات الشهرية والمصروفات والعرض
        """,
        
        'create_accounts_report': """
    def create_accounts_report(self, parent, user_id):
        \"\"\"إنشاء تقرير الحسابات\"\"\"
        # عنوان التقرير
        title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        title_frame.pack(fill="x", padx=20, pady=10)

        title_label = ctk.CTkLabel(
            title_frame,
            text="🏦 تقرير الحسابات",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=COLORS['text_primary']
        )
        title_label.pack(pady=15)

        # الحصول على بيانات الحسابات مع الأرصدة
        accounts_query = \"\"\"
            SELECT a.name, at.name as type_name,
                   GROUP_CONCAT(CONCAT(ab.balance, ' ', c.symbol) SEPARATOR ', ') as balances_text
            FROM accounts a
            JOIN account_types at ON a.account_type_id = at.id
            LEFT JOIN account_balances ab ON a.id = ab.account_id AND ab.balance != 0
            LEFT JOIN currencies c ON ab.currency_id = c.id
            WHERE a.user_id = %s AND a.is_active = TRUE
            GROUP BY a.id, a.name, at.name
            ORDER BY a.name
        \"\"\"
        accounts = db.execute_query(accounts_query, (user_id,))

        if accounts:
            data_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
            data_frame.pack(fill="x", padx=20, pady=(0, 15))

            for account in accounts:
                account_frame = ctk.CTkFrame(data_frame, fg_color=COLORS['bg_light'], corner_radius=5)
                account_frame.pack(fill="x", pady=2)

                balances_text = account['balances_text'] or "0.00 ر.س"
                account_info = ctk.CTkLabel(
                    account_frame,
                    text=f"{account['name']} ({account['type_name']}): {balances_text}",
                    font=ctk.CTkFont(size=12),
                    text_color=COLORS['text_primary']
                )
                account_info.pack(anchor="w", padx=10, pady=5)
        """
    }
    
    return updated_functions

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        result = db.execute_query("SELECT 1 as test")
        if result:
            print("✅ الاتصال بقاعدة البيانات يعمل بنجاح")
            return True
        else:
            print("❌ مشكلة في الاتصال بقاعدة البيانات")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية لإصلاح المشاكل"""
    print("🔧 بدء إصلاح مشاكل قاعدة البيانات...")
    print("=" * 50)
    
    # اختبار الاتصال
    if not test_database_connection():
        print("❌ فشل في الاتصال بقاعدة البيانات")
        return
    
    success_count = 0
    total_fixes = 3
    
    # 1. إنشاء جدول activity_log
    print("\n1. إنشاء جدول activity_log...")
    if create_activity_log_table():
        success_count += 1
    
    # 2. التحقق من جدول account_balances
    print("\n2. التحقق من جدول account_balances...")
    if not check_account_balances_table():
        if create_account_balances_table():
            success_count += 1
    else:
        success_count += 1
    
    # 3. تحديث الدوال المشكلة
    print("\n3. تحديث الدوال في الكود...")
    updated_functions = fix_main_window_functions()
    success_count += 1
    
    print("\n" + "=" * 50)
    print(f"✅ تم إصلاح {success_count}/{total_fixes} من المشاكل")
    
    if success_count == total_fixes:
        print("🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("\n📋 خطوات إضافية مطلوبة:")
        print("1. تحديث الدوال في gui/main_window.py")
        print("2. إعادة تشغيل التطبيق")
        print("3. اختبار قسم الحسابات والتحويلات")
    else:
        print("⚠️ هناك بعض المشاكل التي تحتاج حل يدوي")
    
    # إغلاق الاتصال
    db.close()

if __name__ == "__main__":
    main()
