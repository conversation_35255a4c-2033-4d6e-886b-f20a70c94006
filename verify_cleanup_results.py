#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من نتائج تنظيف الحسابات الاختبارية
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db

def verify_cleanup_results():
    """التحقق من نتائج التنظيف"""
    print("🔍 التحقق من نتائج تنظيف قاعدة البيانات")
    print("=" * 50)
    
    try:
        # 1. عرض جميع الحسابات المتبقية
        print("📋 الحسابات المتبقية:")
        accounts = db.execute_query("SELECT * FROM accounts ORDER BY name")
        
        if accounts:
            print(f"📊 إجمالي الحسابات: {len(accounts)}")
            for account in accounts:
                status = "نشط" if account.get('is_active') else "غير نشط"
                print(f"   ✅ {account['name']} (ID: {account['id']}) - {status}")
                print(f"      المستخدم: {account['user_id']}")
                print(f"      تاريخ الإنشاء: {account['created_at']}")
                
                # عرض الأرصدة
                balances = db.execute_query("""
                    SELECT ab.balance, c.symbol, c.name as currency_name
                    FROM account_balances ab 
                    JOIN currencies c ON ab.currency_id = c.id 
                    WHERE ab.account_id = %s
                """, (account['id'],))
                
                if balances:
                    for balance in balances:
                        print(f"      💰 الرصيد: {balance['balance']} {balance['symbol']} ({balance['currency_name']})")
                else:
                    print(f"      💰 الرصيد: 0")
                print()
        else:
            print("❌ لا توجد حسابات في قاعدة البيانات")
        
        # 2. التحقق من عدم وجود حسابات اختبارية
        print("🔍 البحث عن حسابات اختبارية متبقية:")
        test_keywords = ['اختبار', 'تجريبي', 'test', 'demo', 'sample']
        
        found_test_accounts = False
        for keyword in test_keywords:
            test_accounts = db.execute_query(
                "SELECT * FROM accounts WHERE LOWER(name) LIKE %s", 
                (f"%{keyword.lower()}%",)
            )
            
            if test_accounts:
                found_test_accounts = True
                print(f"⚠️ تحذير: وجدت حسابات تحتوي على '{keyword}':")
                for account in test_accounts:
                    print(f"   - {account['name']} (ID: {account['id']})")
            else:
                print(f"✅ لا توجد حسابات تحتوي على '{keyword}'")
        
        if not found_test_accounts:
            print("✅ لا توجد حسابات اختبارية متبقية")
        
        # 3. التحقق من حساب "معاذ"
        print("\n👤 التحقق من حساب 'معاذ':")
        maaz_account = db.execute_query("SELECT * FROM accounts WHERE name = 'معاذ'")
        
        if maaz_account:
            account = maaz_account[0]
            print("✅ حساب 'معاذ' موجود:")
            print(f"   📋 المعرف: {account['id']}")
            print(f"   👤 المستخدم: {account['user_id']}")
            print(f"   🔄 الحالة: {'نشط' if account['is_active'] else 'غير نشط'}")
            print(f"   📅 تاريخ الإنشاء: {account['created_at']}")
            
            # عرض أرصدة حساب معاذ
            maaz_balances = db.execute_query("""
                SELECT ab.balance, c.symbol, c.name as currency_name
                FROM account_balances ab 
                JOIN currencies c ON ab.currency_id = c.id 
                WHERE ab.account_id = %s
            """, (account['id'],))
            
            if maaz_balances:
                print("   💰 الأرصدة:")
                for balance in maaz_balances:
                    print(f"      - {balance['balance']} {balance['symbol']} ({balance['currency_name']})")
            else:
                print("   💰 الأرصدة: لا توجد أرصدة")
        else:
            print("❌ حساب 'معاذ' غير موجود!")
        
        # 4. إحصائيات عامة
        print("\n📊 إحصائيات قاعدة البيانات:")
        
        # عدد الحسابات
        accounts_count = db.execute_query("SELECT COUNT(*) as count FROM accounts")[0]['count']
        print(f"   🏦 إجمالي الحسابات: {accounts_count}")
        
        # عدد الحسابات النشطة
        active_accounts = db.execute_query("SELECT COUNT(*) as count FROM accounts WHERE is_active = TRUE")[0]['count']
        print(f"   ✅ الحسابات النشطة: {active_accounts}")
        
        # عدد الأرصدة
        balances_count = db.execute_query("SELECT COUNT(*) as count FROM account_balances")[0]['count']
        print(f"   💰 إجمالي الأرصدة: {balances_count}")
        
        # إجمالي الأموال
        total_money = db.execute_query("""
            SELECT SUM(ab.balance) as total, c.symbol
            FROM account_balances ab 
            JOIN currencies c ON ab.currency_id = c.id 
            GROUP BY c.id, c.symbol
        """)
        
        if total_money:
            print(f"   💵 إجمالي الأموال:")
            for money in total_money:
                print(f"      - {money['total']} {money['symbol']}")
        else:
            print(f"   💵 إجمالي الأموال: 0")
        
        # 5. التحقق من سلامة البيانات
        print("\n🔍 فحص سلامة البيانات:")
        
        # التحقق من وجود أرصدة بدون حسابات
        orphaned_balances = db.execute_query("""
            SELECT ab.* FROM account_balances ab 
            LEFT JOIN accounts a ON ab.account_id = a.id 
            WHERE a.id IS NULL
        """)
        
        if orphaned_balances:
            print(f"⚠️ تحذير: وجدت {len(orphaned_balances)} رصيد بدون حساب مرتبط")
            for balance in orphaned_balances:
                print(f"   - رصيد ID: {balance['id']}, حساب ID: {balance['account_id']}")
        else:
            print("✅ جميع الأرصدة مرتبطة بحسابات صحيحة")
        
        # التحقق من وجود حسابات بدون مستخدمين
        orphaned_accounts = db.execute_query("""
            SELECT a.* FROM accounts a 
            LEFT JOIN users u ON a.user_id = u.id 
            WHERE u.id IS NULL
        """)
        
        if orphaned_accounts:
            print(f"⚠️ تحذير: وجدت {len(orphaned_accounts)} حساب بدون مستخدم مرتبط")
            for account in orphaned_accounts:
                print(f"   - حساب: {account['name']} (ID: {account['id']}, مستخدم: {account['user_id']})")
        else:
            print("✅ جميع الحسابات مرتبطة بمستخدمين صحيحين")
        
        print("\n" + "=" * 50)
        print("🎉 انتهى التحقق من نتائج التنظيف")
        
        # خلاصة النتائج
        if accounts_count == 1 and maaz_account and not found_test_accounts:
            print("✅ نجح التنظيف بالكامل:")
            print("   - تم حذف جميع الحسابات الاختبارية")
            print("   - تم الاحتفاظ بحساب 'معاذ' فقط")
            print("   - قاعدة البيانات نظيفة ومرتبة")
        else:
            print("⚠️ قد تحتاج إلى مراجعة إضافية")
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    verify_cleanup_results()

if __name__ == "__main__":
    main()
