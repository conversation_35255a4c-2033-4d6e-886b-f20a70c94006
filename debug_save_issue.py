#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص وإصلاح مشكلة عدم حفظ التعديلات - نهائي
"""

import sys
import os
import traceback

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_database_connection():
    """فحص الاتصال بقاعدة البيانات"""
    print("🔌 فحص الاتصال بقاعدة البيانات...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        if db.connect():
            print("✅ الاتصال بقاعدة البيانات نشط")
            
            # اختبار استعلام
            result = db.execute_query("SELECT DATABASE() as db_name")
            if result:
                print(f"   قاعدة البيانات: {result[0]['db_name']}")
            
            return True
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            print("تأكد من:")
            print("1. تشغيل XAMPP")
            print("2. تشغيل خدمة MySQL")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def fix_database_schema():
    """إصلاح مخطط قاعدة البيانات"""
    print("\n🔧 إصلاح مخطط قاعدة البيانات...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        # فحص الأعمدة الموجودة
        columns = db.execute_query("DESCRIBE transactions")
        column_names = [col['Field'] for col in columns]
        
        # إضافة عمود category_name إذا لم يكن موجوداً
        if 'category_name' not in column_names:
            print("🔧 إضافة عمود category_name...")
            db.execute_update("""
                ALTER TABLE transactions 
                ADD COLUMN category_name VARCHAR(100) NULL 
                COMMENT 'اسم التصنيف المدخل يدوياً'
            """)
            print("✅ تم إضافة عمود category_name")
        else:
            print("✅ عمود category_name موجود")
        
        # إضافة عمود updated_at إذا لم يكن موجوداً
        if 'updated_at' not in column_names:
            print("🔧 إضافة عمود updated_at...")
            db.execute_update("""
                ALTER TABLE transactions 
                ADD COLUMN updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                COMMENT 'تاريخ آخر تحديث'
            """)
            print("✅ تم إضافة عمود updated_at")
        else:
            print("✅ عمود updated_at موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح المخطط: {e}")
        return False

def test_direct_update():
    """اختبار التحديث المباشر في قاعدة البيانات"""
    print("\n🧪 اختبار التحديث المباشر...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        # البحث عن معاملة للاختبار
        transactions = db.execute_query("SELECT id, description FROM transactions LIMIT 1")
        
        if not transactions:
            print("⚠️ لا توجد معاملات للاختبار")
            return True
        
        transaction_id = transactions[0]['id']
        original_description = transactions[0]['description']
        test_description = f"اختبار مباشر - {original_description}"
        
        print(f"📝 اختبار المعاملة رقم: {transaction_id}")
        print(f"   الوصف الأصلي: '{original_description}'")
        print(f"   الوصف الجديد: '{test_description}'")
        
        # تحديث مباشر
        result = db.execute_update(
            "UPDATE transactions SET description = %s WHERE id = %s",
            (test_description, transaction_id)
        )
        
        print(f"   نتيجة التحديث: {result} صف متأثر")
        
        if result > 0:
            # التحقق من التحديث
            updated = db.execute_query(
                "SELECT description FROM transactions WHERE id = %s",
                (transaction_id,)
            )
            
            if updated and updated[0]['description'] == test_description:
                print("   ✅ التحديث المباشر نجح")
                
                # إعادة الوصف الأصلي
                db.execute_update(
                    "UPDATE transactions SET description = %s WHERE id = %s",
                    (original_description, transaction_id)
                )
                print("   ✅ تم إعادة الوصف الأصلي")
                return True
            else:
                print("   ❌ التحديث لم يتم حفظه")
                return False
        else:
            print("   ❌ لم يتم تحديث أي صف")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في التحديث المباشر: {e}")
        traceback.print_exc()
        return False

def test_transaction_model():
    """اختبار نموذج Transaction"""
    print("\n🧪 اختبار نموذج Transaction...")
    print("=" * 50)
    
    try:
        from database.models import Transaction
        from database.connection import db
        
        # التحقق من وجود دالة update
        if not hasattr(Transaction, 'update'):
            print("❌ دالة Transaction.update غير موجودة!")
            return False
        
        print("✅ دالة Transaction.update موجودة")
        
        # البحث عن معاملة للاختبار
        transactions = db.execute_query("SELECT id, description FROM transactions LIMIT 1")
        
        if not transactions:
            print("⚠️ لا توجد معاملات للاختبار")
            return True
        
        transaction_id = transactions[0]['id']
        original_description = transactions[0]['description']
        test_description = f"اختبار نموذج - {original_description}"
        
        print(f"📝 اختبار المعاملة رقم: {transaction_id}")
        print(f"   الوصف الأصلي: '{original_description}'")
        print(f"   الوصف الجديد: '{test_description}'")
        
        # تحديث باستخدام النموذج
        success = Transaction.update(
            transaction_id=transaction_id,
            description=test_description
        )
        
        print(f"   نتيجة التحديث: {success}")
        
        if success:
            # التحقق من التحديث
            updated = db.execute_query(
                "SELECT description FROM transactions WHERE id = %s",
                (transaction_id,)
            )
            
            if updated and updated[0]['description'] == test_description:
                print("   ✅ تحديث النموذج نجح")
                
                # إعادة الوصف الأصلي
                Transaction.update(transaction_id=transaction_id, description=original_description)
                print("   ✅ تم إعادة الوصف الأصلي")
                return True
            else:
                print("   ❌ تحديث النموذج لم يتم حفظه")
                return False
        else:
            print("   ❌ فشل تحديث النموذج")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النموذج: {e}")
        traceback.print_exc()
        return False

def create_test_data():
    """إنشاء بيانات تجريبية إذا لزم الأمر"""
    print("\n📊 التحقق من البيانات التجريبية...")
    print("=" * 50)
    
    try:
        from database.connection import db
        from utils.auth import auth_manager
        
        # التحقق من وجود مستخدم
        users = db.execute_query("SELECT id FROM users LIMIT 1")
        if not users:
            print("⚠️ لا يوجد مستخدمون - إنشاء مستخدم تجريبي...")
            db.execute_update("""
                INSERT INTO users (username, email, password_hash, full_name, is_active)
                VALUES ('test', '<EMAIL>', 'test', 'مستخدم تجريبي', TRUE)
            """)
            users = db.execute_query("SELECT id FROM users WHERE username = 'test'")
        
        user_id = users[0]['id']
        auth_manager.current_user = {'id': user_id, 'full_name': 'مستخدم تجريبي'}
        print(f"✅ المستخدم: {user_id}")
        
        # التحقق من وجود حسابات
        accounts = db.execute_query("SELECT id FROM accounts WHERE user_id = %s LIMIT 1", (user_id,))
        if not accounts:
            print("⚠️ لا توجد حسابات - إنشاء حساب تجريبي...")
            db.execute_update("""
                INSERT INTO accounts (user_id, name, account_type, currency_id, balance, is_active)
                VALUES (%s, 'حساب تجريبي', 'cash', 1, 1000.00, TRUE)
            """, (user_id,))
            accounts = db.execute_query("SELECT id FROM accounts WHERE user_id = %s LIMIT 1", (user_id,))
        
        account_id = accounts[0]['id']
        print(f"✅ الحساب: {account_id}")
        
        # التحقق من وجود معاملات
        transactions = db.execute_query("SELECT COUNT(*) as count FROM transactions WHERE user_id = %s", (user_id,))
        if transactions[0]['count'] == 0:
            print("⚠️ لا توجد معاملات - إنشاء معاملة تجريبية...")
            db.execute_update("""
                INSERT INTO transactions (user_id, account_id, currency_id, transaction_type, amount, description, transaction_date)
                VALUES (%s, %s, 1, 'income', 500.00, 'معاملة تجريبية للاختبار', CURDATE())
            """, (user_id, account_id))
        
        print("✅ البيانات التجريبية جاهزة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص وإصلاح مشكلة عدم حفظ التعديلات")
    print("=" * 60)
    
    # 1. فحص الاتصال
    if not check_database_connection():
        print("\n❌ يجب إصلاح مشكلة الاتصال أولاً!")
        input("اضغط Enter للخروج...")
        return
    
    # 2. إصلاح مخطط قاعدة البيانات
    if not fix_database_schema():
        print("\n❌ فشل في إصلاح مخطط قاعدة البيانات!")
        input("اضغط Enter للخروج...")
        return
    
    # 3. إنشاء بيانات تجريبية
    create_test_data()
    
    # 4. اختبار التحديث المباشر
    direct_ok = test_direct_update()
    
    # 5. اختبار نموذج Transaction
    model_ok = test_transaction_model()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص التشخيص:")
    print(f"   🔌 قاعدة البيانات: ✅ متصلة")
    print(f"   📋 مخطط الجدول: ✅ صحيح")
    print(f"   🔄 التحديث المباشر: {'✅ يعمل' if direct_ok else '❌ مشكلة'}")
    print(f"   🧪 نموذج Transaction: {'✅ يعمل' if model_ok else '❌ مشكلة'}")
    
    if direct_ok and model_ok:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("   قاعدة البيانات والنموذج يعملان بشكل صحيح")
        print("   المشكلة في واجهة المستخدم أو استدعاء الدالة")
        
        print("\n🔧 الخطوات التالية:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. فتح نافذة التعديل")
        print("3. مراقبة رسائل وحدة التحكم بعناية")
        print("4. نسخ أي رسائل خطأ تظهر")
        
    else:
        print("\n❌ توجد مشاكل في النظام!")
        if not direct_ok:
            print("   🔧 مشكلة في التحديث المباشر لقاعدة البيانات")
        if not model_ok:
            print("   🔧 مشكلة في نموذج Transaction")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
