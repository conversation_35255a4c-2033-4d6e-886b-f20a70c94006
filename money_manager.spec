# -*- mode: python ; coding: utf-8 -*-
# Money Manager PyInstaller Spec File
# ملف إعدادات PyInstaller لبرنامج إدارة الأموال

import os
import sys
from pathlib import Path

# تحديد المسار الحالي
current_dir = Path(os.getcwd()).absolute()

# البيانات الإضافية والمجلدات
added_files = [
    (str(current_dir / 'assets'), 'assets'),
    (str(current_dir / 'config'), 'config'),
    (str(current_dir / 'database'), 'database'),
    (str(current_dir / 'sample_excel_files'), 'sample_excel_files'),
    (str(current_dir / 'requirements.txt'), '.'),
    (str(current_dir / 'README.md'), '.'),
    (str(current_dir / 'accounts.json'), '.'),
    (str(current_dir / 'users.json'), '.'),
    (str(current_dir / 'transactions.json'), '.'),
]

# الحزم المخفية
hidden_imports = [
    'customtkinter',
    'mysql.connector',
    'bcrypt',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'reportlab',
    'matplotlib',
    'pandas',
    'openpyxl',
    'schedule',
    'hijri_converter',
    'psutil',
    'dateutil',
    'inspect',
    'importlib',
    'importlib.abc',
    'importlib.resources',
    'importlib.resources._common',
    'importlib.util',
    'importlib.machinery',
    'pkg_resources',
    'mysql.connector.pooling',
    'mysql.connector.errors',
    'mysql.connector.cursor',
    'tkinter',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.ttk',
    'sqlite3',
    'json',
    'datetime',
    'logging',
    'threading',
    'queue',
    'csv',
    'xlsxwriter',
    'io',
    'base64',
    'hashlib',
    'uuid',
    'random',
    'math',
    'calendar',
    'locale',
    'platform',
    'subprocess',
    'shutil',
    'tempfile',
    'zipfile',
    'ssl',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'email',
    'smtplib',
    'imaplib',
    'email.mime.text',
    'email.mime.multipart',
    'email.mime.application',
    'configparser',
    'pickle',
    'copy',
    'collections',
    'itertools',
    'functools',
    'operator',
    'typing',
    'warnings',
    'gc',
    'weakref',
    'ctypes',
    'struct',
    'array',
    'binascii',
    'zlib',
    'gzip',
    'bz2',
    'lzma',
    'tarfile',
    'zipimport',
    'importlib',
    'importlib.util',
    'importlib.machinery',
    'pkg_resources',
    'setuptools',
    'distutils',
    'site',
    'sysconfig',
    'traceback',
    'inspect',
    'ast',
    'types',
    'builtins',
    'encodings',
    'encodings.utf_8',
    'encodings.cp1256',
    'encodings.ascii',
    'encodings.latin1',
    'codecs',
    'unicodedata',
    'locale',
    'gettext',
    'babel',
    'babel.dates',
    'babel.numbers',
    'babel.localedata',
    'babel.core',
    'babel.support',
    'babel.util',
    'babel.messages',
    'babel.messages.catalog',
    'babel.messages.mofile',
    'babel.messages.plurals',
    'babel.messages.pofile',
    'babel.messages.frontend',
    'babel.messages.extract',
    'babel.messages.checkers',
    'babel.localedata',
    'babel.dates',
    'babel.numbers',
    'babel.units',
    'babel.currencies',
    'babel.lists',
    'babel.plural',
    'babel.localedata',
    'babel.core',
    'babel.support',
    'babel.util',
    'babel.messages',
    'babel.messages.catalog',
    'babel.messages.mofile',
    'babel.messages.plurals',
    'babel.messages.pofile',
    'babel.messages.frontend',
    'babel.messages.extract',
    'babel.messages.checkers',
]

# تكوين التحليل
a = Analysis(
    ['main.py'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib.backends._backend_tk',
        'matplotlib.backends._backend_tkagg',
        'matplotlib.backends._backend_agg',
        'matplotlib.tests',
        'pandas.tests',
        'numpy.tests',
        'scipy.tests',
        'sklearn.tests',
        'test',
        'tests',
        'testing',
        'unittest',
        'pdb',
        'pydoc',
        'doctest',
        'difflib',
        'inspect',
        'turtle',
        'email.test',
        'distutils.tests',
        'lib2to3',
        'multiprocessing',
        'concurrent.futures',
        'asyncio',
        'xml.etree',
        'xml.dom',
        'xml.sax',
        'xml.parsers',
        'html',
        'http',
        'urllib2',
        'urlparse',
        'cookielib',
        'BaseHTTPServer',
        'SimpleHTTPServer',
        'CGIHTTPServer',
        'SocketServer',
        'xmlrpclib',
        'DocXMLRPCServer',
        'SimpleXMLRPCServer',
        'audioop',
        'chunk',
        'colorsys',
        'imghdr',
        'sndhdr',
        'sunau',
        'wave',
        'aifc',
        'ossaudiodev',
        'linuxaudiodev',
        'winsound',
        'msilib',
        'msvcrt',
        'winreg',
        '_winreg',
        'winsound',
        'nt',
        'ntpath',
        'nturl2path',
        'posix',
        'posixpath',
        'pwd',
        'grp',
        'crypt',
        'dl',
        'termios',
        'tty',
        'pty',
        'fcntl',
        'pipes',
        'resource',
        'nis',
        'syslog',
        'commands',
        'popen2',
        'posixfile',
        'SUNAUDIODEV',
        'CDROM',
        'DLFCN',
        'IN',
        'STROPTS',
        'TERMIOS',
        'CDIO',
        'IOCTL',
        'FCNTL',
        'TYPES',
        'Carbon',
        'Carbon.Files',
        'Carbon.Folder',
        'Carbon.Folders',
        'Carbon.CarbonEvt',
        'Carbon.CarbonEvents',
        'EasyDialogs',
        'MacOS',
        'Nav',
        'PixMapWrapper',
        'videoreader',
        'ImageGL',
        'PIL.ImageCms',
        'PIL.ImageDraw2',
        'PIL.ImageGL',
        'PIL.ImageGrab',
        'PIL.ImageWin',
        'PIL.FpxImagePlugin',
        'PIL.MicImagePlugin',
        'PIL.SgiImagePlugin',
        'PIL.SunImagePlugin',
        'PIL.TgaImagePlugin',
        'PIL.Hdf5StubImagePlugin',
        'PIL.FitsStubImagePlugin',
        'PIL.GbrImagePlugin',
        'PIL.ImImagePlugin',
        'PIL.IptcImagePlugin',
        'PIL.McIdasImagePlugin',
        'PIL.MspImagePlugin',
        'PIL.PsdImagePlugin',
        'PIL.XVThumbImagePlugin',
        'PIL.XbmImagePlugin',
        'PIL.XpmImagePlugin',
        'PIL.CurImagePlugin',
        'PIL.DcxImagePlugin',
        'PIL.EpsImagePlugin',
        'PIL.FlcImagePlugin',
        'PIL.FliImagePlugin',
        'PIL.FpxImagePlugin',
        'PIL.GdImageFile',
        'PIL.GifImagePlugin',
        'PIL.IcoImagePlugin',
        'PIL.ImImagePlugin',
        'PIL.ImtImagePlugin',
        'PIL.IptcImagePlugin',
        'PIL.Jpeg2KImagePlugin',
        'PIL.JpegImagePlugin',
        'PIL.McIdasImagePlugin',
        'PIL.MicImagePlugin',
        'PIL.MpegImagePlugin',
        'PIL.MspImagePlugin',
        'PIL.PalmImagePlugin',
        'PIL.PcdImagePlugin',
        'PIL.PcxImagePlugin',
        'PIL.PdfImagePlugin',
        'PIL.PixarImagePlugin',
        'PIL.PngImagePlugin',
        'PIL.PpmImagePlugin',
        'PIL.PsdImagePlugin',
        'PIL.SgiImagePlugin',
        'PIL.SpiderImagePlugin',
        'PIL.SunImagePlugin',
        'PIL.TgaImagePlugin',
        'PIL.TiffImagePlugin',
        'PIL.WalImageFile',
        'PIL.WmfImagePlugin',
        'PIL.XbmImagePlugin',
        'PIL.XpmImagePlugin',
        'PIL.XVThumbImagePlugin',
    ],
    noarchive=False,
    optimize=0,
)

# تجميع الملفات
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء ملف تنفيذي واحد
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Money Manager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # إخفاء نافذة الـ console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(current_dir / 'assets' / 'icon.ico'),  # أيقونة البرنامج
    version='version_info.txt',  # سننشئ هذا الملف
    manifest=None,
    uac_admin=False,
    uac_uiaccess=False,
)

# إنشاء مجلد التوزيع (اختياري)
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='Money Manager Distribution',
)
