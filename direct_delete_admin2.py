#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حذف مباشر للمستخدم admin2 لاختبار الميزة الجديدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from utils.auth import auth_manager
import logging

def direct_delete_admin2():
    """حذف مباشر لـ admin2"""
    print("🎯 حذف مباشر للمستخدم admin2")
    print("=" * 40)
    
    try:
        # الاتصال وتسجيل الدخول
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول كمدير")
        
        # البحث عن admin2
        user_query = "SELECT * FROM users WHERE username = 'admin2'"
        admin2_users = db.execute_query(user_query)
        
        if not admin2_users:
            print("❌ لم يتم العثور على المستخدم admin2")
            return False
        
        admin2_user = admin2_users[0]
        print(f"✅ تم العثور على admin2:")
        print(f"   - ID: {admin2_user['id']}")
        print(f"   - نشط: {admin2_user['is_active']}")
        print(f"   - الدور: {admin2_user['role']}")
        
        # تفعيل admin2 أولاً إذا لم يكن نشطاً
        if not admin2_user['is_active']:
            print("🔄 تفعيل admin2...")
            activate_query = "UPDATE users SET is_active = TRUE WHERE id = %s"
            db.execute_update(activate_query, (admin2_user['id'],))
            print("✅ تم تفعيل admin2")
        
        # فحص عدد المديرين النشطين
        admin_count_query = "SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = TRUE"
        admin_result = db.execute_query(admin_count_query)
        active_admin_count = admin_result[0]['count'] if admin_result else 0
        
        print(f"📊 عدد المديرين النشطين: {active_admin_count}")
        
        if active_admin_count <= 1:
            print("❌ لا يمكن حذف المدير الوحيد النشط")
            return False
        
        print("🔒 فحص الأمان: يمكن حذف admin2 بأمان")
        
        # حذف admin2
        print("🗑️ حذف admin2...")
        success, message = auth_manager.delete_user_permanently(admin2_user['id'])
        
        if success:
            print("✅ تم حذف admin2 بنجاح!")
            
            # التحقق من النتيجة
            verification_query = "SELECT * FROM users WHERE username = 'admin2'"
            remaining_users = db.execute_query(verification_query)
            
            if not remaining_users:
                print("✅ تأكيد: تم حذف admin2 نهائياً")
            else:
                print("⚠️ admin2 لا يزال موجوداً")
            
            # فحص عدد المديرين بعد الحذف
            final_admin_result = db.execute_query(admin_count_query)
            final_admin_count = final_admin_result[0]['count'] if final_admin_result else 0
            print(f"📊 عدد المديرين بعد الحذف: {final_admin_count}")
            
        else:
            print(f"❌ فشل في حذف admin2: {message}")
            return False
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def show_all_users():
    """عرض جميع المستخدمين"""
    print("\n👥 جميع المستخدمين في النظام:")
    try:
        if not (db.is_connected() or db.connect()):
            return
        
        users_query = "SELECT id, username, role, is_active FROM users ORDER BY role DESC, username"
        users = db.execute_query(users_query)
        
        if users:
            for user in users:
                status = "نشط" if user['is_active'] else "غير نشط"
                role_text = "مدير" if user['role'] == 'admin' else "مستخدم"
                print(f"   - {user['username']} (ID: {user['id']}) - {role_text} - {status}")
        else:
            print("   لا توجد مستخدمين")
            
    except Exception as e:
        print(f"   خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار حذف المدير admin2")
    print("=" * 35)
    
    # عرض المستخدمين قبل الحذف
    print("📋 المستخدمون قبل الحذف:")
    show_all_users()
    
    # تنفيذ الحذف
    print("\n🎯 تنفيذ عملية الحذف...")
    if direct_delete_admin2():
        print("\n🎉 تمت العملية بنجاح!")
        
        # عرض المستخدمين بعد الحذف
        print("\n📋 المستخدمون بعد الحذف:")
        show_all_users()
        
        print("\n✅ تم اختبار الميزة الجديدة بنجاح:")
        print("   ✅ حذف المستخدم المدير admin2")
        print("   ✅ التحقق من الضمانات الأمنية")
        print("   ✅ تسجيل العملية")
        print("   ✅ التحقق من النتيجة")
        
    else:
        print("\n❌ فشلت العملية")
        
        # عرض المستخدمين بعد الفشل
        print("\n📋 المستخدمون بعد المحاولة:")
        show_all_users()

if __name__ == "__main__":
    main()
