#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع للبرنامج مع تجاوز مشكلة تسجيل الدخول
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_mysql():
    """تشغيل MySQL"""
    print("🔄 تشغيل MySQL...")
    
    import subprocess
    try:
        # تشغيل MySQL من XAMPP
        subprocess.Popen([
            "C:\\xampp\\mysql\\bin\\mysqld.exe",
            "--defaults-file=C:\\xampp\\mysql\\bin\\my.ini",
            "--standalone"
        ])
        print("✅ تم تشغيل MySQL")
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل MySQL: {e}")
        return False

def setup_auth_manager():
    """إعداد مدير المصادقة مع المستخدم الافتراضي"""
    print("🔄 إعداد نظام المصادقة...")
    
    try:
        from utils.auth import auth_manager
        
        # تعيين المستخدم الحالي مباشرة
        auth_manager.current_user = {
            'id': 1,
            'username': 'admin',
            'email': '<EMAIL>',
            'full_name': 'المدير الافتراضي',
            'role': 'admin'
        }
        
        auth_manager.session_token = "default_session_token"
        
        print("✅ تم إعداد نظام المصادقة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد المصادقة: {e}")
        return False

def start_main_window():
    """تشغيل النافذة الرئيسية مباشرة"""
    print("🚀 تشغيل النافذة الرئيسية...")
    
    try:
        from gui.main_window import MainWindow
        
        # إنشاء وتشغيل النافذة الرئيسية
        main_app = MainWindow()
        main_app.run()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة الرئيسية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🚀 تشغيل سريع لبرنامج إدارة الأموال")
    print("=" * 60)
    
    # تشغيل MySQL
    start_mysql()
    
    # انتظار قليل لتشغيل MySQL
    import time
    time.sleep(2)
    
    # إعداد نظام المصادقة
    if not setup_auth_manager():
        print("❌ فشل في إعداد نظام المصادقة")
        return False
    
    # تشغيل النافذة الرئيسية
    if not start_main_window():
        print("❌ فشل في تشغيل النافذة الرئيسية")
        return False
    
    print("✅ تم إغلاق البرنامج بنجاح")
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    finally:
        input("\n🔄 اضغط Enter للخروج...")
