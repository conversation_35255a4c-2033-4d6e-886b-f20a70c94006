#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث مخطط جدول المعاملات لإضافة عمود category_name
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def update_transactions_schema():
    """تحديث مخطط جدول المعاملات"""
    try:
        from database.connection import db
        
        print("🔧 تحديث مخطط جدول المعاملات...")
        print("=" * 50)
        
        # التحقق من الاتصال
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return False
        
        print("✅ الاتصال بقاعدة البيانات نشط")
        
        # إضافة عمود category_name
        try:
            print("➕ إضافة عمود category_name...")
            db.execute_update("""
                ALTER TABLE transactions 
                ADD COLUMN category_name VARCHAR(100) NULL 
                COMMENT 'اسم التصنيف المدخل يدوياً'
            """)
            print("✅ تم إضافة عمود category_name")
        except Exception as e:
            if "Duplicate column name" in str(e) or "already exists" in str(e):
                print("✅ عمود category_name موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إضافة عمود category_name: {e}")
        
        # إنشاء فهرس للبحث السريع
        try:
            print("📊 إنشاء فهرس للبحث السريع...")
            db.execute_update("""
                CREATE INDEX idx_category_name ON transactions(category_name)
            """)
            print("✅ تم إنشاء فهرس category_name")
        except Exception as e:
            if "already exists" in str(e) or "Duplicate key name" in str(e):
                print("✅ فهرس category_name موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إنشاء الفهرس: {e}")
        
        # التحقق من هيكل الجدول
        print("\n📋 هيكل جدول المعاملات الحالي:")
        try:
            columns = db.execute_query("DESCRIBE transactions")
            for col in columns:
                print(f"   - {col['Field']}: {col['Type']} {'NULL' if col['Null'] == 'YES' else 'NOT NULL'}")
        except Exception as e:
            print(f"⚠️ خطأ في عرض هيكل الجدول: {e}")
        
        print("\n✅ تم تحديث مخطط جدول المعاملات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث المخطط: {e}")
        return False

if __name__ == "__main__":
    print("🏦 برنامج تحديث مخطط جدول المعاملات")
    print("=" * 60)
    
    success = update_transactions_schema()
    
    if success:
        print("\n🎉 تم التحديث بنجاح!")
        print("يمكنك الآن استخدام ميزة التعديل الشاملة للمعاملات.")
    else:
        print("\n❌ فشل في التحديث!")
        print("يرجى التحقق من الأخطاء أعلاه وإعادة المحاولة.")
    
    input("\nاضغط Enter للخروج...")
