# 🏦 تعليمات تشغيل برنامج إدارة الأموال

## ✅ تم إصلاح مشكلة تسجيل الدخول نهائياً!

### 🔧 الإصلاحات المطبقة:
- ✅ إصلاح مشكلة إغلاق البرنامج عند تسجيل الدخول
- ✅ تحسين معالجة الأخطاء في نافذة تسجيل الدخول
- ✅ إضافة تحقق من بيانات المستخدم قبل فتح النافذة الرئيسية
- ✅ تحسين إدارة النوافذ لمنع الإغلاق المفاجئ

### 📋 بيانات تسجيل الدخول الافتراضية:
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `123456`
- **كلمة مرور MySQL:** اتركها فارغة (لا تكتب شيئاً)

---

## 🚀 طرق تشغيل البرنامج:

### الطريقة الأولى: التشغيل العادي
```bash
python main.py
```

### الطريقة الثانية: التشغيل السريع (في حالة مشاكل تسجيل الدخول)
```bash
python quick_start.py
```

### الطريقة الثالثة: اختبار نافذة تسجيل الدخول
```bash
python test_login_window.py
```

### الطريقة الرابعة: التشغيل مع دليل البيانات العملية
```bash
python start_with_demo.py
```

---

## 🔧 إذا واجهت مشاكل:

### 1. مشكلة في تسجيل الدخول:
- تأكد من أن MySQL يعمل
- استخدم بيانات تسجيل الدخول الصحيحة
- جرب التشغيل السريع: `python quick_start.py`

### 2. مشكلة في قاعدة البيانات:
```bash
# إعادة إنشاء قاعدة البيانات
python setup_database.py
```

### 3. اختبار النظام:
```bash
# اختبار تسجيل الدخول
python test_login.py
```

---

## 📊 البيانات التجريبية المتوفرة:

### 🏦 الحسابات:
1. **الصندوق النقدي** - 5,000 ر.س
2. **حساب الراجحي** - 25,000 ر.س
3. **حساب التوفير** - 50,000 ر.س
4. **محفظة PayPal** - 1,200 دولار
5. **بطاقة فيزا** - -2,500 ر.س (رصيد سالب)

### 💰 المعاملات:
- **10 معاملات** متنوعة (واردات ومصروفات)
- **2 تحويلات** بين الحسابات
- تواريخ متنوعة خلال الشهر الحالي

---

## 🎯 المميزات المُفعلة والجاهزة للاستخدام:

### 🏠 **لوحة التحكم**
✅ **إحصائيات فورية** - إجمالي الأرصدة والواردات والمصروفات
✅ **المعاملات الأخيرة** - عرض آخر 10 معاملات
✅ **بطاقات تفاعلية** - عرض مرئي للبيانات المالية

### 🏦 **إدارة الحسابات**
✅ **إضافة حسابات جديدة** - مع أنواع وعملات مختلفة
✅ **عرض جميع الحسابات** - مع الأرصدة الحالية
✅ **أنواع متعددة** - نقدي، بنكي، ائتمان، إلكتروني، استثمار، توفير
✅ **عملات متعددة** - ريال سعودي، دولار، يورو

### 💰 **إدارة الواردات**
✅ **إضافة واردات جديدة** - مع التصنيفات والحسابات
✅ **عرض جميع الواردات** - مرتبة حسب التاريخ
✅ **تصنيفات متنوعة** - راتب، أعمال، استثمار، عمل حر
✅ **تحديث الأرصدة تلقائياً** - عند إضافة واردات جديدة

### 💸 **إدارة المصروفات**
✅ **إضافة مصروفات جديدة** - مع التصنيفات والحسابات
✅ **عرض جميع المصروفات** - مرتبة حسب التاريخ
✅ **تصنيفات شاملة** - طعام، مواصلات، سكن، فواتير، صحة، تعليم، ترفيه، تسوق
✅ **خصم تلقائي من الأرصدة** - عند إضافة مصروفات جديدة

### 🔄 **التحويلات بين الحسابات**
✅ **تحويل بين الحسابات** - مع التحقق من الرصيد
✅ **عرض جميع التحويلات** - مع تفاصيل الحسابات
✅ **تحديث الأرصدة** - خصم من المرسل وإضافة للمستقبل
✅ **منع التحويل للحساب نفسه** - حماية من الأخطاء

### 📊 **التقارير والإحصائيات**
✅ **الملخص المالي** - إجمالي الأرصدة والصافي الشهري
✅ **تقرير الحسابات** - جميع الحسابات مع الأرصدة
✅ **المعاملات الشهرية** - إحصائيات آخر 6 أشهر
✅ **عرض مرئي للبيانات** - بطاقات ملونة وواضحة

### ⚙️ **إعدادات النظام**
✅ **معلومات المستخدم** - عرض بيانات المستخدم الحالي
✅ **إحصائيات النظام** - عدد الحسابات والمعاملات والتحويلات
✅ **إدارة البيانات** - أزرار للنسخ الاحتياطي والتصدير (قريباً)
✅ **معلومات الإصدار** - تفاصيل النظام

### 🔐 **نظام المصادقة**
✅ **تسجيل دخول آمن** - مع تشفير كلمات المرور
✅ **إدارة الجلسات** - تسجيل دخول وخروج
✅ **صلاحيات المستخدمين** - مدير ومستخدم عادي
✅ **حماية البيانات** - كل مستخدم يرى بياناته فقط

---

## 🎯 للاستخدام العملي الفوري

### 📋 خطوات سريعة لتجربة البرنامج:

1. **شغل البرنامج:** `python start_with_demo.py`
2. **سجل دخول:** admin / 123456
3. **أضف حسابين:**
   - الحساب الجاري الرئيسي (حساب بنكي) - 15,000 ر.س
   - المحفظة النقدية (صندوق نقدي) - 2,000 ر.س

4. **أضف بعض الواردات:**
   - راتب شهر ديسمبر: 8,500 ر.س
   - مشروع عمل حر: 2,200 ر.س
   - مكافأة: 1,500 ر.س

5. **أضف بعض المصروفات:**
   - إيجار الشقة: 2,500 ر.س
   - فاتورة كهرباء: 380 ر.س
   - تسوق طعام: 250 ر.س

6. **جرب التحويلات:**
   - حول 500 ر.س من البنك للمحفظة

7. **راجع التقارير** لرؤية الإحصائيات

### 📖 للحصول على دليل مفصل:
راجع ملف `demo_data_guide.md` للحصول على قائمة كاملة بالبيانات العملية المقترحة.

---

## 🔄 إعادة تعيين النظام:

إذا كنت تريد البدء من جديد:

```bash
# حذف وإعادة إنشاء قاعدة البيانات
python reset_database.py

# إنشاء بيانات تجريبية جديدة
python create_sample_data.py
```

---

## 📞 الدعم:

إذا واجهت أي مشاكل:
1. راجع ملفات السجل في مجلد `logs/`
2. تأكد من تشغيل MySQL Server
3. تحقق من إعدادات قاعدة البيانات في `config/settings.py`

---

## 🎉 استمتع بإدارة أموالك بطريقة احترافية!

> 💡 **نصيحة:** احفظ هذا الملف كمرجع سريع للعودة إليه عند الحاجة.
