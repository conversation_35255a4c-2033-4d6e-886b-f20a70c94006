import mysql.connector
import os

def get_db_connection():
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='mohdam',
            database='money_manager'
        )
        return connection
    except mysql.connector.Error as err:
        print(f"Error connecting to database: {err}")
        return None

def execute_sql_file(connection, file_path):
    cursor = connection.cursor()
    with open(file_path, 'r', encoding='utf-8') as f:
        sql_script = f.read()
    
    # Use a dictionary to store errors for each command
    errors = {}
    # Split script into individual statements
    sql_commands = [cmd.strip() for cmd in sql_script.split(';') if cmd.strip()]
    
    for command in sql_commands:
        if not command:
            continue
        try:
            print(f"Executing: {command[:100]}...")
            cursor.execute(command)
            # Fetch results if any, to avoid "Unread result found"
            if cursor.with_rows:
                cursor.fetchall()
        except mysql.connector.Error as err:
            # Store error and continue to next command
            errors[command] = err
            print(f"  -> Error: {err}")

    if not errors or all("exists" in str(e).lower() or "duplicate" in str(e).lower() for e in errors.values()):
        connection.commit()
        print(f"SQL script executed with some expected errors that were ignored.")
    else:
        print("\nErrors occurred during execution:")
        for command, err in errors.items():
            print(f"- Command: {command}\n  Error: {err}")
        connection.rollback()

    cursor.close()

if __name__ == "__main__":
    db_connection = get_db_connection()
    if db_connection:
        sql_file_path = os.path.join(os.path.dirname(__file__), 'update_transfers_table.sql')
        
        if os.path.exists(sql_file_path):
            execute_sql_file(db_connection, sql_file_path)
        else:
            print(f"SQL file not found at: {sql_file_path}")
        
        db_connection.close()