#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من إصلاح مشكلة KeyError في نافذة تسجيل الدخول
"""

import sys
import os

# إضافة مسار المشروع للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_colors_fix():
    """التحقق من إصلاح مشكلة الألوان"""
    print("🔍 التحقق من إصلاح مشكلة الألوان...")
    
    try:
        from config.colors import COLORS
        
        # التحقق من وجود الألوان المطلوبة
        required_colors = ['success', 'error', 'warning', 'text_secondary']
        
        print("📋 الألوان المتاحة:")
        for color in required_colors:
            if color in COLORS:
                print(f"✅ {color}: {COLORS[color]}")
            else:
                print(f"❌ {color}: مفقود")
                return False
        
        # التحقق من عدم وجود 'danger' في COLORS
        if 'danger' in COLORS:
            print("⚠️ تحذير: COLORS['danger'] موجود (قد يسبب تعارض)")
        else:
            print("✅ COLORS['danger'] غير موجود (صحيح)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def verify_update_status_fix():
    """التحقق من إصلاح دالة update_status"""
    print("\n🔍 التحقق من إصلاح دالة update_status...")
    
    try:
        from config.colors import COLORS
        
        # محاكاة الكود المُصلح
        def test_update_status(message, status_type="info"):
            colors = {
                "success": COLORS['success'],
                "error": COLORS['error'],  # استخدام 'error' بدلاً من 'danger'
                "warning": COLORS['warning'],
                "info": COLORS['text_secondary']
            }
            
            color = colors.get(status_type, COLORS['text_secondary'])
            return color
        
        # اختبار جميع أنواع الحالات
        test_cases = ["success", "error", "warning", "info", "unknown"]
        
        print("📋 اختبار أنواع الحالات:")
        for status_type in test_cases:
            try:
                color = test_update_status(f"رسالة {status_type}", status_type)
                print(f"✅ {status_type}: {color}")
            except KeyError as e:
                print(f"❌ {status_type}: KeyError - {e}")
                return False
            except Exception as e:
                print(f"❌ {status_type}: خطأ - {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def verify_button_styles():
    """التحقق من أنماط الأزرار"""
    print("\n🔍 التحقق من أنماط الأزرار...")
    
    try:
        from config.colors import BUTTON_STYLES
        
        # التحقق من وجود نمط 'danger'
        if 'danger' in BUTTON_STYLES:
            print(f"✅ BUTTON_STYLES['danger'] موجود")
            print(f"   fg_color: {BUTTON_STYLES['danger']['fg_color']}")
            print(f"   hover_color: {BUTTON_STYLES['danger']['hover_color']}")
            return True
        else:
            print("❌ BUTTON_STYLES['danger'] مفقود")
            return False
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 التحقق من إصلاح مشكلة KeyError في نافذة تسجيل الدخول")
    print("=" * 60)
    
    tests = [
        ("إصلاح الألوان", verify_colors_fix),
        ("إصلاح دالة update_status", verify_update_status_fix),
        ("أنماط الأزرار", verify_button_styles)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج التحقق:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("\n🎉 تم إصلاح مشكلة KeyError بنجاح!")
        print("✅ نافذة تسجيل الدخول ستعمل بدون أخطاء KeyError")
        print("✅ رسائل الخطأ ستظهر بشكل صحيح")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى إصلاح")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
