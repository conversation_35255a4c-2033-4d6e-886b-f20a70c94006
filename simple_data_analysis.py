#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل مبسط للبيانات الحالية
"""

import os
import json
import sqlite3

def check_files():
    """فحص الملفات الموجودة"""
    print("📁 فحص الملفات الموجودة:")
    
    files_to_check = [
        "money_manager.db",
        "accounts.json", 
        "users.json",
        "transactions.json",
        "config/database_settings.json"
    ]
    
    existing_files = []
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size} bytes)")
            existing_files.append(file_path)
        else:
            print(f"❌ {file_path} (غير موجود)")
    
    return existing_files

def analyze_sqlite():
    """تحليل قاعدة بيانات SQLite"""
    print("\n🔍 تحليل قاعدة بيانات SQLite:")
    
    if not os.path.exists("money_manager.db"):
        print("❌ ملف SQLite غير موجود")
        return {}
    
    try:
        conn = sqlite3.connect("money_manager.db")
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📋 الجداول: {', '.join(tables)}")
        
        data = {}
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} سجل")
                data[table] = count
            except Exception as e:
                print(f"   ❌ خطأ في {table}: {e}")
        
        conn.close()
        return data
        
    except Exception as e:
        print(f"❌ خطأ في SQLite: {e}")
        return {}

def analyze_json_files():
    """تحليل ملفات JSON"""
    print("\n📄 تحليل ملفات JSON:")
    
    json_files = ["accounts.json", "users.json", "transactions.json"]
    data = {}
    
    for json_file in json_files:
        if os.path.exists(json_file):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    content = json.load(f)
                    
                if isinstance(content, list):
                    count = len(content)
                    print(f"📄 {json_file}: {count} عنصر")
                    data[json_file] = {'type': 'list', 'count': count}
                elif isinstance(content, dict):
                    count = len(content)
                    print(f"📄 {json_file}: {count} مفتاح")
                    data[json_file] = {'type': 'dict', 'count': count}
                    
            except Exception as e:
                print(f"❌ خطأ في {json_file}: {e}")
        else:
            print(f"❌ {json_file} غير موجود")
    
    return data

def analyze_config():
    """تحليل ملفات التكوين"""
    print("\n⚙️ تحليل ملفات التكوين:")
    
    config_files = [
        "config/database_settings.json",
        "config/settings.py"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file} موجود")
            
            if config_file.endswith('.json'):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        print(f"   قاعدة البيانات: {config.get('database', 'غير محدد')}")
                        print(f"   المضيف: {config.get('host', 'غير محدد')}")
                except Exception as e:
                    print(f"   ❌ خطأ في قراءة التكوين: {e}")
        else:
            print(f"❌ {config_file} غير موجود")

def check_backups():
    """فحص النسخ الاحتياطية"""
    print("\n💾 فحص النسخ الاحتياطية:")
    
    if os.path.exists("backups"):
        backup_files = os.listdir("backups")
        print(f"📦 عدد النسخ الاحتياطية: {len(backup_files)}")
        
        for backup_file in backup_files[:5]:  # أول 5 ملفات
            file_path = os.path.join("backups", backup_file)
            size = os.path.getsize(file_path)
            print(f"   - {backup_file} ({size} bytes)")
            
        if len(backup_files) > 5:
            print(f"   ... و {len(backup_files) - 5} ملف آخر")
    else:
        print("❌ مجلد النسخ الاحتياطية غير موجود")

def main():
    """الدالة الرئيسية"""
    print("🔍 تحليل مبسط للبيانات الحالية")
    print("=" * 50)
    
    # فحص الملفات
    existing_files = check_files()
    
    # تحليل SQLite
    sqlite_data = analyze_sqlite()
    
    # تحليل JSON
    json_data = analyze_json_files()
    
    # تحليل التكوين
    analyze_config()
    
    # فحص النسخ الاحتياطية
    check_backups()
    
    # ملخص النتائج
    print("\n📊 ملخص النتائج:")
    print("=" * 30)
    print(f"📁 الملفات الموجودة: {len(existing_files)}")
    print(f"🗄️ جداول SQLite: {len(sqlite_data)}")
    print(f"📄 ملفات JSON: {len(json_data)}")
    
    # حفظ النتائج
    results = {
        'existing_files': existing_files,
        'sqlite_data': sqlite_data,
        'json_data': json_data
    }
    
    with open('simple_analysis_result.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ تم حفظ النتائج في: simple_analysis_result.json")

if __name__ == "__main__":
    main()
