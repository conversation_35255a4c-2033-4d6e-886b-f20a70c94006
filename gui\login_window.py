import customtkinter as ctk
from tkinter import messagebox
import threading
from config.colors import COLORS, BUTTON_STYLES, INPUT_STYLES, ARABIC_TEXT_STYLES
from config.settings import APP_CONFIG
from config.fonts import (
    get_title_font, get_subtitle_font, get_header_font, get_body_font,
    create_rtl_label, create_rtl_button, create_rtl_entry
)
from config.database_config import db_config
from utils.auth import auth_manager
from database.connection import db

class LoginWindow:
    """نافذة تسجيل الدخول"""

    def __init__(self):
        self.window = None
        self.setup_window()
        self.create_widgets()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        # إعداد المظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")

        # إنشاء النافذة
        self.window = ctk.CTk()
        self.window.title(APP_CONFIG['title'])
        self.window.geometry("600x750")  # زيادة العرض لاستيعاب التبويبات
        self.window.resizable(False, False)

        # تعيين لون الخلفية
        self.window.configure(fg_color=COLORS['bg_light'])

        # توسيط النافذة
        self.center_window()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة مع نظام التبويبات"""
        # الحاوية الرئيسية
        main_frame = ctk.CTkFrame(
            self.window,
            fg_color=COLORS['bg_card'],
            corner_radius=20,
            border_width=1,
            border_color=COLORS['border']
        )
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)

        # العنوان الرئيسي
        title_label = create_rtl_label(
            main_frame,
            text="مدير الأموال",
            font_size='title',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 10))

        # العنوان الفرعي
        subtitle_label = create_rtl_label(
            main_frame,
            text="نظام إدارة الأموال الشخصية",
            font_size='subtitle',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['subtitle']
        )
        subtitle_label.pack(pady=(0, 30))

        # إنشاء نظام التبويبات
        self.create_tabview(main_frame)

    def create_tabview(self, parent):
        """إنشاء نظام التبويبات"""
        # إنشاء التبويبات
        self.tabview = ctk.CTkTabview(
            parent,
            width=500,
            height=450,
            fg_color=COLORS['bg_light'],
            segmented_button_fg_color=COLORS['primary'],
            segmented_button_selected_color=COLORS['primary_dark'],
            segmented_button_selected_hover_color=COLORS['primary_light'],
            text_color=COLORS['text_primary']
        )
        self.tabview.pack(fill="both", expand=True, padx=20, pady=20)

        # إضافة التبويبات
        self.login_tab = self.tabview.add("تسجيل الدخول")
        self.db_settings_tab = self.tabview.add("إعدادات قاعدة البيانات")

        # إنشاء محتوى التبويبات
        self.create_login_tab()
        self.create_db_settings_tab()

    def create_login_tab(self):
        """إنشاء تبويب تسجيل الدخول"""
        # حاوية النموذج
        form_frame = ctk.CTkFrame(
            self.login_tab,
            fg_color="transparent"
        )
        form_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # حقل اسم المستخدم
        username_label = create_rtl_label(
            form_frame,
            text="اسم المستخدم:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        username_label.pack(fill="x", pady=(0, 5))

        self.username_entry = create_rtl_entry(
            form_frame,
            placeholder_text="أدخل اسم المستخدم",
            height=45,
            **INPUT_STYLES['rtl']
        )
        self.username_entry.pack(fill="x", pady=(0, 20))

        # حقل كلمة المرور
        password_label = create_rtl_label(
            form_frame,
            text="كلمة المرور:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        password_label.pack(fill="x", pady=(0, 5))

        self.password_entry = create_rtl_entry(
            form_frame,
            placeholder_text="أدخل كلمة المرور",
            show="*",
            height=45,
            **INPUT_STYLES['rtl']
        )
        self.password_entry.pack(fill="x", pady=(0, 30))

        # زر تسجيل الدخول
        self.login_button = create_rtl_button(
            form_frame,
            text="تسجيل الدخول",
            command=self.login,
            **BUTTON_STYLES['primary']
        )
        self.login_button.pack(fill="x", pady=(0, 15))

        # معلومات الاتصال
        info_frame = ctk.CTkFrame(
            form_frame,
            fg_color=COLORS['primary'],
            corner_radius=10
        )
        info_frame.pack(fill="x", pady=(20, 0))

        info_label = create_rtl_label(
            info_frame,
            text="💡 يمكن تسجيل الدخول للمدير فقط باستخدام اسم المستخدم 'admin'",
            font_size='small',
            text_color=COLORS['text_light'],
            wraplength=350,
            **ARABIC_TEXT_STYLES['subtitle']
        )
        info_label.pack(pady=15)

        # ربط مفتاح Enter
        self.window.bind('<Return>', lambda event: self.login())

    def create_db_settings_tab(self):
        """إنشاء تبويب إعدادات قاعدة البيانات"""
        # حاوية النموذج
        settings_frame = ctk.CTkScrollableFrame(
            self.db_settings_tab,
            fg_color="transparent"
        )
        settings_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان القسم
        title_label = create_rtl_label(
            settings_frame,
            text="⚙️ إعدادات الاتصال بقاعدة البيانات",
            font_size='header',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['header']
        )
        title_label.pack(fill="x", pady=(0, 20))

        # تحميل الإعدادات الحالية
        current_config = db_config.get_config()

        # حقل عنوان الخادم
        host_label = create_rtl_label(
            settings_frame,
            text="عنوان الخادم:",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        host_label.pack(fill="x", pady=(0, 5))

        self.host_entry = create_rtl_entry(
            settings_frame,
            placeholder_text="localhost",
            height=40,
            **INPUT_STYLES['rtl']
        )
        self.host_entry.pack(fill="x", pady=(0, 15))
        self.host_entry.insert(0, current_config.get('host', 'localhost'))

        # حقل المنفذ
        port_label = create_rtl_label(
            settings_frame,
            text="المنفذ:",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        port_label.pack(fill="x", pady=(0, 5))

        self.port_entry = create_rtl_entry(
            settings_frame,
            placeholder_text="3306",
            height=40,
            **INPUT_STYLES['rtl']
        )
        self.port_entry.pack(fill="x", pady=(0, 15))
        self.port_entry.insert(0, str(current_config.get('port', 3306)))

        # حقل اسم قاعدة البيانات
        database_label = create_rtl_label(
            settings_frame,
            text="اسم قاعدة البيانات:",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        database_label.pack(fill="x", pady=(0, 5))

        self.database_entry = create_rtl_entry(
            settings_frame,
            placeholder_text="money_manager",
            height=40,
            **INPUT_STYLES['rtl']
        )
        self.database_entry.pack(fill="x", pady=(0, 15))
        self.database_entry.insert(0, current_config.get('database', 'money_manager'))

        # حقل اسم المستخدم
        user_label = create_rtl_label(
            settings_frame,
            text="اسم المستخدم:",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        user_label.pack(fill="x", pady=(0, 5))

        self.db_user_entry = create_rtl_entry(
            settings_frame,
            placeholder_text="root",
            height=40,
            **INPUT_STYLES['rtl']
        )
        self.db_user_entry.pack(fill="x", pady=(0, 15))
        self.db_user_entry.insert(0, current_config.get('user', 'root'))

        # حقل كلمة المرور
        db_password_label = create_rtl_label(
            settings_frame,
            text="كلمة المرور:",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        db_password_label.pack(fill="x", pady=(0, 5))

        self.db_password_entry = create_rtl_entry(
            settings_frame,
            placeholder_text="كلمة مرور قاعدة البيانات",
            show="*",
            height=40,
            **INPUT_STYLES['rtl']
        )
        self.db_password_entry.pack(fill="x", pady=(0, 20))
        self.db_password_entry.insert(0, current_config.get('password', ''))

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(
            settings_frame,
            fg_color="transparent"
        )
        buttons_frame.pack(fill="x", pady=(10, 0))

        # زر اختبار الاتصال
        self.test_button = create_rtl_button(
            buttons_frame,
            text="🔍 اختبار الاتصال",
            command=self.test_database_connection,
            **BUTTON_STYLES['secondary']
        )
        self.test_button.pack(fill="x", pady=(0, 10))

        # زر حفظ الإعدادات
        self.save_button = create_rtl_button(
            buttons_frame,
            text="💾 حفظ الإعدادات",
            command=self.save_database_settings,
            **BUTTON_STYLES['primary']
        )
        self.save_button.pack(fill="x", pady=(0, 10))

        # زر استعادة الافتراضي
        self.reset_button = create_rtl_button(
            buttons_frame,
            text="🔄 استعادة الافتراضي",
            command=self.reset_database_settings,
            **BUTTON_STYLES['danger']
        )
        self.reset_button.pack(fill="x", pady=(0, 10))

        # منطقة حالة الاتصال
        self.status_frame = ctk.CTkFrame(
            settings_frame,
            fg_color=COLORS['bg_light'],
            corner_radius=10
        )
        self.status_frame.pack(fill="x", pady=(20, 0))

        self.status_label = create_rtl_label(
            self.status_frame,
            text="💡 قم بتعديل الإعدادات واختبار الاتصال قبل الحفظ",
            font_size='small',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['subtitle']
        )
        self.status_label.pack(pady=15)

    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            # تعطيل الزر أثناء الاختبار
            self.test_button.configure(state="disabled", text="🔄 جاري الاختبار...")
            self.window.update()

            # جمع الإعدادات من الحقول
            config = {
                'host': self.host_entry.get().strip(),
                'port': self.port_entry.get().strip(),
                'database': self.database_entry.get().strip(),
                'user': self.db_user_entry.get().strip(),
                'password': self.db_password_entry.get(),
                'charset': 'utf8mb4',
                'autocommit': True
            }

            # التحقق من صحة الإعدادات
            is_valid, errors = db_config.validate_config(config)
            if not is_valid:
                error_message = "خطأ في الإعدادات:\n" + "\n".join(errors)
                self.update_status(error_message, "error")
                return

            # اختبار الاتصال
            success, message = db_config.test_connection(config)

            if success:
                self.update_status(f"✅ {message}", "success")
            else:
                self.update_status(f"❌ {message}", "error")

        except Exception as e:
            self.update_status(f"❌ خطأ غير متوقع: {str(e)}", "error")
        finally:
            # إعادة تفعيل الزر
            self.test_button.configure(state="normal", text="🔍 اختبار الاتصال")

    def save_database_settings(self):
        """حفظ إعدادات قاعدة البيانات"""
        try:
            # جمع الإعدادات من الحقول
            config = {
                'host': self.host_entry.get().strip(),
                'port': self.port_entry.get().strip(),
                'database': self.database_entry.get().strip(),
                'user': self.db_user_entry.get().strip(),
                'password': self.db_password_entry.get(),
                'charset': 'utf8mb4',
                'autocommit': True
            }

            # التحقق من صحة الإعدادات
            is_valid, errors = db_config.validate_config(config)
            if not is_valid:
                error_message = "لا يمكن حفظ الإعدادات:\n" + "\n".join(errors)
                self.update_status(error_message, "error")
                return

            # اختبار الاتصال قبل الحفظ
            success, test_message = db_config.test_connection(config)
            if not success:
                confirm = messagebox.askyesno(
                    "تحذير",
                    f"فشل اختبار الاتصال:\n{test_message}\n\nهل تريد حفظ الإعدادات رغم ذلك؟"
                )
                if not confirm:
                    self.update_status("تم إلغاء الحفظ", "warning")
                    return

            # حفظ الإعدادات
            success, message = db_config.update_config(config)

            if success:
                # إعادة تحميل الاتصال بقاعدة البيانات مع الإعدادات الجديدة
                try:
                    db.reconnect()
                    self.update_status(f"✅ {message} وتم تحديث الاتصال", "success")
                    messagebox.showinfo("نجح الحفظ", "تم حفظ إعدادات قاعدة البيانات وتحديث الاتصال بنجاح")
                except Exception as e:
                    self.update_status(f"✅ تم الحفظ ولكن فشل تحديث الاتصال: {str(e)}", "warning")
                    messagebox.showwarning("تحذير", f"تم حفظ الإعدادات ولكن فشل في تحديث الاتصال:\n{str(e)}")
            else:
                self.update_status(f"❌ {message}", "error")

        except Exception as e:
            error_message = f"❌ خطأ في حفظ الإعدادات: {str(e)}"
            self.update_status(error_message, "error")

    def reset_database_settings(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        try:
            confirm = messagebox.askyesno(
                "تأكيد الإعادة",
                "هل أنت متأكد من إعادة تعيين الإعدادات الافتراضية؟\n\nسيتم فقدان جميع الإعدادات الحالية."
            )

            if not confirm:
                return

            # إعادة تعيين الإعدادات
            success, message = db_config.reset_to_default()

            if success:
                # تحديث الحقول
                default_config = db_config.get_config()
                self.host_entry.delete(0, 'end')
                self.host_entry.insert(0, default_config.get('host', 'localhost'))

                self.port_entry.delete(0, 'end')
                self.port_entry.insert(0, str(default_config.get('port', 3306)))

                self.database_entry.delete(0, 'end')
                self.database_entry.insert(0, default_config.get('database', 'money_manager'))

                self.db_user_entry.delete(0, 'end')
                self.db_user_entry.insert(0, default_config.get('user', 'root'))

                self.db_password_entry.delete(0, 'end')
                self.db_password_entry.insert(0, default_config.get('password', ''))

                self.update_status(f"✅ {message}", "success")
            else:
                self.update_status(f"❌ {message}", "error")

        except Exception as e:
            error_message = f"❌ خطأ في إعادة التعيين: {str(e)}"
            self.update_status(error_message, "error")

    def update_status(self, message, status_type="info"):
        """تحديث رسالة الحالة"""
        colors = {
            "success": COLORS['success'],
            "error": COLORS['error'],  # استخدام 'error' بدلاً من 'danger'
            "warning": COLORS['warning'],
            "info": COLORS['text_secondary']
        }

        color = colors.get(status_type, COLORS['text_secondary'])

        self.status_label.configure(text=message, text_color=color)
        self.window.update()

    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()

        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # تعطيل الزر أثناء المعالجة
        self.login_button.configure(state="disabled", text="جاري تسجيل الدخول...")

        # تنفيذ تسجيل الدخول في خيط منفصل
        threading.Thread(target=self._perform_login, args=(username, password), daemon=True).start()

    def _perform_login(self, username, password):
        """تنفيذ عملية تسجيل الدخول"""
        try:
            # تحميل إعدادات قاعدة البيانات المحفوظة
            current_config = db_config.get_config()

            # اختبار الاتصال بقاعدة البيانات أولاً
            success, test_message = db_config.test_connection(current_config)
            if not success:
                self.window.after(0, lambda: self._login_failed(f"فشل الاتصال بقاعدة البيانات: {test_message}\n\nتحقق من إعدادات قاعدة البيانات في التبويب المخصص"))
                return

            # محاولة الاتصال بقاعدة البيانات
            if not db.connect():
                # محاولة إعادة الاتصال مرة واحدة
                if not db.reconnect():
                    self.window.after(0, lambda: self._login_failed("فشل الاتصال بقاعدة البيانات.\n\nتأكد من:\n• تشغيل خادم MySQL\n• صحة إعدادات قاعدة البيانات\n• صحة اسم المستخدم وكلمة المرور"))
                    return

            # محاولة تسجيل الدخول
            success, message = auth_manager.login(username, password)

            if success:
                # التأكد من أن بيانات المستخدم محفوظة
                if auth_manager.current_user:
                    self.window.after(0, self._login_success)
                else:
                    self.window.after(0, lambda: self._login_failed("خطأ في حفظ بيانات المستخدم"))
            else:
                self.window.after(0, lambda: self._login_failed(message))

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"خطأ في تسجيل الدخول: {error_details}")
            self.window.after(0, lambda: self._login_failed(f"خطأ غير متوقع: {str(e)}"))



    def _login_success(self):
        """نجح تسجيل الدخول"""
        try:
            # استيراد وإنشاء النافذة الرئيسية أولاً
            from gui.main_window import MainWindow
            main_app = MainWindow()

            # إخفاء نافذة تسجيل الدخول
            self.window.withdraw()

            # تشغيل النافذة الرئيسية
            main_app.run()

            # تدمير نافذة تسجيل الدخول بعد إغلاق النافذة الرئيسية
            self.window.destroy()

        except Exception as e:
            # في حالة حدوث خطأ، إظهار رسالة خطأ وإعادة تفعيل زر تسجيل الدخول
            self.window.deiconify()  # إظهار النافذة مرة أخرى
            self._login_failed(f"خطأ في فتح النافذة الرئيسية: {str(e)}")

    def _login_failed(self, message):
        """فشل تسجيل الدخول"""
        self.login_button.configure(state="normal", text="تسجيل الدخول")
        messagebox.showerror("خطأ في تسجيل الدخول", message)



    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()
