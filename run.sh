#!/bin/bash

# تعيين ترميز UTF-8
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دالة طباعة ملونة
print_colored() {
    echo -e "${2}${1}${NC}"
}

# رأس البرنامج
echo "========================================"
print_colored "🏦 برنامج إدارة الأموال الشخصية" $PURPLE
echo "========================================"
print_colored "📅 التاريخ: $(date '+%Y-%m-%d %H:%M:%S')" $CYAN
print_colored "💻 نظام التشغيل: $(uname -s) $(uname -r)" $CYAN
echo "========================================"
echo

# فحص Python
print_colored "🔍 فحص Python..." $BLUE
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        print_colored "❌ Python غير مثبت" $RED
        echo
        print_colored "💡 خيارات التثبيت:" $YELLOW
        echo "   • Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "   • CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "   • macOS: brew install python3"
        echo "   • أو حمل من: https://python.org/downloads/"
        echo
        read -p "❓ هل تريد فتح صفحة تحميل Python؟ (y/n): " choice
        if [[ $choice == "y" || $choice == "Y" ]]; then
            if command -v xdg-open &> /dev/null; then
                xdg-open "https://python.org/downloads/"
            elif command -v open &> /dev/null; then
                open "https://python.org/downloads/"
            fi
        fi
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# التحقق من إصدار Python
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
print_colored "✅ Python $PYTHON_VERSION موجود" $GREEN

# فحص إصدار Python
PYTHON_MAJOR=$($PYTHON_CMD -c "import sys; print(sys.version_info.major)")
PYTHON_MINOR=$($PYTHON_CMD -c "import sys; print(sys.version_info.minor)")

if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 8 ]; then
    print_colored "✅ إصدار Python متوافق (3.8+)" $GREEN
else
    print_colored "❌ إصدار Python غير متوافق (يتطلب 3.8+)" $RED
    print_colored "💡 الإصدار الحالي: $PYTHON_VERSION" $YELLOW
    exit 1
fi
echo

# فحص pip
print_colored "🔍 فحص pip..." $BLUE
if command -v pip3 &> /dev/null; then
    PIP_CMD="pip3"
elif command -v pip &> /dev/null; then
    PIP_CMD="pip"
else
    print_colored "❌ pip غير موجود" $RED
    print_colored "💡 محاولة تثبيت pip..." $YELLOW
    $PYTHON_CMD -m ensurepip --upgrade
    if [ $? -eq 0 ]; then
        PIP_CMD="$PYTHON_CMD -m pip"
    else
        print_colored "❌ فشل في تثبيت pip" $RED
        exit 1
    fi
fi

print_colored "✅ pip متوفر" $GREEN
echo

# فحص المتطلبات
print_colored "🔍 فحص المتطلبات الأساسية..." $BLUE
$PYTHON_CMD -c "import customtkinter, mysql.connector, bcrypt" 2>/dev/null
if [ $? -ne 0 ]; then
    print_colored "⚠️  بعض المتطلبات غير مثبتة" $YELLOW
    echo
    read -p "❓ هل تريد تثبيت المتطلبات تلقائياً؟ (y/n): " install_choice

    if [[ $install_choice == "y" || $install_choice == "Y" ]]; then
        echo
        print_colored "📦 بدء تثبيت المتطلبات..." $BLUE
        print_colored "⏳ هذا قد يستغرق بضع دقائق..." $YELLOW
        echo

        if [ -f "requirements.txt" ]; then
            print_colored "📋 تثبيت من requirements.txt..." $BLUE
            $PIP_CMD install -r requirements.txt
        else
            print_colored "📦 تثبيت المكتبات الأساسية..." $BLUE
            $PIP_CMD install mysql-connector-python customtkinter Pillow bcrypt python-dateutil
        fi

        if [ $? -ne 0 ]; then
            echo
            print_colored "❌ فشل في تثبيت بعض المتطلبات" $RED
            print_colored "💡 جرب تشغيل: $PYTHON_CMD install_requirements.py" $YELLOW
            print_colored "💡 أو راجع ملف INSTALLATION_GUIDE.md" $YELLOW
            exit 1
        fi

        print_colored "✅ تم تثبيت المتطلبات بنجاح" $GREEN
    else
        print_colored "⚠️  لن يتم تثبيت المتطلبات" $YELLOW
        print_colored "💡 لتثبيتها لاحقاً: $PYTHON_CMD install_requirements.py" $YELLOW
        echo
    fi
fi

print_colored "✅ جميع المتطلبات متوفرة" $GREEN
echo

# فحص ملفات المشروع
print_colored "🔍 فحص ملفات المشروع..." $BLUE
if [ ! -f "main.py" ]; then
    print_colored "❌ ملف main.py غير موجود" $RED
    print_colored "💡 تأكد من وجود جميع ملفات المشروع" $YELLOW
    exit 1
fi

if [ ! -f "config/settings.py" ]; then
    print_colored "❌ ملفات الإعدادات غير موجودة" $RED
    print_colored "💡 تأكد من وجود مجلد config وملفاته" $YELLOW
    exit 1
fi

print_colored "✅ ملفات المشروع موجودة" $GREEN
echo

# تشغيل البرنامج
print_colored "🚀 بدء تشغيل البرنامج..." $PURPLE
print_colored "💡 إذا كانت هذه المرة الأولى، ستحتاج إلى:" $YELLOW
echo "   • كلمة مرور MySQL"
echo "   • إنشاء حساب المدير الأول"
echo
print_colored "⏳ جاري التشغيل..." $CYAN
echo

$PYTHON_CMD main.py
EXIT_CODE=$?

echo
if [ $EXIT_CODE -eq 0 ]; then
    print_colored "✅ تم إغلاق البرنامج بنجاح" $GREEN
else
    print_colored "❌ حدث خطأ أثناء تشغيل البرنامج (رمز الخطأ: $EXIT_CODE)" $RED
    echo
    print_colored "🔍 خطوات استكشاف الأخطاء:" $YELLOW
    echo "   1. راجع ملفات السجل في مجلد logs/"
    echo "   2. تأكد من تشغيل MySQL Server"
    echo "   3. تحقق من كلمة مرور MySQL"
    echo "   4. شغل: $PYTHON_CMD check_requirements.py"
    echo

    if [ -d "logs" ]; then
        print_colored "📋 آخر ملفات السجل:" $CYAN
        ls -t logs/*.log 2>/dev/null | head -3
    fi
fi

echo
print_colored "👋 شكراً لاستخدام برنامج إدارة الأموال" $PURPLE
print_colored "💡 للمساعدة: راجع README.md أو INSTALLATION_GUIDE.md" $CYAN
