#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لميزة إدارة المستخدمين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_user_management():
    """إصلاح شامل لميزة إدارة المستخدمين"""
    print("🚀 إصلاح شامل لميزة إدارة المستخدمين")
    print("=" * 50)
    
    fixes_applied = []
    issues_found = []
    
    # 1. التحقق من الاتصال بقاعدة البيانات
    print("\n1. التحقق من الاتصال بقاعدة البيانات...")
    try:
        from database.connection import db
        if db.is_connected() or db.connect():
            print("✅ الاتصال بقاعدة البيانات ناجح")
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            issues_found.append("مشكلة في الاتصال بقاعدة البيانات")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        issues_found.append(f"خطأ في الاتصال: {e}")
        return False
    
    # 2. التحقق من وجود المستخدمين
    print("\n2. التحقق من وجود المستخدمين...")
    try:
        users = db.execute_query("SELECT username, role, is_active FROM users")
        print(f"📊 عدد المستخدمين: {len(users)}")
        
        admin_users = [u for u in users if u['role'] == 'admin' and u['is_active']]
        print(f"👑 المديرين النشطين: {len(admin_users)}")
        
        for user in users:
            status = "نشط" if user['is_active'] else "معطل"
            print(f"   - {user['username']} ({user['role']}) - {status}")
        
        # التحقق من وجود admin2
        admin2_exists = any(u['username'] == 'admin2' for u in users)
        if not admin2_exists:
            print("⚠️ المستخدم admin2 غير موجود")
            issues_found.append("المستخدم admin2 غير موجود")
        else:
            print("✅ المستخدم admin2 موجود")
            
    except Exception as e:
        print(f"❌ خطأ في فحص المستخدمين: {e}")
        issues_found.append(f"خطأ في فحص المستخدمين: {e}")
    
    # 3. إنشاء المستخدم admin2 إذا لم يكن موجوداً
    if "المستخدم admin2 غير موجود" in issues_found:
        print("\n3. إنشاء المستخدم admin2...")
        try:
            import bcrypt
            from datetime import datetime
            
            password_hash = bcrypt.hashpw("123456".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            insert_query = """
                INSERT INTO users (username, password_hash, full_name, role, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            
            result = db.execute_insert(insert_query, (
                'admin2', password_hash, 'المدير الجديد', 'admin', True, datetime.now()
            ))
            
            if result > 0:
                print("✅ تم إنشاء المستخدم admin2 بنجاح")
                fixes_applied.append("إنشاء المستخدم admin2")
                issues_found.remove("المستخدم admin2 غير موجود")
            else:
                print("❌ فشل في إنشاء المستخدم admin2")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء admin2: {e}")
    
    # 4. اختبار تسجيل الدخول
    print("\n4. اختبار تسجيل الدخول...")
    try:
        from utils.auth import auth_manager
        
        # اختبار admin2
        success, message = auth_manager.login("admin2", "123456")
        if success:
            print("✅ تسجيل الدخول بـ admin2 ناجح")
            
            # اختبار الصلاحيات
            if auth_manager.can_manage_users():
                print("✅ صلاحيات إدارة المستخدمين متاحة")
            else:
                print("❌ صلاحيات إدارة المستخدمين غير متاحة")
                issues_found.append("مشكلة في صلاحيات إدارة المستخدمين")
            
            auth_manager.logout()
        else:
            print(f"❌ فشل تسجيل الدخول بـ admin2: {message}")
            issues_found.append("فشل تسجيل الدخول بـ admin2")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        issues_found.append(f"خطأ في تسجيل الدخول: {e}")
    
    # 5. اختبار دالة User.get_all()
    print("\n5. اختبار دالة User.get_all()...")
    try:
        from database.models import User
        users = User.get_all()
        
        if users is not None and len(users) > 0:
            print(f"✅ User.get_all() تعمل بشكل صحيح - {len(users)} مستخدم")
            for user in users:
                print(f"   - {user['username']} ({user['full_name']})")
        else:
            print("❌ User.get_all() لا تعمل بشكل صحيح")
            issues_found.append("مشكلة في دالة User.get_all()")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار User.get_all(): {e}")
        issues_found.append(f"خطأ في User.get_all(): {e}")
    
    # 6. اختبار مكونات واجهة المستخدم
    print("\n6. اختبار مكونات واجهة المستخدم...")
    try:
        from config.colors import COLORS, BUTTON_STYLES
        from config.fonts import create_rtl_label, create_rtl_button
        from gui.user_management_windows import AddUserWindow, EditUserWindow
        print("✅ جميع مكونات واجهة المستخدم متاحة")
    except Exception as e:
        print(f"❌ خطأ في مكونات واجهة المستخدم: {e}")
        issues_found.append(f"خطأ في مكونات واجهة المستخدم: {e}")
    
    # 7. إنشاء جدول activity_log إذا لم يكن موجوداً
    print("\n7. التحقق من جدول activity_log...")
    try:
        tables = db.execute_query("SHOW TABLES LIKE 'activity_log'")
        if not tables:
            print("⚠️ جدول activity_log غير موجود - سيتم إنشاؤه...")
            
            create_table_query = """
                CREATE TABLE activity_log (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    action_type VARCHAR(50) NOT NULL,
                    table_name VARCHAR(50) NOT NULL,
                    record_id INT NULL,
                    description TEXT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            """
            db.execute_update(create_table_query, ())
            print("✅ تم إنشاء جدول activity_log")
            fixes_applied.append("إنشاء جدول activity_log")
        else:
            print("✅ جدول activity_log موجود")
            
    except Exception as e:
        print(f"❌ خطأ في فحص جدول activity_log: {e}")
        issues_found.append(f"خطأ في جدول activity_log: {e}")
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 نتائج الإصلاح:")
    
    if fixes_applied:
        print(f"🔧 الإصلاحات المطبقة ({len(fixes_applied)}):")
        for fix in fixes_applied:
            print(f"   ✅ {fix}")
    
    if issues_found:
        print(f"\n⚠️ المشاكل المتبقية ({len(issues_found)}):")
        for issue in issues_found:
            print(f"   ❌ {issue}")
    else:
        print("\n🎉 لا توجد مشاكل! جميع المكونات تعمل بشكل صحيح")
    
    # تعليمات الاستخدام
    if not issues_found:
        print("\n🚀 تعليمات الاستخدام:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول باستخدام: admin2 / 123456")
        print("3. انقر على '👥 إدارة المستخدمين' في الشريط الجانبي")
        print("4. يجب أن تظهر جميع المستخدمين بشكل صحيح")
        
        return True
    else:
        print("\n🔧 خطوات الإصلاح الإضافية:")
        print("1. تحقق من إعدادات قاعدة البيانات في config/settings.py")
        print("2. تأكد من تشغيل MySQL Server")
        print("3. شغل هذا السكريبت مرة أخرى")
        
        return False

if __name__ == "__main__":
    fix_user_management()
