import mysql.connector
from mysql.connector import Error
import logging
from config.settings import DATABASE_CONFIG
from config.database_config import db_config
import os

class DatabaseConnection:
    """فئة إدارة الاتصال بقاعدة البيانات"""

    def __init__(self):
        self.connection = None
        self.cursor = None

    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            # إغلاق الاتصال السابق إن وجد
            if self.connection and self.connection.is_connected():
                self.close()

            # استخدام الإعدادات من database_config أولاً، ثم الافتراضية
            try:
                config = db_config.get_config()
                logging.info(f"استخدام إعدادات قاعدة البيانات المحفوظة: {config['host']}:{config['port']}/{config['database']}")
            except Exception as e:
                logging.warning(f"فشل في تحميل إعدادات قاعدة البيانات المحفوظة: {e}")
                config = DATABASE_CONFIG.copy()
                logging.info("استخدام الإعدادات الافتراضية")

            # إضافة timeout للاتصال
            config['connection_timeout'] = 10
            config['autocommit'] = True

            # محاولة الاتصال بقاعدة البيانات
            self.connection = mysql.connector.connect(**config)

            if self.connection.is_connected():
                self.cursor = self.connection.cursor(dictionary=True)
                logging.info("تم الاتصال بقاعدة البيانات بنجاح")
                return True
            else:
                logging.error("فشل في الاتصال بقاعدة البيانات")
                return False

        except Error as e:
            # إذا فشل الاتصال بقاعدة البيانات، محاولة إنشائها
            if "Unknown database" in str(e):
                return self._create_database()
            elif "Access denied" in str(e):
                logging.error(f"خطأ في كلمة المرور أو المستخدم: {e}")
                return False
            elif "Can't connect to MySQL server" in str(e):
                logging.error(f"لا يمكن الاتصال بخادم MySQL: {e}")
                return False
            else:
                logging.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
                return False
        except Exception as e:
            logging.error(f"خطأ غير متوقع في الاتصال: {e}")
            return False

    def _create_database(self):
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        try:
            # الاتصال بدون تحديد قاعدة بيانات
            try:
                config = db_config.get_config()
            except:
                config = DATABASE_CONFIG.copy()
            db_name = config.pop('database')  # إزالة اسم قاعدة البيانات للاحتفاظ به

            temp_connection = mysql.connector.connect(**config)
            temp_cursor = temp_connection.cursor()

            # إنشاء قاعدة البيانات
            temp_cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            temp_cursor.close()
            temp_connection.close()

            # الاتصال بقاعدة البيانات الجديدة
            config['database'] = db_name
            self.connection = mysql.connector.connect(**config)
            self.cursor = self.connection.cursor(dictionary=True)

            # تنفيذ سكريبت إنشاء الجداول
            self._execute_schema()

            logging.info("تم إنشاء قاعدة البيانات والجداول بنجاح")
            return True

        except Error as e:
            logging.error(f"خطأ في إنشاء قاعدة البيانات: {e}")
            return False

    def _execute_schema(self):
        """تنفيذ سكريبت إنشاء الجداول"""
        try:
            schema_path = os.path.join(os.path.dirname(__file__), 'schema.sql')
            with open(schema_path, 'r', encoding='utf-8') as file:
                schema_sql = file.read()

            # تقسيم الاستعلامات وتنفيذها
            statements = schema_sql.split(';')
            for statement in statements:
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    self.cursor.execute(statement)

            self.connection.commit()
            logging.info("تم تنفيذ سكريبت إنشاء الجداول بنجاح")

        except Exception as e:
            logging.error(f"خطأ في تنفيذ سكريبت الجداول: {e}")
            raise

    def execute_query(self, query, params=None):
        """تنفيذ استعلام قراءة"""
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            return self.cursor.fetchall()
        except Error as e:
            logging.error(f"خطأ في تنفيذ الاستعلام: {e}")
            return None

    def execute_update(self, query, params=None):
        """تنفيذ استعلام تحديث/إدراج/حذف"""
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            self.connection.commit()
            return self.cursor.rowcount
        except Error as e:
            logging.error(f"خطأ في تنفيذ التحديث: {e}")
            self.connection.rollback()
            return -1

    def execute_insert(self, query, params=None):
        """تنفيذ استعلام إدراج وإرجاع ID الجديد"""
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            self.connection.commit()
            return self.cursor.lastrowid
        except Error as e:
            logging.error(f"خطأ في تنفيذ الإدراج: {e}")
            self.connection.rollback()
            return -1

    def execute_many(self, query, params_list):
        """تنفيذ استعلامات متعددة"""
        try:
            self.cursor.executemany(query, params_list)
            self.connection.commit()
            return self.cursor.rowcount
        except Error as e:
            logging.error(f"خطأ في تنفيذ الاستعلامات المتعددة: {e}")
            self.connection.rollback()
            return -1

    def begin_transaction(self):
        """بدء معاملة"""
        try:
            self.connection.start_transaction()
        except Error as e:
            logging.error(f"خطأ في بدء المعاملة: {e}")

    def commit_transaction(self):
        """تأكيد المعاملة"""
        try:
            self.connection.commit()
        except Error as e:
            logging.error(f"خطأ في تأكيد المعاملة: {e}")

    def rollback_transaction(self):
        """إلغاء المعاملة"""
        try:
            self.connection.rollback()
        except Error as e:
            logging.error(f"خطأ في إلغاء المعاملة: {e}")

    def close(self):
        """إغلاق الاتصال"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection and self.connection.is_connected():
                self.connection.close()
                logging.info("تم إغلاق الاتصال بقاعدة البيانات")
        except Error as e:
            logging.error(f"خطأ في إغلاق الاتصال: {e}")

    def is_connected(self):
        """فحص حالة الاتصال"""
        return self.connection and self.connection.is_connected()

    def test_connection(self):
        """اختبار الاتصال"""
        try:
            if self.is_connected():
                self.cursor.execute("SELECT 1")
                return True
            return False
        except Error:
            return False

    def reconnect(self):
        """إعادة الاتصال بقاعدة البيانات مع الإعدادات الجديدة"""
        try:
            # إغلاق الاتصال الحالي
            self.close()

            # إعادة الاتصال
            return self.connect()
        except Exception as e:
            logging.error(f"خطأ في إعادة الاتصال: {e}")
            return False

# إنشاء مثيل عام لقاعدة البيانات
db = DatabaseConnection()

# ملاحظة: تم إزالة الاتصال التلقائي لتجنب التعارضات
# سيتم الاتصال عند الحاجة من خلال استدعاء db.connect() صراحة
