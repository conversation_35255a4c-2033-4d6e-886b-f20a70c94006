# تحويل برنامج إدارة الأموال إلى حزمة تثبيت

هذا الملف يشرح كيفية تحويل برنامج إدارة الأموال إلى ملف تنفيذي (.exe) وإنشاء حزمة تثبيت قابلة للتوزيع على أجهزة أخرى.

## الملفات المهمة

1. **build_installer.bat**: ملف دفعي لإنشاء الملف التنفيذي وحزمة التثبيت تلقائياً
2. **Money Manager.spec**: ملف إعدادات PyInstaller لإنشاء الملف التنفيذي
3. **installer.iss**: ملف إعدادات Inno Setup لإنشاء حزمة التثبيت
4. **BUILD_INSTRUCTIONS.md**: تعليمات مفصلة حول عملية البناء
5. **INSTALLATION_GUIDE.md**: دليل التثبيت للمستخدم النهائي

## الخطوات السريعة

### 1. إنشاء الملف التنفيذي وحزمة التثبيت

ببساطة قم بتشغيل ملف `build_installer.bat` بالنقر المزدوج عليه. سيقوم هذا الملف بما يلي:

- التحقق من وجود PyInstaller وتثبيته إذا لم يكن موجوداً
- إنشاء الملف التنفيذي باستخدام PyInstaller
- التحقق من وجود Inno Setup
- إنشاء حزمة التثبيت باستخدام Inno Setup

### 2. الملفات الناتجة

بعد اكتمال العملية، ستجد:

- **الملف التنفيذي**: في مجلد `dist\Money Manager\Money Manager.exe`
- **حزمة التثبيت**: في مجلد `installer_output\MoneyManagerSetup.exe`

## المتطلبات

### PyInstaller

لتثبيت PyInstaller، قم بتنفيذ الأمر التالي:

```
pip install pyinstaller
```

### Inno Setup

1. قم بتنزيل Inno Setup من الموقع الرسمي: https://jrsoftware.org/isdl.php
2. قم بتثبيته باتباع تعليمات المثبت

## تخصيص الإعدادات

### تخصيص الملف التنفيذي

يمكنك تعديل ملف `Money Manager.spec` لتخصيص إعدادات الملف التنفيذي، مثل:

- إضافة ملفات أو مجلدات إضافية
- تغيير أيقونة البرنامج
- تعديل خيارات البناء

### تخصيص حزمة التثبيت

يمكنك تعديل ملف `installer.iss` لتخصيص إعدادات حزمة التثبيت، مثل:

- تغيير اسم البرنامج
- تعديل معلومات الناشر
- إضافة خطوات تثبيت مخصصة
- تغيير اللغات المدعومة

## ملاحظات هامة

1. تأكد من تثبيت جميع المكتبات المطلوبة قبل بناء الملف التنفيذي
2. قم باختبار الملف التنفيذي قبل إنشاء حزمة التثبيت
3. قم باختبار حزمة التثبيت على جهاز آخر للتأكد من عملها بشكل صحيح

## استكشاف الأخطاء وإصلاحها

### مشاكل PyInstaller

- إذا واجهت مشاكل في المكتبات المفقودة، أضفها إلى قائمة `hiddenimports` في ملف `.spec`
- تأكد من إضافة جميع الملفات والمجلدات المطلوبة إلى قائمة `datas` في ملف `.spec`

### مشاكل Inno Setup

- تأكد من أن المسار في `Source` يشير إلى المجلد الصحيح الذي يحتوي على الملف التنفيذي
- تحقق من أن `OutputDir` و `OutputBaseFilename` تم تعيينهما بشكل صحيح

## للمزيد من المعلومات

- [توثيق PyInstaller](https://pyinstaller.org/en/stable/)
- [توثيق Inno Setup](https://jrsoftware.org/ishelp/)