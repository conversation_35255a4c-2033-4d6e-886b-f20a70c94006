#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح نافذة تعديل المعاملات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_database():
    """إصلاح قاعدة البيانات"""
    print("🔧 إصلاح قاعدة البيانات لدعم التعديل الشامل...")
    print("=" * 60)
    
    try:
        from database.connection import db
        
        # التحقق من الاتصال
        print("🔌 التحقق من اتصال قاعدة البيانات...")
        if not db.is_connected():
            print("⚠️ قاعدة البيانات غير متصلة، محاولة الاتصال...")
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                print("تأكد من:")
                print("1. تشغيل XAMPP")
                print("2. تشغيل خدمة MySQL")
                print("3. وجود قاعدة البيانات money_manager")
                return False
        
        print("✅ الاتصال بقاعدة البيانات نشط")
        
        # 1. إضافة عمود category_name إذا لم يكن موجوداً
        print("\n📋 إضافة عمود category_name...")
        try:
            db.execute_update("""
                ALTER TABLE transactions 
                ADD COLUMN category_name VARCHAR(100) NULL 
                COMMENT 'اسم التصنيف المدخل يدوياً'
            """)
            print("✅ تم إضافة عمود category_name")
        except Exception as e:
            if "Duplicate column name" in str(e) or "already exists" in str(e):
                print("✅ عمود category_name موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إضافة عمود category_name: {e}")
        
        # 2. التحقق من هيكل الجدول
        print("\n📊 التحقق من هيكل جدول transactions...")
        try:
            columns = db.execute_query("DESCRIBE transactions")
            column_names = [col['Field'] for col in columns]
            
            required_columns = ['id', 'amount', 'account_id', 'currency_id', 'category_name', 'description', 'transaction_date']
            missing_columns = [col for col in required_columns if col not in column_names]
            
            if missing_columns:
                print(f"⚠️ أعمدة مفقودة: {', '.join(missing_columns)}")
            else:
                print("✅ جميع الأعمدة المطلوبة موجودة")
                
            print("   الأعمدة الموجودة:")
            for col in columns:
                status = "✅" if col['Field'] in required_columns else "📋"
                print(f"   {status} {col['Field']}: {col['Type']}")
                
        except Exception as e:
            print(f"❌ خطأ في فحص هيكل الجدول: {e}")
            return False
        
        # 3. اختبار استعلام البيانات
        print("\n🔍 اختبار استعلام البيانات...")
        try:
            test_query = """
                SELECT t.id, t.amount, t.currency_id, t.account_id, t.category_name, 
                       t.description, t.transaction_date, t.transaction_type,
                       a.name as account_name, c.symbol as currency_symbol, c.name as currency_name
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                LIMIT 1
            """
            result = db.execute_query(test_query)
            print("✅ استعلام البيانات يعمل بشكل صحيح")
            
            if result:
                print(f"   عدد النتائج: {len(result)}")
                sample = result[0]
                print(f"   مثال: {sample.get('amount', 'N/A')} {sample.get('currency_symbol', 'N/A')}")
            else:
                print("   لا توجد بيانات للاختبار")
                
        except Exception as e:
            print(f"❌ خطأ في استعلام البيانات: {e}")
            return False
        
        print("\n🎉 تم إصلاح قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في إصلاح قاعدة البيانات: {e}")
        return False

def test_transaction_model():
    """اختبار نموذج Transaction"""
    print("\n🧪 اختبار نموذج Transaction...")
    print("=" * 60)
    
    try:
        from database.models import Transaction
        
        # التحقق من وجود دالة update
        if hasattr(Transaction, 'update'):
            print("✅ دالة Transaction.update موجودة")
            
            # فحص توقيع الدالة
            import inspect
            try:
                sig = inspect.signature(Transaction.update)
                params = list(sig.parameters.keys())
                print(f"   المعاملات: {', '.join(params)}")
                
                required_params = ['transaction_id', 'amount', 'account_id', 'currency_id', 'category_name', 'description', 'transaction_date']
                missing_params = [p for p in required_params if p not in params]
                
                if missing_params:
                    print(f"⚠️ معاملات مفقودة: {', '.join(missing_params)}")
                else:
                    print("✅ جميع المعاملات المطلوبة موجودة")
            except Exception as e:
                print(f"⚠️ خطأ في فحص توقيع الدالة: {e}")
                
        else:
            print("❌ دالة Transaction.update غير موجودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النموذج: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح نافذة تعديل المعاملات")
    print("=" * 60)
    
    # 1. إصلاح قاعدة البيانات
    db_ok = fix_database()
    
    # 2. اختبار نموذج Transaction
    model_ok = test_transaction_model()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if db_ok and model_ok:
        print("🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("\nالآن يمكنك:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. الذهاب لقسم الواردات أو المصروفات")
        print("3. اضغط على زر 'تعديل' بجانب أي معاملة")
        print("4. ستظهر نافذة التعديل مع جميع الحقول:")
        print("   ✅ المبلغ")
        print("   ✅ الحساب")
        print("   ✅ العملة")
        print("   ✅ التصنيف")
        print("   ✅ التاريخ")
        print("   ✅ الوصف")
        
        print("\n💡 نصائح:")
        print("- إذا لم تظهر الحقول الجديدة، أعد تشغيل التطبيق")
        print("- تأكد من وجود بيانات في قسم الواردات أو المصروفات للاختبار")
        print("- جميع الحقول قابلة للتعديل والحفظ")
        
    else:
        print("❌ فشل في إصلاح بعض المشاكل!")
        print("\nيرجى:")
        print("1. التأكد من تشغيل XAMPP")
        print("2. التأكد من وجود قاعدة البيانات money_manager")
        print("3. إعادة تشغيل هذا الملف")
        
        if not db_ok:
            print("4. مراجعة إعدادات قاعدة البيانات")
        if not model_ok:
            print("5. مراجعة ملف database/models.py")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
