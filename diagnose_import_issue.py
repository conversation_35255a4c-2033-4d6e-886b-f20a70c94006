#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة استيراد المعاملات من ملفات Excel
"""

import pandas as pd
import os
from datetime import datetime, date
from database.connection import DatabaseConnection
from utils.auth import auth_manager

def diagnose_import_issue():
    """تشخيص مشكلة استيراد المعاملات"""
    
    print("🔍 تشخيص مشكلة استيراد المعاملات من Excel")
    print("="*60)
    
    # 1. فحص الملفات النموذجية
    test_files = [
        'sample_income_formatted.xlsx',
        'sample_expenses_formatted.xlsx'
    ]
    
    for filename in test_files:
        print(f"\n📊 فحص ملف: {filename}")
        
        if not os.path.exists(filename):
            print(f"❌ الملف غير موجود")
            continue
        
        try:
            # قراءة الملف
            df = pd.read_excel(filename, engine='openpyxl')
            print(f"✅ تم قراءة الملف - {len(df)} صف")
            
            # فحص الأعمدة
            required_columns = ['التاريخ', 'الوصف', 'المبلغ', 'العملة', 'الحساب']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"❌ أعمدة مفقودة: {missing_columns}")
            else:
                print("✅ جميع الأعمدة المطلوبة موجودة")
            
            # فحص البيانات
            print("📋 فحص البيانات:")
            for idx, row in df.iterrows():
                print(f"   الصف {idx+1}:")
                
                # فحص التاريخ
                date_val = row.get('التاريخ')
                print(f"      التاريخ: {date_val} (نوع: {type(date_val).__name__})")
                
                # فحص المبلغ
                amount_val = row.get('المبلغ')
                print(f"      المبلغ: {amount_val} (نوع: {type(amount_val).__name__})")
                
                # فحص العملة
                currency_val = row.get('العملة')
                print(f"      العملة: {currency_val}")
                
                # فحص الحساب
                account_val = row.get('الحساب')
                print(f"      الحساب: {account_val}")
                
                # فحص الوصف
                desc_val = row.get('الوصف')
                print(f"      الوصف: {desc_val}")
                
                if idx >= 2:  # عرض أول 3 صفوف فقط
                    print(f"   ... و {len(df) - 3} صف آخر")
                    break
                    
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {e}")

def check_database_accounts():
    """فحص الحسابات الموجودة في قاعدة البيانات"""
    
    print(f"\n🗄️ فحص الحسابات في قاعدة البيانات:")
    print("="*60)
    
    try:
        # محاولة الاتصال بقاعدة البيانات
        db = DatabaseConnection()
        
        # فحص المستخدم الحالي
        if not auth_manager.current_user:
            print("❌ لا يوجد مستخدم مسجل دخول")
            return
        
        user_id = auth_manager.current_user['id']
        print(f"👤 المستخدم الحالي: {auth_manager.current_user.get('username', 'غير محدد')} (ID: {user_id})")
        
        # الحصول على الحسابات
        accounts_query = "SELECT id, name, currency_id FROM accounts WHERE user_id = %s AND is_active = 1"
        accounts = db.execute_query(accounts_query, (user_id,))
        
        print(f"🏦 الحسابات الموجودة ({len(accounts)} حساب):")
        for account in accounts:
            print(f"   • {account['name']} (ID: {account['id']}, العملة: {account['currency_id']})")
        
        # فحص الحسابات المطلوبة في الملفات النموذجية
        required_accounts = [
            'البنك الأهلي',
            'حساب الدولار', 
            'بنك الإمارات',
            'البنك اليمني',
            'محفظة نقدية'
        ]
        
        existing_account_names = [acc['name'] for acc in accounts]
        
        print(f"\n🔍 فحص الحسابات المطلوبة:")
        for req_account in required_accounts:
            if req_account in existing_account_names:
                print(f"   ✅ {req_account} - موجود")
            else:
                print(f"   ❌ {req_account} - غير موجود")
        
        # فحص العملات
        currencies_query = "SELECT id, code, symbol FROM currencies WHERE is_active = 1"
        currencies = db.execute_query(currencies_query)
        
        print(f"\n💱 العملات المدعومة ({len(currencies)} عملة):")
        for currency in currencies:
            print(f"   • {currency['code']} ({currency['symbol']}) - ID: {currency['id']}")
        
        # فحص العملات المطلوبة
        required_currencies = ['SAR', 'USD', 'AED', 'YER']
        existing_currency_codes = [curr['code'] for curr in currencies]
        
        print(f"\n🔍 فحص العملات المطلوبة:")
        for req_currency in required_currencies:
            if req_currency in existing_currency_codes:
                print(f"   ✅ {req_currency} - مدعومة")
            else:
                print(f"   ❌ {req_currency} - غير مدعومة")
                
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")

def test_parse_date_function():
    """اختبار دالة parse_date مع البيانات الفعلية"""
    
    print(f"\n🧪 اختبار دالة parse_date مع البيانات الفعلية:")
    print("="*60)
    
    # محاكاة دالة parse_date المحسنة
    def parse_date_test(date_value):
        """نسخة اختبار من دالة parse_date المحسنة"""
        try:
            if pd.isna(date_value):
                return None

            # إذا كانت القيمة من نوع التاريخ والوقت، قم بتحويلها مباشرة
            if isinstance(date_value, datetime):
                return date_value.date()
            
            # إذا كانت القيمة من نوع pandas Timestamp
            if hasattr(date_value, 'date') and callable(getattr(date_value, 'date')):
                return date_value.date()
            
            # إذا كانت القيمة من نوع pandas datetime64
            if hasattr(date_value, 'to_pydatetime'):
                return date_value.to_pydatetime().date()
            
            # تحويل إلى نص ومحاولة التحليل
            date_str = str(date_value).strip()
            
            # إزالة الوقت إذا كان موجوداً
            if ' ' in date_str and ':' in date_str:
                date_str = date_str.split(' ')[0]

            # محاولة تحويل التاريخ بصيغ مختلفة
            date_formats = [
                '%Y-%m-%d',
                '%d/%m/%Y',
                '%m/%d/%Y',
                '%d-%m-%Y',
                '%Y/%m/%d'
            ]

            for date_format in date_formats:
                try:
                    parsed_date = datetime.strptime(date_str, date_format)
                    return parsed_date.date()
                except:
                    continue

            # محاولة أخيرة مع pandas to_datetime
            try:
                parsed_date = pd.to_datetime(date_value, errors='coerce')
                if not pd.isna(parsed_date):
                    return parsed_date.date()
            except:
                pass

            return None
        except Exception as e:
            print(f"خطأ في تحليل التاريخ {date_value}: {e}")
            return None
    
    # اختبار مع ملف فعلي
    test_file = 'sample_income_formatted.xlsx'
    if os.path.exists(test_file):
        try:
            df = pd.read_excel(test_file, engine='openpyxl')
            print(f"📊 اختبار مع ملف: {test_file}")
            
            for idx, row in df.head(3).iterrows():
                date_val = row.get('التاريخ')
                print(f"   الصف {idx+1}: {date_val} (نوع: {type(date_val).__name__})")
                
                # اختبار التحليل
                parsed_date = parse_date_test(date_val)
                if parsed_date:
                    print(f"      ✅ تم تحليله إلى: {parsed_date}")
                else:
                    print(f"      ❌ فشل في التحليل")
                    
        except Exception as e:
            print(f"❌ خطأ في اختبار الملف: {e}")
    else:
        print(f"❌ ملف الاختبار {test_file} غير موجود")

def create_missing_accounts():
    """إنشاء الحسابات المفقودة إذا لزم الأمر"""
    
    print(f"\n🔧 إنشاء الحسابات المفقودة:")
    print("="*60)
    
    try:
        db = DatabaseConnection()
        
        if not auth_manager.current_user:
            print("❌ لا يوجد مستخدم مسجل دخول")
            return
        
        user_id = auth_manager.current_user['id']
        
        # الحصول على الحسابات الموجودة
        accounts_query = "SELECT name FROM accounts WHERE user_id = %s AND is_active = 1"
        existing_accounts = db.execute_query(accounts_query, (user_id,))
        existing_names = [acc['name'] for acc in existing_accounts]
        
        # الحسابات المطلوبة مع عملاتها
        required_accounts = [
            ('البنك الأهلي', 'SAR'),
            ('حساب الدولار', 'USD'),
            ('بنك الإمارات', 'AED'),
            ('البنك اليمني', 'YER'),
            ('محفظة نقدية', 'SAR')
        ]
        
        # الحصول على معرفات العملات
        currencies_query = "SELECT id, code FROM currencies WHERE is_active = 1"
        currencies = db.execute_query(currencies_query)
        currency_ids = {curr['code']: curr['id'] for curr in currencies}
        
        created_count = 0
        
        for account_name, currency_code in required_accounts:
            if account_name not in existing_names:
                if currency_code in currency_ids:
                    # إنشاء الحساب
                    insert_query = """
                    INSERT INTO accounts (user_id, name, currency_id, balance, is_active, created_at)
                    VALUES (%s, %s, %s, 0.00, 1, NOW())
                    """
                    
                    result = db.execute_query(
                        insert_query, 
                        (user_id, account_name, currency_ids[currency_code])
                    )
                    
                    if result:
                        print(f"   ✅ تم إنشاء حساب: {account_name} ({currency_code})")
                        created_count += 1
                    else:
                        print(f"   ❌ فشل في إنشاء حساب: {account_name}")
                else:
                    print(f"   ❌ عملة غير مدعومة: {currency_code} للحساب {account_name}")
            else:
                print(f"   ℹ️  الحساب موجود: {account_name}")
        
        if created_count > 0:
            print(f"\n✅ تم إنشاء {created_count} حساب جديد")
        else:
            print(f"\nℹ️  جميع الحسابات المطلوبة موجودة")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحسابات: {e}")

def main():
    """الدالة الرئيسية"""
    
    # تشغيل التشخيص
    diagnose_import_issue()
    check_database_accounts()
    test_parse_date_function()
    
    # اقتراح إنشاء الحسابات المفقودة
    print(f"\n" + "="*60)
    response = input("هل تريد إنشاء الحسابات المفقودة تلقائياً؟ (y/n): ")
    if response.lower() in ['y', 'yes', 'نعم']:
        create_missing_accounts()
    
    print(f"\n" + "="*60)
    print("✅ انتهى التشخيص")
    print("📝 ملاحظات:")
    print("   • تأكد من وجود جميع الحسابات المطلوبة")
    print("   • تأكد من دعم جميع العملات المطلوبة")
    print("   • تم إصلاح مشكلة تحويل التاريخ إلى نص")

if __name__ == "__main__":
    main()
