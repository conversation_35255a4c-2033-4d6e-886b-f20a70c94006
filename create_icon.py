#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء أيقونة لبرنامج إدارة الأموال
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_money_manager_icon():
    """إنشاء أيقونة لبرنامج إدارة الأموال"""
    
    # إنشاء مجلد assets إذا لم يكن موجوداً
    assets_dir = "assets"
    os.makedirs(assets_dir, exist_ok=True)
    
    # إنشاء صورة بحجم 256x256 (حجم كبير للجودة العالية)
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # ألوان البرنامج
    bg_color = (34, 139, 34)  # أخضر داكن
    coin_color = (255, 215, 0)  # ذهبي
    text_color = (255, 255, 255)  # أبيض
    shadow_color = (0, 0, 0, 100)  # ظل شفاف
    
    # رسم الخلفية الدائرية
    margin = 10
    circle_size = size - (margin * 2)
    
    # رسم ظل الدائرة
    draw.ellipse([margin + 3, margin + 3, margin + circle_size + 3, margin + circle_size + 3], 
                 fill=shadow_color)
    
    # رسم الدائرة الرئيسية
    draw.ellipse([margin, margin, margin + circle_size, margin + circle_size], 
                 fill=bg_color, outline=(0, 100, 0), width=3)
    
    # رسم عملة ذهبية في المنتصف
    coin_size = 80
    coin_x = (size - coin_size) // 2
    coin_y = (size - coin_size) // 2 - 20
    
    # رسم ظل العملة
    draw.ellipse([coin_x + 2, coin_y + 2, coin_x + coin_size + 2, coin_y + coin_size + 2], 
                 fill=(0, 0, 0, 80))
    
    # رسم العملة الذهبية
    draw.ellipse([coin_x, coin_y, coin_x + coin_size, coin_y + coin_size], 
                 fill=coin_color, outline=(218, 165, 32), width=3)
    
    # رسم رمز الدولار في العملة
    try:
        # محاولة استخدام خط عربي إذا كان متوفراً
        font_size = 40
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # استخدام الخط الافتراضي
        font = ImageFont.load_default()
    
    # رسم رمز الدولار
    dollar_text = "$"
    bbox = draw.textbbox((0, 0), dollar_text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    text_x = coin_x + (coin_size - text_width) // 2
    text_y = coin_y + (coin_size - text_height) // 2
    
    # رسم ظل النص
    draw.text((text_x + 1, text_y + 1), dollar_text, fill=(0, 0, 0, 150), font=font)
    # رسم النص الرئيسي
    draw.text((text_x, text_y), dollar_text, fill=(0, 0, 0), font=font)
    
    # رسم نص "Money" في الأسفل
    try:
        small_font = ImageFont.truetype("arial.ttf", 24)
    except:
        small_font = ImageFont.load_default()
    
    money_text = "Money"
    bbox = draw.textbbox((0, 0), money_text, font=small_font)
    text_width = bbox[2] - bbox[0]
    text_x = (size - text_width) // 2
    text_y = coin_y + coin_size + 15
    
    # رسم ظل النص
    draw.text((text_x + 1, text_y + 1), money_text, fill=(0, 0, 0, 150), font=small_font)
    # رسم النص الرئيسي
    draw.text((text_x, text_y), money_text, fill=text_color, font=small_font)
    
    # حفظ الأيقونة بأحجام مختلفة
    icon_path = os.path.join(assets_dir, "icon.ico")
    
    # إنشاء أحجام مختلفة للأيقونة
    sizes = [16, 32, 48, 64, 128, 256]
    images = []
    
    for icon_size in sizes:
        resized_img = img.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
        images.append(resized_img)
    
    # حفظ كملف ICO
    img.save(icon_path, format='ICO', sizes=[(s, s) for s in sizes])
    
    # حفظ أيضاً كملف PNG للاستخدام العام
    png_path = os.path.join(assets_dir, "icon.png")
    img.save(png_path, format='PNG')
    
    print("تم إنشاء الأيقونة بنجاح:")
    print(f"   ICO: {icon_path}")
    print(f"   PNG: {png_path}")
    
    return icon_path, png_path

if __name__ == "__main__":
    create_money_manager_icon()
