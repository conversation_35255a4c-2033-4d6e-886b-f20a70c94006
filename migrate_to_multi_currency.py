#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ترقية قاعدة البيانات لدعم الحسابات متعددة العملات
Migration script to upgrade database for multi-currency account support
"""

import sqlite3
import sys
from datetime import datetime

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        import shutil
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"money_manager_backup_{timestamp}.db"
        shutil.copy2("money_manager.db", f"backups/{backup_name}")
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
        return False

def migrate_database():
    """ترقية قاعدة البيانات لدعم متعدد العملات"""
    try:
        conn = sqlite3.connect('money_manager.db')
        cursor = conn.cursor()
        
        print("🔄 بدء ترقية قاعدة البيانات...")
        
        # 1. إنشاء جدول account_balances
        print("📊 إنشاء جدول account_balances...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_balances (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id INTEGER NOT NULL,
                currency_id INTEGER NOT NULL,
                balance REAL DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (currency_id) REFERENCES currencies(id),
                UNIQUE(account_id, currency_id)
            )
        """)
        
        # 2. فحص هيكل جدول accounts الحالي
        print("🔍 فحص هيكل جدول accounts...")
        cursor.execute("PRAGMA table_info(accounts)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        # 3. نقل البيانات الموجودة إذا كانت متوفرة
        print("📋 نقل البيانات الموجودة...")
        if 'currency_id' in column_names and 'current_balance' in column_names:
            # النظام القديم مع currency_id
            cursor.execute("""
                INSERT OR IGNORE INTO account_balances (account_id, currency_id, balance)
                SELECT id, currency_id, current_balance
                FROM accounts
                WHERE current_balance > 0
            """)
        else:
            # النظام الحالي بدون currency_id - استخدام SAR كعملة افتراضية
            cursor.execute("SELECT id FROM currencies WHERE code = 'SAR'")
            sar_currency_id = cursor.fetchone()
            if sar_currency_id:
                sar_currency_id = sar_currency_id[0]
                if 'current_balance' in column_names:
                    cursor.execute("""
                        INSERT OR IGNORE INTO account_balances (account_id, currency_id, balance)
                        SELECT id, ?, current_balance
                        FROM accounts
                        WHERE current_balance > 0
                    """, (sar_currency_id,))
                else:
                    # لا توجد أرصدة حالية - سيتم إضافتها لاحقاً
                    print("   لا توجد أرصدة حالية للنقل")
        
        # 4. تحديث جدول accounts إذا لزم الأمر
        print("🏦 تحديث جدول accounts...")
        if 'currency_id' in column_names:
            # إنشاء جدول accounts جديد بدون currency_id
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS accounts_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    account_type_id INTEGER NOT NULL,
                    description TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (account_type_id) REFERENCES account_types(id)
                )
            """)

            # نسخ البيانات إلى الجدول الجديد
            cursor.execute("""
                INSERT INTO accounts_new (id, user_id, name, account_type_id, description, is_active, created_at, updated_at)
                SELECT id, user_id, name, account_type_id, description, is_active, created_at, updated_at
                FROM accounts
            """)

            # حذف الجدول القديم وإعادة تسمية الجديد
            cursor.execute("DROP TABLE accounts")
            cursor.execute("ALTER TABLE accounts_new RENAME TO accounts")
        else:
            print("   جدول accounts لا يحتاج تحديث")
        
        # 6. تحديث العملات المدعومة لتشمل فقط العملات المطلوبة
        print("💰 تحديث العملات المدعومة...")
        
        # حذف العملات غير المطلوبة
        cursor.execute("DELETE FROM currencies WHERE code NOT IN ('SAR', 'YER', 'AED', 'USD')")
        
        # التأكد من وجود العملات المطلوبة
        required_currencies = [
            ('SAR', 'ريال سعودي', 'ر.س'),
            ('YER', 'ريال يمني', 'ر.ي'),
            ('AED', 'درهم إماراتي', 'د.إ'),
            ('USD', 'دولار أمريكي', '$')
        ]
        
        for code, name, symbol in required_currencies:
            cursor.execute("""
                INSERT OR REPLACE INTO currencies (code, name, symbol, is_active, exchange_rate)
                VALUES (?, ?, ?, 1, 1.0)
            """, (code, name, symbol))
        
        # 7. تحديث معرفات العملات في account_balances
        print("🔄 تحديث معرفات العملات...")
        for code, name, symbol in required_currencies:
            cursor.execute("SELECT id FROM currencies WHERE code = ?", (code,))
            currency_id = cursor.fetchone()[0]
            cursor.execute("""
                UPDATE account_balances 
                SET currency_id = ? 
                WHERE currency_id IN (
                    SELECT id FROM currencies WHERE code = ?
                )
            """, (currency_id, code))
        
        conn.commit()
        print("✅ تم ترقية قاعدة البيانات بنجاح!")
        
        # عرض إحصائيات
        cursor.execute("SELECT COUNT(*) FROM accounts")
        accounts_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM account_balances")
        balances_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM currencies WHERE is_active = 1")
        currencies_count = cursor.fetchone()[0]
        
        print(f"\n📊 إحصائيات بعد الترقية:")
        print(f"   - عدد الحسابات: {accounts_count}")
        print(f"   - عدد أرصدة العملات: {balances_count}")
        print(f"   - عدد العملات النشطة: {currencies_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ترقية قاعدة البيانات: {e}")
        conn.rollback()
        conn.close()
        return False

def verify_migration():
    """التحقق من نجاح الترقية"""
    try:
        conn = sqlite3.connect('money_manager.db')
        cursor = conn.cursor()
        
        print("\n🔍 التحقق من الترقية...")
        
        # التحقق من وجود جدول account_balances
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='account_balances'")
        if not cursor.fetchone():
            print("❌ جدول account_balances غير موجود")
            return False
        
        # التحقق من العملات
        cursor.execute("SELECT code FROM currencies WHERE is_active = 1")
        active_currencies = [row[0] for row in cursor.fetchall()]
        required_currencies = ['SAR', 'YER', 'AED', 'USD']
        
        if set(active_currencies) != set(required_currencies):
            print(f"❌ العملات النشطة غير صحيحة: {active_currencies}")
            return False
        
        print("✅ تم التحقق من الترقية بنجاح!")
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء ترقية قاعدة البيانات لدعم متعدد العملات")
    print("=" * 60)
    
    # إنشاء نسخة احتياطية
    if not backup_database():
        print("❌ فشل في إنشاء النسخة الاحتياطية. توقف العملية.")
        return False
    
    # ترقية قاعدة البيانات
    if not migrate_database():
        print("❌ فشل في ترقية قاعدة البيانات.")
        return False
    
    # التحقق من الترقية
    if not verify_migration():
        print("❌ فشل في التحقق من الترقية.")
        return False
    
    print("\n🎉 تم إكمال ترقية قاعدة البيانات بنجاح!")
    print("يمكنك الآن استخدام ميزة الحسابات متعددة العملات.")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
