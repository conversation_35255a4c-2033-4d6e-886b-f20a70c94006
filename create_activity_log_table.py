#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جدول سجل الأنشطة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import mysql.connector
from config.settings import DATABASE_CONFIG

def create_activity_log_table():
    """إنشاء جدول سجل الأنشطة"""
    try:
        print("🔄 الاتصال بقاعدة البيانات...")
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()
        
        print("📝 إنشاء جدول سجل الأنشطة...")
        
        # إنشاء جدول سجل الأنشطة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                action_type VARCHAR(50) NOT NULL,
                table_name VARCHAR(50) NOT NULL,
                record_id INT NULL,
                description TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        """)
        
        print("✅ تم إنشاء جدول سجل الأنشطة بنجاح")
        
        # حفظ التغييرات
        connection.commit()
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    print("🚀 إنشاء جدول سجل الأنشطة")
    print("=" * 30)
    
    if create_activity_log_table():
        print("\n🎉 تم إنشاء جدول سجل الأنشطة بنجاح!")
    else:
        print("\n❌ فشل في إنشاء جدول سجل الأنشطة")
