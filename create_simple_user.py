#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء مستخدم افتراضي بسيط
"""

import json
import os
import bcrypt
from datetime import datetime

def create_simple_user():
    """إنشاء مستخدم افتراضي في ملف JSON"""
    
    # بيانات المستخدم الافتراضي
    username = "admin"
    password = "123456"
    full_name = "المدير الافتراضي"
    email = "<EMAIL>"
    
    print("🔄 إنشاء مستخدم افتراضي...")
    
    try:
        # تشفير كلمة المرور
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        # بيانات المستخدم
        user_data = {
            "id": 1,
            "username": username,
            "password_hash": password_hash,
            "full_name": full_name,
            "email": email,
            "role": "admin",
            "is_active": True,
            "created_at": datetime.now().isoformat(),
            "last_login": None
        }
        
        # حفظ في ملف JSON
        users_file = "users.json"
        
        if os.path.exists(users_file):
            with open(users_file, 'r', encoding='utf-8') as f:
                users = json.load(f)
        else:
            users = []
        
        # التحقق من وجود المستخدم
        existing_user = next((u for u in users if u['username'] == username), None)
        
        if existing_user:
            print(f"✅ المستخدم '{username}' موجود بالفعل")
        else:
            users.append(user_data)
            
            with open(users_file, 'w', encoding='utf-8') as f:
                json.dump(users, f, ensure_ascii=False, indent=2)
            
            print(f"✅ تم إنشاء المستخدم '{username}' بنجاح")
        
        print("\n📋 بيانات تسجيل الدخول:")
        print(f"   اسم المستخدم: {username}")
        print(f"   كلمة المرور: {password}")
        print(f"   الاسم الكامل: {full_name}")
        print(f"   البريد الإلكتروني: {email}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")
        return False

def create_sample_accounts_json():
    """إنشاء حسابات تجريبية في ملف JSON"""
    
    print("\n🔄 إنشاء حسابات تجريبية...")
    
    accounts = [
        {
            "id": 1,
            "user_id": 1,
            "name": "الصندوق النقدي",
            "type": "نقدي",
            "currency": "ريال سعودي",
            "balance": 5000.0,
            "description": "النقد المتوفر في اليد"
        },
        {
            "id": 2,
            "user_id": 1,
            "name": "حساب الراجحي",
            "type": "بنكي",
            "currency": "ريال سعودي", 
            "balance": 25000.0,
            "description": "الحساب الجاري في بنك الراجحي"
        },
        {
            "id": 3,
            "user_id": 1,
            "name": "حساب التوفير",
            "type": "توفير",
            "currency": "ريال سعودي",
            "balance": 50000.0,
            "description": "حساب التوفير طويل المدى"
        }
    ]
    
    try:
        with open("accounts.json", 'w', encoding='utf-8') as f:
            json.dump(accounts, f, ensure_ascii=False, indent=2)
        
        print("✅ تم إنشاء الحسابات التجريبية")
        
        for account in accounts:
            print(f"   • {account['name']}: {account['balance']:,.2f} {account['currency']}")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحسابات: {e}")
        return False

def create_sample_transactions_json():
    """إنشاء معاملات تجريبية في ملف JSON"""
    
    print("\n🔄 إنشاء معاملات تجريبية...")
    
    transactions = [
        {
            "id": 1,
            "user_id": 1,
            "account_id": 2,
            "type": "دخل",
            "amount": 8000.0,
            "category": "راتب",
            "description": "راتب شهر ديسمبر",
            "date": "2025-06-25",
            "created_at": datetime.now().isoformat()
        },
        {
            "id": 2,
            "user_id": 1,
            "account_id": 1,
            "type": "مصروف",
            "amount": 300.0,
            "category": "طعام وشراب",
            "description": "تسوق من السوبر ماركت",
            "date": "2025-06-28",
            "created_at": datetime.now().isoformat()
        },
        {
            "id": 3,
            "user_id": 1,
            "account_id": 2,
            "type": "مصروف",
            "amount": 1200.0,
            "category": "سكن",
            "description": "إيجار الشقة",
            "date": "2025-06-26",
            "created_at": datetime.now().isoformat()
        },
        {
            "id": 4,
            "user_id": 1,
            "account_id": 1,
            "type": "دخل",
            "amount": 1500.0,
            "category": "أعمال",
            "description": "دخل من مشروع جانبي",
            "date": "2025-06-20",
            "created_at": datetime.now().isoformat()
        }
    ]
    
    try:
        with open("transactions.json", 'w', encoding='utf-8') as f:
            json.dump(transactions, f, ensure_ascii=False, indent=2)
        
        print("✅ تم إنشاء المعاملات التجريبية")
        
        for transaction in transactions:
            print(f"   • {transaction['type']}: {transaction['amount']:,.2f} - {transaction['description']}")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المعاملات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏦 إنشاء بيانات افتراضية لبرنامج إدارة الأموال")
    print("=" * 60)
    
    success = True
    
    # إنشاء المستخدم الافتراضي
    if not create_simple_user():
        success = False
    
    # إنشاء الحسابات التجريبية
    if not create_sample_accounts_json():
        success = False
    
    # إنشاء المعاملات التجريبية
    if not create_sample_transactions_json():
        success = False
    
    if success:
        print("\n🎉 تم إنشاء جميع البيانات الافتراضية بنجاح!")
        print("\n💡 يمكنك الآن تشغيل البرنامج وتسجيل الدخول باستخدام:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: 123456")
    else:
        print("\n❌ حدثت بعض الأخطاء أثناء إنشاء البيانات")

if __name__ == "__main__":
    main()
