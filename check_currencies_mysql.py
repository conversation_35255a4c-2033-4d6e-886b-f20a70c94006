#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from database.connection import db

def check_currencies():
    """فحص العملات الموجودة في قاعدة البيانات"""
    try:
        print("💰 فحص العملات الموجودة...")
        
        query = "SELECT * FROM currencies ORDER BY id"
        currencies = db.execute_query(query)
        
        print(f"📊 عدد العملات: {len(currencies)}")
        print("\nالعملات الموجودة:")
        for currency in currencies:
            print(f"   ID: {currency['id']} | Code: {currency['code']} | Name: {currency['name']} | Symbol: {currency['symbol']} | Active: {currency['is_active']}")
        
        # إضافة العملات المفقودة
        required_currencies = [
            ('SAR', 'ريال سعودي', 'ر.س'),
            ('YER', 'ريال يمني', 'ر.ي'),
            ('AED', 'درهم إماراتي', 'د.إ'),
            ('USD', 'دولار أمريكي', '$')
        ]
        
        print("\n🔧 إضافة العملات المطلوبة...")
        for code, name, symbol in required_currencies:
            query = """
                INSERT INTO currencies (code, name, symbol, is_active, exchange_rate)
                VALUES (%s, %s, %s, 1, 1.0)
                ON DUPLICATE KEY UPDATE 
                name = VALUES(name), 
                symbol = VALUES(symbol),
                is_active = 1
            """
            result = db.execute_update(query, (code, name, symbol))
            print(f"   - {code}: {'تم التحديث' if result >= 0 else 'فشل'}")
        
        # عرض العملات بعد التحديث
        print("\n💰 العملات بعد التحديث:")
        currencies = db.execute_query("SELECT * FROM currencies ORDER BY id")
        for currency in currencies:
            print(f"   ID: {currency['id']} | Code: {currency['code']} | Name: {currency['name']} | Symbol: {currency['symbol']}")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    check_currencies()
