"""
أدوات إدارة العملات والتحويل
"""

import requests
import json
from datetime import datetime, timedelta
from config.settings import SUPPORTED_CURRENCIES
from database.connection import db
import logging

class CurrencyManager:
    """مدير العملات وأسعار الصرف"""
    
    def __init__(self):
        self.exchange_rates = {}
        self.last_update = None
        self.base_currency = 'USD'  # العملة الأساسية للتحويل
        
    def get_supported_currencies(self):
        """الحصول على العملات المدعومة"""
        return SUPPORTED_CURRENCIES
        
    def get_currency_info(self, currency_code):
        """الحصول على معلومات العملة"""
        return SUPPORTED_CURRENCIES.get(currency_code, None)
        
    def format_amount(self, amount, currency_code):
        """تنسيق المبلغ مع رمز العملة"""
        currency_info = self.get_currency_info(currency_code)
        if currency_info:
            symbol = currency_info['symbol']
            return f"{amount:,.2f} {symbol}"
        return f"{amount:,.2f} {currency_code}"
        
    def get_exchange_rate(self, from_currency, to_currency):
        """الحصول على سعر الصرف بين عملتين"""
        if from_currency == to_currency:
            return 1.0
            
        # محاولة الحصول على السعر من قاعدة البيانات
        rate = self._get_rate_from_db(from_currency, to_currency)
        if rate:
            return rate
            
        # إذا لم يوجد، استخدام أسعار افتراضية
        return self._get_default_rate(from_currency, to_currency)
        
    def _get_rate_from_db(self, from_currency, to_currency):
        """الحصول على سعر الصرف من قاعدة البيانات"""
        try:
            query = """
                SELECT exchange_rate 
                FROM currencies 
                WHERE code = %s
            """
            result = db.execute_query(query, (to_currency,))
            if result:
                return float(result[0]['exchange_rate'])
        except Exception as e:
            logging.error(f"خطأ في الحصول على سعر الصرف من قاعدة البيانات: {e}")
        return None
        
    def _get_default_rate(self, from_currency, to_currency):
        """الحصول على أسعار صرف افتراضية"""
        # أسعار صرف تقريبية (يجب تحديثها دورياً)
        default_rates = {
            'USD': {
                'SAR': 3.75,
                'EUR': 0.85,
                'YER': 250.0,
                'AED': 3.67,
                'KWD': 0.30,
                'QAR': 3.64,
                'BHD': 0.38,
                'OMR': 0.38,
                'JOD': 0.71,
                'EGP': 30.0
            },
            'SAR': {
                'USD': 0.27,
                'EUR': 0.23,
                'YER': 66.67,
                'AED': 0.98,
                'KWD': 0.08,
                'QAR': 0.97,
                'BHD': 0.10,
                'OMR': 0.10,
                'JOD': 0.19,
                'EGP': 8.0
            }
        }
        
        # محاولة الحصول على السعر المباشر
        if from_currency in default_rates and to_currency in default_rates[from_currency]:
            return default_rates[from_currency][to_currency]
            
        # محاولة الحصول على السعر العكسي
        if to_currency in default_rates and from_currency in default_rates[to_currency]:
            return 1.0 / default_rates[to_currency][from_currency]
            
        # إذا لم يوجد، استخدام التحويل عبر الدولار
        if from_currency != 'USD' and to_currency != 'USD':
            usd_from_rate = self._get_default_rate(from_currency, 'USD')
            usd_to_rate = self._get_default_rate('USD', to_currency)
            if usd_from_rate and usd_to_rate:
                return usd_from_rate * usd_to_rate
                
        # افتراضي
        return 1.0
        
    def convert_amount(self, amount, from_currency, to_currency):
        """تحويل مبلغ من عملة إلى أخرى"""
        if from_currency == to_currency:
            return amount
            
        rate = self.get_exchange_rate(from_currency, to_currency)
        return amount * rate
        
    def update_exchange_rates_online(self):
        """تحديث أسعار الصرف من الإنترنت"""
        try:
            # يمكن استخدام API مجاني مثل exchangerate-api.com
            # هذا مثال تجريبي - يحتاج إلى API key حقيقي
            
            # API_KEY = "your_api_key_here"
            # url = f"https://v6.exchangerate-api.com/v6/{API_KEY}/latest/USD"
            
            # response = requests.get(url, timeout=10)
            # if response.status_code == 200:
            #     data = response.json()
            #     if data['result'] == 'success':
            #         self.exchange_rates = data['conversion_rates']
            #         self.last_update = datetime.now()
            #         self._save_rates_to_db()
            #         return True
            
            # للآن، نستخدم أسعار افتراضية
            logging.info("استخدام أسعار صرف افتراضية")
            return True
            
        except Exception as e:
            logging.error(f"خطأ في تحديث أسعار الصرف: {e}")
            return False
            
    def _save_rates_to_db(self):
        """حفظ أسعار الصرف في قاعدة البيانات"""
        try:
            for currency_code, rate in self.exchange_rates.items():
                if currency_code in SUPPORTED_CURRENCIES:
                    query = """
                        UPDATE currencies 
                        SET exchange_rate = %s 
                        WHERE code = %s
                    """
                    db.execute_update(query, (rate, currency_code))
                    
            logging.info("تم حفظ أسعار الصرف في قاعدة البيانات")
            
        except Exception as e:
            logging.error(f"خطأ في حفظ أسعار الصرف: {e}")
            
    def get_currency_statistics(self, user_id):
        """الحصول على إحصائيات العملات للمستخدم"""
        try:
            query = """
                SELECT 
                    c.code,
                    c.name,
                    c.symbol,
                    SUM(CASE WHEN a.current_balance > 0 THEN a.current_balance ELSE 0 END) as total_balance,
                    COUNT(a.id) as accounts_count,
                    SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END) as total_income,
                    SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END) as total_expense
                FROM currencies c
                LEFT JOIN accounts a ON c.id = a.currency_id AND a.user_id = %s AND a.is_active = TRUE
                LEFT JOIN transactions t ON c.id = t.currency_id AND t.user_id = %s
                WHERE c.is_active = TRUE
                GROUP BY c.id, c.code, c.name, c.symbol
                HAVING total_balance > 0 OR accounts_count > 0 OR total_income > 0 OR total_expense > 0
                ORDER BY total_balance DESC
            """
            
            return db.execute_query(query, (user_id, user_id))
            
        except Exception as e:
            logging.error(f"خطأ في الحصول على إحصائيات العملات: {e}")
            return []
            
    def convert_to_base_currency(self, amounts_by_currency, base_currency='SAR'):
        """تحويل مبالغ متعددة العملات إلى عملة أساسية"""
        total = 0.0
        conversions = {}
        
        for currency_code, amount in amounts_by_currency.items():
            if amount > 0:
                converted_amount = self.convert_amount(amount, currency_code, base_currency)
                conversions[currency_code] = {
                    'original_amount': amount,
                    'converted_amount': converted_amount,
                    'exchange_rate': self.get_exchange_rate(currency_code, base_currency)
                }
                total += converted_amount
                
        return {
            'total': total,
            'base_currency': base_currency,
            'conversions': conversions
        }
        
    def get_popular_currencies(self):
        """الحصول على العملات الأكثر استخداماً"""
        try:
            query = """
                SELECT 
                    c.code,
                    c.name,
                    c.symbol,
                    COUNT(DISTINCT a.id) as accounts_count,
                    COUNT(DISTINCT t.id) as transactions_count
                FROM currencies c
                LEFT JOIN accounts a ON c.id = a.currency_id AND a.is_active = TRUE
                LEFT JOIN transactions t ON c.id = t.currency_id
                WHERE c.is_active = TRUE
                GROUP BY c.id, c.code, c.name, c.symbol
                ORDER BY (accounts_count + transactions_count) DESC
                LIMIT 5
            """
            
            return db.execute_query(query)
            
        except Exception as e:
            logging.error(f"خطأ في الحصول على العملات الشائعة: {e}")
            return []
            
    def validate_currency_code(self, currency_code):
        """التحقق من صحة رمز العملة"""
        return currency_code in SUPPORTED_CURRENCIES
        
    def get_currency_by_country(self, country_code):
        """الحصول على العملة حسب رمز البلد"""
        country_currency_map = {
            'SA': 'SAR',  # السعودية
            'US': 'USD',  # أمريكا
            'EU': 'EUR',  # أوروبا
            'YE': 'YER',  # اليمن
            'AE': 'AED',  # الإمارات
            'KW': 'KWD',  # الكويت
            'QA': 'QAR',  # قطر
            'BH': 'BHD',  # البحرين
            'OM': 'OMR',  # عمان
            'JO': 'JOD',  # الأردن
            'EG': 'EGP',  # مصر
        }
        
        return country_currency_map.get(country_code.upper())

# إنشاء مثيل عام لمدير العملات
currency_manager = CurrencyManager()
