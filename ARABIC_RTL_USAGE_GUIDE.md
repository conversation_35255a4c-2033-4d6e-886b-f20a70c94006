# 📖 دليل استخدام دعم النصوص العربية RTL

## 🚀 البدء السريع

### 1. **تشغيل التطبيق مع الإصلاحات:**
```bash
python main.py
```

### 2. **اختبار الإصلاحات:**
```bash
python test_arabic_rtl.py
```

## 🛠️ للمطورين - كيفية استخدام الدوال الجديدة

### استيراد الدوال المطلوبة:
```python
from config.fonts import (
    create_rtl_label, 
    create_rtl_button, 
    create_rtl_entry,
    get_arabic_font
)
from config.colors import ARABIC_TEXT_STYLES
```

### 1. **إنشاء تسميات مع دعم RTL:**
```python
# الطريقة الجديدة (مُحسنة)
label = create_rtl_label(
    parent,
    text="النص العربي",
    font_size='body',  # title, subtitle, header, body, small
    text_color=COLORS['text_primary']
)

# بدلاً من الطريقة القديمة
label = ctk.CTkLabel(
    parent,
    text="النص العربي",
    font=ctk.CTkFont(size=14),
    anchor="w"  # محاذاة خاطئة للعربية
)
```

### 2. **إنشاء أزرار مع دعم RTL:**
```python
# الطريقة الجديدة
button = create_rtl_button(
    parent,
    text="زر عربي",
    command=my_function,
    **BUTTON_STYLES['primary']
)

# بدلاً من الطريقة القديمة
button = ctk.CTkButton(
    parent,
    text="زر عربي",
    font=ctk.CTkFont(size=14),
    command=my_function
)
```

### 3. **إنشاء حقول إدخال مع دعم RTL:**
```python
# الطريقة الجديدة
entry = create_rtl_entry(
    parent,
    placeholder_text="أدخل النص هنا...",
    **INPUT_STYLES['rtl']
)

# بدلاً من الطريقة القديمة
entry = ctk.CTkEntry(
    parent,
    placeholder_text="أدخل النص هنا...",
    justify='left'  # محاذاة خاطئة للعربية
)
```

## 🎨 أنماط المحاذاة المتاحة

### للتسميات:
```python
# محاذاة لليمين (افتراضي للعربية)
**ARABIC_TEXT_STYLES['label']

# محاذاة للوسط (للعناوين)
**ARABIC_TEXT_STYLES['title']

# محاذاة مخصصة
label = create_rtl_label(
    parent,
    text="نص",
    anchor='center',  # center, e, w
    justify='center'  # center, right, left
)
```

### للأزرار:
```python
# محاذاة للوسط (افتراضي)
**ARABIC_TEXT_STYLES['button']

# أو استخدام أنماط الأزرار
**BUTTON_STYLES['primary']    # زر أساسي
**BUTTON_STYLES['secondary']  # زر ثانوي
**BUTTON_STYLES['success']    # زر نجاح
**BUTTON_STYLES['danger']     # زر خطر
```

## 🔤 أحجام الخطوط المتاحة

```python
font_sizes = {
    'title': 28,      # العناوين الرئيسية
    'subtitle': 20,   # العناوين الفرعية  
    'header': 16,     # رؤوس الأقسام
    'body': 14,       # النص العادي (افتراضي)
    'small': 12,      # النص الصغير
    'button': 14,     # نص الأزرار
    'input': 14,      # نص الحقول
}

# الاستخدام
label = create_rtl_label(parent, text="عنوان", font_size='title')
```

## 🎯 أمثلة عملية

### مثال 1: نموذج تسجيل دخول
```python
def create_login_form(parent):
    # العنوان
    title = create_rtl_label(
        parent,
        text="تسجيل الدخول",
        font_size='title',
        **ARABIC_TEXT_STYLES['title']
    )
    
    # تسمية اسم المستخدم
    username_label = create_rtl_label(
        parent,
        text="اسم المستخدم:",
        font_size='header',
        **ARABIC_TEXT_STYLES['label']
    )
    
    # حقل اسم المستخدم
    username_entry = create_rtl_entry(
        parent,
        placeholder_text="أدخل اسم المستخدم",
        **INPUT_STYLES['rtl']
    )
    
    # زر تسجيل الدخول
    login_button = create_rtl_button(
        parent,
        text="دخول",
        command=login_function,
        **BUTTON_STYLES['primary']
    )
```

### مثال 2: قائمة جانبية
```python
def create_sidebar_menu(parent):
    # عنوان القائمة
    menu_title = create_rtl_label(
        parent,
        text="القائمة الرئيسية",
        font_size='subtitle',
        **ARABIC_TEXT_STYLES['title']
    )
    
    # عناصر القائمة
    menu_items = [
        ("🏠 الرئيسية", show_home),
        ("💰 الواردات", show_income),
        ("💸 المصروفات", show_expenses)
    ]
    
    for text, command in menu_items:
        button = create_rtl_button(
            parent,
            text=text,
            command=command,
            anchor="e",  # محاذاة لليمين
            **BUTTON_STYLES['secondary']
        )
```

## 🔧 تخصيص الإعدادات

### تغيير الخط الافتراضي:
```python
# في config/settings.py
ARABIC_FONT_CONFIG['font_families'] = [
    'خط مخصص',
    'Segoe UI',
    'Tahoma'
]
```

### تخصيص أنماط المحاذاة:
```python
# في config/colors.py
ARABIC_TEXT_STYLES['custom'] = {
    'anchor': 'center',
    'justify': 'center'
}

# الاستخدام
label = create_rtl_label(
    parent,
    text="نص مخصص",
    **ARABIC_TEXT_STYLES['custom']
)
```

## 🐛 حل المشاكل الشائعة

### 1. **النص لا يظهر بـ RTL:**
```python
# تأكد من استخدام الدوال الجديدة
❌ ctk.CTkLabel(parent, text="عربي")
✅ create_rtl_label(parent, text="عربي")
```

### 2. **الخط غير واضح:**
```python
# جرب خط مختلف
label = create_rtl_label(
    parent,
    text="نص",
    font_size='header'  # خط أكبر
)
```

### 3. **المحاذاة خاطئة:**
```python
# استخدم الأنماط المناسبة
label = create_rtl_label(
    parent,
    text="نص",
    **ARABIC_TEXT_STYLES['label']  # محاذاة RTL صحيحة
)
```

## 📋 قائمة مرجعية للتحديث

عند تحديث ملف GUI موجود:

- [ ] استيراد الدوال الجديدة
- [ ] استبدال `ctk.CTkLabel` بـ `create_rtl_label`
- [ ] استبدال `ctk.CTkButton` بـ `create_rtl_button`  
- [ ] استبدال `ctk.CTkEntry` بـ `create_rtl_entry`
- [ ] إضافة أنماط RTL المناسبة
- [ ] اختبار النتيجة

## 🎉 نصائح للحصول على أفضل النتائج

1. **استخدم الدوال الجديدة دائماً** للنصوص العربية
2. **اختبر على أنظمة تشغيل مختلفة** للتأكد من الخطوط
3. **استخدم أحجام خطوط مناسبة** للقراءة
4. **تأكد من المحاذاة الصحيحة** لكل نوع عنصر
5. **اختبر النصوص المختلطة** (عربي + إنجليزي + أرقام)
