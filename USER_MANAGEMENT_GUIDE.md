# دليل إدارة المستخدمين

## نظرة عامة

تم إضافة ميزة إدارة المستخدمين الشاملة إلى تطبيق مدير الأموال، والتي تتيح للمديرين إدارة حسابات المستخدمين بشكل كامل مع دعم RTL والأمان المتقدم.

## الميزات الجديدة

### 1. **إدارة المستخدمين المتعددين**
- إنشاء حسابات مستخدمين جديدة (مدير أو مستخدم عادي)
- تعديل بيانات المستخدمين الموجودين
- تفعيل وتعطيل الحسابات
- إعادة تعيين كلمات المرور
- عرض إحصائيات المستخدمين

### 2. **نظام الصلاحيات**
- **المدير**: صلاحيات كاملة لإدارة النظام والمستخدمين
- **المستخدم العادي**: صلاحيات محدودة للبيانات الشخصية

### 3. **الأمان المتقدم**
- تشفير كلمات المرور باستخدام bcrypt
- التحقق من قوة كلمات المرور
- منع المستخدمين من حذف أنفسهم
- تسجيل جميع العمليات في سجل الأنشطة
- مربعات حوار تأكيد للعمليات الحساسة

## بيانات الاعتماد الجديدة

تم إنشاء حساب مدير جديد:
- **اسم المستخدم**: `admin2`
- **كلمة المرور**: `123456`
- **الدور**: مدير

## كيفية الوصول لإدارة المستخدمين

1. سجل الدخول باستخدام حساب المدير
2. انقر على "👥 إدارة المستخدمين" في الشريط الجانبي
3. ستظهر لك صفحة إدارة المستخدمين مع قائمة المستخدمين الحاليين

## العمليات المتاحة

### إضافة مستخدم جديد
1. انقر على "إضافة مستخدم جديد"
2. املأ البيانات المطلوبة:
   - اسم المستخدم (3 أحرف على الأقل)
   - كلمة المرور (6 أحرف على الأقل)
   - تأكيد كلمة المرور
   - الاسم الكامل
   - الدور (مدير أو مستخدم)
3. انقر على "حفظ المستخدم"

### تعديل بيانات المستخدم
1. انقر على "تعديل" بجانب المستخدم المطلوب
2. عدل البيانات المطلوبة:
   - الاسم الكامل
   - الدور
   - حالة المستخدم (نشط/معطل)
3. انقر على "حفظ التغييرات"

### إعادة تعيين كلمة المرور
1. انقر على "إعادة تعيين" بجانب المستخدم المطلوب
2. أدخل كلمة المرور الجديدة وتأكيدها
3. انقر على "إعادة تعيين كلمة المرور"

### تفعيل/تعطيل المستخدم
1. انقر على "تعطيل" لتعطيل المستخدم أو "تفعيل" لتفعيله
2. أكد العملية في مربع الحوار

## الملفات المضافة/المحدثة

### ملفات جديدة:
- `gui/user_management_windows.py` - نوافذ إدارة المستخدمين
- `update_user_management.py` - سكريبت تحديث قاعدة البيانات
- `test_user_management.py` - اختبارات ميزة إدارة المستخدمين
- `create_activity_log_table.py` - إنشاء جدول سجل الأنشطة
- `USER_MANAGEMENT_GUIDE.md` - هذا الدليل

### ملفات محدثة:
- `database/models.py` - إضافة وظائف CRUD للمستخدمين
- `utils/auth.py` - تحديث نظام المصادقة لدعم المستخدمين المتعددين
- `gui/main_window.py` - إضافة واجهة إدارة المستخدمين
- `database/schema.sql` - تحديث بنية جدول المستخدمين

## قاعدة البيانات

### جداول جديدة:
- `activity_log` - سجل أنشطة المستخدمين

### تحديثات الجداول:
- `users` - إضافة عمود `created_by` لتتبع منشئ المستخدم

## الاختبارات

تم إنشاء مجموعة شاملة من الاختبارات تغطي:
- الاتصال بقاعدة البيانات
- تسجيل الدخول
- عمليات CRUD للمستخدمين
- ميزات المصادقة
- الإجراءات الأمنية

لتشغيل الاختبارات:
```bash
python test_user_management.py
```

## الأمان

### إجراءات الأمان المطبقة:
1. **تشفير كلمات المرور**: استخدام bcrypt مع salt
2. **التحقق من الصلاحيات**: فحص الصلاحيات قبل كل عملية
3. **منع العمليات الخطيرة**: منع المستخدم من حذف نفسه
4. **تسجيل الأنشطة**: تسجيل جميع العمليات الحساسة
5. **التحقق من البيانات**: فحص صحة البيانات المدخلة
6. **مربعات التأكيد**: تأكيد العمليات الحساسة

### قواعد كلمات المرور:
- الحد الأدنى: 6 أحرف
- يجب تأكيد كلمة المرور عند الإنشاء أو التغيير

### قواعد أسماء المستخدمين:
- الحد الأدنى: 3 أحرف
- أحرف وأرقام فقط (a-z, A-Z, 0-9, _)
- يجب أن تكون فريدة

## الدعم الفني

في حالة وجود مشاكل:
1. تأكد من تشغيل MySQL Server
2. تحقق من صحة بيانات الاتصال في `config/settings.py`
3. شغل `python test_user_management.py` للتحقق من سلامة النظام
4. راجع ملفات السجلات للأخطاء

## التطوير المستقبلي

يمكن إضافة الميزات التالية مستقبلاً:
- نظام صلاحيات أكثر تفصيلاً
- تصدير واستيراد المستخدمين
- إعدادات أمان متقدمة
- تقارير أنشطة المستخدمين
- نظام إشعارات للمديرين
