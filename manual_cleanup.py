#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيف يدوي للبيانات المتضاربة
"""

import os
import shutil
import json
from datetime import datetime

def create_final_backup():
    """إنشاء نسخة احتياطية نهائية"""
    print("💾 إنشاء نسخة احتياطية نهائية...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"final_cleanup_backup_{timestamp}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        # قائمة الملفات للنسخ الاحتياطي
        files_to_backup = [
            "money_manager.db",
            "accounts.json",
            "users.json",
            "transactions.json"
        ]
        
        backed_up_files = []
        
        for file_path in files_to_backup:
            if os.path.exists(file_path):
                shutil.copy2(file_path, backup_dir)
                size = os.path.getsize(file_path)
                print(f"✅ تم نسخ {file_path} ({size} bytes)")
                backed_up_files.append(file_path)
            else:
                print(f"⚠️ {file_path} غير موجود")
        
        # نسخ مجلد النسخ الاحتياطية
        if os.path.exists("backups"):
            backup_backups_dir = os.path.join(backup_dir, "old_backups")
            shutil.copytree("backups", backup_backups_dir)
            print("✅ تم نسخ النسخ الاحتياطية القديمة")
        
        print(f"✅ تم إنشاء النسخة الاحتياطية النهائية: {backup_dir}")
        return backup_dir, backed_up_files
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None, []

def remove_sqlite_files():
    """حذف ملفات SQLite"""
    print("\n🗄️ حذف ملفات SQLite...")
    
    sqlite_files = []
    
    # البحث عن ملفات SQLite
    for file in os.listdir('.'):
        if file.endswith('.db'):
            sqlite_files.append(file)
    
    if not sqlite_files:
        print("✅ لا توجد ملفات SQLite للحذف")
        return []
    
    removed_files = []
    
    for sqlite_file in sqlite_files:
        try:
            size = os.path.getsize(sqlite_file)
            os.remove(sqlite_file)
            print(f"🗑️ تم حذف {sqlite_file} ({size} bytes)")
            removed_files.append(sqlite_file)
        except Exception as e:
            print(f"❌ خطأ في حذف {sqlite_file}: {e}")
    
    return removed_files

def remove_json_files():
    """حذف ملفات JSON"""
    print("\n📄 حذف ملفات JSON...")
    
    json_files = ["accounts.json", "users.json", "transactions.json"]
    removed_files = []
    
    for json_file in json_files:
        if os.path.exists(json_file):
            try:
                size = os.path.getsize(json_file)
                os.remove(json_file)
                print(f"🗑️ تم حذف {json_file} ({size} bytes)")
                removed_files.append(json_file)
            except Exception as e:
                print(f"❌ خطأ في حذف {json_file}: {e}")
        else:
            print(f"⚠️ {json_file} غير موجود")
    
    return removed_files

def cleanup_old_backups():
    """تنظيف النسخ الاحتياطية القديمة"""
    print("\n💾 تنظيف النسخ الاحتياطية القديمة...")
    
    if not os.path.exists("backups"):
        print("✅ مجلد النسخ الاحتياطية غير موجود")
        return []
    
    try:
        backup_files = os.listdir("backups")
        backup_files.sort(reverse=True)  # ترتيب تنازلي (الأحدث أولاً)
        
        print(f"📦 عدد النسخ الاحتياطية: {len(backup_files)}")
        
        # الاحتفاظ بآخر 3 نسخ فقط
        files_to_keep = 3
        removed_files = []
        
        if len(backup_files) > files_to_keep:
            files_to_remove = backup_files[files_to_keep:]
            
            for backup_file in files_to_remove:
                try:
                    backup_path = os.path.join("backups", backup_file)
                    size = os.path.getsize(backup_path)
                    os.remove(backup_path)
                    print(f"🗑️ تم حذف النسخة الاحتياطية القديمة: {backup_file} ({size} bytes)")
                    removed_files.append(backup_file)
                except Exception as e:
                    print(f"❌ خطأ في حذف {backup_file}: {e}")
        else:
            print(f"✅ عدد النسخ الاحتياطية مناسب ({len(backup_files)} نسخ)")
        
        return removed_files
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف النسخ الاحتياطية: {e}")
        return []

def remove_temp_files():
    """حذف الملفات المؤقتة"""
    print("\n🧹 حذف الملفات المؤقتة...")
    
    temp_extensions = ['.tmp', '.temp', '.bak', '.old']
    temp_prefixes = ['temp_', 'tmp_', 'backup_', 'test_']
    
    removed_files = []
    
    for file in os.listdir('.'):
        should_remove = False
        
        # فحص الامتدادات
        for ext in temp_extensions:
            if file.endswith(ext):
                should_remove = True
                break
        
        # فحص البادئات
        if not should_remove:
            for prefix in temp_prefixes:
                if file.startswith(prefix):
                    should_remove = True
                    break
        
        if should_remove:
            try:
                size = os.path.getsize(file)
                os.remove(file)
                print(f"🗑️ تم حذف الملف المؤقت: {file} ({size} bytes)")
                removed_files.append(file)
            except Exception as e:
                print(f"❌ خطأ في حذف {file}: {e}")
    
    if not removed_files:
        print("✅ لا توجد ملفات مؤقتة للحذف")
    
    return removed_files

def generate_cleanup_report(backup_dir, removed_files):
    """إنشاء تقرير التنظيف"""
    print("\n📋 إنشاء تقرير التنظيف...")
    
    report = {
        'cleanup_timestamp': datetime.now().isoformat(),
        'backup_directory': backup_dir,
        'removed_files': removed_files,
        'cleanup_summary': {
            'sqlite_files_removed': len(removed_files.get('sqlite', [])),
            'json_files_removed': len(removed_files.get('json', [])),
            'old_backups_removed': len(removed_files.get('backups', [])),
            'temp_files_removed': len(removed_files.get('temp', []))
        },
        'status': 'completed',
        'database_unified': True,
        'mysql_only': True
    }
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"database_unification_report_{timestamp}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم إنشاء تقرير التنظيف: {report_file}")
        return report_file
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("🧹 بدء التنظيف اليدوي للبيانات المتضاربة")
    print("=" * 60)
    
    # 1. إنشاء نسخة احتياطية نهائية
    backup_dir, backed_up_files = create_final_backup()
    if not backup_dir:
        print("❌ فشل في إنشاء النسخة الاحتياطية - توقف العملية")
        return
    
    # 2. حذف ملفات SQLite
    removed_sqlite = remove_sqlite_files()
    
    # 3. حذف ملفات JSON
    removed_json = remove_json_files()
    
    # 4. تنظيف النسخ الاحتياطية القديمة
    removed_backups = cleanup_old_backups()
    
    # 5. حذف الملفات المؤقتة
    removed_temp = remove_temp_files()
    
    # 6. تجميع الملفات المحذوفة
    all_removed_files = {
        'sqlite': removed_sqlite,
        'json': removed_json,
        'backups': removed_backups,
        'temp': removed_temp
    }
    
    # 7. إنشاء تقرير التنظيف
    report_file = generate_cleanup_report(backup_dir, all_removed_files)
    
    # 8. ملخص النتائج
    print("\n🎉 تم تنظيف البيانات المتضاربة بنجاح!")
    print("=" * 50)
    print(f"💾 النسخة الاحتياطية: {backup_dir}")
    print(f"🗑️ ملفات SQLite محذوفة: {len(removed_sqlite)}")
    print(f"🗑️ ملفات JSON محذوفة: {len(removed_json)}")
    print(f"🗑️ نسخ احتياطية قديمة محذوفة: {len(removed_backups)}")
    print(f"🗑️ ملفات مؤقتة محذوفة: {len(removed_temp)}")
    
    if report_file:
        print(f"📋 تقرير التنظيف: {report_file}")
    
    print("\n✅ قاعدة البيانات موحدة الآن - MySQL فقط!")

if __name__ == "__main__":
    main()
