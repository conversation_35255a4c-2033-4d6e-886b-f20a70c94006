import sys
import os
from decimal import Decimal
from datetime import datetime

# إضافة مسار المشروع للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from database.models import Account, Currency, Transfer, User
from utils.auth import auth_manager

def debug_transfer_issue():
    """تشخيص مشكلة عرض التحويلات"""
    
    print("🔍 بدء تشخيص مشكلة التحويلات...")
    
    try:
        # الاتصال بقاعدة البيانات
        print("📡 الاتصال بقاعدة البيانات...")
        db.connect()
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # محاولة تسجيل دخول المستخدم admin
        print("🔐 محاولة تسجيل دخول المستخدم admin...")
        success, message = auth_manager.login("admin", "admin123")

        if not success:
            print(f"❌ فشل في تسجيل دخول المستخدم admin: {message}")
            return
            
        print("✅ تم تسجيل دخول المستخدم admin بنجاح")
        user_id = auth_manager.current_user['id']
        print(f"🆔 معرف المستخدم: {user_id}")
        
        # فحص الحسابات المتاحة
        print("\n📊 فحص الحسابات المتاحة...")
        accounts = Account.get_by_user(user_id)
        print(f"📈 عدد الحسابات: {len(accounts)}")
        
        if len(accounts) < 2:
            print("⚠️ يجب وجود حسابين على الأقل لإجراء تحويل")
            return
            
        for i, account in enumerate(accounts[:2]):
            print(f"  حساب {i+1}: {account['name']} (ID: {account['id']})")
            balances = account.get('balances', [])
            for balance in balances:
                print(f"    - {balance['code']}: {balance['balance']}")
        
        # فحص العملات المتاحة
        print("\n💱 فحص العملات المتاحة...")
        currencies = Currency.get_all()
        print(f"💰 عدد العملات: {len(currencies)}")
        for currency in currencies:
            print(f"  - {currency['name']} ({currency['code']}) - {currency['symbol']}")
        
        # فحص التحويلات الحالية
        print("\n🔄 فحص التحويلات الحالية...")
        transfers = Transfer.get_by_user(user_id)
        print(f"📋 عدد التحويلات الحالية: {len(transfers)}")
        
        if transfers:
            print("📝 التحويلات الموجودة:")
            for transfer in transfers:
                print(f"  - ID: {transfer['id']}")
                print(f"    من: {transfer['from_account_name']} ({transfer['from_currency_code']})")
                print(f"    إلى: {transfer['to_account_name']} ({transfer['to_currency_code']})")
                print(f"    المبلغ: {transfer['from_amount']} -> {transfer['to_amount']}")
                print(f"    التاريخ: {transfer['transfer_date']}")
                print()
        
        # إنشاء تحويل تجريبي
        print("\n🧪 إنشاء تحويل تجريبي...")
        
        # استخدام أول حسابين
        from_account = accounts[0]
        to_account = accounts[1]
        
        # استخدام أول عملة متاحة في الحساب المصدر
        from_balances = from_account.get('balances', [])
        if not from_balances:
            print("❌ لا توجد أرصدة في الحساب المصدر")
            return
            
        from_currency_id = from_balances[0]['currency_id']
        from_amount = Decimal('10.00')  # مبلغ تجريبي صغير
        
        print(f"📤 من الحساب: {from_account['name']} (ID: {from_account['id']})")
        print(f"📥 إلى الحساب: {to_account['name']} (ID: {to_account['id']})")
        print(f"💰 العملة: {from_balances[0]['code']} (ID: {from_currency_id})")
        print(f"💵 المبلغ: {from_amount}")
        
        # إنشاء التحويل
        transfer_id = Transfer.create(
            user_id=user_id,
            from_account_id=from_account['id'],
            to_account_id=to_account['id'],
            from_amount=from_amount,
            from_currency_id=from_currency_id,
            to_amount=from_amount,  # نفس المبلغ للتبسيط
            to_currency_id=from_currency_id,  # نفس العملة للتبسيط
            description="تحويل تجريبي للتشخيص",
            transfer_date=datetime.now().date()
        )
        
        if transfer_id > 0:
            print(f"✅ تم إنشاء التحويل بنجاح! ID: {transfer_id}")
            
            # فحص التحويل المنشأ
            print("\n🔍 فحص التحويل المنشأ...")
            created_transfer = Transfer.get_by_id(transfer_id)
            if created_transfer:
                print("✅ تم العثور على التحويل في قاعدة البيانات")
                print(f"  - ID: {created_transfer['id']}")
                print(f"  - من: {created_transfer['from_account_name']}")
                print(f"  - إلى: {created_transfer['to_account_name']}")
                print(f"  - المبلغ: {created_transfer['from_amount']}")
                print(f"  - الوصف: {created_transfer['description']}")
            else:
                print("❌ لم يتم العثور على التحويل في قاعدة البيانات")
            
            # فحص قائمة التحويلات بعد الإنشاء
            print("\n📋 فحص قائمة التحويلات بعد الإنشاء...")
            updated_transfers = Transfer.get_by_user(user_id)
            print(f"📊 عدد التحويلات الآن: {len(updated_transfers)}")
            
            if updated_transfers:
                print("📝 آخر التحويلات:")
                for transfer in updated_transfers[:3]:  # أول 3 تحويلات
                    print(f"  - ID: {transfer['id']} | {transfer['transfer_date']} | {transfer['description']}")
            
        else:
            print("❌ فشل في إنشاء التحويل")
            
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # إغلاق الاتصال
        try:
            db.close()
            print("\n🔌 تم إغلاق الاتصال بقاعدة البيانات")
        except:
            pass

if __name__ == "__main__":
    debug_transfer_issue()
