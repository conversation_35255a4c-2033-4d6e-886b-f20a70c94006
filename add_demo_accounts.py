#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة حسابات وعمليات تجريبية للاختبار
"""

import mysql.connector
from mysql.connector import Error
import sys
import os
from datetime import datetime, date, timedelta

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def add_demo_data():
    """إضافة بيانات تجريبية شاملة"""
    
    print("🔄 إضافة بيانات تجريبية...")
    
    try:
        # الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='mohdam',
            database='money_manager',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # الحصول على معرف المستخدم الافتراضي
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        user_result = cursor.fetchone()
        if not user_result:
            print("❌ لم يتم العثور على المستخدم الافتراضي")
            return False
        
        user_id = user_result[0]
        print(f"✅ تم العثور على المستخدم: {user_id}")
        
        # حذف البيانات الموجودة (اختياري)
        print("🗑️ حذف البيانات القديمة...")
        cursor.execute("DELETE FROM transfers WHERE user_id = %s", (user_id,))
        cursor.execute("DELETE FROM transactions WHERE user_id = %s", (user_id,))
        cursor.execute("DELETE FROM accounts WHERE user_id = %s", (user_id,))
        
        # إضافة حسابات جديدة
        print("🏦 إضافة الحسابات...")
        accounts_data = [
            (user_id, 'الحساب الشخصي - الراجحي', 2, 1, 15000.00, 15000.00, 'الحساب الجاري الشخصي في بنك الراجحي'),
            (user_id, 'حساب الأعمال - الأهلي', 2, 1, 25000.00, 25000.00, 'حساب الأعمال في البنك الأهلي'),
            (user_id, 'الصندوق النقدي', 1, 1, 2000.00, 2000.00, 'النقد المتوفر في المنزل'),
            (user_id, 'حساب التوفير', 6, 1, 50000.00, 50000.00, 'حساب التوفير طويل المدى'),
            (user_id, 'محفظة دولارية', 2, 2, 5000.00, 5000.00, 'حساب بالدولار الأمريكي')
        ]
        
        cursor.executemany("""
            INSERT INTO accounts (user_id, name, account_type_id, currency_id, initial_balance, current_balance, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, accounts_data)
        
        # الحصول على معرفات الحسابات المضافة
        cursor.execute("SELECT id, name FROM accounts WHERE user_id = %s ORDER BY id", (user_id,))
        accounts = cursor.fetchall()
        account_ids = {name: account_id for account_id, name in accounts}
        
        print(f"✅ تم إضافة {len(accounts)} حسابات")
        
        # إضافة معاملات واردات متنوعة
        print("💰 إضافة الواردات...")
        
        # تواريخ متنوعة خلال الشهرين الماضيين
        today = date.today()
        
        income_transactions = [
            # راتب شهري
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'income', 12000.00, 1, 1, 'راتب شهر ديسمبر 2024', today - timedelta(days=5)),
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'income', 12000.00, 1, 1, 'راتب شهر نوفمبر 2024', today - timedelta(days=35)),
            
            # دخل من الأعمال
            (user_id, account_ids['حساب الأعمال - الأهلي'], 'income', 8000.00, 1, 2, 'دخل من مشروع تطوير تطبيق', today - timedelta(days=10)),
            (user_id, account_ids['حساب الأعمال - الأهلي'], 'income', 5500.00, 1, 2, 'استشارة تقنية', today - timedelta(days=15)),
            (user_id, account_ids['حساب الأعمال - الأهلي'], 'income', 3200.00, 1, 2, 'عمل حر - تصميم موقع', today - timedelta(days=25)),
            
            # عوائد استثمارية
            (user_id, account_ids['حساب التوفير'], 'income', 750.00, 1, 3, 'عائد سنوي من الاستثمار', today - timedelta(days=3)),
            (user_id, account_ids['حساب التوفير'], 'income', 1200.00, 1, 3, 'أرباح أسهم', today - timedelta(days=20)),
            
            # دخل إضافي
            (user_id, account_ids['الصندوق النقدي'], 'income', 800.00, 1, 4, 'بيع أشياء غير مستخدمة', today - timedelta(days=8)),
            (user_id, account_ids['محفظة دولارية'], 'income', 1500.00, 2, 4, 'عمل حر دولي', today - timedelta(days=12))
        ]
        
        cursor.executemany("""
            INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_id, description, transaction_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, income_transactions)
        
        print(f"✅ تم إضافة {len(income_transactions)} معاملة وارد")
        
        # إضافة معاملات مصروفات متنوعة
        print("💸 إضافة المصروفات...")
        
        expense_transactions = [
            # مصروفات طعام وشراب
            (user_id, account_ids['الصندوق النقدي'], 'expense', 150.00, 1, 1, 'تسوق من البقالة', today - timedelta(days=1)),
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 280.00, 1, 1, 'عشاء في مطعم', today - timedelta(days=3)),
            (user_id, account_ids['الصندوق النقدي'], 'expense', 45.00, 1, 1, 'قهوة وحلويات', today - timedelta(days=5)),
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 320.00, 1, 1, 'تسوق شهري من السوبر ماركت', today - timedelta(days=7)),
            
            # مواصلات
            (user_id, account_ids['الصندوق النقدي'], 'expense', 25.00, 1, 2, 'تاكسي إلى العمل', today - timedelta(days=1)),
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 80.00, 1, 2, 'وقود السيارة', today - timedelta(days=4)),
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 200.00, 1, 2, 'صيانة السيارة', today - timedelta(days=12)),
            
            # سكن
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 2500.00, 1, 3, 'إيجار الشقة - ديسمبر', today - timedelta(days=5)),
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 2500.00, 1, 3, 'إيجار الشقة - نوفمبر', today - timedelta(days=35)),
            
            # فواتير
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 180.00, 1, 4, 'فاتورة الكهرباء', today - timedelta(days=6)),
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 85.00, 1, 4, 'فاتورة الإنترنت', today - timedelta(days=8)),
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 120.00, 1, 4, 'فاتورة الهاتف', today - timedelta(days=10)),
            
            # صحة
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 350.00, 1, 5, 'زيارة طبيب أسنان', today - timedelta(days=14)),
            (user_id, account_ids['الصندوق النقدي'], 'expense', 65.00, 1, 5, 'أدوية من الصيدلية', today - timedelta(days=9)),
            
            # تعليم
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 450.00, 1, 6, 'كورس تطوير مهارات', today - timedelta(days=18)),
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 85.00, 1, 6, 'كتب تقنية', today - timedelta(days=22)),
            
            # ترفيه
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 120.00, 1, 7, 'تذاكر سينما', today - timedelta(days=2)),
            (user_id, account_ids['الصندوق النقدي'], 'expense', 200.00, 1, 7, 'رحلة نهاية الأسبوع', today - timedelta(days=11)),
            
            # تسوق
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 380.00, 1, 8, 'ملابس جديدة', today - timedelta(days=16)),
            (user_id, account_ids['الحساب الشخصي - الراجحي'], 'expense', 150.00, 1, 8, 'هدايا للأصدقاء', today - timedelta(days=20))
        ]
        
        cursor.executemany("""
            INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_id, description, transaction_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, expense_transactions)
        
        print(f"✅ تم إضافة {len(expense_transactions)} معاملة مصروف")
        
        # إضافة تحويلات بين الحسابات
        print("🔄 إضافة التحويلات...")
        
        transfer_transactions = [
            # تحويل للطوارئ
            (user_id, account_ids['حساب التوفير'], account_ids['الحساب الشخصي - الراجحي'], 3000.00, 1, 'تحويل للطوارئ', today - timedelta(days=13)),
            
            # تحويل للأعمال
            (user_id, account_ids['الحساب الشخصي - الراجحي'], account_ids['حساب الأعمال - الأهلي'], 5000.00, 1, 'تحويل رأس مال للأعمال', today - timedelta(days=21)),
            
            # تحويل إلى النقد
            (user_id, account_ids['الحساب الشخصي - الراجحي'], account_ids['الصندوق النقدي'], 1000.00, 1, 'سحب نقدي للمصروفات اليومية', today - timedelta(days=7)),
            
            # تحويل للتوفير
            (user_id, account_ids['حساب الأعمال - الأهلي'], account_ids['حساب التوفير'], 2000.00, 1, 'توفير من أرباح الأعمال', today - timedelta(days=4)),
            
            # تحويل من الدولار
            (user_id, account_ids['محفظة دولارية'], account_ids['الحساب الشخصي - الراجحي'], 800.00, 2, 'تحويل من الدولار للريال', today - timedelta(days=17))
        ]
        
        cursor.executemany("""
            INSERT INTO transfers (user_id, from_account_id, to_account_id, amount, currency_id, description, transfer_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, transfer_transactions)
        
        print(f"✅ تم إضافة {len(transfer_transactions)} تحويل")
        
        # تحديث أرصدة الحسابات بناءً على المعاملات
        print("🔄 تحديث أرصدة الحسابات...")
        
        # حساب الأرصدة الجديدة
        for account_name, account_id in account_ids.items():
            # حساب إجمالي الواردات
            cursor.execute("""
                SELECT COALESCE(SUM(amount), 0) 
                FROM transactions 
                WHERE account_id = %s AND transaction_type = 'income'
            """, (account_id,))
            total_income = cursor.fetchone()[0]
            
            # حساب إجمالي المصروفات
            cursor.execute("""
                SELECT COALESCE(SUM(amount), 0) 
                FROM transactions 
                WHERE account_id = %s AND transaction_type = 'expense'
            """, (account_id,))
            total_expenses = cursor.fetchone()[0]
            
            # حساب التحويلات الواردة
            cursor.execute("""
                SELECT COALESCE(SUM(amount), 0) 
                FROM transfers 
                WHERE to_account_id = %s
            """, (account_id,))
            transfers_in = cursor.fetchone()[0]
            
            # حساب التحويلات الصادرة
            cursor.execute("""
                SELECT COALESCE(SUM(amount), 0) 
                FROM transfers 
                WHERE from_account_id = %s
            """, (account_id,))
            transfers_out = cursor.fetchone()[0]
            
            # الحصول على الرصيد الابتدائي
            cursor.execute("SELECT initial_balance FROM accounts WHERE id = %s", (account_id,))
            initial_balance = cursor.fetchone()[0]
            
            # حساب الرصيد الجديد
            new_balance = initial_balance + total_income - total_expenses + transfers_in - transfers_out
            
            # تحديث الرصيد
            cursor.execute("""
                UPDATE accounts 
                SET current_balance = %s 
                WHERE id = %s
            """, (new_balance, account_id))
            
            print(f"  📊 {account_name}: {new_balance:,.2f} ر.س")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("\n🎉 تم إضافة جميع البيانات التجريبية بنجاح!")
        print("\n📋 ملخص البيانات المضافة:")
        print(f"   • {len(accounts)} حسابات مصرفية")
        print(f"   • {len(income_transactions)} معاملة وارد")
        print(f"   • {len(expense_transactions)} معاملة مصروف")
        print(f"   • {len(transfer_transactions)} تحويل بين الحسابات")
        
        print("\n💡 يمكنك الآن تسجيل الدخول باستخدام:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: 123456")
        
        return True
        
    except Error as e:
        print(f"❌ خطأ في MySQL: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🏦 إضافة بيانات تجريبية لمدير الأموال")
    print("=" * 60)
    
    if add_demo_data():
        print("\n✅ تمت العملية بنجاح!")
        print("🚀 يمكنك الآن تشغيل البرنامج الرئيسي:")
        print("   python main.py")
    else:
        print("\n❌ فشلت العملية")
        print("💡 تأكد من:")
        print("   - تشغيل MySQL Server")
        print("   - تشغيل setup_database.py أولاً")
    
    input("\n🔄 اضغط Enter للخروج...")
