import customtkinter as ctk
from tkinter import messagebox, filedialog
import threading
from datetime import datetime
from decimal import Decimal, InvalidOperation
import pandas as pd
from config.colors import COLORS, BUTTON_STYLES, CARD_STYLES, ARABIC_TEXT_STYLES
from config.settings import APP_CONFIG, SUPPORTED_CURRENCIES
from config.fonts import (
    get_title_font, get_subtitle_font, get_header_font, get_body_font,
    create_rtl_label, create_rtl_button, create_rtl_entry
)
from utils.auth import auth_manager
from database.connection import db
from database.models import Account, Transfer, Currency, Transaction
import time

class PerformanceCache:
    """نظام تخزين مؤقت للبيانات لتحسين الأداء"""

    def __init__(self):
        self.cache = {}
        self.cache_timeout = 30  # 30 ثانية
        self.last_update = {}

    def get(self, key):
        """الحصول على البيانات من التخزين المؤقت"""
        if key in self.cache:
            if time.time() - self.last_update.get(key, 0) < self.cache_timeout:
                return self.cache[key]
        return None

    def set(self, key, value):
        """حفظ البيانات في التخزين المؤقت"""
        self.cache[key] = value
        self.last_update[key] = time.time()

    def clear(self, key=None):
        """مسح التخزين المؤقت"""
        if key:
            self.cache.pop(key, None)
            self.last_update.pop(key, None)
        else:
            self.cache.clear()
            self.last_update.clear()

# إنشاء مثيل التخزين المؤقت العام
performance_cache = PerformanceCache()

class MainWindow:
    """النافذة الرئيسية للتطبيق"""

    def __init__(self):
        self.window = None
        self.current_page = "dashboard"
        self.setup_window()
        self.create_layout()
        self.load_dashboard()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        ctk.set_appearance_mode("light")

        self.window = ctk.CTk()

        # التحقق من وجود بيانات المستخدم
        if auth_manager.current_user and 'full_name' in auth_manager.current_user:
            user_name = auth_manager.current_user['full_name']
        else:
            user_name = "مستخدم"

        self.window.title(f"{APP_CONFIG['title']} - {user_name}")
        self.window.geometry(APP_CONFIG['window_size'])
        self.window.minsize(*APP_CONFIG['min_window_size'])
        self.window.configure(fg_color=COLORS['bg_light'])

        # توسيط النافذة
        self.center_window()

        # ربط إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = int(APP_CONFIG['window_size'].split('x')[0])
        height = int(APP_CONFIG['window_size'].split('x')[1])
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def create_layout(self):
        """إنشاء تخطيط النافذة"""
        # الشريط العلوي
        self.create_header()

        # الحاوية الرئيسية
        main_container = ctk.CTkFrame(
            self.window,
            fg_color="transparent"
        )
        main_container.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # الشريط الجانبي
        self.create_sidebar(main_container)

        # منطقة المحتوى
        self.create_content_area(main_container)

    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = ctk.CTkFrame(
            self.window,
            height=80,
            fg_color=COLORS['primary'],
            corner_radius=0
        )
        header_frame.pack(fill="x")
        header_frame.pack_propagate(False)

        # العنوان
        title_label = create_rtl_label(
            header_frame,
            text=APP_CONFIG['title'],
            font_size='title',
            text_color=COLORS['text_light'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(side="right", padx=20, pady=20)

        # معلومات المستخدم
        user_frame = ctk.CTkFrame(
            header_frame,
            fg_color="transparent"
        )
        user_frame.pack(side="left", padx=20, pady=15)

        welcome_label = create_rtl_label(
            user_frame,
            text=f"مرحباً، {auth_manager.current_user['full_name']}",
            font_size='header',
            text_color=COLORS['text_light'],
            **ARABIC_TEXT_STYLES['label']
        )
        welcome_label.pack(anchor="e")

        role_text = "مدير النظام" if auth_manager.is_admin() else "مستخدم"
        role_label = create_rtl_label(
            user_frame,
            text=f"الصلاحية: {role_text}",
            font_size='small',
            text_color=COLORS['primary_light'],
            **ARABIC_TEXT_STYLES['label']
        )
        role_label.pack(anchor="e")

        # زر تسجيل الخروج
        logout_button = create_rtl_button(
            header_frame,
            text="تسجيل الخروج",
            command=self.logout,
            fg_color=COLORS['error'],
            hover_color="#DC2626",
            height=40
        )
        logout_button.pack(side="left", padx=(0, 20), pady=22)

    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        self.sidebar = ctk.CTkFrame(
            parent,
            width=250,
            fg_color=COLORS['bg_card'],
            corner_radius=15,
            border_width=1,
            border_color=COLORS['border']
        )
        self.sidebar.pack(side="right", fill="y", padx=(0, 20))
        self.sidebar.pack_propagate(False)

        # عنوان القائمة
        menu_title = create_rtl_label(
            self.sidebar,
            text="القائمة الرئيسية",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        menu_title.pack(pady=(20, 30))

        # أزرار القائمة
        self.menu_buttons = {}

        menu_items = [
            ("dashboard", "🏠 لوحة التحكم", self.show_dashboard),
            ("income", "💰 الواردات", self.show_income),
            ("expense", "💸 المصروفات", self.show_expense),
            ("accounts", "🏦 الحسابات", self.show_accounts),
            ("transfers", "🔄 التحويلات", self.show_transfers),
            ("search", "🔍 البحث", self.show_search),
            ("reports", "📊 التقارير", self.show_reports),
            ("users", "👥 إدارة المستخدمين", self.show_users),
        ]

        # إضافة زر إدارة قاعدة البيانات للمديرين فقط
        if auth_manager.is_admin():
            menu_items.append(("database", "🗄️ إدارة قاعدة البيانات", self.show_database_management))

        for item_id, text, command in menu_items:
            button = create_rtl_button(
                self.sidebar,
                text=text,
                command=command,
                anchor="e",
                **BUTTON_STYLES['secondary']
            )
            button.pack(pady=5, padx=20)
            self.menu_buttons[item_id] = button

        # تحديد الزر النشط
        self.set_active_button("dashboard")

    def create_content_area(self, parent):
        """إنشاء منطقة المحتوى"""
        self.content_frame = ctk.CTkFrame(
            parent,
            fg_color=COLORS['bg_card'],
            corner_radius=15,
            border_width=1,
            border_color=COLORS['border']
        )
        self.content_frame.pack(side="left", fill="both", expand=True)

    def set_active_button(self, active_id):
        """تحديد الزر النشط"""
        for button_id, button in self.menu_buttons.items():
            if button_id == active_id:
                button.configure(**BUTTON_STYLES['primary'])
            else:
                button.configure(**BUTTON_STYLES['secondary'])

    def clear_content(self):
        """مسح محتوى المنطقة الرئيسية"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def load_dashboard(self):
        """تحميل لوحة التحكم المحسنة مع دعم تعدد العملات"""
        self.clear_content()

        # عنوان الصفحة
        title_label = create_rtl_label(
            self.content_frame,
            text="لوحة التحكم",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 20))

        # إطار قابل للتمرير للمحتوى
        main_scroll_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color="transparent"
        )
        main_scroll_frame.pack(fill="both", expand=True, padx=30, pady=(0, 30))

        # ملخص إجمالي بجميع العملات
        self.create_currency_summary_section(main_scroll_frame)

        # بطاقات منفصلة للعملات
        self.create_currency_cards_section(main_scroll_frame)

        # إحصائيات مالية شهرية
        self.create_monthly_stats_section(main_scroll_frame)

        # تحويل العملات (اختياري)
        self.create_currency_conversion_section(main_scroll_frame)

        # المعاملات الأخيرة
        self.create_recent_transactions_section(main_scroll_frame)

    def create_currency_summary_section(self, parent):
        """إنشاء قسم ملخص العملات الإجمالي"""
        # عنوان القسم
        section_title = create_rtl_label(
            parent,
            text="📊 ملخص الأرصدة بجميع العملات",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        section_title.pack(pady=(20, 15))

        # إطار الملخص
        summary_frame = ctk.CTkFrame(
            parent,
            fg_color=COLORS['bg_card'],
            corner_radius=15
        )
        summary_frame.pack(fill="x", padx=20, pady=(0, 20))

        # الحصول على بيانات العملات
        currency_data = self.get_currency_balances_summary()

        if not currency_data:
            no_data_label = create_rtl_label(
                summary_frame,
                text="لا توجد أرصدة متاحة حالياً",
                font_size='header',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['title']
            )
            no_data_label.pack(pady=30)
            return

        # عرض الأرصدة بشكل شبكي
        grid_frame = ctk.CTkFrame(summary_frame, fg_color="transparent")
        grid_frame.pack(fill="x", padx=20, pady=20)

        for i, currency_info in enumerate(currency_data):
            currency_card = ctk.CTkFrame(
                grid_frame,
                fg_color=COLORS['bg_light'],
                corner_radius=10
            )
            currency_card.grid(row=i//2, column=i%2, padx=10, pady=10, sticky="ew")
            grid_frame.grid_columnconfigure(0, weight=1)
            grid_frame.grid_columnconfigure(1, weight=1)

            # رمز العملة
            currency_symbol = create_rtl_label(
                currency_card,
                text=currency_info['symbol'],
                font_size='subtitle',
                text_color=COLORS['primary'],
                **ARABIC_TEXT_STYLES['title']
            )
            currency_symbol.pack(pady=(15, 5))

            # اسم العملة
            currency_name = create_rtl_label(
                currency_card,
                text=currency_info['name'],
                font_size='header',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['title']
            )
            currency_name.pack(pady=(0, 5))

            # إجمالي الرصيد
            total_balance = create_rtl_label(
                currency_card,
                text=f"{currency_info['total_balance']:,.2f} {currency_info['symbol']}",
                font_size='header',
                text_color=COLORS['success'] if currency_info['total_balance'] > 0 else COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['title']
            )
            total_balance.pack(pady=(0, 10))

            # عدد الحسابات
            accounts_count = create_rtl_label(
                currency_card,
                text=f"عدد الحسابات: {currency_info['accounts_count']}",
                font_size='body',
                text_color=COLORS['text_secondary'],
                **ARABIC_TEXT_STYLES['title']
            )
            accounts_count.pack(pady=(0, 15))

    def create_currency_cards_section(self, parent):
        """إنشاء قسم بطاقات العملات المنفصلة"""
        # عنوان القسم
        section_title = create_rtl_label(
            parent,
            text="💳 تفاصيل الحسابات حسب العملة",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        section_title.pack(pady=(20, 15))

        # الحصول على بيانات الحسابات مجمعة حسب العملة
        currency_accounts = self.get_accounts_by_currency()

        for currency_code, accounts_data in currency_accounts.items():
            self.create_single_currency_card(parent, currency_code, accounts_data)

    def create_single_currency_card(self, parent, currency_code, accounts_data):
        """إنشاء بطاقة واحدة لعملة معينة مع ألوان محسنة للوضوح"""
        from config.settings import SUPPORTED_CURRENCIES

        currency_info = SUPPORTED_CURRENCIES.get(currency_code, {})
        currency_name = currency_info.get('name', currency_code)
        currency_symbol = currency_info.get('symbol', currency_code)

        # تحديد ألوان مميزة ومحسنة لكل عملة
        currency_themes = {
            'SAR': {'header_bg': '#059669', 'header_text': '#FFFFFF'},  # أخضر داكن مع أبيض
            'YER': {'header_bg': '#2563EB', 'header_text': '#FFFFFF'},  # أزرق داكن مع أبيض
            'AED': {'header_bg': '#D97706', 'header_text': '#FFFFFF'},  # ذهبي داكن مع أبيض
            'USD': {'header_bg': '#7C3AED', 'header_text': '#FFFFFF'},  # بنفسجي داكن مع أبيض
        }

        theme = currency_themes.get(currency_code, currency_themes['SAR'])

        # إطار البطاقة مع حدود ملونة
        card_frame = ctk.CTkFrame(
            parent,
            fg_color=COLORS['bg_card'],
            corner_radius=15,
            border_width=2,
            border_color=theme['header_bg']
        )
        card_frame.pack(fill="x", padx=20, pady=(0, 15))

        # رأس البطاقة مع لون محسن
        header_frame = ctk.CTkFrame(
            card_frame,
            fg_color=theme['header_bg'],  # لون داكن للتباين العالي
            corner_radius=12
        )
        header_frame.pack(fill="x", padx=15, pady=(15, 10))

        # عنوان العملة مع تباين عالي
        currency_title = create_rtl_label(
            header_frame,
            text=f"{currency_symbol} {currency_name}",
            font_size='subtitle',
            text_color=theme['header_text'],  # أبيض للتباين الأمثل
            **ARABIC_TEXT_STYLES['title']
        )
        currency_title.pack(pady=15)

        # محتوى البطاقة
        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=15, pady=(0, 15))

        if not accounts_data['accounts']:
            no_accounts_label = create_rtl_label(
                content_frame,
                text="لا توجد حسابات بهذه العملة",
                font_size='header',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['title']
            )
            no_accounts_label.pack(pady=20)
        else:
            # إجمالي الرصيد للعملة مع خلفية محسنة
            total_frame = ctk.CTkFrame(
                content_frame,
                fg_color='#F8FAFC',  # خلفية بيضاء فاتحة للوضوح
                corner_radius=10,
                border_width=1,
                border_color=theme['header_bg']  # حدود بلون العملة
            )
            total_frame.pack(fill="x", pady=(0, 12))

            # تحديد لون النص بناءً على الرصيد مع تحسين التباين
            if accounts_data['total_balance'] > 0:
                balance_color = theme['header_bg']  # لون العملة للأرصدة الموجبة
            elif accounts_data['total_balance'] < 0:
                balance_color = '#DC2626'  # أحمر داكن للأرصدة السالبة
            else:
                balance_color = '#6B7280'  # رمادي للأرصدة الصفرية

            total_label = create_rtl_label(
                total_frame,
                text=f"إجمالي الرصيد: {accounts_data['total_balance']:,.2f} {currency_symbol}",
                font_size='subtitle',
                text_color=balance_color,
                **ARABIC_TEXT_STYLES['title']
            )
            total_label.pack(pady=12)

            # قائمة الحسابات مع ألوان محسنة
            for account in accounts_data['accounts']:
                # إطار الحساب مع خلفية فاتحة للوضوح
                account_frame = ctk.CTkFrame(
                    content_frame,
                    fg_color='#FFFFFF',  # خلفية بيضاء نظيفة
                    corner_radius=10,
                    border_width=1,
                    border_color='#E5E7EB'  # حدود رمادية خفيفة
                )
                account_frame.pack(fill="x", pady=3)

                account_info_frame = ctk.CTkFrame(account_frame, fg_color="transparent")
                account_info_frame.pack(fill="x", padx=15, pady=10)

                # اسم الحساب مع لون واضح
                account_name = create_rtl_label(
                    account_info_frame,
                    text=account['name'],
                    font_size='header',
                    text_color='#1F2937',  # نص أساسي داكن للوضوح الأمثل
                    **ARABIC_TEXT_STYLES['label']
                )
                account_name.pack(side="right")

                # رصيد الحساب مع ألوان محسنة
                if account['balance'] > 0:
                    balance_color = theme['header_bg']  # لون العملة للأرصدة الموجبة
                elif account['balance'] < 0:
                    balance_color = '#DC2626'  # أحمر داكن للأرصدة السالبة
                else:
                    balance_color = '#6B7280'  # رمادي للأرصدة الصفرية

                account_balance = create_rtl_label(
                    account_info_frame,
                    text=f"{account['balance']:,.2f} {currency_symbol}",
                    font_size='header',
                    text_color=balance_color,
                    **ARABIC_TEXT_STYLES['label']
                )
                account_balance.pack(side="left")

    def create_monthly_stats_section(self, parent):
        """إنشاء قسم الإحصائيات الشهرية"""
        # عنوان القسم
        section_title = create_rtl_label(
            parent,
            text="📈 الإحصائيات المالية الشهرية",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        section_title.pack(pady=(20, 15))

        # إطار الإحصائيات
        stats_frame = ctk.CTkFrame(
            parent,
            fg_color=COLORS['bg_card'],
            corner_radius=15
        )
        stats_frame.pack(fill="x", padx=20, pady=(0, 20))

        # الحصول على الإحصائيات الشهرية
        monthly_stats = self.get_monthly_statistics_by_currency()

        if not monthly_stats:
            no_stats_label = create_rtl_label(
                stats_frame,
                text="لا توجد معاملات هذا الشهر",
                font_size='header',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['title']
            )
            no_stats_label.pack(pady=30)
            return

        # عرض الإحصائيات في شبكة
        grid_frame = ctk.CTkFrame(stats_frame, fg_color="transparent")
        grid_frame.pack(fill="x", padx=20, pady=20)

        for i, currency_stats in enumerate(monthly_stats):
            stats_card = ctk.CTkFrame(
                grid_frame,
                fg_color=COLORS['bg_light'],
                corner_radius=10
            )
            stats_card.grid(row=i//2, column=i%2, padx=10, pady=10, sticky="ew")
            grid_frame.grid_columnconfigure(0, weight=1)
            grid_frame.grid_columnconfigure(1, weight=1)

            # عنوان العملة
            currency_header = create_rtl_label(
                stats_card,
                text=f"{currency_stats['symbol']} {currency_stats['name']}",
                font_size='header',
                text_color=COLORS['primary'],
                **ARABIC_TEXT_STYLES['title']
            )
            currency_header.pack(pady=(15, 10))

            # الواردات
            income_label = create_rtl_label(
                stats_card,
                text=f"📈 الواردات: {currency_stats['income']:,.2f} {currency_stats['symbol']}",
                font_size='body',
                text_color=COLORS['success'],
                **ARABIC_TEXT_STYLES['label']
            )
            income_label.pack(pady=2)

            # المصروفات
            expense_label = create_rtl_label(
                stats_card,
                text=f"📉 المصروفات: {currency_stats['expense']:,.2f} {currency_stats['symbol']}",
                font_size='body',
                text_color=COLORS['error'],
                **ARABIC_TEXT_STYLES['label']
            )
            expense_label.pack(pady=2)

            # الصافي
            net_amount = currency_stats['income'] - currency_stats['expense']
            net_color = COLORS['success'] if net_amount > 0 else COLORS['error'] if net_amount < 0 else COLORS['text_muted']
            net_label = create_rtl_label(
                stats_card,
                text=f"💰 الصافي: {net_amount:,.2f} {currency_stats['symbol']}",
                font_size='header',
                text_color=net_color,
                **ARABIC_TEXT_STYLES['label']
            )
            net_label.pack(pady=(5, 15))

    def create_currency_conversion_section(self, parent):
        """إنشاء قسم تحويل العملات (اختياري)"""
        # عنوان القسم
        section_title = create_rtl_label(
            parent,
            text="💱 تحويل العملات - عرض الأرصدة بعملة مرجعية",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        section_title.pack(pady=(20, 15))

        # إطار التحويل
        conversion_frame = ctk.CTkFrame(
            parent,
            fg_color=COLORS['bg_card'],
            corner_radius=15
        )
        conversion_frame.pack(fill="x", padx=20, pady=(0, 20))

        # إطار اختيار العملة المرجعية
        selection_frame = ctk.CTkFrame(conversion_frame, fg_color="transparent")
        selection_frame.pack(fill="x", padx=20, pady=15)

        # تسمية اختيار العملة
        currency_label = create_rtl_label(
            selection_frame,
            text="اختر العملة المرجعية:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        currency_label.pack(anchor="w", pady=(0, 10))

        # قائمة العملات
        from config.settings import SUPPORTED_CURRENCIES
        currency_options = [f"{info['symbol']} - {info['name']}" for info in SUPPORTED_CURRENCIES.values()]

        self.base_currency_combo = ctk.CTkComboBox(
            selection_frame,
            values=currency_options,
            height=40,
            font=ctk.CTkFont(size=14),
            command=self.update_currency_conversion
        )
        self.base_currency_combo.pack(fill="x", pady=(0, 10))
        self.base_currency_combo.set(currency_options[0])  # SAR افتراضياً

        # زر التحديث
        refresh_button = create_rtl_button(
            selection_frame,
            text="🔄 تحديث التحويل",
            command=self.update_currency_conversion,
            **BUTTON_STYLES['primary']
        )
        refresh_button.pack(pady=10)

        # إطار النتائج
        self.conversion_results_frame = ctk.CTkFrame(conversion_frame, fg_color="transparent")
        self.conversion_results_frame.pack(fill="x", padx=20, pady=(0, 15))

        # تحديث التحويل الأولي
        self.update_currency_conversion()

    def update_currency_conversion(self, *args):
        """تحديث عرض تحويل العملات"""
        try:
            # مسح النتائج السابقة
            for widget in self.conversion_results_frame.winfo_children():
                widget.destroy()

            # الحصول على العملة المرجعية المختارة
            selected_option = self.base_currency_combo.get()
            base_currency_code = selected_option.split(' - ')[0].replace('ر.س', 'SAR').replace('ر.ي', 'YER').replace('د.إ', 'AED').replace('$', 'USD')

            # تحديد رمز العملة الصحيح
            currency_mapping = {'ر.س': 'SAR', 'ر.ي': 'YER', 'د.إ': 'AED', '$': 'USD'}
            for symbol, code in currency_mapping.items():
                if symbol in selected_option:
                    base_currency_code = code
                    break

            # الحصول على بيانات التحويل
            conversion_data = self.get_currency_conversion_data(base_currency_code)

            if not conversion_data:
                no_data_label = create_rtl_label(
                    self.conversion_results_frame,
                    text="لا توجد بيانات للتحويل",
                    font_size='header',
                    text_color=COLORS['text_muted'],
                    **ARABIC_TEXT_STYLES['title']
                )
                no_data_label.pack(pady=20)
                return

            # عرض النتائج
            total_label = create_rtl_label(
                self.conversion_results_frame,
                text=f"إجمالي الأرصدة محولة إلى {conversion_data['base_currency']}:",
                font_size='header',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['title']
            )
            total_label.pack(pady=(10, 5))

            total_amount_label = create_rtl_label(
                self.conversion_results_frame,
                text=f"{conversion_data['total_converted']:,.2f} {conversion_data['base_symbol']}",
                font_size='subtitle',
                text_color=COLORS['success'],
                **ARABIC_TEXT_STYLES['title']
            )
            total_amount_label.pack(pady=(0, 15))

            # تفاصيل التحويل
            details_frame = ctk.CTkFrame(self.conversion_results_frame, fg_color=COLORS['bg_light'], corner_radius=8)
            details_frame.pack(fill="x", pady=(0, 10))

            details_title = create_rtl_label(
                details_frame,
                text="تفاصيل التحويل:",
                font_size='header',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['title']
            )
            details_title.pack(pady=(10, 5))

            for currency_code, details in conversion_data['details'].items():
                if details['original_amount'] > 0:
                    detail_text = f"{details['original_amount']:,.2f} {details['original_symbol']} = {details['converted_amount']:,.2f} {conversion_data['base_symbol']}"
                    if details['exchange_rate'] != 1.0:
                        detail_text += f" (سعر الصرف: {details['exchange_rate']:.4f})"

                    detail_label = create_rtl_label(
                        details_frame,
                        text=detail_text,
                        font_size='body',
                        text_color=COLORS['text_secondary'],
                        **ARABIC_TEXT_STYLES['label']
                    )
                    detail_label.pack(anchor="w", padx=15, pady=2)

            details_frame.pack_configure(pady=(0, 10))

        except Exception as e:
            print(f"خطأ في تحديث تحويل العملات: {e}")

    def create_recent_transactions_section(self, parent):
        """إنشاء قسم المعاملات الأخيرة المحسن"""
        # عنوان القسم
        section_title = create_rtl_label(
            parent,
            text="🕒 المعاملات الأخيرة",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        section_title.pack(pady=(20, 15))

        # إطار المعاملات
        transactions_frame = ctk.CTkFrame(
            parent,
            fg_color=COLORS['bg_card'],
            corner_radius=15
        )
        transactions_frame.pack(fill="x", padx=20, pady=(0, 20))

        # الحصول على المعاملات الأخيرة
        recent_transactions = self.get_recent_transactions_enhanced()

        if not recent_transactions:
            no_transactions_label = create_rtl_label(
                transactions_frame,
                text="لا توجد معاملات حديثة",
                font_size='header',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['title']
            )
            no_transactions_label.pack(pady=30)
            return

        # عرض المعاملات
        transactions_list_frame = ctk.CTkScrollableFrame(
            transactions_frame,
            fg_color="transparent",
            height=300
        )
        transactions_list_frame.pack(fill="x", padx=15, pady=15)

        for transaction in recent_transactions:
            self.create_transaction_item(transactions_list_frame, transaction)

    def create_stats_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        # الحصول على الإحصائيات من قاعدة البيانات
        stats = self.get_dashboard_stats()

        cards_data = [
            ("إجمالي الرصيد", f"{stats['total_balance']:.2f} ر.س", COLORS['success'], "💰"),
            ("الواردات الشهرية", f"{stats['monthly_income']:.2f} ر.س", COLORS['info'], "📈"),
            ("المصروفات الشهرية", f"{stats['monthly_expense']:.2f} ر.س", COLORS['error'], "📉"),
            ("عدد الحسابات", str(stats['accounts_count']), COLORS['primary'], "🏦"),
        ]

        for i, (title, value, color, icon) in enumerate(cards_data):
            card = ctk.CTkFrame(
                parent,
                **CARD_STYLES['elevated']
            )
            card.grid(row=0, column=i, padx=10, pady=10, sticky="ew")
            parent.grid_columnconfigure(i, weight=1)

            # الأيقونة
            icon_label = create_rtl_label(
                card,
                text=icon,
                font_size='header',
                **ARABIC_TEXT_STYLES['title']
            )
            icon_label.pack(pady=(15, 5))

            # القيمة
            value_label = create_rtl_label(
                card,
                text=value,
                font_size='subtitle',
                text_color=color,
                **ARABIC_TEXT_STYLES['title']
            )
            value_label.pack()

            # العنوان
            title_label = create_rtl_label(
                card,
                text=title,
                font_size='small',
                text_color=COLORS['text_secondary'],
                **ARABIC_TEXT_STYLES['title']
            )
            title_label.pack(pady=(0, 15))

    def create_recent_transactions(self, parent):
        """إنشاء قائمة المعاملات الأخيرة"""
        transactions_frame = ctk.CTkFrame(
            parent,
            **CARD_STYLES['default']
        )
        transactions_frame.pack(fill="both", expand=True, pady=(20, 0))

        # عنوان القسم
        title_label = create_rtl_label(
            transactions_frame,
            text="المعاملات الأخيرة",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 15))

        # قائمة المعاملات
        transactions_list = ctk.CTkScrollableFrame(
            transactions_frame,
            fg_color="transparent"
        )
        transactions_list.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # الحصول على المعاملات الأخيرة
        recent_transactions = self.get_recent_transactions()

        if not recent_transactions:
            no_data_label = create_rtl_label(
                transactions_list,
                text="لا توجد معاملات حتى الآن",
                font_size='body',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['title']
            )
            no_data_label.pack(pady=50)
        else:
            for transaction in recent_transactions:
                self.create_transaction_item(transactions_list, transaction)

    def create_transaction_item(self, parent, transaction):
        """إنشاء عنصر معاملة"""
        item_frame = ctk.CTkFrame(
            parent,
            fg_color=COLORS['bg_light'],
            corner_radius=8,
            border_width=1,
            border_color=COLORS['border']
        )
        item_frame.pack(fill="x", pady=5)

            # نوع المعاملة والأيقونة
        type_color = COLORS['success'] if transaction['transaction_type'] == 'income' else COLORS['error']
        type_icon = "📈" if transaction['transaction_type'] == 'income' else "📉"
        type_text = "وارد" if transaction['transaction_type'] == 'income' else "مصروف"

        # الجانب الأيمن - المعلومات
        info_frame = ctk.CTkFrame(item_frame, fg_color="transparent")
        info_frame.pack(side="right", fill="both", expand=True, padx=15, pady=10)

        # الوصف والتاريخ
        desc_label = create_rtl_label(
            info_frame,
            text=transaction.get('description', 'بدون وصف'),
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="e")

        date_label = create_rtl_label(
            info_frame,
            text=transaction['transaction_date'].strftime("%Y-%m-%d"),
            font_size='small',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        date_label.pack(anchor="e")

        # الجانب الأيسر - المبلغ والنوع
        amount_frame = ctk.CTkFrame(item_frame, fg_color="transparent")
        amount_frame.pack(side="left", padx=15, pady=10)

        # المبلغ
        amount_label = create_rtl_label(
            amount_frame,
            text=f"{transaction['amount']:.2f} {transaction['currency_symbol']}",
            font_size='header',
            text_color=type_color,
            **ARABIC_TEXT_STYLES['title']
        )
        amount_label.pack()

        # النوع
        type_label = create_rtl_label(
            amount_frame,
            text=f"{type_icon} {type_text}",
            font_size='small',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['title']
        )
        type_label.pack()

    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة التحكم"""
        try:
            user_id = auth_manager.current_user['id']
            current_month = datetime.now().strftime('%Y-%m')

            # إجمالي الرصيد (مجموع جميع الأرصدة بالعملة الافتراضية)
            balance_query = """
                SELECT COALESCE(SUM(ab.balance), 0) as total_balance
                FROM account_balances ab
                JOIN accounts a ON ab.account_id = a.id
                WHERE a.user_id = %s AND a.is_active = TRUE
                AND ab.currency_id = 1
            """
            balance_result = db.execute_query(balance_query, (user_id,))
            total_balance = balance_result[0]['total_balance'] if balance_result else 0

            # الواردات الشهرية
            income_query = """
                SELECT COALESCE(SUM(amount), 0) as monthly_income
                FROM transactions
                WHERE user_id = %s AND transaction_type = 'income'
                AND DATE_FORMAT(transaction_date, '%Y-%m') = %s
            """
            income_result = db.execute_query(income_query, (user_id, current_month))
            monthly_income = income_result[0]['monthly_income'] if income_result else 0

            # المصروفات الشهرية
            expense_query = """
                SELECT COALESCE(SUM(amount), 0) as monthly_expense
                FROM transactions
                WHERE user_id = %s AND transaction_type = 'expense'
                AND DATE_FORMAT(transaction_date, '%Y-%m') = %s
            """
            expense_result = db.execute_query(expense_query, (user_id, current_month))
            monthly_expense = expense_result[0]['monthly_expense'] if expense_result else 0

            # عدد الحسابات
            accounts_query = """
                SELECT COUNT(*) as accounts_count
                FROM accounts
                WHERE user_id = %s AND is_active = TRUE
            """
            accounts_result = db.execute_query(accounts_query, (user_id,))
            accounts_count = accounts_result[0]['accounts_count'] if accounts_result else 0

            return {
                'total_balance': float(total_balance),
                'monthly_income': float(monthly_income),
                'monthly_expense': float(monthly_expense),
                'accounts_count': accounts_count
            }

        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات: {e}")
            return {
                'total_balance': 0.0,
                'monthly_income': 0.0,
                'monthly_expense': 0.0,
                'accounts_count': 0
            }

    def get_currency_balances_summary(self):
        """الحصول على ملخص أرصدة جميع العملات (محسن مع التخزين المؤقت)"""

        # التحقق من التخزين المؤقت أولاً
        cache_key = f"currency_balances_{auth_manager.current_user['id']}"
        cached_data = performance_cache.get(cache_key)
        if cached_data is not None:
            return cached_data

        try:
            user_id = auth_manager.current_user['id']

            # استعلام محسن مع LIMIT
            query = """
                SELECT
                    c.code,
                    c.name,
                    c.symbol,
                    COALESCE(SUM(ab.balance), 0) as total_balance,
                    COUNT(DISTINCT a.id) as accounts_count
                FROM currencies c
                LEFT JOIN account_balances ab ON c.id = ab.currency_id
                INNER JOIN accounts a ON ab.account_id = a.id
                WHERE c.is_active = TRUE
                AND a.user_id = %s
                AND a.is_active = TRUE
                GROUP BY c.id, c.code, c.name, c.symbol
                HAVING total_balance > 0 OR accounts_count > 0
                ORDER BY total_balance DESC
                LIMIT 10
            """

            results = db.execute_query(query, (user_id,))

            # حفظ النتائج في التخزين المؤقت
            performance_cache.set(cache_key, results)

            return results if results else []

        except Exception as e:
            print(f"خطأ في الحصول على ملخص أرصدة العملات: {e}")
            return []

    def get_accounts_by_currency(self):
        """الحصول على الحسابات مجمعة حسب العملة"""
        try:
            user_id = auth_manager.current_user['id']

            query = """
                SELECT
                    c.code as currency_code,
                    c.name as currency_name,
                    c.symbol as currency_symbol,
                    a.id as account_id,
                    a.name as account_name,
                    COALESCE(ab.balance, 0) as balance
                FROM currencies c
                LEFT JOIN account_balances ab ON c.id = ab.currency_id
                INNER JOIN accounts a ON ab.account_id = a.id
                WHERE c.is_active = TRUE
                AND a.user_id = %s
                AND a.is_active = TRUE
                ORDER BY c.code, a.name
            """

            results = db.execute_query(query, (user_id,))

            # تجميع النتائج حسب العملة
            currency_accounts = {}

            for row in results:
                currency_code = row['currency_code']

                if currency_code not in currency_accounts:
                    currency_accounts[currency_code] = {
                        'name': row['currency_name'],
                        'symbol': row['currency_symbol'],
                        'accounts': [],
                        'total_balance': 0.0
                    }

                if row['account_id']:  # إذا كان هناك حساب
                    account_info = {
                        'id': row['account_id'],
                        'name': row['account_name'],
                        'balance': float(row['balance'])
                    }
                    currency_accounts[currency_code]['accounts'].append(account_info)
                    currency_accounts[currency_code]['total_balance'] += float(row['balance'])

            # إزالة العملات التي لا تحتوي على حسابات
            currency_accounts = {k: v for k, v in currency_accounts.items() if v['accounts']}

            return currency_accounts

        except Exception as e:
            print(f"خطأ في الحصول على الحسابات حسب العملة: {e}")
            return {}

    def get_monthly_statistics_by_currency(self):
        """الحصول على الإحصائيات الشهرية مجمعة حسب العملة"""
        try:
            user_id = auth_manager.current_user['id']
            current_month = datetime.now().strftime('%Y-%m')

            query = """
                SELECT
                    c.code,
                    c.name,
                    c.symbol,
                    COALESCE(SUM(CASE WHEN t.transaction_type = 'income' THEN t.amount ELSE 0 END), 0) as income,
                    COALESCE(SUM(CASE WHEN t.transaction_type = 'expense' THEN t.amount ELSE 0 END), 0) as expense
                FROM currencies c
                LEFT JOIN transactions t ON c.id = t.currency_id
                    AND t.user_id = %s
                    AND DATE_FORMAT(t.transaction_date, '%%Y-%%m') = %s
                WHERE c.is_active = TRUE
                GROUP BY c.id, c.code, c.name, c.symbol
                HAVING income > 0 OR expense > 0
                ORDER BY (income + expense) DESC
            """

            results = db.execute_query(query, (user_id, current_month))
            return results if results else []

        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات الشهرية: {e}")
            return []

    def get_recent_transactions_enhanced(self):
        """الحصول على المعاملات الأخيرة مع تفاصيل محسنة"""
        try:
            user_id = auth_manager.current_user['id']

            query = """
                SELECT
                    t.*,
                    c.symbol as currency_symbol,
                    c.name as currency_name,
                    a.name as account_name,
                    CASE
                        WHEN t.transaction_type = 'income' THEN '📈'
                        WHEN t.transaction_type = 'expense' THEN '📉'
                        ELSE '🔄'
                    END as type_icon
                FROM transactions t
                JOIN currencies c ON t.currency_id = c.id
                JOIN accounts a ON t.account_id = a.id
                WHERE t.user_id = %s
                ORDER BY t.created_at DESC
                LIMIT 10
            """

            results = db.execute_query(query, (user_id,))
            return results if results else []

        except Exception as e:
            print(f"خطأ في الحصول على المعاملات الأخيرة: {e}")
            return []

    def create_transaction_item(self, parent, transaction):
        """إنشاء عنصر معاملة في قائمة المعاملات الأخيرة مع ألوان محسنة"""
        # إطار المعاملة مع خلفية فاتحة للوضوح
        item_frame = ctk.CTkFrame(
            parent,
            fg_color='#FFFFFF',  # خلفية بيضاء نظيفة للوضوح الأمثل
            corner_radius=10,
            border_width=1,
            border_color='#E5E7EB'  # حدود رمادية خفيفة
        )
        item_frame.pack(fill="x", pady=4)

        content_frame = ctk.CTkFrame(item_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=15, pady=10)

        # الصف الأول: النوع والمبلغ والتاريخ
        top_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        top_frame.pack(fill="x")

        # نوع المعاملة مع النص العربي الصحيح
        type_color = COLORS['success'] if transaction['transaction_type'] == 'income' else COLORS['error']

        # تحويل نوع المعاملة إلى العربية
        transaction_type_arabic = {
            'income': 'وارد',
            'expense': 'مصروف',
            'transfer': 'تحويل'
        }.get(transaction['transaction_type'], transaction['transaction_type'])

        type_label = create_rtl_label(
            top_frame,
            text=f"{transaction['type_icon']} {transaction_type_arabic}",
            font_size='header',  # حجم أكبر للوضوح
            text_color=type_color,
            **ARABIC_TEXT_STYLES['label']
        )
        type_label.pack(side="right")

        # التاريخ (في اليسار للـ RTL)
        date_label = create_rtl_label(
            top_frame,
            text=str(transaction['transaction_date']),
            font_size='body',
            text_color='#6B7280',  # رمادي داكن للوضوح
            **ARABIC_TEXT_STYLES['label']
        )
        date_label.pack(side="left", padx=(0, 15))

        # المبلغ (في الوسط للـ RTL)
        amount_prefix = "+" if transaction['transaction_type'] == 'income' else "-"
        amount_label = create_rtl_label(
            top_frame,
            text=f"{amount_prefix}{transaction['amount']:,.2f} {transaction['currency_symbol']}",
            font_size='subtitle',  # حجم أكبر للوضوح
            text_color=type_color,
            **ARABIC_TEXT_STYLES['label']
        )
        amount_label.pack(side="left")

        # الصف الثاني: الوصف والحساب مع محاذاة RTL صحيحة
        if transaction.get('description'):
            desc_label = create_rtl_label(
                content_frame,
                text=transaction['description'],
                font_size='header',
                text_color='#1F2937',  # نص أساسي داكن للوضوح الأمثل
                **ARABIC_TEXT_STYLES['label']
            )
            desc_label.pack(anchor="e", pady=(8, 0))  # محاذاة RTL صحيحة

        account_label = create_rtl_label(
            content_frame,
            text=f"الحساب: {transaction['account_name']}",
            font_size='body',
            text_color='#6B7280',  # رمادي داكن للوضوح
            **ARABIC_TEXT_STYLES['label']
        )
        account_label.pack(anchor="e", pady=(4, 0))  # محاذاة RTL صحيحة

    def get_currency_conversion_data(self, base_currency_code):
        """الحصول على بيانات تحويل العملات"""
        try:
            from config.settings import SUPPORTED_CURRENCIES
            from utils.currency import CurrencyManager

            currency_manager = CurrencyManager()
            user_id = auth_manager.current_user['id']

            # الحصول على أرصدة جميع العملات
            currency_balances = self.get_currency_balances_summary()

            if not currency_balances:
                return None

            base_currency_info = SUPPORTED_CURRENCIES.get(base_currency_code, {})
            base_symbol = base_currency_info.get('symbol', base_currency_code)

            total_converted = 0.0
            conversion_details = {}

            for currency_data in currency_balances:
                currency_code = currency_data['code']
                original_amount = float(currency_data['total_balance'])

                if original_amount > 0:
                    # تحويل المبلغ إلى العملة المرجعية
                    exchange_rate = currency_manager.get_exchange_rate(currency_code, base_currency_code)
                    converted_amount = original_amount * exchange_rate

                    conversion_details[currency_code] = {
                        'original_amount': original_amount,
                        'original_symbol': currency_data['symbol'],
                        'converted_amount': converted_amount,
                        'exchange_rate': exchange_rate
                    }

                    total_converted += converted_amount

            return {
                'base_currency': base_currency_code,
                'base_symbol': base_symbol,
                'total_converted': total_converted,
                'details': conversion_details
            }

        except Exception as e:
            print(f"خطأ في الحصول على بيانات تحويل العملات: {e}")
            return None

    def get_recent_transactions(self):
        """الحصول على المعاملات الأخيرة"""
        try:
            user_id = auth_manager.current_user['id']
            query = """
                SELECT t.*, c.symbol as currency_symbol
                FROM transactions t
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = %s
                ORDER BY t.created_at DESC
                LIMIT 10
            """
            return db.execute_query(query, (user_id,))
        except Exception as e:
            print(f"خطأ في الحصول على المعاملات: {e}")
            return []

    # وظائف القائمة
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.current_page = "dashboard"
        self.set_active_button("dashboard")
        self.load_dashboard()

    def show_income(self):
        """عرض صفحة الواردات"""
        self.current_page = "income"
        self.set_active_button("income")
        self.clear_content()

        # عنوان الصفحة
        title_label = create_rtl_label(
            self.content_frame,
            text="إدارة الواردات",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 20))

        # إطار للأزرار
        buttons_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        buttons_frame.pack(pady=20)

        # زر إضافة وارد جديد
        add_button = create_rtl_button(
            buttons_frame,
            text="+ إضافة وارد جديد",
            command=self.add_income,
            **BUTTON_STYLES['primary']
        )
        add_button.pack(side="left", padx=(0, 10))

        # زر استيراد من Excel
        import_button = create_rtl_button(
            buttons_frame,
            text="📂 استيراد من Excel",
            command=lambda: self.import_transactions_from_excel("income"),
            **BUTTON_STYLES['secondary']
        )
        import_button.pack(side="left")

        # قائمة الواردات
        income_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color=COLORS['bg_light'],
            corner_radius=10
        )
        income_frame.pack(fill="both", expand=True, padx=30, pady=(0, 30))

        # تحميل الواردات
        self.load_income_list(income_frame)

    def show_expense(self):
        """عرض صفحة المصروفات"""
        self.current_page = "expense"
        self.set_active_button("expense")
        self.clear_content()

        # عنوان الصفحة
        title_label = create_rtl_label(
            self.content_frame,
            text="إدارة المصروفات",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 20))

        # إطار للأزرار
        buttons_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        buttons_frame.pack(pady=20)

        # زر إضافة مصروف جديد
        add_button = create_rtl_button(
            buttons_frame,
            text="+ إضافة مصروف جديد",
            command=self.add_expense,
            **BUTTON_STYLES['primary']
        )
        add_button.pack(side="left", padx=(0, 10))

        # زر استيراد من Excel
        import_button = create_rtl_button(
            buttons_frame,
            text="📂 استيراد من Excel",
            command=lambda: self.import_transactions_from_excel("expense"),
            **BUTTON_STYLES['secondary']
        )
        import_button.pack(side="left")

        # قائمة المصروفات
        expense_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color=COLORS['bg_light'],
            corner_radius=10
        )
        expense_frame.pack(fill="both", expand=True, padx=30, pady=(0, 30))

        # تحميل المصروفات
        self.load_expense_list(expense_frame)

    def show_accounts(self):
        """عرض صفحة الحسابات"""
        self.current_page = "accounts"
        self.set_active_button("accounts")
        self.clear_content()

        # عنوان الصفحة
        title_label = create_rtl_label(
            self.content_frame,
            text="إدارة الحسابات",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 20))

        # زر إضافة حساب جديد
        add_button = create_rtl_button(
            self.content_frame,
            text="+ إضافة حساب جديد",
            command=self.add_account,
            **BUTTON_STYLES['primary']
        )
        add_button.pack(pady=20)

        # قائمة الحسابات
        accounts_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color=COLORS['bg_light'],
            corner_radius=10
        )
        accounts_frame.pack(fill="both", expand=True, padx=30, pady=(0, 30))

        # تحميل الحسابات
        self.load_accounts_list(accounts_frame)

    def show_transfers(self):
        """عرض صفحة التحويلات"""
        try:
            print("🔄 بدء عرض صفحة التحويلات...")
            self.current_page = "transfers"
            self.set_active_button("transfers")
            self.clear_content()
            print("✅ تم مسح المحتوى وتعيين الصفحة النشطة")

            # عنوان الصفحة
            title_label = create_rtl_label(
                self.content_frame,
                text="التحويلات بين الحسابات",
                font_size='title',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['title']
            )
            title_label.pack(pady=(30, 20))
            print("✅ تم إضافة عنوان الصفحة")

            # زر إضافة تحويل جديد
            add_button = create_rtl_button(
                self.content_frame,
                text="+ إضافة تحويل جديد",
                command=self.add_transfer,
                **BUTTON_STYLES['primary']
            )
            add_button.pack(pady=20)
            print("✅ تم إضافة زر إضافة تحويل")

            # قائمة التحويلات
            transfers_frame = ctk.CTkScrollableFrame(
                self.content_frame,
                fg_color=COLORS['bg_light'],
                corner_radius=10
            )
            transfers_frame.pack(fill="both", expand=True, padx=30, pady=(0, 30))
            print("✅ تم إنشاء إطار التحويلات")

            # تحميل التحويلات
            print("🔄 بدء تحميل قائمة التحويلات...")
            self.load_transfers_list(transfers_frame)
            print("✅ انتهى عرض صفحة التحويلات")

        except Exception as e:
            print(f"❌ خطأ في عرض صفحة التحويلات: {e}")
            import traceback
            traceback.print_exc()

            # عرض رسالة خطأ للمستخدم
            error_label = create_rtl_label(
                self.content_frame,
                text=f"حدث خطأ في عرض صفحة التحويلات: {str(e)}",
                font_size='body',
                text_color=COLORS.get('error', '#ff0000'),
                **ARABIC_TEXT_STYLES['label']
            )
            error_label.pack(pady=50)

    def refresh_transfers_page(self):
        """تحديث صفحة التحويلات بقوة"""
        try:
            print("🔄 تحديث صفحة التحويلات بقوة...")

            # التأكد من أننا في صفحة التحويلات
            if self.current_page == "transfers":
                print("✅ نحن في صفحة التحويلات، إعادة تحميلها...")
                self.show_transfers()
            else:
                print("⚠️ لسنا في صفحة التحويلات، التنقل إليها...")
                self.show_transfers()

            # إجبار تحديث النافذة
            self.window.update_idletasks()
            self.window.update()
            print("✅ تم إجبار تحديث النافذة")

        except Exception as e:
            print(f"❌ خطأ في تحديث صفحة التحويلات: {e}")
            import traceback
            traceback.print_exc()

    def force_refresh_current_page(self):
        """إجبار تحديث الصفحة الحالية"""
        try:
            print(f"🔄 إجبار تحديث الصفحة الحالية: {self.current_page}")

            if self.current_page == "transfers":
                self.show_transfers()
            elif self.current_page == "dashboard":
                self.show_dashboard()
            elif self.current_page == "accounts":
                self.show_accounts()
            elif self.current_page == "income":
                self.show_income()
            elif self.current_page == "expense":
                self.show_expense()
            else:
                print(f"⚠️ صفحة غير معروفة: {self.current_page}")

            # إجبار تحديث النافذة
            self.window.update_idletasks()
            self.window.update()

        except Exception as e:
            print(f"❌ خطأ في إجبار تحديث الصفحة: {e}")
            import traceback
            traceback.print_exc()



    def show_reports(self):
        """عرض صفحة التقارير"""
        self.current_page = "reports"
        self.set_active_button("reports")
        self.clear_content()

        # عنوان الصفحة
        title_label = create_rtl_label(
            self.content_frame,
            text="التقارير والإحصائيات",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 20))

        # إطار التقارير
        reports_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color=COLORS['bg_light'],
            corner_radius=10
        )
        reports_frame.pack(fill="both", expand=True, padx=30, pady=(0, 30))

        # تحميل التقارير
        self.load_reports(reports_frame)

    def load_income_list(self, parent):
        """تحميل قائمة الواردات (محسن مع التخزين المؤقت والتحميل التدريجي)"""

        # التحقق من التخزين المؤقت
        cache_key = f"income_list_{auth_manager.current_user['id']}"
        cached_data = performance_cache.get(cache_key)
        if cached_data is not None:
            self._render_income_list(parent, cached_data)
            return

        try:
            user_id = auth_manager.current_user['id']

            # استعلام محسن مع LIMIT للصفحة الأولى
            query = """
                SELECT t.*, a.name as account_name, c.symbol as currency_symbol
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = %s AND t.transaction_type = 'income'
                ORDER BY t.transaction_date DESC, t.created_at DESC
                LIMIT 20
            """
            incomes = db.execute_query(query, (user_id,))

            # حفظ في التخزين المؤقت
            performance_cache.set(cache_key, incomes)

            self._render_income_list(parent, incomes)

        except Exception as e:
            print(f"خطأ في تحميل الواردات: {e}")

    def _render_income_list(self, parent, incomes):
        """عرض قائمة الواردات"""
        if not incomes:
            no_data_label = create_rtl_label(
                parent,
                text="لا توجد واردات حتى الآن\nاضغط على 'إضافة وارد جديد' لإضافة وارد",
                font_size='header',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['title']
            )
            no_data_label.pack(pady=50)
        else:
            # عرض أول 10 عناصر فقط للسرعة
            for income in incomes[:10]:
                self.create_transaction_card(parent, income, "income")

            # إضافة زر "عرض المزيد" إذا كان هناك المزيد
            if len(incomes) > 10:
                load_more_btn = ctk.CTkButton(
                    parent,
                    text="عرض المزيد",
                    command=lambda: self._load_more_incomes(parent, incomes[10:])
                )
                load_more_btn.pack(pady=10)

    def _load_more_incomes(self, parent, remaining_incomes):
        """تحميل المزيد من الواردات"""
        # عرض 10 عناصر إضافية
        for income in remaining_incomes[:10]:
            self.create_transaction_card(parent, income, "income")

        # إزالة الزر القديم
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget.cget("text") == "عرض المزيد":
                widget.destroy()
                break

        # إضافة زر جديد إذا كان هناك المزيد
        if len(remaining_incomes) > 10:
            load_more_btn = ctk.CTkButton(
                parent,
                text="عرض المزيد",
                command=lambda: self._load_more_incomes(parent, remaining_incomes[10:])
            )
            load_more_btn.pack(pady=10)

    def create_transaction_card(self, parent, transaction, transaction_type):
        """إنشاء بطاقة معاملة"""
        card = ctk.CTkFrame(
            parent,
            **CARD_STYLES['elevated']
        )
        card.pack(fill="x", padx=20, pady=10)

        # معلومات المعاملة
        info_frame = ctk.CTkFrame(card, fg_color="transparent")
        info_frame.pack(fill="x", padx=20, pady=15)

        # الصف الأول: المبلغ والتاريخ
        top_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        top_frame.pack(fill="x")

        # المبلغ
        amount_color = COLORS['success'] if transaction_type == 'income' else COLORS['error']
        amount_prefix = "+" if transaction_type == 'income' else "-"
        amount_label = create_rtl_label(
            top_frame,
            text=f"{amount_prefix}{transaction['amount']:,.2f} {transaction['currency_symbol']}",
            font_size='header',
            text_color=amount_color,
            **ARABIC_TEXT_STYLES['label']
        )
        amount_label.pack(side="left")

        # التاريخ
        date_label = create_rtl_label(
            top_frame,
            text=str(transaction['transaction_date']),
            font_size='small',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        date_label.pack(side="right")

        # الصف الثاني: الوصف والحساب
        if transaction['description']:
            desc_label = create_rtl_label(
                info_frame,
                text=transaction['description'],
                font_size='body',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            desc_label.pack(anchor="w", pady=(5, 0))

        # الحساب
        account_label = create_rtl_label(
            info_frame,
            text=f"الحساب: {transaction['account_name']}",
            font_size='small',
            text_color=COLORS['text_muted'],
            **ARABIC_TEXT_STYLES['label']
        )
        account_label.pack(anchor="w", pady=(5, 0))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(card, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(10, 15))

        # زر التعديل
        edit_button = create_rtl_button(
            buttons_frame,
            text="✏️ تعديل",
            command=lambda: self.show_edit_transaction_dialog(transaction, transaction_type),
            **BUTTON_STYLES['primary']
        )
        edit_button.pack(side="left", padx=(0, 5))

        # زر الحذف
        delete_button = create_rtl_button(
            buttons_frame,
            text="🗑️ حذف",
            command=lambda: self.delete_transaction(transaction['id'], transaction_type),
            **BUTTON_STYLES['danger']
        )
        delete_button.pack(side="right")

    def _clear_transaction_cache(self):
        """مسح التخزين المؤقت للمعاملات"""
        user_id = auth_manager.current_user['id']
        performance_cache.clear(f"income_list_{user_id}")
        performance_cache.clear(f"expense_list_{user_id}")
        performance_cache.clear(f"currency_balances_{user_id}")
        performance_cache.clear(f"recent_transactions_{user_id}")

    def add_income(self):
        """إضافة وارد جديد"""
        self.show_add_transaction_dialog("income")
        # مسح التخزين المؤقت بعد إضافة معاملة جديدة
        self._clear_transaction_cache()

    def load_expense_list(self, parent):
        """تحميل قائمة المصروفات (محسن مع التخزين المؤقت والتحميل التدريجي)"""

        # التحقق من التخزين المؤقت
        cache_key = f"expense_list_{auth_manager.current_user['id']}"
        cached_data = performance_cache.get(cache_key)
        if cached_data is not None:
            self._render_expense_list(parent, cached_data)
            return

        try:
            user_id = auth_manager.current_user['id']

            # استعلام محسن مع LIMIT للصفحة الأولى
            query = """
                SELECT t.*, a.name as account_name, c.symbol as currency_symbol
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = %s AND t.transaction_type = 'expense'
                ORDER BY t.transaction_date DESC, t.created_at DESC
                LIMIT 20
            """
            expenses = db.execute_query(query, (user_id,))

            # حفظ في التخزين المؤقت
            performance_cache.set(cache_key, expenses)

            self._render_expense_list(parent, expenses)

        except Exception as e:
            print(f"خطأ في تحميل المصروفات: {e}")

    def _render_expense_list(self, parent, expenses):
        """عرض قائمة المصروفات"""
        if not expenses:
            no_data_label = create_rtl_label(
                parent,
                text="لا توجد مصروفات حتى الآن\nاضغط على 'إضافة مصروف جديد' لإضافة مصروف",
                font_size='header',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['title']
            )
            no_data_label.pack(pady=50)
        else:
            # عرض أول 10 عناصر فقط للسرعة
            for expense in expenses[:10]:
                self.create_transaction_card(parent, expense, "expense")

            # إضافة زر "عرض المزيد" إذا كان هناك المزيد
            if len(expenses) > 10:
                load_more_btn = ctk.CTkButton(
                    parent,
                    text="عرض المزيد",
                    command=lambda: self._load_more_expenses(parent, expenses[10:])
                )
                load_more_btn.pack(pady=10)

    def _load_more_expenses(self, parent, remaining_expenses):
        """تحميل المزيد من المصروفات"""
        # عرض 10 عناصر إضافية
        for expense in remaining_expenses[:10]:
            self.create_transaction_card(parent, expense, "expense")

        # إزالة الزر القديم
        for widget in parent.winfo_children():
            if isinstance(widget, ctk.CTkButton) and widget.cget("text") == "عرض المزيد":
                widget.destroy()
                break

        # إضافة زر جديد إذا كان هناك المزيد
        if len(remaining_expenses) > 10:
            load_more_btn = ctk.CTkButton(
                parent,
                text="عرض المزيد",
                command=lambda: self._load_more_expenses(parent, remaining_expenses[10:])
            )
            load_more_btn.pack(pady=10)

    def add_expense(self):
        """إضافة مصروف جديد"""
        self.show_add_transaction_dialog("expense")

    def load_accounts_list(self, parent):
        """تحميل قائمة الحسابات مع الأرصدة متعددة العملات"""
        # مسح المحتوى القديم
        for widget in parent.winfo_children():
            widget.destroy()

        try:
            user_id = auth_manager.current_user['id']

            # استخدام نموذج Account المحدث
            from database.models import Account
            accounts = Account.get_by_user(user_id)

            if not accounts:
                no_data_label = create_rtl_label(
                    parent,
                    text="لا توجد حسابات حتى الآن\nاضغط على 'إضافة حساب جديد' لإنشاء حساب",
                    font_size='header',
                    text_color=COLORS['text_muted'],
                    **ARABIC_TEXT_STYLES['title']
                )
                no_data_label.pack(pady=50)
            else:
                for account in accounts:
                    self.create_account_card(parent, account)

        except Exception as e:
            print(f"خطأ في تحميل الحسابات: {e}")
            import traceback
            traceback.print_exc()
            error_label = ctk.CTkLabel(
                parent,
                text=f"خطأ في تحميل الحسابات: {str(e)}",
                font=ctk.CTkFont(size=16),
                text_color=COLORS['error']
            )
            error_label.pack(pady=50)

    def create_account_card(self, parent, account):
        """إنشاء بطاقة حساب تعرض جميع أرصدة العملات"""
        card = ctk.CTkFrame(
            parent,
            **CARD_STYLES['elevated']
        )
        card.pack(fill="x", padx=20, pady=10)

        # معلومات الحساب
        info_frame = ctk.CTkFrame(card, fg_color="transparent")
        info_frame.pack(fill="x", padx=20, pady=15)

        # اسم الحساب ونوعه
        name_label = create_rtl_label(
            info_frame,
            text=account['name'],
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        name_label.pack(anchor="w")

        # معرف الحساب
        id_label = create_rtl_label(
            info_frame,
            text=f"معرف الحساب: {account['id']}",
            font_size='small',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        id_label.pack(anchor="w")

        # إطار عرض الأرصدة
        balances_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        balances_frame.pack(anchor="w", fill="x", pady=(5, 0))

        if not account.get('balances'):
            balance_label = create_rtl_label(
                balances_frame,
                text="الرصيد: 0.00",
                font_size='body',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['label']
            )
            balance_label.pack(anchor="w")
        else:
            # عرض كل رصيد في سطر منفصل
            for balance in account['balances']:
                balance_color = COLORS['success'] if balance['balance'] >= 0 else COLORS['error']
                balance_label = create_rtl_label(
                    balances_frame,
                    text=f"- {balance['balance']:,.2f} {balance['symbol']}",
                    font_size='body',
                    text_color=balance_color,
                    **ARABIC_TEXT_STYLES['label']
                )
                balance_label.pack(anchor="w")

        # الوصف إذا وجد
        if account.get('description'):
            desc_label = create_rtl_label(
                info_frame,
                text=account['description'],
                font_size='small',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['label']
            )
            desc_label.pack(anchor="w", pady=(2, 0))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=(10, 0))

        # زر التعديل
        edit_button = create_rtl_button(
            buttons_frame,
            text="✏️ تعديل",
            command=lambda: self.show_edit_account_dialog(account),
            **BUTTON_STYLES['primary']
        )
        edit_button.pack(side="left", padx=(0, 5))

        # زر التفعيل/إلغاء التفعيل
        if account.get('is_active', True):
            toggle_button = create_rtl_button(
                buttons_frame,
                text="🚫 إلغاء تفعيل",
                command=lambda: self.toggle_account_status(account['id'], False),
                **BUTTON_STYLES['secondary']
            )
        else:
            toggle_button = create_rtl_button(
                buttons_frame,
                text="✅ تفعيل",
                command=lambda: self.toggle_account_status(account['id'], True),
                **BUTTON_STYLES['primary']
            )
        toggle_button.pack(side="left", padx=5)

        # زر الحذف
        delete_button = create_rtl_button(
            buttons_frame,
            text="🗑️ حذف",
            command=lambda: self.delete_account(account['id']),
            **BUTTON_STYLES['danger']
        )
        delete_button.pack(side="right")

    def add_account(self):
        """إضافة حساب جديد"""
        self.show_add_account_dialog()

    def show_add_account_dialog(self):
        """عرض نافذة إضافة حساب جديد"""
        dialog = ctk.CTkToplevel(self.window)
        dialog.title("إضافة حساب جديد")
        dialog.geometry("500x700")  # زيادة الارتفاع
        dialog.resizable(False, False)
        dialog.transient(self.window)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (700 // 2)
        dialog.geometry(f"500x700+{x}+{y}")

        # عنوان النافذة
        title_label = create_rtl_label(
            dialog,
            text="إضافة حساب جديد",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 30))

        # إطار النموذج
        form_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        form_frame.pack(fill="x", padx=30, pady=(0, 10))

        # اسم الحساب
        name_label = create_rtl_label(
            form_frame,
            text="اسم الحساب:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        name_label.pack(anchor="w", pady=(0, 5))

        name_entry = create_rtl_entry(
            form_frame,
            placeholder_text="مثال: حساب الراجحي",
            height=40
        )
        name_entry.pack(fill="x", pady=(0, 15))



        # الأرصدة الابتدائية متعددة العملات
        balances_label = create_rtl_label(
            form_frame,
            text="الأرصدة الابتدائية (اختياري):",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        balances_label.pack(anchor="w", pady=(0, 5))

        # إطار الأرصدة
        balances_frame = ctk.CTkScrollableFrame(
            form_frame,
            height=200,
            fg_color=COLORS['bg_light']
        )
        balances_frame.pack(fill="x", pady=(0, 15))

        # الحصول على العملات المدعومة
        currencies = db.execute_query("SELECT id, code, name, symbol FROM currencies WHERE is_active = 1 ORDER BY name")

        # إنشاء حقول إدخال لكل عملة
        self.currency_entries = {}
        for currency in currencies:
            currency_frame = ctk.CTkFrame(balances_frame, fg_color="transparent")
            currency_frame.pack(fill="x", pady=5)

            # تسمية العملة مع RTL
            currency_label = create_rtl_label(
                currency_frame,
                text=f"{currency['name']} ({currency['symbol']}):",
                font_size='body',
                **ARABIC_TEXT_STYLES['label']
            )
            currency_label.pack(side="right", padx=(10, 0))

            # حقل إدخال الرصيد
            balance_entry = create_rtl_entry(
                currency_frame,
                placeholder_text="0.00",
                width=150,
                height=35
            )
            balance_entry.pack(side="left", padx=(0, 10))

            self.currency_entries[currency['id']] = balance_entry

        # الوصف
        desc_label = create_rtl_label(
            form_frame,
            text="الوصف (اختياري):",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="w", pady=(0, 5))

        from config.fonts import create_rtl_textbox
        desc_entry = create_rtl_textbox(
            form_frame,
            height=60  # تقليل الارتفاع
        )
        desc_entry.pack(fill="x", pady=(0, 5))

        # أزرار الحفظ والإلغاء - تتوسط النافذة
        buttons_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        buttons_frame.pack(pady=(20, 30))

        save_button = create_rtl_button(
            buttons_frame,
            text="حفظ",
            command=lambda: self.save_new_account_multi_currency(
                dialog, name_entry, desc_entry
            ),
            **BUTTON_STYLES['primary']
        )
        save_button.pack(side="left", padx=(0, 10))

        cancel_button = create_rtl_button(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="left")

    def save_new_account(self, dialog, name_entry, currency_combo, balance_entry, desc_entry):
        """حفظ الحساب الجديد (بدون نوع حساب)"""
        try:
            # التحقق من البيانات
            name = name_entry.get().strip()
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحساب")
                return

            # استخراج معرف العملة
            currency_text = currency_combo.get()
            currency_id = int(currency_text.split(' - ')[0]) if currency_text else 1

            # الرصيد الابتدائي
            try:
                initial_balance = float(balance_entry.get() or "0")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رصيد صحيح")
                return

            description = desc_entry.get("1.0", "end-1c").strip()

            # إدراج الحساب في قاعدة البيانات باستخدام النموذج الصحيح
            user_id = auth_manager.current_user['id']

            # استخدام نموذج Account للإنشاء مع تحديد العملة
            from database.models import Account
            result = Account.create(
                user_id=user_id,
                name=name,
                currency_id=currency_id,
                description=description
            )

            # إضافة الرصيد الابتدائي إذا كان أكبر من صفر
            if result > 0 and initial_balance > 0:
                Account.update_balance_for_currency(result, currency_id, initial_balance)

            if result > 0:
                messagebox.showinfo("نجح", "تم إضافة الحساب بنجاح")
                dialog.destroy()
                # إعادة تحميل صفحة الحسابات
                self.show_accounts()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الحساب")

        except Exception as e:
            print(f"خطأ في حفظ الحساب: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def save_new_account_multi_currency(self, dialog, name_entry, desc_entry):
        """حفظ الحساب الجديد مع دعم متعدد العملات"""
        try:
            # التحقق من البيانات
            name = name_entry.get().strip()
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحساب")
                return

            description = desc_entry.get("1.0", "end-1c").strip()

            # إنشاء الحساب
            user_id = auth_manager.current_user['id']
            from database.models import Account

            account_id = Account.create(
                user_id=user_id,
                name=name,
                description=description
            )

            if account_id > 0:
                # إضافة الأرصدة الابتدائية للعملات المختلفة
                balances_added = 0
                for currency_id, entry in self.currency_entries.items():
                    try:
                        balance_text = entry.get().strip()
                        if balance_text and float(balance_text) > 0:
                            initial_balance = float(balance_text)
                            Account.add_currency_balance(account_id, currency_id, initial_balance)
                            balances_added += 1
                    except ValueError:
                        continue  # تجاهل القيم غير الصحيحة

                success_message = "تم إضافة الحساب بنجاح"
                if balances_added > 0:
                    success_message += f" مع {balances_added} عملة"

                messagebox.showinfo("نجح", success_message)
                dialog.destroy()
                # إعادة تحميل صفحة الحسابات
                self.show_accounts()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الحساب")

        except Exception as e:
            print(f"خطأ في حفظ الحساب متعدد العملات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def get_or_create_account_type(self, type_name):
        """الحصول على معرف نوع الحساب أو إنشاء نوع جديد"""
        try:
            # البحث عن نوع الحساب الموجود
            query = "SELECT id FROM account_types WHERE name = %s"
            result = db.execute_query(query, (type_name,))

            if result:
                return result[0]['id']

            # التحقق من وجود عمود is_active في الجدول
            try:
                # محاولة إدراج مع عمود is_active
                insert_query = """
                    INSERT INTO account_types (name, description, icon, is_active)
                    VALUES (%s, %s, %s, %s)
                """
                new_id = db.execute_insert(insert_query, (
                    type_name,
                    f"نوع حساب: {type_name}",
                    "💼",  # أيقونة افتراضية
                    True
                ))
            except Exception:
                # إذا فشل، جرب بدون عمود is_active
                insert_query = """
                    INSERT INTO account_types (name, description, icon)
                    VALUES (%s, %s, %s)
                """
                new_id = db.execute_insert(insert_query, (
                    type_name,
                    f"نوع حساب: {type_name}",
                    "💼"  # أيقونة افتراضية
                ))

            return new_id if new_id > 0 else None

        except Exception as e:
            print(f"خطأ في معالجة نوع الحساب: {e}")
            return None

    def show_add_transaction_dialog(self, transaction_type):
        """عرض نافذة إضافة معاملة (وارد أو مصروف) مع إطار قابل للتمرير"""
        title = "إضافة وارد جديد" if transaction_type == "income" else "إضافة مصروف جديد"

        dialog = ctk.CTkToplevel(self.window)
        dialog.title(title)
        dialog.geometry("500x700")
        dialog.resizable(False, False)
        dialog.transient(self.window)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (700 // 2)
        dialog.geometry(f"500x700+{x}+{y}")

        # عنوان النافذة
        title_label = create_rtl_label(
            dialog,
            text=title,
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 15))

        # إطار قابل للتمرير ليحتوي على حقول الإدخال
        scrollable_frame = ctk.CTkScrollableFrame(dialog, fg_color="transparent")
        scrollable_frame.pack(fill="both", expand=True, padx=30, pady=0)

        # المبلغ
        amount_label = create_rtl_label(
            scrollable_frame,
            text="المبلغ:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        amount_label.pack(anchor="w", pady=(10, 5))
        amount_entry = create_rtl_entry(
            scrollable_frame,
            placeholder_text="0.00",
            height=40
        )
        amount_entry.pack(fill="x", pady=(0, 15))

        # الحساب
        account_label = create_rtl_label(
            scrollable_frame,
            text="الحساب:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        account_label.pack(anchor="w", pady=(0, 5))
        user_id = auth_manager.current_user['id']
        accounts = db.execute_query(
            "SELECT id, name FROM accounts WHERE user_id = %s AND is_active = TRUE", (user_id,)
        )
        account_options = [f"{acc['id']} - {acc['name']}" for acc in accounts] if accounts else []
        if not account_options:
            messagebox.showerror("خطأ", "لا توجد حسابات متاحة. يرجى إضافة حساب أولاً.")
            dialog.destroy()
            return
        account_combo = ctk.CTkComboBox(
            scrollable_frame, values=account_options, height=40, font=ctk.CTkFont(size=14)
        )
        account_combo.pack(fill="x", pady=(0, 15))
        account_combo.set(account_options[0])

        # العملة
        currency_label = create_rtl_label(
            scrollable_frame,
            text="العملة:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        currency_label.pack(anchor="w", pady=(0, 5))
        currencies = db.execute_query("SELECT id, name, symbol, code FROM currencies WHERE is_active = TRUE ORDER BY name")
        currency_options = [f"{c['id']} - {c['name']} ({c['symbol']})" for c in currencies] if currencies else ["1 - ريال سعودي (ر.س)"]
        currency_combo = ctk.CTkComboBox(
            scrollable_frame, values=currency_options, height=40, font=ctk.CTkFont(size=14)
        )
        currency_combo.pack(fill="x", pady=(0, 15))
        currency_combo.set(currency_options[0] if currency_options else "")

        # التاريخ
        date_label = create_rtl_label(
            scrollable_frame,
            text="التاريخ:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        date_label.pack(anchor="w", pady=(0, 5))
        date_entry = create_rtl_entry(
            scrollable_frame,
            placeholder_text="YYYY-MM-DD",
            height=40
        )
        date_entry.pack(fill="x", pady=(0, 15))
        from datetime import date
        date_entry.insert(0, str(date.today()))

        # الوصف
        desc_label = create_rtl_label(
            scrollable_frame,
            text="الوصف (اختياري):",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="w", pady=(0, 5))
        desc_entry = ctk.CTkTextbox(
            scrollable_frame,
            height=80,
            font=ctk.CTkFont(size=14)
        )
        desc_entry.pack(fill="x", pady=(0, 15))

        # أزرار الحفظ والإلغاء - خارج الإطار القابل للتمرير
        buttons_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        buttons_frame.pack(pady=(10, 20))

        save_button = create_rtl_button(
            buttons_frame,
            text="حفظ",
            command=lambda: self.save_new_transaction(
                dialog, transaction_type, amount_entry, account_combo,
                currency_combo, date_entry, desc_entry
            ),
            **BUTTON_STYLES['primary']
        )
        save_button.pack(side="left", padx=(0, 10))

        cancel_button = create_rtl_button(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="left")

    def save_new_transaction(self, dialog, transaction_type, amount_entry, account_combo,
                           currency_combo, date_entry, desc_entry):
        """حفظ المعاملة الجديدة"""
        try:
            # التحقق من البيانات
            try:
                amount = float(amount_entry.get() or "0")
                if amount <= 0:
                    messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح أكبر من صفر")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
                return

            # استخراج معرف الحساب
            account_text = account_combo.get()
            account_id = int(account_text.split(' - ')[0]) if account_text else None
            if not account_id:
                messagebox.showerror("خطأ", "يرجى اختيار حساب")
                return

            # استخراج معرف العملة
            currency_text = currency_combo.get()
            currency_id = int(currency_text.split(' - ')[0]) if currency_text else 1

            # للمصروفات: التحقق من وجود رصيد كافي في العملة المحددة
            if transaction_type == 'expense':
                from database.models import Account
                current_balance = Account.get_currency_balance(account_id, currency_id)

                if current_balance < amount:
                    # الحصول على معلومات العملة
                    currency_info = db.execute_query("SELECT name, symbol FROM currencies WHERE id = %s", (currency_id,))
                    currency_name = currency_info[0]['name'] if currency_info else "غير معروف"
                    currency_symbol = currency_info[0]['symbol'] if currency_info else ""

                    messagebox.showerror(
                        "رصيد غير كافي",
                        f"الرصيد الحالي في {currency_name}: {current_balance} {currency_symbol}\n"
                        f"المبلغ المطلوب: {amount} {currency_symbol}\n\n"
                        f"يرجى تقليل المبلغ أو إضافة رصيد للحساب أولاً."
                    )
                    return

            # التاريخ
            transaction_date = date_entry.get().strip()
            if not transaction_date:
                messagebox.showerror("خطأ", "يرجى إدخال التاريخ")
                return

            description = desc_entry.get("1.0", "end-1c").strip()

            # إدراج المعاملة في قاعدة البيانات باستخدام نموذج Transaction
            user_id = auth_manager.current_user['id']

            # استخدام النموذج الجديد لإنشاء المعاملة
            from database.models import Transaction
            result = Transaction.create(
                user_id=user_id,
                account_id=account_id,
                currency_id=currency_id,
                transaction_type=transaction_type,
                amount=amount,
                description=description or "",
                transaction_date=transaction_date
            )

            if result > 0:

                messagebox.showinfo("نجح", f"تم إضافة {'الوارد' if transaction_type == 'income' else 'المصروف'} بنجاح")
                dialog.destroy()

                # إعادة تحميل الصفحة المناسبة
                if transaction_type == "income":
                    self.show_income()
                else:
                    self.show_expense()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة المعاملة")

        except Exception as e:
            print(f"خطأ في حفظ المعاملة: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def load_transfers_list(self, parent):
        """تحميل قائمة التحويلات مع تفاصيل العملات المتعددة"""
        try:
            print("🔄 بدء تحميل قائمة التحويلات...")

            # مسح المحتوى القديم
            for widget in parent.winfo_children():
                widget.destroy()
            print("✅ تم مسح المحتوى القديم")

            user_id = auth_manager.current_user['id']
            print(f"👤 معرف المستخدم: {user_id}")

            # استخدام النموذج الجديد لجلب التحويلات
            print("🔍 جلب التحويلات من قاعدة البيانات...")
            transfers = Transfer.get_by_user(user_id)
            print(f"📊 عدد التحويلات المسترجعة: {len(transfers) if transfers else 0}")

            if not transfers:
                print("📝 لا توجد تحويلات - عرض رسالة فارغة")
                no_data_label = create_rtl_label(
                    parent,
                    text="لا توجد تحويلات حتى الآن\nاضغط على 'إضافة تحويل جديد' لإضافة تحويل",
                    font_size='header',
                    text_color=COLORS['text_muted'],
                    **ARABIC_TEXT_STYLES['title']
                )
                no_data_label.pack(pady=50)
                print("✅ تم عرض رسالة عدم وجود تحويلات")
            else:
                print("📋 إنشاء بطاقات التحويلات...")
                for i, transfer in enumerate(transfers):
                    print(f"  🔄 إنشاء بطاقة التحويل {i+1}: ID {transfer['id']}")
                    self.create_transfer_card(parent, transfer)
                print("✅ تم إنشاء جميع بطاقات التحويلات")

            print("✅ انتهى تحميل قائمة التحويلات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحميل التحويلات: {e}")
            import traceback
            traceback.print_exc()

            # عرض رسالة خطأ للمستخدم
            error_label = create_rtl_label(
                parent,
                text=f"حدث خطأ في تحميل التحويلات: {str(e)}",
                font_size='body',
                text_color=COLORS.get('error', '#ff0000'),
                **ARABIC_TEXT_STYLES['label']
            )
            error_label.pack(pady=50)

    def create_transfer_card(self, parent, transfer):
        """إنشاء بطاقة تحويل تعرض المبالغ والعملات من وإلى"""
        try:
            print(f"🔄 إنشاء بطاقة التحويل ID: {transfer.get('id', 'غير محدد')}")

            card = ctk.CTkFrame(
                parent,
                **CARD_STYLES['elevated']
            )
            card.pack(fill="x", padx=20, pady=10)

            # معلومات التحويل
            info_frame = ctk.CTkFrame(card, fg_color="transparent")
            info_frame.pack(fill="x", padx=20, pady=15)

            # الصف الأول: المبالغ والتاريخ
            top_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
            top_frame.pack(fill="x")

            # عرض تفاصيل المبالغ والعملات مع معالجة الأخطاء
            try:
                # التأكد من وجود البيانات وتنسيقها بأمان
                from_amount = float(transfer.get('from_amount', 0))
                to_amount = float(transfer.get('to_amount', 0))
                from_symbol = transfer.get('from_currency_symbol', transfer.get('from_currency_code', ''))
                to_symbol = transfer.get('to_currency_symbol', transfer.get('to_currency_code', ''))

                from_text = f"{from_amount:,.2f} {from_symbol}"
                to_text = f"{to_amount:,.2f} {to_symbol}"

                print(f"🔄 تنسيق بطاقة التحويل: {from_text} -> {to_text}")

            except (ValueError, TypeError, KeyError) as e:
                print(f"⚠️ خطأ في تنسيق المبالغ: {e}")
                # استخدام قيم افتراضية آمنة
                from_text = f"{transfer.get('from_amount', 'غير محدد')} {transfer.get('from_currency_code', '')}"
                to_text = f"{transfer.get('to_amount', 'غير محدد')} {transfer.get('to_currency_code', '')}"

            amount_label = create_rtl_label(
                top_frame,
                text=f"{from_text}  ->  {to_text}",
                font_size='header',
                text_color=COLORS['info'],
                **ARABIC_TEXT_STYLES['label']
            )
            amount_label.pack(side="left")

            # التاريخ مع معالجة آمنة
            try:
                transfer_date = transfer.get('transfer_date', 'غير محدد')
                date_text = str(transfer_date)
            except Exception as e:
                print(f"⚠️ خطأ في تنسيق التاريخ: {e}")
                date_text = "غير محدد"

            date_label = create_rtl_label(
                top_frame,
                text=date_text,
                font_size='small',
                text_color=COLORS['text_secondary'],
                **ARABIC_TEXT_STYLES['label']
            )
            date_label.pack(side="right")

            # الصف الثاني: من وإلى الحسابات مع معالجة آمنة
            try:
                from_account = transfer.get('from_account_name', 'غير محدد')
                to_account = transfer.get('to_account_name', 'غير محدد')
                transfer_text = f"من: {from_account}   |   إلى: {to_account}"
            except Exception as e:
                print(f"⚠️ خطأ في تنسيق معلومات الحسابات: {e}")
                transfer_text = "معلومات الحسابات غير متاحة"

            transfer_info = create_rtl_label(
                info_frame,
                text=transfer_text,
                font_size='body',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            transfer_info.pack(anchor="w", pady=(5, 0))

            # الوصف إذا وجد
            if transfer.get('description'):
                desc_label = create_rtl_label(
                    info_frame,
                    text=transfer['description'],
                    font_size='small',
                    text_color=COLORS['text_muted'],
                    **ARABIC_TEXT_STYLES['label']
                )
                desc_label.pack(anchor="w", pady=(2, 0))

            # إطار الأزرار
            buttons_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
            buttons_frame.pack(fill="x", pady=(10, 0))

            # زر الحذف (التعديل أكثر تعقيدًا ويمكن إضافته لاحقًا)
            delete_button = create_rtl_button(
                buttons_frame,
                text="🗑️ حذف",
                command=lambda: self.delete_transfer(transfer.get('id', 0)),
                **BUTTON_STYLES['danger']
            )
            delete_button.pack(side="right")

            print(f"✅ تم إنشاء بطاقة التحويل ID: {transfer.get('id', 'غير محدد')} بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء بطاقة التحويل: {e}")
            import traceback
            traceback.print_exc()

            # إنشاء بطاقة خطأ بسيطة
            try:
                error_card = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
                error_card.pack(fill="x", padx=20, pady=10)

                error_label = create_rtl_label(
                    error_card,
                    text=f"خطأ في عرض التحويل: {str(e)}",
                    font_size='body',
                    text_color=COLORS.get('error', '#ff0000'),
                    **ARABIC_TEXT_STYLES['label']
                )
                error_label.pack(pady=15)
            except:
                print("❌ فشل في إنشاء بطاقة الخطأ أيضاً")

    def add_transfer(self):
        """إضافة تحويل جديد (متعدد العملات)"""
        self.show_add_transfer_dialog()

    def show_add_transfer_dialog(self):
        """عرض نافذة إضافة تحويل جديد (متعددة العملات)"""
        dialog = ctk.CTkToplevel(self.window)
        dialog.title("إضافة تحويل جديد")
        dialog.geometry("600x550")
        dialog.resizable(False, False)
        dialog.transient(self.window)
        dialog.grab_set()

        # --- Data --- #
        accounts = Account.get_by_user(auth_manager.current_user['id'])
        all_currencies = Currency.get_all()

        account_map = {f"{acc['id']} - {acc['name']}": acc for acc in accounts}
        currency_map = {f"{c['id']} - {c['name']} ({c['symbol']})": c for c in all_currencies}

        if len(accounts) < 1:
            messagebox.showerror("خطأ", "يجب أن يكون لديك حساب واحد على الأقل لإجراء تحويل.")
            dialog.destroy()
            return

        # --- UI --- #
        main_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # --- From Section ---
        from_frame = ctk.CTkFrame(main_frame)
        from_frame.pack(fill="x", pady=(0, 10))

        from_title = create_rtl_label(
            from_frame,
            text="من",
            font_size='header',
            **ARABIC_TEXT_STYLES['title']
        )
        from_title.pack(pady=5)

        from_account_label = create_rtl_label(
            from_frame,
            text="الحساب المصدر:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        from_account_label.pack()
        from_account_combo = ctk.CTkComboBox(from_frame, values=list(account_map.keys()))
        from_account_combo.pack(fill="x", padx=10)

        from_currency_label = create_rtl_label(
            from_frame,
            text="العملة والمبلغ:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        from_currency_label.pack()
        from_currency_combo = ctk.CTkComboBox(from_frame, values=[])
        from_currency_combo.pack(side="right", padx=10)
        from_amount_entry = create_rtl_entry(from_frame, placeholder_text="المبلغ المرسل")
        from_amount_entry.pack(side="left", expand=True, padx=10)

        # --- To Section ---
        to_frame = ctk.CTkFrame(main_frame)
        to_frame.pack(fill="x", pady=10)

        to_title = create_rtl_label(
            to_frame,
            text="إلى",
            font_size='header',
            **ARABIC_TEXT_STYLES['title']
        )
        to_title.pack(pady=5)

        to_account_label = create_rtl_label(
            to_frame,
            text="الحساب الهدف:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        to_account_label.pack()
        to_account_combo = ctk.CTkComboBox(to_frame, values=list(account_map.keys()))
        to_account_combo.pack(fill="x", padx=10)

        # --- Other fields ---
        # ... (Date, Description, etc.)

        # --- Logic --- #
        def update_from_currencies(*args):
            selected_key = from_account_combo.get()
            account = account_map.get(selected_key)
            if account:
                balances = account.get('balances', [])
                currency_options = [f"{b['currency_id']} - {b['code']} (الرصيد: {b['balance']})" for b in balances]
                from_currency_combo.configure(values=currency_options)
                from_currency_combo.set(currency_options[0] if currency_options else "")

        from_account_combo.configure(command=update_from_currencies)
        update_from_currencies() # Initial call

        # --- Buttons --- #
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(pady=20)

        save_button = create_rtl_button(
            buttons_frame,
            text="✓ حفظ التحويل",
            command=lambda: self.save_new_transfer(
                dialog, from_account_combo, from_currency_combo, from_amount_entry,
                to_account_combo, account_map, currency_map
            ),
            **BUTTON_STYLES['primary']
        )
        save_button.pack(side="left", padx=(0, 10))

        cancel_button = create_rtl_button(
            buttons_frame,
            text="✗ إلغاء",
            command=dialog.destroy,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="left")

    def save_new_transfer(self, dialog, from_account_combo, from_currency_combo, from_amount_entry,
                          to_account_combo, account_map, currency_map):
        """حفظ التحويل الجديد (متعدد العملات)"""
        try:
            # --- Data Extraction and Validation ---
            from_account_id = int(from_account_combo.get().split(' - ')[0])
            from_currency_id = int(from_currency_combo.get().split(' - ')[0])
            from_amount = Decimal(from_amount_entry.get())

            to_account_id = int(to_account_combo.get().split(' - ')[0])
            to_currency_id = from_currency_id  # استخدام نفس عملة المرسل
            to_amount = from_amount # استخدام نفس المبلغ المرسل

            if from_account_id == to_account_id and from_currency_id == to_currency_id:
                messagebox.showerror("خطأ", "لا يمكن التحويل إلى نفس الحساب وبنفس العملة.")
                return

            # --- Call the new Transfer.create method ---
            transfer_id = Transfer.create(
                user_id=auth_manager.current_user['id'],
                from_account_id=from_account_id,
                to_account_id=to_account_id,
                from_amount=from_amount,
                from_currency_id=from_currency_id,
                to_amount=to_amount,
                to_currency_id=to_currency_id,
                description="تحويل", # Placeholder
                transfer_date=datetime.now().date()
            )

            if transfer_id > 0:
                print(f"✅ تم إنشاء التحويل بنجاح! ID: {transfer_id}")
                messagebox.showinfo("نجح", "تم إجراء التحويل بنجاح")
                dialog.destroy()
                print("🔄 إعادة عرض صفحة التحويلات...")

                # إضافة تأخير قصير للتأكد من إغلاق النافذة
                self.window.after(100, self.refresh_transfers_page)
                print("✅ تم جدولة إعادة عرض صفحة التحويلات")
            else:
                print("❌ فشل في إنشاء التحويل")
                messagebox.showerror("خطأ", "فشل في إجراء التحويل")

        except (ValueError, InvalidOperation):
            messagebox.showerror("خطأ", "يرجى إدخال مبالغ صحيحة.")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {e}")

    def load_reports(self, parent):
        """تحميل التقارير والإحصائيات"""
        try:
            user_id = auth_manager.current_user['id']

            # تقرير الملخص المالي
            self.create_financial_summary_report(parent, user_id)

            # تقرير الحسابات
            self.create_accounts_report(parent, user_id)

            # تقرير المعاملات الشهرية
            self.create_monthly_transactions_report(parent, user_id)

        except Exception as e:
            print(f"خطأ في تحميل التقارير: {e}")

    def create_financial_summary_report(self, parent, user_id):
        """إنشاء تقرير الملخص المالي"""
        # عنوان التقرير
        title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        title_frame.pack(fill="x", padx=20, pady=10)

        title_label = create_rtl_label(
            title_frame,
            text="📊 الملخص المالي",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=15)

        # الحصول على البيانات
        current_month = datetime.now().strftime('%Y-%m')

        # إجمالي الأرصدة (العملة الافتراضية فقط)
        balance_query = """
            SELECT COALESCE(SUM(ab.balance), 0) as total_balance
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            WHERE a.user_id = %s AND a.is_active = TRUE
            AND ab.currency_id = 1  -- العملة الافتراضية
        """
        balance_result = db.execute_query(balance_query, (user_id,))
        total_balance = balance_result[0]['total_balance'] if balance_result else 0

        # الواردات الشهرية
        income_query = """
            SELECT COALESCE(SUM(amount), 0) as monthly_income
            FROM transactions
            WHERE user_id = %s AND transaction_type = 'income'
            AND DATE_FORMAT(transaction_date, '%Y-%m') = %s
        """
        income_result = db.execute_query(income_query, (user_id, current_month))
        monthly_income = income_result[0]['monthly_income'] if income_result else 0

        # المصروفات الشهرية
        expense_query = """
            SELECT COALESCE(SUM(amount), 0) as monthly_expense
            FROM transactions
            WHERE user_id = %s AND transaction_type = 'expense'
            AND DATE_FORMAT(transaction_date, '%Y-%m') = %s
        """
        expense_result = db.execute_query(expense_query, (user_id, current_month))
        monthly_expense = expense_result[0]['monthly_expense'] if expense_result else 0

        # الصافي الشهري
        monthly_net = monthly_income - monthly_expense

        # عرض البيانات
        data_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        data_frame.pack(fill="x", padx=20, pady=(0, 15))

        # إجمالي الأرصدة
        balance_label = create_rtl_label(
            data_frame,
            text=f"إجمالي الأرصدة: {total_balance:,.2f} ر.س",
            font_size='header',
            text_color=COLORS['success'] if total_balance >= 0 else COLORS['error'],
            **ARABIC_TEXT_STYLES['title']
        )
        balance_label.pack(anchor="e", pady=2)

        # الواردات الشهرية
        income_label = create_rtl_label(
            data_frame,
            text=f"الواردات هذا الشهر: +{monthly_income:,.2f} ر.س",
            font_size='body',
            text_color=COLORS['success'],
            **ARABIC_TEXT_STYLES['label']
        )
        income_label.pack(anchor="e", pady=2)

        # المصروفات الشهرية
        expense_label = create_rtl_label(
            data_frame,
            text=f"المصروفات هذا الشهر: -{monthly_expense:,.2f} ر.س",
            font_size='body',
            text_color=COLORS['error'],
            **ARABIC_TEXT_STYLES['label']
        )
        expense_label.pack(anchor="e", pady=2)

        # الصافي الشهري
        net_color = COLORS['success'] if monthly_net >= 0 else COLORS['error']
        net_prefix = "+" if monthly_net >= 0 else ""
        net_label = create_rtl_label(
            data_frame,
            text=f"الصافي الشهري: {net_prefix}{monthly_net:,.2f} ر.س",
            font_size='header',
            text_color=net_color,
            **ARABIC_TEXT_STYLES['title']
        )
        net_label.pack(anchor="e", pady=5)

    def create_accounts_report(self, parent, user_id):
        """إنشاء تقرير الحسابات"""
        # عنوان التقرير
        title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        title_frame.pack(fill="x", padx=20, pady=10)

        title_label = create_rtl_label(
            title_frame,
            text="🏦 تقرير الحسابات",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=15)

        # الحصول على بيانات الحسابات مع الأرصدة
        accounts_query = """
            SELECT a.name, at.name as type_name,
                   GROUP_CONCAT(CONCAT(ab.balance, ' ', c.symbol) SEPARATOR ', ') as balances_text
            FROM accounts a
            JOIN account_types at ON a.account_type_id = at.id
            LEFT JOIN account_balances ab ON a.id = ab.account_id AND ab.balance != 0
            LEFT JOIN currencies c ON ab.currency_id = c.id
            WHERE a.user_id = %s AND a.is_active = TRUE
            GROUP BY a.id, a.name, at.name
            ORDER BY a.name
        """
        accounts = db.execute_query(accounts_query, (user_id,))

        if accounts:
            data_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
            data_frame.pack(fill="x", padx=20, pady=(0, 15))

            for account in accounts:
                account_frame = ctk.CTkFrame(data_frame, fg_color=COLORS['bg_light'], corner_radius=5)
                account_frame.pack(fill="x", pady=2)

                balances_text = account['balances_text'] or "0.00 ر.س"
                account_info = create_rtl_label(
                    account_frame,
                    text=f"{account['name']} ({account['type_name']}): {balances_text}",
                    font_size='body',
                    text_color=COLORS['text_primary'],
                    **ARABIC_TEXT_STYLES['label']
                )
                account_info.pack(anchor="e", padx=10, pady=5)
        else:
            # رسالة عدم وجود حسابات
            no_accounts_label = create_rtl_label(
                title_frame,
                text="لا توجد حسابات مسجلة حتى الآن",
                font_size='body',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['label']
            )
            no_accounts_label.pack(pady=20)

    def create_monthly_transactions_report(self, parent, user_id):
        """إنشاء تقرير المعاملات الشهرية"""
        # عنوان التقرير
        title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        title_frame.pack(fill="x", padx=20, pady=10)

        title_label = create_rtl_label(
            title_frame,
            text="📈 المعاملات الشهرية",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=15)

        # الحصول على إحصائيات المعاملات لآخر 6 أشهر
        data_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        data_frame.pack(fill="x", padx=20, pady=(0, 15))

        for i in range(6):
            month_date = datetime.now().replace(day=1)
            for _ in range(i):
                if month_date.month == 1:
                    month_date = month_date.replace(year=month_date.year - 1, month=12)
                else:
                    month_date = month_date.replace(month=month_date.month - 1)

            month_str = month_date.strftime('%Y-%m')
            month_name = month_date.strftime('%Y/%m')

            # الواردات
            income_query = """
                SELECT COALESCE(SUM(amount), 0) as income
                FROM transactions
                WHERE user_id = %s AND transaction_type = 'income'
                AND DATE_FORMAT(transaction_date, '%Y-%m') = %s
            """
            income_result = db.execute_query(income_query, (user_id, month_str))
            income = income_result[0]['income'] if income_result else 0

            # المصروفات
            expense_query = """
                SELECT COALESCE(SUM(amount), 0) as expense
                FROM transactions
                WHERE user_id = %s AND transaction_type = 'expense'
                AND DATE_FORMAT(transaction_date, '%Y-%m') = %s
            """
            expense_result = db.execute_query(expense_query, (user_id, month_str))
            expense = expense_result[0]['expense'] if expense_result else 0

            # عرض البيانات
            month_frame = ctk.CTkFrame(data_frame, fg_color=COLORS['bg_light'], corner_radius=5)
            month_frame.pack(fill="x", pady=2)

            month_info = create_rtl_label(
                month_frame,
                text=f"{month_name}: واردات {income:,.0f} - مصروفات {expense:,.0f} = صافي {income-expense:,.0f} ر.س",
                font_size='body',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            month_info.pack(anchor="e", padx=10, pady=5)

    def import_transactions_from_excel(self, transaction_type):
        """استيراد المعاملات من ملف Excel"""
        try:
            # اختيار ملف Excel
            file_path = filedialog.askopenfilename(
                title=f"اختر ملف Excel لاستيراد {'الواردات' if transaction_type == 'income' else 'المصروفات'}",
                filetypes=[
                    ("Excel files", "*.xlsx *.xls"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            # قراءة ملف Excel مع دعم أفضل للنصوص العربية
            try:
                # قراءة ملف Excel
                # قراءة ملف Excel
                df = pd.read_excel(file_path, engine='openpyxl')

                # التأكد من أن البيانات تم قراءتها بشكل صحيح
                if df.empty:
                    messagebox.showerror("خطأ", "ملف Excel فارغ أو لا يحتوي على بيانات صالحة")
                    return

            except Exception as e:
                error_msg = f"فشل في قراءة ملف Excel:\n{str(e)}\n\n"
                error_msg += "تأكد من:\n"
                error_msg += "• أن الملف بصيغة .xlsx أو .xls\n"
                error_msg += "• أن الملف غير مفتوح في برنامج آخر\n"
                error_msg += "• أن الملف يحتوي على بيانات صحيحة\n"
                error_msg += "• أن النصوص العربية محفوظة بترميز صحيح"
                messagebox.showerror("خطأ", error_msg)
                return

            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['المبلغ', 'الحساب', 'العملة', 'التاريخ', 'الوصف']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                messagebox.showerror(
                    "خطأ في بنية الملف",
                    f"الأعمدة التالية مفقودة في ملف Excel:\n{', '.join(missing_columns)}\n\n"
                    f"الأعمدة المطلوبة: {', '.join(required_columns)}"
                )
                return

            # عرض نافذة معاينة البيانات
            self.show_import_preview_dialog(df, transaction_type, file_path)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء استيراد الملف:\n{str(e)}")

    def show_import_preview_dialog(self, df, transaction_type, file_path):
        """عرض نافذة معاينة البيانات قبل الاستيراد"""
        dialog = ctk.CTkToplevel(self.window)
        dialog.title(f"معاينة بيانات {'الواردات' if transaction_type == 'income' else 'المصروفات'}")
        dialog.geometry("900x600")
        dialog.resizable(True, True)
        dialog.transient(self.window)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (900 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"900x600+{x}+{y}")

        # عنوان النافذة
        title_label = create_rtl_label(
            dialog,
            text=f"معاينة بيانات {'الواردات' if transaction_type == 'income' else 'المصروفات'}",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=20)

        # معلومات الملف
        file_info_label = create_rtl_label(
            dialog,
            text=f"الملف: {file_path.split('/')[-1]} | عدد الصفوف: {len(df)}",
            font_size='body',
            text_color=COLORS['text_muted'],
            **ARABIC_TEXT_STYLES['label']
        )
        file_info_label.pack(pady=(0, 10))

        # إطار قابل للتمرير لعرض البيانات
        preview_frame = ctk.CTkScrollableFrame(
            dialog,
            fg_color=COLORS['bg_light'],
            corner_radius=10
        )
        preview_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # عرض أول 10 صفوف من البيانات
        preview_data = df.head(10)

        # إنشاء جدول لعرض البيانات
        headers = ['المبلغ', 'الحساب', 'العملة', 'التاريخ', 'الوصف']

        # رؤوس الأعمدة
        header_frame = ctk.CTkFrame(preview_frame, fg_color=COLORS['primary'])
        header_frame.pack(fill="x", pady=(0, 5))

        for i, header in enumerate(headers):
            header_label = create_rtl_label(
                header_frame,
                text=header,
                font_size='header',
                text_color="white",
                width=150,
                **ARABIC_TEXT_STYLES['title']
            )
            header_label.grid(row=0, column=i, padx=5, pady=5, sticky="ew")

        # صفوف البيانات
        for idx, row in preview_data.iterrows():
            row_frame = ctk.CTkFrame(preview_frame, fg_color=COLORS['bg_secondary'])
            row_frame.pack(fill="x", pady=2)

            for i, header in enumerate(headers):
                value = str(row.get(header, ''))
                if len(value) > 20:
                    value = value[:17] + "..."

                cell_label = create_rtl_label(
                    row_frame,
                    text=value,
                    font_size='small',
                    text_color=COLORS['text_on_dark'],
                    width=150,
                    **ARABIC_TEXT_STYLES['label']
                )
                cell_label.grid(row=0, column=i, padx=5, pady=3, sticky="ew")

        # إذا كان هناك أكثر من 10 صفوف
        if len(df) > 10:
            more_label = create_rtl_label(
                preview_frame,
                text=f"... و {len(df) - 10} صف إضافي",
                font_size='body',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['label']
            )
            more_label.pack(pady=10)

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        buttons_frame.pack(pady=20)

        # زر الاستيراد
        import_button = create_rtl_button(
            buttons_frame,
            text="✅ تأكيد الاستيراد",
            command=lambda: self.process_excel_import(dialog, df, transaction_type),
            **BUTTON_STYLES['primary']
        )
        import_button.pack(side="left", padx=(0, 10))

        # زر الإلغاء
        cancel_button = create_rtl_button(
            buttons_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="left")

    def process_excel_import(self, dialog, df, transaction_type):
        """معالجة استيراد البيانات من Excel"""
        try:
            dialog.destroy()

            # إنشاء نافذة تقدم العملية
            progress_dialog = ctk.CTkToplevel(self.window)
            progress_dialog.title("جاري الاستيراد...")
            progress_dialog.geometry("400x200")
            progress_dialog.resizable(False, False)
            progress_dialog.transient(self.window)
            progress_dialog.grab_set()

            # توسيط النافذة
            progress_dialog.update_idletasks()
            x = (progress_dialog.winfo_screenwidth() // 2) - (400 // 2)
            y = (progress_dialog.winfo_screenheight() // 2) - (200 // 2)
            progress_dialog.geometry(f"400x200+{x}+{y}")

            # عنوان
            title_label = create_rtl_label(
                progress_dialog,
                text="جاري استيراد البيانات...",
                font_size='subtitle',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['title']
            )
            title_label.pack(pady=20)

            # شريط التقدم
            progress_bar = ctk.CTkProgressBar(progress_dialog, width=300)
            progress_bar.pack(pady=10)
            progress_bar.set(0)

            # تسمية الحالة
            status_label = create_rtl_label(
                progress_dialog,
                text="بدء العملية...",
                font_size='body',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['label']
            )
            status_label.pack(pady=5)

            progress_dialog.update()

            # متغيرات النتائج
            successful_imports = 0
            failed_imports = 0
            error_messages = []

            user_id = auth_manager.current_user['id']

            # الحصول على قوائم الحسابات والعملات للمطابقة
            accounts_query = "SELECT id, name FROM accounts WHERE user_id = %s AND is_active = 1"
            accounts = db.execute_query(accounts_query, (user_id,))
            accounts_dict = {acc['name']: acc['id'] for acc in accounts}

            currencies_query = "SELECT id, code, symbol FROM currencies WHERE is_active = 1"
            currencies = db.execute_query(currencies_query)
            currencies_dict = {}
            for curr in currencies:
                currencies_dict[curr['code']] = curr['id']
                currencies_dict[curr['symbol']] = curr['id']

            total_rows = len(df)

            # معالجة كل صف
            for index, row in df.iterrows():
                try:
                    # تحديث شريط التقدم
                    progress = (index + 1) / total_rows
                    progress_bar.set(progress)
                    status_label.configure(text=f"معالجة الصف {index + 1} من {total_rows}")
                    progress_dialog.update()

                    # استخراج البيانات
                    amount = self.parse_amount(row.get('المبلغ', 0))
                    account_name = str(row.get('الحساب', '')).strip()
                    currency_code = str(row.get('العملة', '')).strip()
                    date_value = row.get('التاريخ', '')  # الحفاظ على نوع البيانات الأصلي
                    description = str(row.get('الوصف', '')).strip()

                    # التحقق من صحة البيانات
                    if amount <= 0:
                        error_messages.append(f"الصف {index + 1}: مبلغ غير صحيح ({amount})")
                        failed_imports += 1
                        continue

                    if not account_name or account_name not in accounts_dict:
                        error_messages.append(f"الصف {index + 1}: حساب غير موجود ({account_name})")
                        failed_imports += 1
                        continue

                    if not currency_code or currency_code not in currencies_dict:
                        error_messages.append(f"الصف {index + 1}: عملة غير مدعومة ({currency_code})")
                        failed_imports += 1
                        continue

                    # تحويل التاريخ
                    transaction_date = self.parse_date(date_value)
                    if not transaction_date:
                        error_messages.append(f"الصف {index + 1}: تاريخ غير صحيح ({date_value})")
                        failed_imports += 1
                        continue

                    # إنشاء المعاملة
                    account_id = accounts_dict[account_name]
                    currency_id = currencies_dict[currency_code]

                    from database.models import Transaction
                    result = Transaction.create(
                        user_id=user_id,
                        account_id=account_id,
                        currency_id=currency_id,
                        transaction_type=transaction_type,
                        amount=amount,
                        description=description,
                        transaction_date=transaction_date
                    )

                    if result > 0:
                        successful_imports += 1
                    else:
                        error_messages.append(f"الصف {index + 1}: فشل في حفظ المعاملة")
                        failed_imports += 1

                except Exception as e:
                    error_messages.append(f"الصف {index + 1}: خطأ - {str(e)}")
                    failed_imports += 1

            progress_dialog.destroy()

            # عرض تقرير النتائج
            self.show_import_results(successful_imports, failed_imports, error_messages, transaction_type)

            # تحديث القوائم
            if successful_imports > 0:
                if transaction_type == "income":
                    self.show_income()
                else:
                    self.show_expense()

        except Exception as e:
            if 'progress_dialog' in locals():
                progress_dialog.destroy()
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الاستيراد:\n{str(e)}")

    def parse_amount(self, amount_value):
        """تحويل قيمة المبلغ إلى رقم"""
        try:
            if pd.isna(amount_value):
                return 0

            # إزالة الفواصل والرموز
            amount_str = str(amount_value).replace(',', '').replace('ر.س', '').replace('$', '').strip()
            return float(amount_str)
        except:
            return 0

    def parse_date(self, date_value):
        """تحويل قيمة التاريخ إلى تاريخ صحيح مع دعم محسن لتنسيقات Excel"""
        try:
            if pd.isna(date_value):
                return None

            # إذا كانت القيمة من نوع التاريخ والوقت، قم بتحويلها مباشرة
            if isinstance(date_value, datetime):
                return date_value.date()

            # إذا كانت القيمة من نوع pandas Timestamp
            if hasattr(date_value, 'date') and callable(getattr(date_value, 'date')):
                return date_value.date()

            # إذا كانت القيمة من نوع pandas datetime64
            if hasattr(date_value, 'to_pydatetime'):
                return date_value.to_pydatetime().date()

            # إذا كانت القيمة رقم (Excel serial date)
            if isinstance(date_value, (int, float)):
                try:
                    # تحويل Excel serial date إلى تاريخ
                    # Excel يبدأ من 1900-01-01 (مع تعديل للخطأ في Excel)
                    from datetime import timedelta
                    excel_epoch = datetime(1899, 12, 30)  # Excel epoch مع تعديل الخطأ
                    converted_date = excel_epoch + timedelta(days=date_value)
                    return converted_date.date()
                except:
                    pass

            # تحويل إلى نص ومحاولة التحليل
            date_str = str(date_value).strip()

            # إزالة الوقت إذا كان موجوداً
            if ' ' in date_str and ':' in date_str:
                date_str = date_str.split(' ')[0]

            # محاولة تحويل التاريخ بصيغ مختلفة
            date_formats = [
                '%Y-%m-%d %H:%M:%S',  # تنسيق التاريخ والوقت الكامل
                '%Y-%m-%d %H:%M:%S.%f',  # مع microseconds
                '%Y-%m-%d',           # ISO format
                '%d/%m/%Y',           # DD/MM/YYYY
                '%m/%d/%Y',           # MM/DD/YYYY
                '%d-%m-%Y',           # DD-MM-YYYY
                '%Y/%m/%d',           # YYYY/MM/DD
                '%d.%m.%Y',           # DD.MM.YYYY
                '%Y.%m.%d',           # YYYY.MM.DD
                '%d %m %Y',           # DD MM YYYY
                '%Y %m %d'            # YYYY MM DD
            ]

            for date_format in date_formats:
                try:
                    parsed_date = datetime.strptime(date_str, date_format)
                    return parsed_date.date()
                except:
                    continue

            # محاولة أخيرة مع pandas to_datetime
            try:
                parsed_date = pd.to_datetime(date_value, errors='coerce')
                if not pd.isna(parsed_date):
                    return parsed_date.date()
            except:
                pass

            return None
        except Exception as e:
            print(f"خطأ في تحليل التاريخ {date_value}: {e}")
            return None

    def show_import_results(self, successful_imports, failed_imports, error_messages, transaction_type):
        """عرض نتائج عملية الاستيراد"""
        dialog = ctk.CTkToplevel(self.window)
        dialog.title("نتائج الاستيراد")
        dialog.geometry("600x500")
        dialog.resizable(True, True)
        dialog.transient(self.window)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f"600x500+{x}+{y}")

        # عنوان النافذة
        title_label = create_rtl_label(
            dialog,
            text=f"نتائج استيراد {'الواردات' if transaction_type == 'income' else 'المصروفات'}",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=20)

        # إطار النتائج
        results_frame = ctk.CTkFrame(dialog, fg_color=COLORS['bg_light'])
        results_frame.pack(fill="x", padx=20, pady=(0, 20))

        # النتائج الناجحة
        success_label = create_rtl_label(
            results_frame,
            text=f"✅ تم استيراد {successful_imports} معاملة بنجاح",
            font_size='header',
            text_color=COLORS['success'],
            **ARABIC_TEXT_STYLES['title']
        )
        success_label.pack(pady=10)

        # النتائج الفاشلة
        if failed_imports > 0:
            failed_label = create_rtl_label(
                results_frame,
                text=f"❌ فشل في استيراد {failed_imports} معاملة",
                font_size='header',
                text_color=COLORS['error'],
                **ARABIC_TEXT_STYLES['title']
            )
            failed_label.pack(pady=(0, 10))

        # عرض الأخطاء إذا وجدت
        if error_messages:
            errors_label = create_rtl_label(
                dialog,
                text="تفاصيل الأخطاء:",
                font_size='header',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            errors_label.pack(anchor="e", padx=20, pady=(0, 5))

            # إطار قابل للتمرير للأخطاء
            errors_frame = ctk.CTkScrollableFrame(
                dialog,
                fg_color=COLORS['bg_secondary'],
                corner_radius=10
            )
            errors_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

            for error in error_messages[:20]:  # عرض أول 20 خطأ فقط
                error_label = create_rtl_label(
                    errors_frame,
                    text=f"• {error}",
                    font_size='small',
                    text_color=COLORS['error_on_dark'],
                    **ARABIC_TEXT_STYLES['label']
                )
                error_label.pack(fill="x", padx=10, pady=2)

            if len(error_messages) > 20:
                more_errors_label = create_rtl_label(
                    errors_frame,
                    text=f"... و {len(error_messages) - 20} خطأ إضافي",
                    font_size='small',
                    text_color=COLORS['text_on_dark_muted'],
                    **ARABIC_TEXT_STYLES['label']
                )
                more_errors_label.pack(pady=5)

        # زر الإغلاق
        close_button = create_rtl_button(
            dialog,
            text="إغلاق",
            command=dialog.destroy,
            **BUTTON_STYLES['primary']
        )
        close_button.pack(pady=20)

    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟")
        if result:
            auth_manager.logout()
            self.window.destroy()

            # العودة لنافذة تسجيل الدخول
            from gui.login_window import LoginWindow
            login_app = LoginWindow()
            login_app.run()

    def on_closing(self):
        """عند إغلاق النافذة"""
        result = messagebox.askyesno("إغلاق البرنامج", "هل أنت متأكد من إغلاق البرنامج؟")
        if result:
            auth_manager.logout()
            db.close()
            self.window.destroy()

















    def show_edit_transaction_dialog(self, transaction, transaction_type):
        """عرض نافذة تعديل معاملة"""
        title = "تعديل وارد" if transaction_type == "income" else "تعديل مصروف"

        dialog = ctk.CTkToplevel(self.window)
        dialog.title(title)

        window_width = 500
        window_height = 600
        dialog.geometry(f"{window_width}x{window_height}")
        dialog.resizable(False, False)
        dialog.transient(self.window)
        dialog.grab_set()

        dialog.update_idletasks()
        screen_width = dialog.winfo_screenwidth()
        screen_height = dialog.winfo_screenheight()
        x = max(0, (screen_width // 2) - (window_width // 2))
        y = max(0, (screen_height // 2) - (window_height // 2))
        if y + window_height > screen_height:
            y = screen_height - window_height - 50
        dialog.geometry(f"{window_width}x{window_height}+{x}+{y}")

        title_label = create_rtl_label(
            dialog,
            text=title,
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(15, 10))

        scrollable_frame = ctk.CTkScrollableFrame(
            dialog,
            fg_color="transparent",
            height=420
        )
        scrollable_frame.pack(fill="both", expand=True, padx=20, pady=(0, 10))

        amount_label = ctk.CTkLabel(scrollable_frame, text="المبلغ:", font=ctk.CTkFont(size=14, weight="bold"))
        amount_label.pack(anchor="w", pady=(10, 5))
        amount_entry = ctk.CTkEntry(
            scrollable_frame,
            height=40,
            font=ctk.CTkFont(size=14),
            placeholder_text="أدخل المبلغ"
        )
        amount_entry.pack(fill="x", pady=(0, 15))
        amount_entry.insert(0, str(transaction['amount']))

        # الحساب
        account_label = ctk.CTkLabel(scrollable_frame, text="الحساب:", font=ctk.CTkFont(size=14, weight="bold"))
        account_label.pack(anchor="w", pady=(0, 5))

        # --- Start of changes for account combobox ---
        try:
            user_id = auth_manager.current_user['id']
            accounts = db.execute_query(
                "SELECT id, name FROM accounts WHERE user_id = %s AND is_active = TRUE ORDER BY name",
                (user_id,)
            )
            if accounts is None:
                accounts = []
        except Exception as e:
            print(f"خطأ في جلب الحسابات لنافذة التعديل: {e}")
            accounts = []

        if not accounts:
            messagebox.showerror("خطأ", "لا توجد حسابات متاحة. لا يمكن تعديل المعاملة.", parent=dialog)
            dialog.destroy()
            return

        account_values = [f"{acc['id']} - {acc['name']}" for acc in accounts]
        account_combo = ctk.CTkComboBox(
            scrollable_frame,
            values=account_values,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        account_combo.pack(fill="x", pady=(0, 15))

        # تعيين الحساب الحالي
        current_account_text = f"{transaction['account_id']} - {transaction['account_name']}"
        if current_account_text in account_values:
            account_combo.set(current_account_text)
        else:
            account_combo.set(account_values[0])
        # --- End of changes for account combobox ---

        # العملة
        currency_label = ctk.CTkLabel(scrollable_frame, text="العملة:", font=ctk.CTkFont(size=14, weight="bold"))
        currency_label.pack(anchor="w", pady=(0, 5))

        try:
            currencies = db.execute_query("SELECT id, name, symbol FROM currencies WHERE is_active = TRUE ORDER BY name")
            if currencies is None:
                currencies = []
        except Exception as e:
            print(f"خطأ في جلب العملات لنافذة التعديل: {e}")
            currencies = []

        currency_options = [f"{c['id']} - {c['name']} ({c['symbol']})" for c in currencies] if currencies else ["1 - ريال سعودي (ر.س)"]
        currency_combo = ctk.CTkComboBox(
            scrollable_frame,
            values=currency_options,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        currency_combo.pack(fill="x", pady=(0, 15))

        try:
            # This part needs to be robust. The transaction object might not have currency_name.
            # Let's find the currency from the currencies list.
            current_currency_id = transaction.get('currency_id', 1)
            current_currency_info = next((c for c in currencies if c['id'] == current_currency_id), None)

            if current_currency_info:
                 current_currency = f"{current_currency_info['id']} - {current_currency_info['name']} ({current_currency_info['symbol']})"
                 if current_currency in currency_options:
                     currency_combo.set(current_currency)
                 elif currency_options:
                     currency_combo.set(currency_options[0])
            elif currency_options:
                currency_combo.set(currency_options[0])
            else:
                currency_combo.set("")
        except Exception as e:
            print(f"خطأ في تعيين العملة الحالية: {e}")
            if currency_options:
                currency_combo.set(currency_options[0])
            else:
                currency_combo.set("")

        # التاريخ
        date_label = ctk.CTkLabel(scrollable_frame, text="التاريخ:", font=ctk.CTkFont(size=14, weight="bold"))
        date_label.pack(anchor="w", pady=(0, 5))
        date_entry = ctk.CTkEntry(
            scrollable_frame,
            height=40,
            font=ctk.CTkFont(size=14),
            placeholder_text="YYYY-MM-DD"
        )
        date_entry.pack(fill="x", pady=(0, 15))
        date_entry.insert(0, str(transaction['transaction_date']))

        # الوصف
        desc_label = ctk.CTkLabel(scrollable_frame, text="الوصف (اختياري):", font=ctk.CTkFont(size=14, weight="bold"))
        desc_label.pack(anchor="w", pady=(0, 5))
        desc_entry = ctk.CTkTextbox(
            scrollable_frame,
            height=80,
            font=ctk.CTkFont(size=14)
        )
        desc_entry.pack(fill="x", pady=(0, 20))
        if transaction.get('description'):
            desc_entry.insert("1.0", transaction['description'])

        buttons_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        buttons_frame.pack(pady=(10, 20))

        save_button = create_rtl_button(
            buttons_frame,
            text="حفظ التغييرات",
            command=lambda: self.save_transaction_changes(
                dialog, transaction['id'], transaction_type, amount_entry,
                account_combo, currency_combo, date_entry, desc_entry
            ),
            **BUTTON_STYLES['primary']
        )
        save_button.pack(side="left", padx=(0, 10))

        cancel_button = create_rtl_button(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="left")

    def save_transaction_changes(self, dialog, transaction_id, transaction_type,
                                amount_entry, account_combo, currency_combo,
                                date_entry, desc_entry):
        """حفظ تغييرات المعاملة"""
        try:
            # التحقق من البيانات
            amount_text = amount_entry.get().strip()
            if not amount_text:
                messagebox.showerror("خطأ", "يرجى إدخال المبلغ", parent=dialog)
                return

            try:
                amount = float(amount_text)
                if amount <= 0:
                    messagebox.showerror("خطأ", "يجب أن يكون المبلغ أكبر من صفر", parent=dialog)
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح", parent=dialog)
                return

            # الحساب
            account_text = account_combo.get()
            if not account_text:
                messagebox.showerror("خطأ", "يرجى اختيار الحساب", parent=dialog)
                return
            
            try:
                account_id = int(account_text.split(' - ')[0])
            except (ValueError, IndexError):
                messagebox.showerror("خطأ", "الحساب المختار غير صالح.", parent=dialog)
                return

            # العملة
            currency_text = currency_combo.get()
            if not currency_text:
                messagebox.showerror("خطأ", "يرجى اختيار العملة", parent=dialog)
                return

            try:
                currency_id = int(currency_text.split(' - ')[0])
            except (ValueError, IndexError):
                messagebox.showerror("خطأ", "العملة المختارة غير صالحة", parent=dialog)
                return

            # التاريخ
            transaction_date = date_entry.get().strip()
            if not transaction_date:
                messagebox.showerror("خطأ", "يرجى إدخال التاريخ", parent=dialog)
                return

            description = desc_entry.get("1.0", "end-1c").strip()

            # تحديث المعاملة
            from database.models import Transaction
            success = Transaction.update(
                transaction_id=transaction_id,
                amount=amount,
                account_id=account_id,
                currency_id=currency_id,
                description=description,
                transaction_date=transaction_date
            )

            if success:
                messagebox.showinfo("نجح", "تم تحديث المعاملة بنجاح")
                dialog.destroy()
                # إعادة تحميل الصفحة المناسبة
                if transaction_type == "income":
                    self.show_income()
                else:
                    self.show_expense()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث المعاملة.\nقد تكون المشكلة في الاتصال بقاعدة البيانات أو في البيانات المدخلة.")

        except Exception as e:
            print(f"خطأ في تحديث المعاملة: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}", parent=dialog)

    def delete_transaction(self, transaction_id, transaction_type):
        """حذف معاملة"""
        try:
            # تأكيد الحذف
            result = messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف هذه {'الوارد' if transaction_type == 'income' else 'المصروف'}؟\n"
                "لا يمكن التراجع عن هذا الإجراء."
            )

            if not result:
                return

            # حذف المعاملة
            from database.models import Transaction
            success = Transaction.delete(transaction_id)

            if success:
                messagebox.showinfo("نجح", "تم حذف المعاملة بنجاح")
                # إعادة تحميل الصفحة المناسبة
                if transaction_type == "income":
                    self.show_income()
                else:
                    self.show_expense()
            else:
                messagebox.showerror("خطأ", "فشل في حذف المعاملة")

        except Exception as e:
            print(f"خطأ في حذف المعاملة: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def show_edit_transfer_dialog(self, transfer):
        """عرض نافذة تعديل تحويل"""
        dialog = ctk.CTkToplevel(self.window)
        dialog.title("تعديل تحويل")
        dialog.geometry("500x600")
        dialog.resizable(False, False)
        dialog.transient(self.window)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"500x600+{x}+{y}")

        # عنوان النافذة
        title_label = create_rtl_label(
            dialog,
            text="تعديل تحويل",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 30))

        # إطار النموذج
        form_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        form_frame.pack(fill="both", expand=True, padx=30, pady=(0, 20))

        # تحميل الحسابات
        accounts = db.execute_query("""
            SELECT a.id, a.name, c.symbol
            FROM accounts a
            JOIN currencies c ON a.currency_id = c.id
            WHERE a.user_id = %s AND a.is_active = TRUE
        """, (auth_manager.current_user['id'],))

        account_values = [f"{acc['name']} ({acc['symbol']})" for acc in accounts]

        # الحساب المرسل
        from_label = ctk.CTkLabel(form_frame, text="من الحساب:", font=ctk.CTkFont(size=14))
        from_label.pack(anchor="w", pady=(0, 5))

        from_combo = ctk.CTkComboBox(
            form_frame,
            values=account_values,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        from_combo.pack(fill="x", pady=(0, 15))

        # تعيين الحساب المرسل الحالي
        current_from = f"{transfer['from_account_name']} ({transfer['currency_symbol']})"
        if current_from in account_values:
            from_combo.set(current_from)

        # الحساب المستقبل
        to_label = ctk.CTkLabel(form_frame, text="إلى الحساب:", font=ctk.CTkFont(size=14))
        to_label.pack(anchor="w", pady=(0, 5))

        to_combo = ctk.CTkComboBox(
            form_frame,
            values=account_values,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        to_combo.pack(fill="x", pady=(0, 15))

        # تعيين الحساب المستقبل الحالي
        current_to = f"{transfer['to_account_name']} ({transfer['currency_symbol']})"
        if current_to in account_values:
            to_combo.set(current_to)

        # المبلغ
        amount_label = ctk.CTkLabel(form_frame, text="المبلغ:", font=ctk.CTkFont(size=14))
        amount_label.pack(anchor="w", pady=(0, 5))

        amount_entry = ctk.CTkEntry(
            form_frame,
            height=40,
            font=ctk.CTkFont(size=14),
            placeholder_text="أدخل المبلغ"
        )
        amount_entry.pack(fill="x", pady=(0, 15))
        amount_entry.insert(0, str(transfer['amount']))

        # التاريخ
        date_label = ctk.CTkLabel(form_frame, text="التاريخ:", font=ctk.CTkFont(size=14))
        date_label.pack(anchor="w", pady=(0, 5))

        date_entry = ctk.CTkEntry(
            form_frame,
            height=40,
            font=ctk.CTkFont(size=14),
            placeholder_text="YYYY-MM-DD"
        )
        date_entry.pack(fill="x", pady=(0, 15))
        date_entry.insert(0, str(transfer['transfer_date']))

        # الوصف
        desc_label = ctk.CTkLabel(form_frame, text="الوصف (اختياري):", font=ctk.CTkFont(size=14))
        desc_label.pack(anchor="w", pady=(0, 5))

        desc_entry = ctk.CTkTextbox(
            form_frame,
            height=80,
            font=ctk.CTkFont(size=14)
        )
        desc_entry.pack(fill="x", pady=(0, 20))
        if transfer['description']:
            desc_entry.insert("1.0", transfer['description'])

        # أزرار الحفظ والإلغاء
        buttons_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=(10, 0))

        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            width=100,
            height=40,
            command=dialog.destroy,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="right", padx=(10, 0))

        save_button = ctk.CTkButton(
            buttons_frame,
            text="حفظ التغييرات",
            width=120,
            height=40,
            command=lambda: self.save_transfer_changes(
                dialog, transfer['id'], from_combo, to_combo, accounts,
                amount_entry, date_entry, desc_entry
            ),
            **BUTTON_STYLES['primary']
        )
        save_button.pack(side="right")

    def save_transfer_changes(self, dialog, transfer_id, from_combo, to_combo,
                             accounts, amount_entry, date_entry, desc_entry):
        """حفظ تغييرات التحويل"""
        try:
            # التحقق من البيانات
            from_index = from_combo.current()
            to_index = to_combo.current()

            if from_index < 0 or to_index < 0:
                messagebox.showerror("خطأ", "يرجى اختيار الحسابات")
                return

            if from_index == to_index:
                messagebox.showerror("خطأ", "لا يمكن التحويل من وإلى نفس الحساب")
                return

            from_account_id = accounts[from_index]['id']
            to_account_id = accounts[to_index]['id']

            amount_text = amount_entry.get().strip()
            if not amount_text:
                messagebox.showerror("خطأ", "يرجى إدخال المبلغ")
                return

            amount = float(amount_text)
            if amount <= 0:
                messagebox.showerror("خطأ", "يجب أن يكون المبلغ أكبر من صفر")
                return

            transfer_date = date_entry.get().strip()
            if not transfer_date:
                messagebox.showerror("خطأ", "يرجى إدخال التاريخ")
                return

            description = desc_entry.get("1.0", "end-1c").strip()

            # تحديث التحويل
            from database.models import Transfer
            success = Transfer.update(
                transfer_id=transfer_id,
                from_account_id=from_account_id,
                to_account_id=to_account_id,
                amount=amount,
                description=description,
                transfer_date=transfer_date
            )

            if success:
                messagebox.showinfo("نجح", "تم تحديث التحويل بنجاح")
                dialog.destroy()
                self.show_transfers()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث التحويل")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
        except Exception as e:
            print(f"خطأ في تحديث التحويل: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def delete_transfer(self, transfer_id):
        """حذف تحويل"""
        try:
            # تأكيد الحذف
            result = messagebox.askyesno(
                "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا التحويل؟\n"
                "لا يمكن التراجع عن هذا الإجراء."
            )

            if not result:
                return

            # حذف التحويل
            from database.models import Transfer
            success = Transfer.delete(transfer_id)

            if success:
                messagebox.showinfo("نجح", "تم حذف التحويل بنجاح")
                self.show_transfers()
            else:
                messagebox.showerror("خطأ", "فشل في حذف التحويل")

        except Exception as e:
            print(f"خطأ في حذف التحويل: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def show_edit_account_dialog(self, account):
        """عرض نافذة تعديل حساب"""
        dialog = ctk.CTkToplevel(self.window)
        dialog.title("تعديل حساب")
        dialog.geometry("500x600")
        dialog.resizable(False, False)
        dialog.transient(self.window)
        dialog.grab_set()

        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"500x600+{x}+{y}")

        title_label = create_rtl_label(
            dialog,
            text="تعديل حساب",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 10))

        # Use a scrollable frame to ensure all fields are visible
        scrollable_frame = ctk.CTkScrollableFrame(dialog, fg_color="transparent")
        scrollable_frame.pack(fill="both", expand=True, padx=30, pady=0)

        # اسم الحساب
        name_label = create_rtl_label(
            scrollable_frame,
            text="اسم الحساب:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        name_label.pack(anchor="w", pady=(10, 5))
        name_entry = create_rtl_entry(
            scrollable_frame,
            placeholder_text="أدخل اسم الحساب",
            height=40
        )
        name_entry.pack(fill="x", pady=(0, 15))
        name_entry.insert(0, account.get('name', ''))



        # ملاحظة: لا يمكن تغيير عملة الحساب بعد إنشائه لتجنب تضارب البيانات
        currency_info_label = create_rtl_label(
            scrollable_frame,
            text="عملة الحساب (غير قابلة للتعديل):",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        currency_info_label.pack(anchor="w", pady=(10, 5))
        
        from database.models import Account as AccountModel
        full_account_details = AccountModel.get_by_id(account['id'])
        currency_display = "غير محددة"
        if full_account_details and full_account_details.get('balances'):
            currency_display = f"{full_account_details['balances'][0]['name']} ({full_account_details['balances'][0]['symbol']})"

        currency_label = create_rtl_label(
            scrollable_frame,
            text=currency_display,
            font_size='body',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        currency_label.pack(anchor="w", pady=(0, 15))

        # الوصف
        desc_label = create_rtl_label(
            scrollable_frame,
            text="الوصف (اختياري):",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="w", pady=(0, 5))
        from config.fonts import create_rtl_textbox
        desc_entry = create_rtl_textbox(
            scrollable_frame,
            height=80
        )
        desc_entry.pack(fill="x", pady=(0, 10), expand=True)
        if account.get('description'):
            desc_entry.insert("1.0", account['description'])

        # أزرار الحفظ والإلغاء - خارج الإطار القابل للتمرير
        buttons_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        buttons_frame.pack(pady=(10, 20))

        save_button = create_rtl_button(
            buttons_frame,
            text="حفظ التغييرات",
            command=lambda: self.save_account_changes(
                dialog, account['id'], name_entry, desc_entry
            ),
            **BUTTON_STYLES['primary']
        )
        save_button.pack(side="left", padx=(0, 10))

        cancel_button = create_rtl_button(
            buttons_frame,
            text="إلغاء",
            command=dialog.destroy,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="left")

    def save_account_changes(self, dialog, account_id, name_entry, desc_entry):
        """حفظ تغييرات الحساب (بدون نوع حساب)"""
        try:
            name = name_entry.get().strip()
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحساب", parent=dialog)
                return

            description = desc_entry.get("1.0", "end-1c").strip()

            # تحديث الحساب بدون نوع حساب
            query = """
                UPDATE accounts
                SET name = %s, description = %s, updated_at = NOW()
                WHERE id = %s
            """
            result = db.execute_update(query, (name, description, account_id))

            if result > 0:
                messagebox.showinfo("نجح", "تم تحديث الحساب بنجاح", parent=dialog)
                dialog.destroy()
                self.show_accounts()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث الحساب", parent=dialog)

        except Exception as e:
            print(f"خطأ في تحديث الحساب: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}", parent=dialog)

    def toggle_account_status(self, account_id, activate):
        """تفعيل أو إلغاء تفعيل حساب"""
        try:
            from database.models import Account

            if activate:
                success = Account.activate(account_id)
                action = "تفعيل"
            else:
                success = Account.deactivate(account_id)
                action = "إلغاء تفعيل"

            if success:
                messagebox.showinfo("نجح", f"تم {action} الحساب بنجاح")
                self.show_accounts()
            else:
                messagebox.showerror("خطأ", f"فشل في {action} الحساب")

        except Exception as e:
            print(f"خطأ في تغيير حالة الحساب: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def delete_account(self, account_id):
        """حذف حساب"""
        try:
            from database.models import Account

            # محاولة حذف الحساب
            success, message = Account.delete(account_id)

            if success:
                messagebox.showinfo("نجح", message)
                self.show_accounts()
            else:
                # إذا فشل الحذف، اعرض خيار إلغاء التفعيل
                result = messagebox.askyesnocancel(
                    "تحذير",
                    f"{message}\n\n"
                    "هل تريد إلغاء تفعيل الحساب بدلاً من حذفه؟\n"
                    "• اضغط 'نعم' لإلغاء التفعيل\n"
                    "• اضغط 'لا' للتراجع"
                )

                if result:  # نعم - إلغاء تفعيل
                    self.toggle_account_status(account_id, False)

        except Exception as e:
            print(f"خطأ في حذف الحساب: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def show_search(self):
        """عرض صفحة البحث"""
        self.current_page = "search"
        self.set_active_button("search")
        self.clear_content()

        # عنوان الصفحة
        title_label = create_rtl_label(
            self.content_frame,
            text="البحث في المعاملات والتحويلات",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 20))

        # إطار البحث الرئيسي
        search_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color=COLORS['bg_light'],
            corner_radius=10
        )
        search_frame.pack(fill="both", expand=True, padx=30, pady=(0, 30))

        # تحميل واجهة البحث
        self.load_search_interface(search_frame)

    def load_search_interface(self, parent):
        """تحميل واجهة البحث"""
        # إطار أزرار نوع البحث
        search_type_frame = ctk.CTkFrame(parent, fg_color="transparent")
        search_type_frame.pack(fill="x", padx=20, pady=20)

        # عنوان نوع البحث
        type_label = create_rtl_label(
            search_type_frame,
            text="نوع البحث:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        type_label.pack(anchor="w", pady=(0, 10))

        # أزرار نوع البحث
        buttons_frame = ctk.CTkFrame(search_type_frame, fg_color="transparent")
        buttons_frame.pack(fill="x")

        # زر البحث العادي
        self.simple_search_button = create_rtl_button(
            buttons_frame,
            text="🔍 البحث العادي",
            command=self.show_simple_search,
            **BUTTON_STYLES['primary']
        )
        self.simple_search_button.pack(side="right", padx=(10, 0))

        # زر البحث المتقدم
        self.advanced_search_button = create_rtl_button(
            buttons_frame,
            text="🔧 البحث المتقدم",
            command=self.show_advanced_search,
            **BUTTON_STYLES['secondary']
        )
        self.advanced_search_button.pack(side="right")

        # إطار محتوى البحث
        self.search_content_frame = ctk.CTkFrame(parent, fg_color="transparent")
        self.search_content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # عرض البحث العادي افتراضياً
        self.show_simple_search()

    def show_simple_search(self):
        """عرض واجهة البحث العادي"""
        # تحديث أزرار نوع البحث
        self.simple_search_button.configure(**BUTTON_STYLES['primary'])
        self.advanced_search_button.configure(**BUTTON_STYLES['secondary'])

        # مسح المحتوى السابق
        for widget in self.search_content_frame.winfo_children():
            widget.destroy()

        # عنوان البحث العادي
        title_label = create_rtl_label(
            self.search_content_frame,
            text="البحث العادي بالكلمات",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 15))

        # وصف البحث
        desc_label = create_rtl_label(
            self.search_content_frame,
            text="ابحث في جميع المعاملات والتحويلات باستخدام الكلمات المفتاحية",
            font_size='body',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['title']
        )
        desc_label.pack(pady=(0, 20))

        # إطار البحث
        search_input_frame = ctk.CTkFrame(
            self.search_content_frame,
            fg_color=COLORS['bg_card'],
            corner_radius=10
        )
        search_input_frame.pack(fill="x", padx=20, pady=(0, 20))

        # حقل البحث
        search_label = create_rtl_label(
            search_input_frame,
            text="كلمات البحث:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        search_label.pack(anchor="w", padx=20, pady=(20, 5))

        self.simple_search_entry = create_rtl_entry(
            search_input_frame,
            placeholder_text="مثال: راتب، طعام، تحويل، إلخ...",
            height=50
        )
        self.simple_search_entry.pack(fill="x", padx=20, pady=(0, 20))

        # زر البحث
        search_button = create_rtl_button(
            search_input_frame,
            text="🔍 بحث",
            command=self.perform_simple_search,
            **BUTTON_STYLES['primary']
        )
        search_button.pack(pady=(0, 20))

        # إطار النتائج
        self.results_frame = ctk.CTkScrollableFrame(
            self.search_content_frame,
            fg_color=COLORS['bg_card'],
            corner_radius=10
        )
        self.results_frame.pack(fill="both", expand=True, padx=20)

        # رسالة ترحيبية
        welcome_label = create_rtl_label(
            self.results_frame,
            text="أدخل كلمات البحث واضغط على زر البحث لعرض النتائج",
            font_size='header',
            text_color=COLORS['text_muted'],
            **ARABIC_TEXT_STYLES['title']
        )
        welcome_label.pack(pady=50)

    def show_advanced_search(self):
        """عرض واجهة البحث المتقدم"""
        # تحديث أزرار نوع البحث
        self.simple_search_button.configure(**BUTTON_STYLES['secondary'])
        self.advanced_search_button.configure(**BUTTON_STYLES['primary'])

        # مسح المحتوى السابق
        for widget in self.search_content_frame.winfo_children():
            widget.destroy()

        # عنوان البحث المتقدم
        title_label = create_rtl_label(
            self.search_content_frame,
            text="البحث المتقدم",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 15))

        # وصف البحث
        desc_label = create_rtl_label(
            self.search_content_frame,
            text="بحث متقدم مع خيارات تصفية متعددة",
            font_size='body',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['title']
        )
        desc_label.pack(pady=(0, 20))

        # إطار البحث المتقدم
        advanced_search_frame = ctk.CTkFrame(
            self.search_content_frame,
            fg_color=COLORS['bg_card'],
            corner_radius=10
        )
        advanced_search_frame.pack(fill="x", padx=20, pady=(0, 20))

        # إطار قابل للتمرير للحقول
        fields_frame = ctk.CTkScrollableFrame(
            advanced_search_frame,
            fg_color="transparent",
            height=400
        )
        fields_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # كلمات البحث
        search_label = create_rtl_label(
            fields_frame,
            text="كلمات البحث (اختياري):",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        search_label.pack(anchor="w", pady=(10, 5))

        self.advanced_search_entry = create_rtl_entry(
            fields_frame,
            placeholder_text="ابحث في الوصف والملاحظات...",
            height=40
        )
        self.advanced_search_entry.pack(fill="x", pady=(0, 15))

        # نوع المعاملة
        type_label = create_rtl_label(
            fields_frame,
            text="نوع المعاملة:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        type_label.pack(anchor="w", pady=(0, 5))

        self.transaction_type_combo = ctk.CTkComboBox(
            fields_frame,
            values=["الكل", "الواردات", "المصروفات", "التحويلات"],
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.transaction_type_combo.pack(fill="x", pady=(0, 15))
        self.transaction_type_combo.set("الكل")

        # الحساب
        account_label = create_rtl_label(
            fields_frame,
            text="الحساب:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        account_label.pack(anchor="w", pady=(0, 5))

        # الحصول على الحسابات
        user_id = auth_manager.current_user['id']
        accounts = db.execute_query(
            "SELECT id, name FROM accounts WHERE user_id = %s AND is_active = TRUE ORDER BY name",
            (user_id,)
        )
        account_options = ["جميع الحسابات"] + [f"{acc['id']} - {acc['name']}" for acc in accounts] if accounts else ["جميع الحسابات"]

        self.account_combo = ctk.CTkComboBox(
            fields_frame,
            values=account_options,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.account_combo.pack(fill="x", pady=(0, 15))
        self.account_combo.set("جميع الحسابات")

        # التاريخ من
        date_from_label = create_rtl_label(
            fields_frame,
            text="من تاريخ (اختياري):",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        date_from_label.pack(anchor="w", pady=(0, 5))

        self.date_from_entry = create_rtl_entry(
            fields_frame,
            placeholder_text="YYYY-MM-DD",
            height=40
        )
        self.date_from_entry.pack(fill="x", pady=(0, 15))

        # التاريخ إلى
        date_to_label = create_rtl_label(
            fields_frame,
            text="إلى تاريخ (اختياري):",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        date_to_label.pack(anchor="w", pady=(0, 5))

        self.date_to_entry = create_rtl_entry(
            fields_frame,
            placeholder_text="YYYY-MM-DD",
            height=40
        )
        self.date_to_entry.pack(fill="x", pady=(0, 15))

        # المبلغ من
        amount_from_label = create_rtl_label(
            fields_frame,
            text="المبلغ من (اختياري):",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        amount_from_label.pack(anchor="w", pady=(0, 5))

        self.amount_from_entry = create_rtl_entry(
            fields_frame,
            placeholder_text="0.00",
            height=40
        )
        self.amount_from_entry.pack(fill="x", pady=(0, 15))

        # المبلغ إلى
        amount_to_label = create_rtl_label(
            fields_frame,
            text="المبلغ إلى (اختياري):",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        amount_to_label.pack(anchor="w", pady=(0, 5))

        self.amount_to_entry = create_rtl_entry(
            fields_frame,
            placeholder_text="0.00",
            height=40
        )
        self.amount_to_entry.pack(fill="x", pady=(0, 15))

        # أزرار البحث والمسح
        buttons_frame = ctk.CTkFrame(advanced_search_frame, fg_color="transparent")
        buttons_frame.pack(pady=(0, 20))

        search_button = create_rtl_button(
            buttons_frame,
            text="🔍 بحث متقدم",
            command=self.perform_advanced_search,
            **BUTTON_STYLES['primary']
        )
        search_button.pack(side="left", padx=(0, 10))

        clear_button = create_rtl_button(
            buttons_frame,
            text="🗑️ مسح الحقول",
            command=self.clear_advanced_search,
            **BUTTON_STYLES['secondary']
        )
        clear_button.pack(side="left")

        # إطار النتائج
        self.results_frame = ctk.CTkScrollableFrame(
            self.search_content_frame,
            fg_color=COLORS['bg_card'],
            corner_radius=10
        )
        self.results_frame.pack(fill="both", expand=True, padx=20)

        # رسالة ترحيبية
        welcome_label = ctk.CTkLabel(
            self.results_frame,
            text="اختر معايير البحث واضغط على زر البحث المتقدم لعرض النتائج",
            font=ctk.CTkFont(size=16),
            text_color=COLORS['text_muted']
        )
        welcome_label.pack(pady=50)

    def perform_simple_search(self):
        """تنفيذ البحث العادي"""
        search_text = self.simple_search_entry.get().strip()

        if not search_text:
            messagebox.showwarning("تنبيه", "يرجى إدخال كلمات البحث")
            return

        try:
            # مسح النتائج السابقة
            for widget in self.results_frame.winfo_children():
                widget.destroy()

            # عرض رسالة التحميل
            loading_label = create_rtl_label(
                self.results_frame,
                text="جاري البحث...",
                font_size='header',
                text_color=COLORS['text_secondary'],
                **ARABIC_TEXT_STYLES['title']
            )
            loading_label.pack(pady=20)

            # تحديث الواجهة
            self.results_frame.update()

            # تنفيذ البحث
            results = self.search_in_database(search_text)

            # مسح رسالة التحميل
            loading_label.destroy()

            # عرض النتائج
            self.display_search_results(results, f"نتائج البحث عن: '{search_text}'")

        except Exception as e:
            print(f"خطأ في البحث: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def perform_advanced_search(self):
        """تنفيذ البحث المتقدم"""
        try:
            # جمع معايير البحث
            search_criteria = {
                'search_text': self.advanced_search_entry.get().strip(),
                'transaction_type': self.transaction_type_combo.get(),
                'account': self.account_combo.get(),
                'date_from': self.date_from_entry.get().strip(),
                'date_to': self.date_to_entry.get().strip(),
                'amount_from': self.amount_from_entry.get().strip(),
                'amount_to': self.amount_to_entry.get().strip()
            }

            # مسح النتائج السابقة
            for widget in self.results_frame.winfo_children():
                widget.destroy()

            # عرض رسالة التحميل
            loading_label = create_rtl_label(
                self.results_frame,
                text="جاري البحث المتقدم...",
                font_size='header',
                text_color=COLORS['text_secondary'],
                **ARABIC_TEXT_STYLES['title']
            )
            loading_label.pack(pady=20)

            # تحديث الواجهة
            self.results_frame.update()

            # تنفيذ البحث المتقدم
            results = self.advanced_search_in_database(search_criteria)

            # مسح رسالة التحميل
            loading_label.destroy()

            # عرض النتائج
            self.display_search_results(results, "نتائج البحث المتقدم")

        except Exception as e:
            print(f"خطأ في البحث المتقدم: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث المتقدم: {str(e)}")

    def clear_advanced_search(self):
        """مسح حقول البحث المتقدم"""
        self.advanced_search_entry.delete(0, 'end')
        self.transaction_type_combo.set("الكل")
        self.account_combo.set("جميع الحسابات")
        self.date_from_entry.delete(0, 'end')
        self.date_to_entry.delete(0, 'end')
        self.amount_from_entry.delete(0, 'end')
        self.amount_to_entry.delete(0, 'end')

        # مسح النتائج
        for widget in self.results_frame.winfo_children():
            widget.destroy()

        # عرض رسالة ترحيبية
        welcome_label = create_rtl_label(
            self.results_frame,
            text="اختر معايير البحث واضغط على زر البحث المتقدم لعرض النتائج",
            font_size='header',
            text_color=COLORS['text_muted'],
            **ARABIC_TEXT_STYLES['title']
        )
        welcome_label.pack(pady=50)

    def search_in_database(self, search_text):
        """البحث في قاعدة البيانات - البحث العادي"""
        user_id = auth_manager.current_user['id']
        results = []

        try:
            # البحث في المعاملات (الواردات والمصروفات)
            transactions_query = """
                SELECT
                    t.id,
                    t.transaction_type,
                    t.amount,
                    t.description,
                    t.transaction_date,
                    t.created_at,
                    a.name as account_name,
                    c.symbol as currency_symbol,
                    'transaction' as result_type
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = %s
                AND (
                    t.description LIKE %s
                    OR a.name LIKE %s
                )
                ORDER BY t.transaction_date DESC, t.created_at DESC
            """

            search_pattern = f"%{search_text}%"
            transactions = db.execute_query(
                transactions_query,
                (user_id, search_pattern, search_pattern)
            )

            if transactions:
                results.extend(transactions)

            # البحث في التحويلات
            transfers_query = """
                SELECT
                    tr.id,
                    'transfer' as transaction_type,
                    tr.amount,
                    tr.description,
                    tr.transfer_date as transaction_date,
                    tr.created_at,
                    CONCAT(a1.name, ' → ', a2.name) as account_name,
                    c.symbol as currency_symbol,
                    'transfer' as result_type
                FROM transfers tr
                JOIN accounts a1 ON tr.from_account_id = a1.id
                JOIN accounts a2 ON tr.to_account_id = a2.id
                JOIN currencies c ON tr.currency_id = c.id
                WHERE tr.user_id = %s
                AND (
                    tr.description LIKE %s
                    OR a1.name LIKE %s
                    OR a2.name LIKE %s
                )
                ORDER BY tr.transfer_date DESC, tr.created_at DESC
            """

            transfers = db.execute_query(
                transfers_query,
                (user_id, search_pattern, search_pattern, search_pattern)
            )

            if transfers:
                results.extend(transfers)

            # ترتيب النتائج حسب التاريخ
            results.sort(key=lambda x: x['transaction_date'], reverse=True)

        except Exception as e:
            print(f"خطأ في البحث في قاعدة البيانات: {e}")
            raise

        return results

    def advanced_search_in_database(self, criteria):
        """البحث المتقدم في قاعدة البيانات"""
        user_id = auth_manager.current_user['id']
        results = []

        try:
            # بناء شروط البحث
            conditions = ["t.user_id = %s"]
            params = [user_id]

            # شرط البحث النصي
            if criteria['search_text']:
                conditions.append("(t.description LIKE %s OR a.name LIKE %s)")
                search_pattern = f"%{criteria['search_text']}%"
                params.extend([search_pattern, search_pattern])

            # شرط التاريخ من
            if criteria['date_from']:
                conditions.append("t.transaction_date >= %s")
                params.append(criteria['date_from'])

            # شرط التاريخ إلى
            if criteria['date_to']:
                conditions.append("t.transaction_date <= %s")
                params.append(criteria['date_to'])

            # شرط المبلغ من
            if criteria['amount_from']:
                try:
                    amount_from = float(criteria['amount_from'])
                    conditions.append("t.amount >= %s")
                    params.append(amount_from)
                except ValueError:
                    pass

            # شرط المبلغ إلى
            if criteria['amount_to']:
                try:
                    amount_to = float(criteria['amount_to'])
                    conditions.append("t.amount <= %s")
                    params.append(amount_to)
                except ValueError:
                    pass

            # شرط الحساب
            if criteria['account'] != "جميع الحسابات":
                account_id = int(criteria['account'].split(' - ')[0])
                conditions.append("t.account_id = %s")
                params.append(account_id)

            # البحث في المعاملات حسب النوع
            transaction_type = criteria['transaction_type']

            if transaction_type in ["الكل", "الواردات", "المصروفات"]:
                # بناء استعلام المعاملات
                transaction_conditions = conditions.copy()
                transaction_params = params.copy()

                if transaction_type == "الواردات":
                    transaction_conditions.append("t.transaction_type = 'income'")
                elif transaction_type == "المصروفات":
                    transaction_conditions.append("t.transaction_type = 'expense'")

                transactions_query = f"""
                    SELECT
                        t.id,
                        t.transaction_type,
                        t.amount,
                        t.description,
                        t.transaction_date,
                        t.created_at,
                        a.name as account_name,
                        c.symbol as currency_symbol,
                        'transaction' as result_type
                    FROM transactions t
                    JOIN accounts a ON t.account_id = a.id
                    JOIN currencies c ON t.currency_id = c.id
                    WHERE {' AND '.join(transaction_conditions)}
                    ORDER BY t.transaction_date DESC, t.created_at DESC
                """

                transactions = db.execute_query(transactions_query, transaction_params)
                if transactions:
                    results.extend(transactions)

            # البحث في التحويلات
            if transaction_type in ["الكل", "التحويلات"]:
                # بناء شروط التحويلات
                transfer_conditions = ["tr.user_id = %s"]
                transfer_params = [user_id]

                # شرط البحث النصي للتحويلات
                if criteria['search_text']:
                    transfer_conditions.append("(tr.description LIKE %s OR a1.name LIKE %s OR a2.name LIKE %s)")
                    search_pattern = f"%{criteria['search_text']}%"
                    transfer_params.extend([search_pattern, search_pattern, search_pattern])

                # شرط التاريخ من للتحويلات
                if criteria['date_from']:
                    transfer_conditions.append("tr.transfer_date >= %s")
                    transfer_params.append(criteria['date_from'])

                # شرط التاريخ إلى للتحويلات
                if criteria['date_to']:
                    transfer_conditions.append("tr.transfer_date <= %s")
                    transfer_params.append(criteria['date_to'])

                # شرط المبلغ من للتحويلات
                if criteria['amount_from']:
                    try:
                        amount_from = float(criteria['amount_from'])
                        transfer_conditions.append("tr.amount >= %s")
                        transfer_params.append(amount_from)
                    except ValueError:
                        pass

                # شرط المبلغ إلى للتحويلات
                if criteria['amount_to']:
                    try:
                        amount_to = float(criteria['amount_to'])
                        transfer_conditions.append("tr.amount <= %s")
                        transfer_params.append(amount_to)
                    except ValueError:
                        pass

                # شرط الحساب للتحويلات
                if criteria['account'] != "جميع الحسابات":
                    account_id = int(criteria['account'].split(' - ')[0])
                    transfer_conditions.append("(tr.from_account_id = %s OR tr.to_account_id = %s)")
                    transfer_params.extend([account_id, account_id])

                transfers_query = f"""
                    SELECT
                        tr.id,
                        'transfer' as transaction_type,
                        tr.amount,
                        tr.description,
                        tr.transfer_date as transaction_date,
                        tr.created_at,
                        CONCAT(a1.name, ' → ', a2.name) as account_name,
                        c.symbol as currency_symbol,
                        'transfer' as result_type
                    FROM transfers tr
                    JOIN accounts a1 ON tr.from_account_id = a1.id
                    JOIN accounts a2 ON tr.to_account_id = a2.id
                    JOIN currencies c ON tr.currency_id = c.id
                    WHERE {' AND '.join(transfer_conditions)}
                    ORDER BY tr.transfer_date DESC, tr.created_at DESC
                """

                transfers = db.execute_query(transfers_query, transfer_params)
                if transfers:
                    results.extend(transfers)

            # ترتيب النتائج حسب التاريخ
            results.sort(key=lambda x: x['transaction_date'], reverse=True)

        except Exception as e:
            print(f"خطأ في البحث المتقدم في قاعدة البيانات: {e}")
            raise

        return results

    def display_search_results(self, results, title):
        """عرض نتائج البحث"""
        # عنوان النتائج
        title_label = create_rtl_label(
            self.results_frame,
            text=title,
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 10))

        if not results:
            # لا توجد نتائج
            no_results_label = create_rtl_label(
                self.results_frame,
                text="لم يتم العثور على نتائج مطابقة لمعايير البحث",
                font_size='header',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['title']
            )
            no_results_label.pack(pady=30)
            return

        # عدد النتائج
        count_label = create_rtl_label(
            self.results_frame,
            text=f"تم العثور على {len(results)} نتيجة",
            font_size='body',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['title']
        )
        count_label.pack(pady=(0, 20))

        # عرض النتائج
        for result in results:
            self.create_search_result_card(self.results_frame, result)

    def create_search_result_card(self, parent, result):
        """إنشاء بطاقة نتيجة بحث مع دعم RTL محسن"""
        card = ctk.CTkFrame(
            parent,
            **CARD_STYLES['elevated']
        )
        card.pack(fill="x", padx=20, pady=10)

        # معلومات النتيجة
        info_frame = ctk.CTkFrame(card, fg_color="transparent")
        info_frame.pack(fill="x", padx=20, pady=15)

        # الصف الأول: النوع والمبلغ والتاريخ (مع دعم RTL)
        top_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        top_frame.pack(fill="x")

        # نوع المعاملة والأيقونة
        if result['result_type'] == 'transfer':
            type_icon = "🔄"
            type_text = "تحويل"
            type_color = COLORS['info']
        elif result['transaction_type'] == 'income':
            type_icon = "📈"
            type_text = "وارد"
            type_color = COLORS['success']
        else:  # expense
            type_icon = "📉"
            type_text = "مصروف"
            type_color = COLORS['error']

        # نوع المعاملة (في أقصى اليمين للـ RTL)
        type_label = create_rtl_label(
            top_frame,
            text=f"{type_icon} {type_text}",
            font_size='header',
            text_color=type_color,
            **ARABIC_TEXT_STYLES['label']
        )
        type_label.pack(side="right")

        # التاريخ (في اليسار للـ RTL)
        date_label = create_rtl_label(
            top_frame,
            text=str(result['transaction_date']),
            font_size='body',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        date_label.pack(side="left", padx=(0, 20))

        # المبلغ (في الوسط للـ RTL)
        amount_prefix = ""
        if result['transaction_type'] == 'income':
            amount_prefix = "+"
        elif result['transaction_type'] == 'expense':
            amount_prefix = "-"

        amount_label = create_rtl_label(
            top_frame,
            text=f"{amount_prefix}{result['amount']:,.2f} {result['currency_symbol']}",
            font_size='subtitle',
            text_color=type_color,
            **ARABIC_TEXT_STYLES['label']
        )
        amount_label.pack(side="left")

        # الصف الثاني: الوصف
        if result.get('description'):
            desc_label = create_rtl_label(
                info_frame,
                text=result['description'],
                font_size='header',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            desc_label.pack(anchor="e", pady=(10, 0))  # تغيير من "w" إلى "e" للـ RTL

        # الصف الثالث: الحساب
        account_label = create_rtl_label(
            info_frame,
            text=f"الحساب: {result['account_name']}",
            font_size='body',
            text_color=COLORS['text_muted'],
            **ARABIC_TEXT_STYLES['label']
        )
        account_label.pack(anchor="e", pady=(5, 0))  # تغيير من "w" إلى "e" للـ RTL

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(card, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(10, 15))

        # زر عرض التفاصيل
        details_button = ctk.CTkButton(
            buttons_frame,
            text="👁️ عرض التفاصيل",
            font=ctk.CTkFont(size=12),
            command=lambda: self.show_result_details(result),
            **BUTTON_STYLES['primary']
        )
        details_button.pack(side="left", padx=(0, 5))

        # زر التعديل (فقط للمعاملات، ليس للتحويلات)
        if result['result_type'] == 'transaction':
            edit_button = ctk.CTkButton(
                buttons_frame,
                text="✏️ تعديل",
                font=ctk.CTkFont(size=12),
                command=lambda: self.show_edit_transaction_dialog(result, result['transaction_type']),
                **BUTTON_STYLES['secondary']
            )
            edit_button.pack(side="left", padx=5)

        # زر الحذف
        delete_button = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف",
            font=ctk.CTkFont(size=12),
            command=lambda: self.delete_search_result(result),
            **BUTTON_STYLES['danger']
        )
        delete_button.pack(side="right")

    def show_result_details(self, result):
        """عرض تفاصيل النتيجة"""
        # إنشاء نافذة التفاصيل
        details_window = ctk.CTkToplevel(self.window)
        details_window.title("تفاصيل المعاملة")
        details_window.geometry("500x600")
        details_window.resizable(False, False)
        details_window.transient(self.window)
        details_window.grab_set()

        # توسيط النافذة
        details_window.update_idletasks()
        x = (details_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (details_window.winfo_screenheight() // 2) - (600 // 2)
        details_window.geometry(f"500x600+{x}+{y}")

        # عنوان النافذة
        if result['result_type'] == 'transfer':
            title_text = "تفاصيل التحويل"
        elif result['transaction_type'] == 'income':
            title_text = "تفاصيل الوارد"
        else:
            title_text = "تفاصيل المصروف"

        title_label = ctk.CTkLabel(
            details_window,
            text=title_text,
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=COLORS['text_primary']
        )
        title_label.pack(pady=(20, 30))

        # إطار التفاصيل
        details_frame = ctk.CTkScrollableFrame(
            details_window,
            fg_color=COLORS['bg_card'],
            corner_radius=10
        )
        details_frame.pack(fill="both", expand=True, padx=30, pady=(0, 20))

        # عرض التفاصيل
        details_data = [
            ("المعرف", str(result['id'])),
            ("النوع", self.get_transaction_type_text(result)),
            ("المبلغ", f"{result['amount']:,.2f} {result['currency_symbol']}"),
            ("التاريخ", str(result['transaction_date'])),
            ("الحساب", result['account_name']),
            ("الوصف", result.get('description', 'لا يوجد')),
            ("تاريخ الإنشاء", str(result['created_at']))
        ]

        for label, value in details_data:
            detail_frame = ctk.CTkFrame(details_frame, fg_color="transparent")
            detail_frame.pack(fill="x", pady=5)

            label_widget = ctk.CTkLabel(
                detail_frame,
                text=f"{label}:",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=COLORS['text_primary']
            )
            label_widget.pack(side="right", padx=(10, 0))

            value_widget = ctk.CTkLabel(
                detail_frame,
                text=str(value),
                font=ctk.CTkFont(size=14),
                text_color=COLORS['text_secondary']
            )
            value_widget.pack(side="left")

        # زر الإغلاق
        close_button = ctk.CTkButton(
            details_window,
            text="إغلاق",
            font=ctk.CTkFont(size=16, weight="bold"),
            command=details_window.destroy,
            **BUTTON_STYLES['secondary']
        )
        close_button.pack(pady=20)

    def get_transaction_type_text(self, result):
        """الحصول على نص نوع المعاملة"""
        if result['result_type'] == 'transfer':
            return "تحويل بين الحسابات"
        elif result['transaction_type'] == 'income':
            return "وارد"
        else:
            return "مصروف"

    def delete_search_result(self, result):
        """حذف نتيجة البحث"""
        # تأكيد الحذف
        confirm = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف هذه المعاملة؟\n\n"
            f"النوع: {self.get_transaction_type_text(result)}\n"
            f"المبلغ: {result['amount']:,.2f} {result['currency_symbol']}\n"
            f"التاريخ: {result['transaction_date']}"
        )

        if confirm:
            try:
                if result['result_type'] == 'transfer':
                    # حذف التحويل
                    self.delete_transfer(result['id'])
                else:
                    # حذف المعاملة
                    self.delete_transaction(result['id'], result['transaction_type'])

                # إعادة تنفيذ البحث لتحديث النتائج
                if hasattr(self, 'simple_search_entry') and self.simple_search_entry.winfo_exists():
                    # إذا كنا في البحث العادي
                    self.perform_simple_search()
                else:
                    # إذا كنا في البحث المتقدم
                    self.perform_advanced_search()

            except Exception as e:
                print(f"خطأ في حذف النتيجة: {e}")
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    def show_users(self):
        """عرض صفحة إدارة المستخدمين"""
        # التحقق من صلاحيات المدير
        if not auth_manager.can_manage_users():
            messagebox.showerror(
                "غير مصرح",
                "ليس لديك صلاحية لإدارة المستخدمين.\nيجب أن تكون مديراً للوصول إلى هذه الميزة."
            )
            return

        # تسجيل محاولة الوصول
        auth_manager.log_activity('access', 'user_management', None, 'دخول إلى صفحة إدارة المستخدمين')

        self.current_page = "users"
        self.set_active_button("users")
        self.clear_content()

        # عنوان الصفحة
        title_label = create_rtl_label(
            self.content_frame,
            text="إدارة المستخدمين",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 20))

        # إطار للأزرار
        buttons_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        buttons_frame.pack(pady=20)

        # زر إضافة مستخدم جديد
        add_button = create_rtl_button(
            buttons_frame,
            text="+ إضافة مستخدم جديد",
            command=self.add_user,
            **BUTTON_STYLES['primary']
        )
        add_button.pack(side="right", padx=10)

        # زر تحديث القائمة
        refresh_button = create_rtl_button(
            buttons_frame,
            text="🔄 تحديث",
            command=self.load_users_list,
            **BUTTON_STYLES['secondary']
        )
        refresh_button.pack(side="right", padx=10)

        # إطار قائمة المستخدمين
        self.users_list_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color=COLORS['bg_light'],
            corner_radius=10,
            height=400
        )
        self.users_list_frame.pack(fill="both", expand=True, pady=20, padx=20)

        # تحميل قائمة المستخدمين
        self.load_users_list()

    def load_users_list(self):
        """تحميل قائمة المستخدمين - محسن"""
        try:
            print("🔄 بدء تحميل قائمة المستخدمين...")

            # مسح المحتوى السابق
            for widget in self.users_list_frame.winfo_children():
                widget.destroy()

            # جلب المستخدمين من قاعدة البيانات
            from database.models import User
            print("📊 جلب المستخدمين من قاعدة البيانات...")
            users = User.get_all()

            print(f"📋 تم جلب {len(users) if users else 0} مستخدم")

            if not users:
                print("⚠️ لا توجد مستخدمين في النظام")
                no_users_label = create_rtl_label(
                    self.users_list_frame,
                    text="لا توجد مستخدمين في النظام",
                    font_size='body',
                    text_color=COLORS['text_secondary']
                )
                no_users_label.pack(pady=50)
                return

            # عرض المستخدمين مع معالجة محسنة للأخطاء
            print("🎨 إنشاء بطاقات المستخدمين...")
            successful_cards = 0
            skipped_cards = 0

            for i, user in enumerate(users):
                try:
                    username = user.get('username', f'مستخدم_{i+1}') if user else f'مستخدم_{i+1}'
                    print(f"   إنشاء بطاقة للمستخدم {i+1}: {username}")

                    # التحقق من البيانات الأساسية قبل إنشاء البطاقة
                    if user and isinstance(user, dict) and user.get('id'):
                        self.create_user_card(user)
                        successful_cards += 1
                    else:
                        print(f"   ⚠️ تم تجاهل المستخدم {i+1} بسبب بيانات ناقصة")
                        skipped_cards += 1

                except Exception as e:
                    print(f"   ⚠️ مشكلة في المستخدم {i+1}: {e}")
                    skipped_cards += 1

            print(f"✅ تم تحميل قائمة المستخدمين: {successful_cards} نجح، {skipped_cards} تم تجاهله")

        except Exception as e:
            error_msg = f"خطأ في تحميل المستخدمين: {str(e)}"
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()

            # عرض رسالة خطأ مبسطة في الواجهة فقط للأخطاء الحرجة
            try:
                # محاولة عرض رسالة خطأ مفيدة
                if "connection" in str(e).lower() or "database" in str(e).lower():
                    error_text = "مشكلة في الاتصال بقاعدة البيانات\nيرجى التحقق من الاتصال والمحاولة مرة أخرى"
                else:
                    error_text = "مشكلة في تحميل قائمة المستخدمين\nيرجى إعادة تحميل الصفحة"

                error_label = create_rtl_label(
                    self.users_list_frame,
                    text=error_text,
                    font_size='body',
                    text_color=COLORS.get('error', '#ff4444')
                )
                error_label.pack(pady=50)

                # عرض زر إعادة المحاولة
                retry_button = create_rtl_button(
                    self.users_list_frame,
                    text="🔄 إعادة المحاولة",
                    command=self.load_users_list,
                    **BUTTON_STYLES.get('primary', {})
                )
                retry_button.pack(pady=10)

            except:
                # إذا فشل عرض رسالة الخطأ، اعرض رسالة بسيطة
                simple_label = create_rtl_label(
                    self.users_list_frame,
                    text="مشكلة في تحميل المستخدمين",
                    font_size='body',
                    text_color='#ff4444'
                )
                simple_label.pack(pady=50)

    def create_user_card(self, user):
        """إنشاء بطاقة مستخدم - محسن"""
        try:
            # التحقق من صحة بيانات المستخدم
            if not user or not isinstance(user, dict):
                print(f"⚠️ بيانات مستخدم غير صحيحة: {user}")
                return

            # إطار البطاقة
            card_frame = ctk.CTkFrame(
                self.users_list_frame,
                fg_color=COLORS['bg_card'],
                corner_radius=10,
                border_width=1,
                border_color=COLORS['border']
            )
            card_frame.pack(fill="x", pady=5, padx=10)

            # إطار المعلومات
            info_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
            info_frame.pack(side="right", fill="both", expand=True, padx=15, pady=10)

            # اسم المستخدم
            username = user.get('username', 'غير محدد')
            username_label = create_rtl_label(
                info_frame,
                text=f"اسم المستخدم: {username}",
                font_size='subtitle',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            username_label.pack(anchor="e")

            # الاسم الكامل
            fullname = user.get('full_name', 'غير محدد')
            fullname_label = create_rtl_label(
                info_frame,
                text=f"الاسم الكامل: {fullname}",
                font_size='body',
                text_color=COLORS['text_secondary']
            )
            fullname_label.pack(anchor="e")

            # الدور والحالة
            role = user.get('role', 'user')
            is_active = user.get('is_active', True)

            role_text = "مدير" if role == 'admin' else "مستخدم"
            status_text = "نشط" if is_active else "معطل"
            status_color = COLORS['success'] if is_active else COLORS['error']

            details_label = create_rtl_label(
                info_frame,
                text=f"الدور: {role_text} | الحالة: {status_text}",
                font_size='body',
                text_color=status_color
            )
            details_label.pack(anchor="e")

            # تاريخ الإنشاء
            created_at = user.get('created_at')
            if created_at:
                try:
                    if hasattr(created_at, 'strftime'):
                        created_at_text = created_at.strftime("%Y-%m-%d %H:%M")
                    else:
                        created_at_text = str(created_at)
                except:
                    created_at_text = "غير محدد"
            else:
                created_at_text = "غير محدد"

            created_label = create_rtl_label(
                info_frame,
                text=f"تاريخ الإنشاء: {created_at_text}",
                font_size='small',
                text_color=COLORS['text_secondary']
            )
            created_label.pack(anchor="e")

            # إطار الأزرار
            buttons_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
            buttons_frame.pack(side="left", padx=10, pady=10)

            # زر التعديل
            edit_button = create_rtl_button(
                buttons_frame,
                text="✏️ تعديل",
                command=lambda: self.edit_user(user),
                width=80,
                **BUTTON_STYLES['secondary']
            )
            edit_button.pack(pady=2)

            # زر إعادة تعيين كلمة المرور
            reset_button = create_rtl_button(
                buttons_frame,
                text="🔑 إعادة تعيين",
                command=lambda: self.reset_user_password(user),
                width=80,
                **BUTTON_STYLES['warning']
            )
            reset_button.pack(pady=2)

            # زر الحذف/التفعيل
            if is_active:
                delete_button = create_rtl_button(
                    buttons_frame,
                    text="🗑️ تعطيل",
                    command=lambda: self.toggle_user_status(user, False),
                    width=80,
                    **BUTTON_STYLES['danger']
                )
            else:
                delete_button = create_rtl_button(
                    buttons_frame,
                    text="✅ تفعيل",
                    command=lambda: self.toggle_user_status(user, True),
                    width=80,
                    **BUTTON_STYLES['success']
                )
            delete_button.pack(pady=2)

            # زر الحذف النهائي (للمديرين فقط)
            from utils.auth import auth_manager
            if auth_manager.is_admin() and auth_manager.current_user['id'] != user.get('id'):
                permanent_delete_button = create_rtl_button(
                    buttons_frame,
                    text="🗑️ حذف نهائياً",
                    command=lambda: self.delete_user_permanently(user),
                    width=80,
                    **BUTTON_STYLES['danger']
                )
                permanent_delete_button.pack(pady=2)

        except Exception as e:
            # تسجيل الخطأ في وحدة التحكم فقط بدون عرض رسالة خطأ للمستخدم
            print(f"⚠️ تحذير: مشكلة في عرض بطاقة المستخدم {user.get('username', 'غير معروف')}: {e}")

            # محاولة إنشاء بطاقة مبسطة بدلاً من عرض رسالة خطأ
            try:
                self.create_simple_user_card(user)
            except:
                # إذا فشلت البطاقة المبسطة أيضاً، تجاهل المستخدم ولا تعرض رسالة خطأ
                print(f"⚠️ تم تجاهل المستخدم {user.get('username', 'غير معروف')} بسبب مشاكل في البيانات")

    def create_simple_user_card(self, user):
        """إنشاء بطاقة مستخدم مبسطة في حالة وجود مشاكل في البيانات"""
        try:
            # إطار البطاقة المبسطة
            card_frame = ctk.CTkFrame(
                self.users_list_frame,
                fg_color=COLORS.get('bg_card', '#2b2b2b'),
                corner_radius=10,
                border_width=1,
                border_color=COLORS.get('border', '#404040')
            )
            card_frame.pack(fill="x", pady=5, padx=10)

            # معلومات أساسية فقط
            info_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
            info_frame.pack(side="right", fill="both", expand=True, padx=15, pady=10)

            # اسم المستخدم (مع قيم افتراضية آمنة)
            username = str(user.get('username', 'مستخدم غير معروف')) if user else 'بيانات مفقودة'

            username_label = create_rtl_label(
                info_frame,
                text=f"اسم المستخدم: {username}",
                font_size='subtitle',
                text_color=COLORS.get('text_primary', '#ffffff')
            )
            username_label.pack(anchor="e")

            # الاسم الكامل
            fullname = str(user.get('full_name', 'غير محدد')) if user else 'غير محدد'

            fullname_label = create_rtl_label(
                info_frame,
                text=f"الاسم الكامل: {fullname}",
                font_size='body',
                text_color=COLORS.get('text_secondary', '#cccccc')
            )
            fullname_label.pack(anchor="e")

            # حالة تحذيرية
            warning_label = create_rtl_label(
                info_frame,
                text="⚠️ بيانات غير مكتملة - يرجى مراجعة هذا المستخدم",
                font_size='small',
                text_color=COLORS.get('warning', '#ffaa00')
            )
            warning_label.pack(anchor="e")

        except Exception as e:
            print(f"⚠️ فشل في إنشاء البطاقة المبسطة: {e}")

    def add_user(self):
        """إضافة مستخدم جديد"""
        try:
            from gui.user_management_windows import AddUserWindow
            AddUserWindow(self.window, callback=self.load_users_list)
        except Exception as e:
            print(f"خطأ في فتح نافذة إضافة المستخدم: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء فتح نافذة إضافة المستخدم: {str(e)}")

    def edit_user(self, user):
        """تعديل بيانات المستخدم"""
        try:
            from gui.user_management_windows import EditUserWindow
            EditUserWindow(self.window, user, callback=self.load_users_list)
        except Exception as e:
            print(f"خطأ في فتح نافذة تعديل المستخدم: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء فتح نافذة تعديل المستخدم: {str(e)}")

    def reset_user_password(self, user):
        """إعادة تعيين كلمة مرور المستخدم"""
        try:
            from gui.user_management_windows import ResetPasswordWindow
            ResetPasswordWindow(self.window, user, callback=self.load_users_list)
        except Exception as e:
            print(f"خطأ في فتح نافذة إعادة تعيين كلمة المرور: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء فتح نافذة إعادة تعيين كلمة المرور: {str(e)}")

    def toggle_user_status(self, user, activate):
        """تفعيل أو تعطيل المستخدم"""
        try:
            # التحقق من الصلاحيات
            if not auth_manager.can_manage_users():
                messagebox.showerror("غير مصرح", "ليس لديك صلاحية لإدارة المستخدمين")
                return

            # منع المستخدم من تعطيل نفسه
            if not activate and auth_manager.current_user['id'] == user['id']:
                messagebox.showerror("خطأ", "لا يمكنك تعطيل حسابك الخاص")
                return

            action = "تفعيل" if activate else "تعطيل"
            warning_text = f"هل أنت متأكد من {action} المستخدم {user['username']}؟"

            if not activate:
                warning_text += "\n\nتحذير: سيفقد المستخدم إمكانية الوصول إلى النظام."

            result = messagebox.askyesno(
                "تأكيد العملية",
                warning_text,
                icon='warning'
            )

            if result:
                if activate:
                    success, message = auth_manager.update_user_data(user['id'], is_active=True)
                    log_action = f"تفعيل المستخدم {user['username']}"
                else:
                    success, message = auth_manager.delete_user_account(user['id'])
                    log_action = f"تعطيل المستخدم {user['username']}"

                if success:
                    messagebox.showinfo("نجح", message)
                    # تسجيل العملية
                    auth_manager.log_activity('update', 'users', user['id'], log_action)
                    self.load_users_list()  # تحديث القائمة
                else:
                    messagebox.showerror("خطأ", message)

        except Exception as e:
            print(f"خطأ في تغيير حالة المستخدم: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تغيير حالة المستخدم: {str(e)}")

    def delete_user_permanently(self, user):
        """حذف المستخدم نهائياً من قاعدة البيانات (محدث لدعم حذف جميع أنواع المستخدمين)"""
        try:
            # التحقق من الصلاحيات
            if not auth_manager.can_manage_users():
                messagebox.showerror("غير مصرح", "ليس لديك صلاحية لحذف المستخدمين")
                return

            # منع المستخدم من حذف نفسه
            if auth_manager.current_user['id'] == user['id']:
                messagebox.showerror("خطأ", "لا يمكنك حذف حسابك الخاص")
                return

            # تحذير خاص للمديرين
            is_admin_user = user.get('role') == 'admin'
            user_type_text = "المدير" if is_admin_user else "المستخدم"

            # التحقق من أمان حذف المدير
            if is_admin_user:
                can_delete, safety_message = self.check_admin_deletion_safety(user)
                if not can_delete:
                    messagebox.showerror("لا يمكن الحذف", safety_message)
                    return

            # رسالة تحذير مختلفة للمديرين
            if is_admin_user:
                confirmation_message = f"""🚨 تحذير خطير: حذف مدير النظام 🚨

أنت على وشك حذف المستخدم المدير '{user['username']}' نهائياً!

⚠️ تحذيرات مهمة:
• هذا مستخدم مدير له صلاحيات كاملة في النظام
• سيفقد جميع صلاحياته الإدارية فوراً
• لن يتمكن من الوصول لإدارة النظام مرة أخرى
• سيتم حذف جميع بياناته وسجلاته نهائياً
• لا يمكن التراجع عن هذا الإجراء أبداً

🔒 تأكد من وجود مديرين آخرين في النظام قبل المتابعة

هل أنت متأكد من حذف هذا المدير نهائياً؟"""
            else:
                confirmation_message = f"""⚠️ تحذير: حذف نهائي ⚠️

هل أنت متأكد من حذف {user_type_text} '{user['username']}' نهائياً؟

⚠️ هذا الإجراء:
• سيحذف المستخدم نهائياً من قاعدة البيانات
• لا يمكن التراجع عنه أو استرداد البيانات
• سيحذف جميع البيانات المرتبطة بهذا المستخدم
• سيؤثر على سجلات النظام

هل تريد المتابعة؟"""

            result = messagebox.askyesno(
                f"تأكيد حذف {user_type_text}",
                confirmation_message,
                icon='warning'
            )

            if not result:
                return

            # تأكيد إضافي مع تحذير أقوى للمديرين
            if is_admin_user:
                final_confirmation = messagebox.askyesno(
                    "تأكيد نهائي - حذف مدير",
                    f"""🚨 التأكيد الأخير لحذف المدير 🚨

هذا هو التأكيد الأخير لحذف المدير '{user['username']}'!

⚠️ تذكير أخير:
• سيفقد جميع صلاحياته الإدارية
• لن يتمكن من إدارة النظام مرة أخرى
• سيتم حذف جميع بياناته نهائياً
• لا يمكن التراجع عن هذا الإجراء

هل أنت متأكد 100% من حذف هذا المدير؟""",
                    icon='error'
                )
            else:
                final_confirmation = messagebox.askyesno(
                    "تأكيد نهائي",
                    f"هذا هو التأكيد الأخير!\n\nسيتم حذف {user_type_text} '{user['username']}' نهائياً.\n\nهل أنت متأكد 100%؟",
                    icon='warning'
                )

            if not final_confirmation:
                return

            # حذف المستخدم
            success, message = auth_manager.delete_user_permanently(user['id'])

            if success:
                success_message = f"تم حذف {user_type_text} '{user['username']}' نهائياً"
                messagebox.showinfo("تم الحذف", success_message)

                # تسجيل مفصل للعملية
                log_message = f"حذف نهائي للمستخدم {user['username']} (النوع: {user.get('role', 'user')}) بواسطة المدير {auth_manager.current_user['username']}"

                auth_manager.log_activity(
                    'delete_user_permanently',
                    'users',
                    user['id'],
                    log_message
                )

                # تسجيل إضافي في ملف السجل
                import logging
                if is_admin_user:
                    logging.warning(f"تم حذف مدير النظام: {user['username']} (ID: {user['id']}) بواسطة المدير: {auth_manager.current_user['username']} (ID: {auth_manager.current_user['id']})")
                else:
                    logging.info(f"تم حذف المستخدم: {user['username']} (ID: {user['id']}) بواسطة المدير: {auth_manager.current_user['username']} (ID: {auth_manager.current_user['id']})")

                # تحديث القائمة
                self.load_users_list()

                # إظهار رسالة إضافية للمديرين
                if is_admin_user:
                    messagebox.showinfo(
                        "تم حذف المدير",
                        f"تم حذف المدير '{user['username']}' نهائياً من النظام.\n\nتم تسجيل هذه العملية في ملف السجل للمراجعة."
                    )

            else:
                error_message = f"فشل في حذف {user_type_text}: {message}"
                messagebox.showerror("خطأ", error_message)

                # تسجيل محاولة الحذف الفاشلة
                import logging
                logging.error(f"فشل في حذف المستخدم {user['username']} (ID: {user['id']}) بواسطة المدير {auth_manager.current_user['username']}: {message}")

        except Exception as e:
            print(f"خطأ في حذف المستخدم: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف المستخدم: {str(e)}")

            # تسجيل الخطأ
            import logging
            logging.error(f"خطأ في حذف المستخدم {user.get('username', 'غير معروف')}: {str(e)}")

    def get_admin_count(self):
        """الحصول على عدد المديرين النشطين في النظام"""
        try:
            query = "SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = TRUE"
            result = db.execute_query(query)
            return result[0]['count'] if result else 0
        except Exception as e:
            print(f"خطأ في الحصول على عدد المديرين: {e}")
            return 0

    def check_admin_deletion_safety(self, user):
        """التحقق من أمان حذف المدير"""
        if user.get('role') != 'admin':
            return True, ""

        admin_count = self.get_admin_count()
        if admin_count <= 1:
            return False, "لا يمكن حذف المدير الوحيد في النظام"

        return True, f"يوجد {admin_count} مديرين في النظام، يمكن حذف هذا المدير بأمان"

    def show_database_management(self):
        """عرض صفحة إدارة قاعدة البيانات (للمديرين فقط)"""
        # التحقق من صلاحيات المدير
        if not auth_manager.is_admin():
            messagebox.showerror("غير مصرح", "هذه الميزة متاحة للمديرين فقط")
            return

        self.current_page = "database"
        self.set_active_button("database")
        self.clear_content()

        # عنوان الصفحة
        title_label = create_rtl_label(
            self.content_frame,
            text="🗄️ إدارة قاعدة البيانات",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 20))

        # إطار المحتوى القابل للتمرير
        database_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            fg_color=COLORS['bg_light'],
            corner_radius=10
        )
        database_frame.pack(fill="both", expand=True, padx=30, pady=(0, 30))

        # تحميل أقسام إدارة قاعدة البيانات
        self.load_database_management_sections(database_frame)

    def load_database_management_sections(self, parent):
        """تحميل أقسام إدارة قاعدة البيانات"""
        try:
            # القسم الأول: إدارة النسخ الاحتياطية
            self.create_backup_management_section(parent)

            # القسم الثاني: إعدادات الاتصال بقاعدة البيانات
            self.create_database_connection_section(parent)

        except Exception as e:
            print(f"خطأ في تحميل أقسام إدارة قاعدة البيانات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل الصفحة: {str(e)}")

    def create_backup_management_section(self, parent):
        """إنشاء قسم إدارة النسخ الاحتياطية"""
        # عنوان القسم
        title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        title_frame.pack(fill="x", padx=20, pady=10)

        title_label = create_rtl_label(
            title_frame,
            text="📥 إدارة النسخ الاحتياطية",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=15)

        # إطار المحتوى
        content_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=20, pady=(0, 15))

        # أزرار النسخ الاحتياطي الفورية
        buttons_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=(0, 20))

        # زر إنشاء نسخة احتياطية فورية
        create_backup_button = create_rtl_button(
            buttons_frame,
            text="📥 إنشاء نسخة احتياطية فورية",
            command=self.create_instant_backup,
            **BUTTON_STYLES['primary']
        )
        create_backup_button.pack(fill="x", pady=5)

        # زر استعادة نسخة احتياطية
        restore_backup_button = create_rtl_button(
            buttons_frame,
            text="📤 استعادة نسخة احتياطية",
            command=self.restore_backup_dialog,
            **BUTTON_STYLES['secondary']
        )
        restore_backup_button.pack(fill="x", pady=5)

        # إعدادات النسخ الاحتياطي التلقائي
        auto_backup_frame = ctk.CTkFrame(content_frame, fg_color=COLORS['bg_secondary'], corner_radius=8)
        auto_backup_frame.pack(fill="x", pady=(10, 0))

        auto_backup_title = create_rtl_label(
            auto_backup_frame,
            text="⚙️ إعدادات النسخ الاحتياطي التلقائي",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        auto_backup_title.pack(pady=(15, 10))

        # تفعيل/إلغاء النسخ التلقائي
        self.auto_backup_var = ctk.BooleanVar(value=True)
        auto_backup_checkbox = ctk.CTkCheckBox(
            auto_backup_frame,
            text="تفعيل النسخ الاحتياطي التلقائي",
            variable=self.auto_backup_var,
            font=ctk.CTkFont(size=14),
            command=self.toggle_auto_backup
        )
        auto_backup_checkbox.pack(anchor="e", padx=20, pady=5)

        # اختيار الفترة الزمنية
        interval_frame = ctk.CTkFrame(auto_backup_frame, fg_color="transparent")
        interval_frame.pack(fill="x", padx=20, pady=10)

        interval_label = create_rtl_label(
            interval_frame,
            text="فترة النسخ الاحتياطي:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        interval_label.pack(anchor="e", pady=(0, 5))

        from config.settings import BACKUP_INTERVALS
        interval_options = [info['name'] for info in BACKUP_INTERVALS.values()]
        self.interval_combo = ctk.CTkComboBox(
            interval_frame,
            values=interval_options,
            height=35,
            font=ctk.CTkFont(size=14),
            command=self.update_backup_interval
        )
        self.interval_combo.pack(fill="x", pady=(0, 10))
        self.interval_combo.set("يومياً")  # القيمة الافتراضية

        # عدد النسخ المحفوظة
        max_files_frame = ctk.CTkFrame(auto_backup_frame, fg_color="transparent")
        max_files_frame.pack(fill="x", padx=20, pady=10)

        max_files_label = create_rtl_label(
            max_files_frame,
            text="عدد النسخ الاحتياطية المحفوظة:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        max_files_label.pack(anchor="e", pady=(0, 5))

        self.max_files_entry = ctk.CTkEntry(
            max_files_frame,
            height=35,
            font=ctk.CTkFont(size=14),
            placeholder_text="30"
        )
        self.max_files_entry.pack(fill="x", pady=(0, 10))
        self.max_files_entry.insert(0, "30")

        # زر حفظ إعدادات النسخ الاحتياطي
        save_backup_settings_button = create_rtl_button(
            auto_backup_frame,
            text="💾 حفظ إعدادات النسخ الاحتياطي",
            command=self.save_backup_settings,
            **BUTTON_STYLES['success']
        )
        save_backup_settings_button.pack(fill="x", padx=20, pady=(0, 15))

    def create_database_connection_section(self, parent):
        """إنشاء قسم إعدادات الاتصال بقاعدة البيانات"""
        # عنوان القسم
        title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        title_frame.pack(fill="x", padx=20, pady=10)

        title_label = create_rtl_label(
            title_frame,
            text="🔗 إعدادات الاتصال بقاعدة البيانات",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=15)

        # إطار المحتوى
        content_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=20, pady=(0, 15))

        # عرض الإعدادات الحالية
        current_settings_frame = ctk.CTkFrame(content_frame, fg_color=COLORS['bg_secondary'], corner_radius=8)
        current_settings_frame.pack(fill="x", pady=(0, 20))

        current_title = create_rtl_label(
            current_settings_frame,
            text="📊 الإعدادات الحالية",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        current_title.pack(pady=(15, 10))

        # عرض إعدادات الاتصال الحالية
        from config.settings import DATABASE_CONFIG
        current_info = [
            ("الخادم (Host)", DATABASE_CONFIG.get('host', 'غير محدد')),
            ("المنفذ (Port)", str(DATABASE_CONFIG.get('port', 'غير محدد'))),
            ("اسم قاعدة البيانات", DATABASE_CONFIG.get('database', 'غير محدد')),
            ("اسم المستخدم", DATABASE_CONFIG.get('user', 'غير محدد')),
            ("ترميز الأحرف", DATABASE_CONFIG.get('charset', 'غير محدد'))
        ]

        for label, value in current_info:
            info_frame = ctk.CTkFrame(current_settings_frame, fg_color=COLORS['bg_light'], corner_radius=5)
            info_frame.pack(fill="x", padx=15, pady=2)

            info_label = create_rtl_label(
                info_frame,
                text=f"{label}: {value}",
                font_size='body',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            info_label.pack(anchor="e", padx=10, pady=5)

        # زر اختبار الاتصال
        test_connection_button = create_rtl_button(
            current_settings_frame,
            text="🔄 اختبار الاتصال",
            command=self.test_database_connection,
            **BUTTON_STYLES['info']
        )
        test_connection_button.pack(fill="x", padx=15, pady=(10, 15))

        # تعديل إعدادات الاتصال
        edit_settings_frame = ctk.CTkFrame(content_frame, fg_color=COLORS['bg_secondary'], corner_radius=8)
        edit_settings_frame.pack(fill="x", pady=(10, 0))

        edit_title = create_rtl_label(
            edit_settings_frame,
            text="✏️ تعديل إعدادات الاتصال",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        edit_title.pack(pady=(15, 10))

        # حقول تعديل الإعدادات
        fields_frame = ctk.CTkFrame(edit_settings_frame, fg_color="transparent")
        fields_frame.pack(fill="x", padx=20, pady=10)

        # الخادم
        host_label = create_rtl_label(
            fields_frame,
            text="الخادم (Host):",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        host_label.pack(anchor="e", pady=(0, 5))

        self.host_entry = ctk.CTkEntry(
            fields_frame,
            height=35,
            font=ctk.CTkFont(size=14),
            placeholder_text="localhost"
        )
        self.host_entry.pack(fill="x", pady=(0, 10))
        self.host_entry.insert(0, DATABASE_CONFIG.get('host', 'localhost'))

        # المنفذ
        port_label = create_rtl_label(
            fields_frame,
            text="المنفذ (Port):",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        port_label.pack(anchor="e", pady=(0, 5))

        self.port_entry = ctk.CTkEntry(
            fields_frame,
            height=35,
            font=ctk.CTkFont(size=14),
            placeholder_text="3306"
        )
        self.port_entry.pack(fill="x", pady=(0, 10))
        self.port_entry.insert(0, str(DATABASE_CONFIG.get('port', 3306)))

        # اسم قاعدة البيانات
        database_label = create_rtl_label(
            fields_frame,
            text="اسم قاعدة البيانات:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        database_label.pack(anchor="e", pady=(0, 5))

        self.database_entry = ctk.CTkEntry(
            fields_frame,
            height=35,
            font=ctk.CTkFont(size=14),
            placeholder_text="money_manager"
        )
        self.database_entry.pack(fill="x", pady=(0, 10))
        self.database_entry.insert(0, DATABASE_CONFIG.get('database', 'money_manager'))

        # اسم المستخدم
        user_label = create_rtl_label(
            fields_frame,
            text="اسم المستخدم:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        user_label.pack(anchor="e", pady=(0, 5))

        self.user_entry = ctk.CTkEntry(
            fields_frame,
            height=35,
            font=ctk.CTkFont(size=14),
            placeholder_text="root"
        )
        self.user_entry.pack(fill="x", pady=(0, 10))
        self.user_entry.insert(0, DATABASE_CONFIG.get('user', 'root'))

        # كلمة المرور
        password_label = create_rtl_label(
            fields_frame,
            text="كلمة المرور:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        password_label.pack(anchor="e", pady=(0, 5))

        self.password_entry = ctk.CTkEntry(
            fields_frame,
            height=35,
            font=ctk.CTkFont(size=14),
            placeholder_text="كلمة المرور",
            show="*"
        )
        self.password_entry.pack(fill="x", pady=(0, 15))
        self.password_entry.insert(0, DATABASE_CONFIG.get('password', ''))

        # أزرار الحفظ والاختبار
        buttons_frame = ctk.CTkFrame(edit_settings_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(0, 15))

        # زر اختبار الإعدادات الجديدة
        test_new_button = create_rtl_button(
            buttons_frame,
            text="🔄 اختبار الإعدادات الجديدة",
            command=self.test_new_database_settings,
            **BUTTON_STYLES['warning']
        )
        test_new_button.pack(fill="x", pady=5)

        # زر حفظ الإعدادات
        save_db_settings_button = create_rtl_button(
            buttons_frame,
            text="💾 حفظ إعدادات قاعدة البيانات",
            command=self.save_database_settings,
            **BUTTON_STYLES['success']
        )
        save_db_settings_button.pack(fill="x", pady=5)

    def create_instant_backup(self):
        """إنشاء نسخة احتياطية فورية"""
        try:
            from utils.backup import backup_manager

            # إنشاء النسخة الاحتياطية
            success, message = backup_manager.create_backup()

            if success:
                messagebox.showinfo("نجح", message)
            else:
                messagebox.showerror("خطأ", message)

        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup_dialog(self):
        """حوار استعادة نسخة احتياطية"""
        try:
            # اختيار ملف النسخة الاحتياطية
            file_path = filedialog.askopenfilename(
                title="اختر ملف النسخة الاحتياطية",
                filetypes=[("SQL files", "*.sql"), ("All files", "*.*")]
            )

            if file_path:
                # تأكيد الاستعادة
                result = messagebox.askyesno(
                    "تأكيد الاستعادة",
                    "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n\nتحذير: سيتم استبدال جميع البيانات الحالية!",
                    icon='warning'
                )

                if result:
                    from utils.backup import backup_manager

                    # استعادة النسخة الاحتياطية
                    success, message = backup_manager.restore_backup(file_path)

                    if success:
                        messagebox.showinfo("نجح", message)
                        # إعادة تحميل الصفحة
                        self.show_database_management()
                    else:
                        messagebox.showerror("خطأ", message)

        except Exception as e:
            print(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}")

    def toggle_auto_backup(self):
        """تفعيل/إلغاء النسخ الاحتياطي التلقائي"""
        try:
            enabled = self.auto_backup_var.get()
            print(f"النسخ الاحتياطي التلقائي: {'مفعل' if enabled else 'معطل'}")

        except Exception as e:
            print(f"خطأ في تغيير إعدادات النسخ التلقائي: {e}")

    def update_backup_interval(self, selected_interval):
        """تحديث فترة النسخ الاحتياطي"""
        try:
            print(f"تم تحديد فترة النسخ الاحتياطي: {selected_interval}")

        except Exception as e:
            print(f"خطأ في تحديث فترة النسخ الاحتياطي: {e}")

    def save_backup_settings(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        try:
            from utils.backup import backup_manager

            # جمع الإعدادات الجديدة
            new_settings = {
                'auto_backup_enabled': self.auto_backup_var.get(),
                'backup_interval': self.interval_combo.get(),
                'max_backup_files': int(self.max_files_entry.get() or 30)
            }

            # تحديث الإعدادات
            success = backup_manager.update_settings(new_settings)

            if success:
                messagebox.showinfo("نجح", "تم حفظ إعدادات النسخ الاحتياطي بنجاح")
            else:
                messagebox.showerror("خطأ", "فشل في حفظ إعدادات النسخ الاحتياطي")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح لعدد النسخ المحفوظة")
        except Exception as e:
            print(f"خطأ في حفظ إعدادات النسخ الاحتياطي: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")

    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات الحالية"""
        try:
            from utils.backup import backup_manager

            success, message = backup_manager.test_database_connection()

            if success:
                messagebox.showinfo("نجح الاتصال", message)
            else:
                messagebox.showerror("فشل الاتصال", message)

        except Exception as e:
            print(f"خطأ في اختبار الاتصال: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء اختبار الاتصال: {str(e)}")

    def test_new_database_settings(self):
        """اختبار الإعدادات الجديدة لقاعدة البيانات"""
        try:
            from utils.backup import backup_manager

            # جمع الإعدادات الجديدة
            new_config = {
                'host': self.host_entry.get(),
                'port': int(self.port_entry.get()),
                'database': self.database_entry.get(),
                'user': self.user_entry.get(),
                'password': self.password_entry.get(),
                'charset': 'utf8mb4'
            }

            # اختبار الاتصال
            success, message = backup_manager.test_database_connection(new_config)

            if success:
                messagebox.showinfo("نجح الاتصال", "تم الاتصال بقاعدة البيانات بنجاح باستخدام الإعدادات الجديدة")
            else:
                messagebox.showerror("فشل الاتصال", f"فشل الاتصال بقاعدة البيانات: {message}")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح للمنفذ")
        except Exception as e:
            print(f"خطأ في اختبار الإعدادات الجديدة: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء اختبار الإعدادات: {str(e)}")

    def save_database_settings(self):
        """حفظ إعدادات قاعدة البيانات الجديدة"""
        try:
            # تحذير المستخدم
            result = messagebox.askyesno(
                "تأكيد الحفظ",
                "هل أنت متأكد من حفظ إعدادات قاعدة البيانات الجديدة؟\n\nسيتم إعادة تشغيل التطبيق لتطبيق التغييرات.",
                icon='warning'
            )

            if result:
                messagebox.showinfo("تنبيه", "ميزة حفظ إعدادات قاعدة البيانات ستكون متاحة في التحديث القادم")

        except Exception as e:
            print(f"خطأ في حفظ إعدادات قاعدة البيانات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")

    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()
