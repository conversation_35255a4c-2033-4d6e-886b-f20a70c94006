#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التطبيق بعد توحيد قاعدة البيانات
"""

import sys
import os
import mysql.connector

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔗 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from database.connection import DatabaseConnection
        
        db = DatabaseConnection()
        connection = db.get_connection()
        
        if connection and connection.is_connected():
            print("✅ الاتصال بقاعدة البيانات ناجح")
            
            cursor = connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"👥 عدد المستخدمين: {user_count}")
            
            cursor.execute("SELECT COUNT(*) FROM accounts")
            account_count = cursor.fetchone()[0]
            print(f"🏦 عدد الحسابات: {account_count}")
            
            cursor.close()
            connection.close()
            return True
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")
        return False

def test_user_authentication():
    """اختبار تسجيل الدخول"""
    print("\n🔐 اختبار تسجيل الدخول...")
    
    try:
        from database.models import User
        
        # اختبار تسجيل دخول المدير
        admin_user = User.authenticate('admin', 'admin123')
        if admin_user:
            print("✅ تسجيل دخول المدير ناجح")
            print(f"   الاسم: {admin_user['full_name']}")
            print(f"   الدور: {admin_user['role']}")
        else:
            print("❌ فشل في تسجيل دخول المدير")
            return False
        
        # اختبار تسجيل دخول المستخدم العادي
        user = User.authenticate('user', 'user123')
        if user:
            print("✅ تسجيل دخول المستخدم العادي ناجح")
            print(f"   الاسم: {user['full_name']}")
            print(f"   الدور: {user['role']}")
        else:
            print("❌ فشل في تسجيل دخول المستخدم العادي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def test_account_operations():
    """اختبار عمليات الحسابات"""
    print("\n🏦 اختبار عمليات الحسابات...")
    
    try:
        from database.models import Account
        
        # الحصول على جميع الحسابات
        accounts = Account.get_all()
        print(f"📊 عدد الحسابات الإجمالي: {len(accounts)}")
        
        if accounts:
            # عرض أول 3 حسابات
            for i, account in enumerate(accounts[:3]):
                print(f"   {i+1}. {account['name']} (المستخدم: {account.get('user_id', 'غير محدد')})")
        
        # اختبار الحصول على حسابات مستخدم معين
        user_accounts = Account.get_by_user_id(1)  # المدير
        print(f"🔍 حسابات المدير: {len(user_accounts)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات الحسابات: {e}")
        return False

def test_currency_operations():
    """اختبار عمليات العملات"""
    print("\n💰 اختبار عمليات العملات...")
    
    try:
        from database.models import Currency
        
        # الحصول على جميع العملات
        currencies = Currency.get_all()
        print(f"📊 عدد العملات: {len(currencies)}")
        
        for currency in currencies:
            status = "نشط" if currency['is_active'] else "غير نشط"
            print(f"   - {currency['code']}: {currency['name']} ({currency['symbol']}) - {status}")
        
        # اختبار الحصول على عملة معينة
        sar_currency = Currency.get_by_code('SAR')
        if sar_currency:
            print(f"✅ تم العثور على الريال السعودي: {sar_currency['name']}")
        else:
            print("❌ لم يتم العثور على الريال السعودي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات العملات: {e}")
        return False

def test_transaction_operations():
    """اختبار عمليات المعاملات"""
    print("\n📊 اختبار عمليات المعاملات...")
    
    try:
        from database.models import Transaction
        
        # الحصول على جميع المعاملات
        transactions = Transaction.get_all()
        print(f"📊 عدد المعاملات الإجمالي: {len(transactions)}")
        
        if transactions:
            # تحليل أنواع المعاملات
            transaction_types = {}
            for transaction in transactions:
                trans_type = transaction['type']
                transaction_types[trans_type] = transaction_types.get(trans_type, 0) + 1
            
            print("📈 أنواع المعاملات:")
            for trans_type, count in transaction_types.items():
                print(f"   - {trans_type}: {count} معاملة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات المعاملات: {e}")
        return False

def test_gui_import():
    """اختبار استيراد واجهة المستخدم"""
    print("\n🖥️ اختبار استيراد واجهة المستخدم...")
    
    try:
        # اختبار استيراد النافذة الرئيسية
        from gui.main_window import MainWindow
        print("✅ تم استيراد النافذة الرئيسية بنجاح")
        
        # اختبار استيراد نافذة تسجيل الدخول
        from gui.login_window import LoginWindow
        print("✅ تم استيراد نافذة تسجيل الدخول بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد واجهة المستخدم: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار التطبيق بعد توحيد قاعدة البيانات")
    print("=" * 60)
    
    test_results = {
        'database_connection': False,
        'user_authentication': False,
        'account_operations': False,
        'currency_operations': False,
        'transaction_operations': False,
        'gui_import': False
    }
    
    # تشغيل الاختبارات
    test_results['database_connection'] = test_database_connection()
    test_results['user_authentication'] = test_user_authentication()
    test_results['account_operations'] = test_account_operations()
    test_results['currency_operations'] = test_currency_operations()
    test_results['transaction_operations'] = test_transaction_operations()
    test_results['gui_import'] = test_gui_import()
    
    # عرض النتائج
    print("\n📋 نتائج الاختبارات:")
    print("=" * 30)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n📊 الملخص: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج التطبيق لمراجعة")
        return False

if __name__ == "__main__":
    main()
