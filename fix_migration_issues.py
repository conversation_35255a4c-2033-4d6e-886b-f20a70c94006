#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل الترحيل
"""

import mysql.connector
import sqlite3
import os

def get_user_id_mapping():
    """الحصول على تطابق معرفات المستخدمين بين SQLite و MySQL"""
    print("🔍 تحليل معرفات المستخدمين...")
    
    try:
        # الاتصال بـ SQLite
        sqlite_conn = sqlite3.connect("money_manager.db")
        sqlite_conn.row_factory = sqlite3.Row
        sqlite_cursor = sqlite_conn.cursor()
        
        # الاتصال بـ MySQL
        mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        mysql_conn = mysql.connector.connect(**mysql_config)
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        
        # الحصول على المستخدمين من SQLite
        sqlite_cursor.execute("SELECT id, username FROM users")
        sqlite_users = {row['username']: row['id'] for row in sqlite_cursor.fetchall()}
        
        # الحصول على المستخدمين من MySQL
        mysql_cursor.execute("SELECT id, username FROM users")
        mysql_users = {row['username']: row['id'] for row in mysql_cursor.fetchall()}
        
        print("👥 المستخدمين في SQLite:")
        for username, user_id in sqlite_users.items():
            print(f"   - {username}: ID {user_id}")
        
        print("👥 المستخدمين في MySQL:")
        for username, user_id in mysql_users.items():
            print(f"   - {username}: ID {user_id}")
        
        # إنشاء تطابق المعرفات
        id_mapping = {}
        for username in sqlite_users:
            if username in mysql_users:
                sqlite_id = sqlite_users[username]
                mysql_id = mysql_users[username]
                id_mapping[sqlite_id] = mysql_id
                print(f"🔗 تطابق {username}: SQLite ID {sqlite_id} -> MySQL ID {mysql_id}")
        
        sqlite_conn.close()
        mysql_conn.close()
        
        return id_mapping
        
    except Exception as e:
        print(f"❌ خطأ في تحليل المعرفات: {e}")
        return {}

def migrate_accounts_with_mapping(id_mapping):
    """ترحيل الحسابات مع تصحيح المعرفات"""
    print("🏦 ترحيل الحسابات مع تصحيح المعرفات...")
    
    try:
        # الاتصال بـ SQLite
        sqlite_conn = sqlite3.connect("money_manager.db")
        sqlite_conn.row_factory = sqlite3.Row
        sqlite_cursor = sqlite_conn.cursor()
        
        # الاتصال بـ MySQL
        mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        mysql_conn = mysql.connector.connect(**mysql_config)
        mysql_cursor = mysql_conn.cursor()
        
        # الحصول على الحسابات من SQLite
        sqlite_cursor.execute("SELECT * FROM accounts")
        accounts = sqlite_cursor.fetchall()
        
        for account in accounts:
            sqlite_user_id = account['user_id']
            
            # تحويل معرف المستخدم
            if sqlite_user_id in id_mapping:
                mysql_user_id = id_mapping[sqlite_user_id]
                
                # التحقق من وجود الحساب
                mysql_cursor.execute(
                    "SELECT id FROM accounts WHERE name = %s AND user_id = %s", 
                    (account['name'], mysql_user_id)
                )
                existing_account = mysql_cursor.fetchone()
                
                if not existing_account:
                    # إدراج الحساب الجديد
                    insert_query = """
                        INSERT INTO accounts (user_id, name, account_type_id, description, is_active, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """
                    mysql_cursor.execute(insert_query, (
                        mysql_user_id, account['name'], account['account_type_id'],
                        account['description'], account['is_active'], account['created_at']
                    ))
                    
                    new_account_id = mysql_cursor.lastrowid
                    print(f"✅ تم ترحيل الحساب: {account['name']} (MySQL ID: {new_account_id})")
                    
                    # ترحيل الرصيد إذا كان موجوداً
                    if 'current_balance' in account and account['current_balance'] != 0:
                        # الحصول على معرف العملة الافتراضية (SAR)
                        mysql_cursor.execute("SELECT id FROM currencies WHERE code = 'SAR'")
                        currency_result = mysql_cursor.fetchone()
                        if currency_result:
                            currency_id = currency_result[0]
                            
                            # التحقق من وجود الرصيد
                            mysql_cursor.execute(
                                "SELECT id FROM account_balances WHERE account_id = %s AND currency_id = %s",
                                (new_account_id, currency_id)
                            )
                            existing_balance = mysql_cursor.fetchone()
                            
                            if not existing_balance:
                                balance_query = """
                                    INSERT INTO account_balances (account_id, currency_id, balance)
                                    VALUES (%s, %s, %s)
                                """
                                mysql_cursor.execute(balance_query, (new_account_id, currency_id, account['current_balance']))
                                print(f"✅ تم ترحيل رصيد الحساب: {account['current_balance']} SAR")
                else:
                    print(f"⚠️ الحساب موجود مسبقاً: {account['name']}")
            else:
                print(f"❌ لم يتم العثور على تطابق للمستخدم ID {sqlite_user_id}")
        
        mysql_conn.commit()
        
        sqlite_conn.close()
        mysql_conn.close()
        
        print("✅ تم ترحيل الحسابات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ترحيل الحسابات: {e}")
        return False

def migrate_transactions_with_mapping(id_mapping):
    """ترحيل المعاملات مع تصحيح المعرفات"""
    print("📊 ترحيل المعاملات...")
    
    try:
        # الاتصال بـ SQLite
        sqlite_conn = sqlite3.connect("money_manager.db")
        sqlite_conn.row_factory = sqlite3.Row
        sqlite_cursor = sqlite_conn.cursor()
        
        # الاتصال بـ MySQL
        mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        mysql_conn = mysql.connector.connect(**mysql_config)
        mysql_cursor = mysql_conn.cursor()
        
        # الحصول على تطابق الحسابات
        account_mapping = {}
        
        # من SQLite
        sqlite_cursor.execute("SELECT id, name, user_id FROM accounts")
        sqlite_accounts = sqlite_cursor.fetchall()
        
        # من MySQL
        mysql_cursor.execute("SELECT id, name, user_id FROM accounts")
        mysql_accounts = mysql_cursor.fetchall()
        
        # إنشاء تطابق الحسابات
        for sqlite_account in sqlite_accounts:
            sqlite_user_id = sqlite_account['user_id']
            if sqlite_user_id in id_mapping:
                mysql_user_id = id_mapping[sqlite_user_id]
                
                for mysql_account in mysql_accounts:
                    if (mysql_account['name'] == sqlite_account['name'] and 
                        mysql_account['user_id'] == mysql_user_id):
                        account_mapping[sqlite_account['id']] = mysql_account['id']
                        break
        
        print(f"🔗 تطابق الحسابات: {len(account_mapping)} حساب")
        
        # ترحيل المعاملات
        sqlite_cursor.execute("SELECT * FROM transactions")
        transactions = sqlite_cursor.fetchall()
        
        for transaction in transactions:
            sqlite_user_id = transaction['user_id']
            sqlite_account_id = transaction['account_id']
            
            if (sqlite_user_id in id_mapping and 
                sqlite_account_id in account_mapping):
                
                mysql_user_id = id_mapping[sqlite_user_id]
                mysql_account_id = account_mapping[sqlite_account_id]
                
                # التحقق من وجود المعاملة
                mysql_cursor.execute("""
                    SELECT id FROM transactions 
                    WHERE user_id = %s AND account_id = %s AND amount = %s AND transaction_date = %s
                """, (mysql_user_id, mysql_account_id, transaction['amount'], transaction['transaction_date']))
                
                existing_transaction = mysql_cursor.fetchone()
                
                if not existing_transaction:
                    # إدراج المعاملة
                    insert_query = """
                        INSERT INTO transactions (user_id, account_id, type, amount, currency_id, 
                                                description, transaction_date, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    mysql_cursor.execute(insert_query, (
                        mysql_user_id, mysql_account_id, transaction['type'], 
                        transaction['amount'], transaction['currency_id'],
                        transaction['description'], transaction['transaction_date'],
                        transaction['created_at']
                    ))
                    print(f"✅ تم ترحيل معاملة: {transaction['type']} - {transaction['amount']}")
                else:
                    print(f"⚠️ المعاملة موجودة مسبقاً")
        
        mysql_conn.commit()
        
        sqlite_conn.close()
        mysql_conn.close()
        
        print("✅ تم ترحيل المعاملات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ترحيل المعاملات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح مشاكل الترحيل")
    print("=" * 40)
    
    # 1. تحليل معرفات المستخدمين
    id_mapping = get_user_id_mapping()
    
    if not id_mapping:
        print("❌ فشل في الحصول على تطابق المعرفات")
        return
    
    # 2. ترحيل الحسابات
    if not migrate_accounts_with_mapping(id_mapping):
        print("❌ فشل في ترحيل الحسابات")
        return
    
    # 3. ترحيل المعاملات
    if not migrate_transactions_with_mapping(id_mapping):
        print("❌ فشل في ترحيل المعاملات")
        return
    
    print("\n🎉 تم إصلاح مشاكل الترحيل بنجاح!")

if __name__ == "__main__":
    main()
