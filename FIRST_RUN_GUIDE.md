# 🚀 دليل التشغيل الأول - برنامج إدارة الأموال

## ✅ تم إصلاح المشاكل!

### 🔧 التحديثات المطبقة:
- ✅ **إزالة متطلب البريد الإلكتروني** - لم يعد مطلوباً
- ✅ **تحسين معالجة أخطاء قاعدة البيانات**
- ✅ **تبسيط متطلبات كلمة المرور** (6 أحرف بدلاً من 8)
- ✅ **تحسين رسائل الخطأ** لتكون أكثر وضوحاً

## 🎯 خطوات التشغيل الأول

### 1️⃣ عند ظهور نافذة تسجيل الدخول:
- **كلمة مرور MySQL:** أدخل كلمة مرور MySQL أو اتركها فارغة
- **إذا لم يكن MySQL مثبتاً:** ستظهر رسالة خطأ واضحة

### 2️⃣ إنشاء المدير الأول:
إذا كانت هذه المرة الأولى، ستظهر رسالة لإنشاء حساب المدير:

**البيانات المطلوبة:**
- ✅ **الاسم الكامل** (مطلوب)
- ✅ **اسم المستخدم** (مطلوب - أحرف وأرقام فقط)
- ✅ **كلمة المرور** (6 أحرف على الأقل + أحرف وأرقام)
- ✅ **تأكيد كلمة المرور**
- ❌ **البريد الإلكتروني** (لم يعد مطلوباً!)

### 3️⃣ بعد التسجيل الناجح:
- ستظهر لوحة التحكم الرئيسية
- يمكنك البدء بإضافة الحسابات والمعاملات

## 🗄️ إعداد MySQL (إذا لم يكن مثبتاً)

### خيارات سهلة:
1. **XAMPP** (الأسهل):
   - حمل من: https://www.apachefriends.org/
   - ثبت وشغل MySQL من Control Panel

2. **MySQL Server مباشرة**:
   - حمل من: https://dev.mysql.com/downloads/mysql/
   - اتبع خطوات التثبيت

3. **WAMP** (للويندوز):
   - حمل من: http://www.wampserver.com/

### بعد تثبيت MySQL:
- أعد تشغيل البرنامج
- أدخل كلمة مرور MySQL (أو اتركها فارغة)

## 🎨 ما تتوقعه من البرنامج

### التصميم:
- 🟣 **ألوان بنفسجية متدرجة** أنيقة
- 🇸🇦 **واجهة عربية** بالكامل
- 📱 **تصميم حديث** وسهل الاستخدام

### الميزات الأساسية:
- 👥 **إدارة المستخدمين** (مدير/مستخدم عادي)
- 💰 **11 عملة مدعومة** (ريال، دولار، يورو، إلخ)
- 🏦 **أنواع حسابات متعددة** (صندوق، بنك، محفظة إلكترونية)
- 📊 **تقارير وإحصائيات** فورية
- 🔄 **تحويلات بين الحسابات**
- 💾 **نسخ احتياطي تلقائي**

## 🔧 حل المشاكل الشائعة

### "لا يمكن الاتصال بـ MySQL":
```bash
# تأكد من تشغيل MySQL
# Windows:
net start mysql

# أو من XAMPP Control Panel
```

### "نافذة البرنامج لا تظهر":
- ابحث عن نافذة "مدير الأموال" في شريط المهام
- قد تكون خلف النوافذ الأخرى
- تأكد من تثبيت customtkinter بشكل صحيح

### "خطأ في التسجيل":
- تأكد من أن اسم المستخدم يحتوي على أحرف وأرقام فقط
- كلمة المرور يجب أن تكون 6 أحرف على الأقل
- تأكد من تطابق كلمة المرور وتأكيدها

## 📊 إنشاء بيانات تجريبية (اختياري)

بعد إنشاء حسابك، يمكنك إضافة بيانات تجريبية:

```bash
python create_sample_data.py
```

سيتم إنشاء:
- 5 حسابات مختلفة
- 10+ معاملات (واردات ومصروفات)
- تحويلات بين الحسابات

## 🎉 نصائح للبداية

### للمبتدئين:
1. ابدأ بإنشاء حساب واحد (مثل: الصندوق النقدي)
2. أضف بعض المعاملات البسيطة
3. استكشف لوحة التحكم
4. جرب إنشاء تقرير بسيط

### للمستخدمين المتقدمين:
1. استخدم عدة عملات
2. أنشئ حسابات متعددة
3. استفد من التحويلات بين الحسابات
4. استخدم النسخ الاحتياطي التلقائي

## 📞 إذا احتجت مساعدة

### الملفات المرجعية:
- `README.md` - الدليل الشامل
- `INSTALLATION_GUIDE.md` - دليل التثبيت المفصل
- `QUICK_START.md` - دليل البدء السريع

### أدوات التشخيص:
```bash
# فحص المتطلبات
python check_requirements.py

# تثبيت المتطلبات
python install_requirements.py

# مشغل ذكي
python launcher.py
```

### ملفات السجل:
- راجع مجلد `logs/` للأخطاء المفصلة
- آخر ملف سجل: `logs/money_manager_YYYYMMDD.log`

## ✨ مبروك!

أنت الآن جاهز لاستخدام برنامج إدارة الأموال بدون متطلب البريد الإلكتروني!

### الخطوات التالية:
1. 🏦 أنشئ حساباتك المالية
2. 💰 أضف معاملاتك اليومية  
3. 📊 راجع التقارير والإحصائيات
4. ⚙️ خصص الإعدادات حسب احتياجاتك

---

**استمتع بإدارة أموالك بطريقة احترافية وبسيطة! 💰✨**

> 💡 **تذكير:** البريد الإلكتروني لم يعد مطلوباً للتسجيل!
