#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 فحص حالة قاعدة البيانات...")

try:
    from database.connection import db
    
    if not (db.is_connected() or db.connect()):
        print("❌ فشل الاتصال بقاعدة البيانات")
        exit(1)
    
    print("✅ الاتصال بقاعدة البيانات ناجح")
    
    # فحص الجداول الموجودة
    print("\n📋 الجداول الموجودة:")
    tables = db.execute_query("SHOW TABLES")
    
    if tables:
        table_names = [list(table.values())[0] for table in tables]
        for table_name in table_names:
            print(f"   - {table_name}")
    else:
        print("   لا توجد جداول")
    
    # فحص الجداول المطلوبة
    required_tables = ['users', 'accounts', 'transactions', 'transfers', 'currencies', 'income_categories', 'expense_categories']
    missing_tables = []
    
    print("\n🔍 فحص الجداول المطلوبة:")
    for table in required_tables:
        if table in table_names:
            print(f"   ✅ {table}")
        else:
            print(f"   ❌ {table} (مفقود)")
            missing_tables.append(table)
    
    if missing_tables:
        print(f"\n⚠️ جداول مفقودة: {', '.join(missing_tables)}")
        print("💡 تشغيل: python database/init_db.py")
    else:
        print("\n✅ جميع الجداول المطلوبة موجودة")
    
    # فحص المستخدمين
    print("\n👤 فحص المستخدمين:")
    try:
        users = db.execute_query("SELECT id, username, role FROM users")
        if users:
            for user in users:
                print(f"   - {user['username']} (ID: {user['id']}, Role: {user['role']})")
        else:
            print("   لا يوجد مستخدمين")
    except Exception as e:
        print(f"   ❌ خطأ: {e}")
    
    # فحص الفئات
    print("\n📂 فحص الفئات:")
    try:
        income_cats = db.execute_query("SELECT COUNT(*) as count FROM income_categories")
        income_count = income_cats[0]['count'] if income_cats else 0
        print(f"   - فئات الدخل: {income_count}")
    except Exception as e:
        print(f"   ❌ فئات الدخل: خطأ - {e}")
    
    try:
        expense_cats = db.execute_query("SELECT COUNT(*) as count FROM expense_categories")
        expense_count = expense_cats[0]['count'] if expense_cats else 0
        print(f"   - فئات المصروفات: {expense_count}")
    except Exception as e:
        print(f"   ❌ فئات المصروفات: خطأ - {e}")

except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
