#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص الحسابات في قاعدة البيانات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database.connection import DatabaseConnection
    from utils.auth import auth_manager
    
    def check_accounts():
        """فحص الحسابات الموجودة"""
        
        print("🔍 فحص الحسابات في قاعدة البيانات")
        print("="*50)
        
        try:
            db = DatabaseConnection()
            
            # فحص المستخدم
            print("👤 فحص المستخدم الحالي...")
            if auth_manager.current_user:
                user_id = auth_manager.current_user['id']
                username = auth_manager.current_user.get('username', 'غير محدد')
                print(f"✅ المستخدم: {username} (ID: {user_id})")
            else:
                print("❌ لا يوجد مستخدم مسجل دخول")
                print("💡 قم بتسجيل الدخول أولاً في التطبيق")
                return
            
            # فحص الحسابات
            print(f"\n🏦 فحص الحسابات...")
            accounts_query = "SELECT id, name, currency_id FROM accounts WHERE user_id = %s AND is_active = 1"
            accounts = db.execute_query(accounts_query, (user_id,))
            
            print(f"📊 عدد الحسابات الموجودة: {len(accounts)}")
            
            if accounts:
                print("📋 قائمة الحسابات:")
                for account in accounts:
                    print(f"   • {account['name']} (ID: {account['id']}, العملة: {account['currency_id']})")
            
            # فحص الحسابات المطلوبة
            required_accounts = [
                'البنك الأهلي',
                'حساب الدولار',
                'بنك الإمارات', 
                'البنك اليمني',
                'محفظة نقدية'
            ]
            
            existing_names = [acc['name'] for acc in accounts]
            
            print(f"\n🔍 فحص الحسابات المطلوبة:")
            missing_accounts = []
            
            for req_account in required_accounts:
                if req_account in existing_names:
                    print(f"   ✅ {req_account}")
                else:
                    print(f"   ❌ {req_account} - مفقود")
                    missing_accounts.append(req_account)
            
            # فحص العملات
            print(f"\n💱 فحص العملات المدعومة...")
            currencies_query = "SELECT id, code, symbol FROM currencies WHERE is_active = 1"
            currencies = db.execute_query(currencies_query)
            
            print(f"📊 عدد العملات المدعومة: {len(currencies)}")
            
            if currencies:
                print("📋 قائمة العملات:")
                for currency in currencies:
                    print(f"   • {currency['code']} ({currency['symbol']}) - ID: {currency['id']}")
            
            # فحص العملات المطلوبة
            required_currencies = ['SAR', 'USD', 'AED', 'YER']
            existing_codes = [curr['code'] for curr in currencies]
            
            print(f"\n🔍 فحص العملات المطلوبة:")
            missing_currencies = []
            
            for req_currency in required_currencies:
                if req_currency in existing_codes:
                    print(f"   ✅ {req_currency}")
                else:
                    print(f"   ❌ {req_currency} - مفقودة")
                    missing_currencies.append(req_currency)
            
            # ملخص النتائج
            print(f"\n" + "="*50)
            print("📊 ملخص النتائج:")
            
            if missing_accounts:
                print(f"❌ حسابات مفقودة ({len(missing_accounts)}): {', '.join(missing_accounts)}")
            else:
                print("✅ جميع الحسابات المطلوبة موجودة")
            
            if missing_currencies:
                print(f"❌ عملات مفقودة ({len(missing_currencies)}): {', '.join(missing_currencies)}")
            else:
                print("✅ جميع العملات المطلوبة مدعومة")
            
            if missing_accounts or missing_currencies:
                print(f"\n💡 لحل المشكلة:")
                if missing_accounts:
                    print("   1. أنشئ الحسابات المفقودة من واجهة التطبيق")
                if missing_currencies:
                    print("   2. تأكد من إعداد العملات المطلوبة")
            else:
                print(f"\n🎉 جميع المتطلبات متوفرة - يجب أن يعمل الاستيراد الآن!")
                
        except Exception as e:
            print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
            print(f"💡 تأكد من تشغيل التطبيق أولاً")
    
    if __name__ == "__main__":
        check_accounts()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("💡 تأكد من تشغيل هذا السكريبت من مجلد التطبيق")
except Exception as e:
    print(f"❌ خطأ عام: {e}")
