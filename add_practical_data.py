#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات عملية للاستخدام الفعلي للبرنامج
"""

import sys
import os
from datetime import datetime, timedelta
import random

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection import db

def add_practical_accounts():
    """إضافة حسابين عمليين للمستخدم الافتراضي"""
    print("🏦 إضافة حسابين عمليين...")
    
    try:
        user_id = 1  # المستخدم الافتراضي admin
        
        # حذف الحسابات الموجودة أولاً لتجنب التكرار
        db.execute_update("DELETE FROM transfers WHERE user_id = %s", (user_id,))
        db.execute_update("DELETE FROM transactions WHERE user_id = %s", (user_id,))
        db.execute_update("DELETE FROM accounts WHERE user_id = %s", (user_id,))
        
        # الحساب الأول: حساب جاري رئيسي
        account1_query = """
            INSERT INTO accounts (user_id, name, account_type_id, currency_id, initial_balance, current_balance, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        account1_data = (
            user_id, 
            "الحساب الجاري الرئيسي", 
            2,  # حساب بنكي
            1,  # ريال سعودي
            15000.00, 
            15000.00, 
            "الحساب الجاري الرئيسي في بنك الراجحي - للمعاملات اليومية"
        )
        account1_id = db.execute_insert(account1_query, account1_data)
        print(f"✅ تم إنشاء الحساب الجاري الرئيسي (ID: {account1_id})")
        
        # الحساب الثاني: محفظة نقدية
        account2_query = """
            INSERT INTO accounts (user_id, name, account_type_id, currency_id, initial_balance, current_balance, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        account2_data = (
            user_id, 
            "المحفظة النقدية", 
            1,  # صندوق نقدي
            1,  # ريال سعودي
            2000.00, 
            2000.00, 
            "النقد المتوفر في المحفظة للمصروفات اليومية الصغيرة"
        )
        account2_id = db.execute_insert(account2_query, account2_data)
        print(f"✅ تم إنشاء المحفظة النقدية (ID: {account2_id})")
        
        return account1_id, account2_id
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الحسابات: {e}")
        return None, None

def add_practical_income_transactions(account1_id, account2_id):
    """إضافة واردات عملية متنوعة"""
    print("💰 إضافة واردات عملية...")
    
    try:
        user_id = 1
        
        # قائمة الواردات العملية
        income_transactions = [
            # الراتب الشهري
            {
                "account_id": account1_id,
                "amount": 8500.00,
                "category_id": 1,  # راتب
                "description": "راتب شهر ديسمبر 2025",
                "days_ago": 5
            },
            # دخل من عمل حر
            {
                "account_id": account1_id,
                "amount": 2200.00,
                "category_id": 4,  # عمل حر
                "description": "مشروع تطوير موقع إلكتروني للعميل أحمد",
                "days_ago": 12
            },
            # مكافأة من العمل
            {
                "account_id": account1_id,
                "amount": 1500.00,
                "category_id": 1,  # راتب
                "description": "مكافأة نهاية السنة",
                "days_ago": 20
            },
            # دخل من استثمار
            {
                "account_id": account1_id,
                "amount": 750.00,
                "category_id": 3,  # استثمار
                "description": "أرباح من الأسهم - الربع الرابع",
                "days_ago": 15
            },
            # دخل من أعمال جانبية
            {
                "account_id": account2_id,
                "amount": 450.00,
                "category_id": 2,  # أعمال
                "description": "بيع منتجات يدوية في المعرض",
                "days_ago": 8
            },
            # استشارة مهنية
            {
                "account_id": account1_id,
                "amount": 1200.00,
                "category_id": 4,  # عمل حر
                "description": "استشارة تقنية لشركة النور",
                "days_ago": 25
            }
        ]
        
        for income in income_transactions:
            transaction_date = (datetime.now() - timedelta(days=income["days_ago"])).date()
            
            query = """
                INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_id, description, transaction_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            result = db.execute_insert(query, (
                user_id,
                income["account_id"],
                "income",
                income["amount"],
                1,  # ريال سعودي
                income["category_id"],
                income["description"],
                transaction_date
            ))
            
            if result > 0:
                # تحديث رصيد الحساب
                db.execute_update(
                    "UPDATE accounts SET current_balance = current_balance + %s WHERE id = %s",
                    (income["amount"], income["account_id"])
                )
                print(f"  ✅ وارد: {income['amount']:,.2f} ر.س - {income['description']}")
        
        print(f"✅ تم إضافة {len(income_transactions)} وارد بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الواردات: {e}")

def add_practical_expense_transactions(account1_id, account2_id):
    """إضافة مصروفات عملية متنوعة"""
    print("💸 إضافة مصروفات عملية...")
    
    try:
        user_id = 1
        
        # قائمة المصروفات العملية
        expense_transactions = [
            # مصروفات السكن
            {
                "account_id": account1_id,
                "amount": 2500.00,
                "category_id": 3,  # سكن
                "description": "إيجار الشقة - شهر ديسمبر",
                "days_ago": 3
            },
            # فواتير الخدمات
            {
                "account_id": account1_id,
                "amount": 380.00,
                "category_id": 4,  # فواتير
                "description": "فاتورة الكهرباء والماء",
                "days_ago": 7
            },
            {
                "account_id": account1_id,
                "amount": 120.00,
                "category_id": 4,  # فواتير
                "description": "فاتورة الإنترنت والهاتف",
                "days_ago": 10
            },
            # مصروفات الطعام
            {
                "account_id": account2_id,
                "amount": 85.00,
                "category_id": 1,  # طعام وشراب
                "description": "تسوق من السوبر ماركت",
                "days_ago": 1
            },
            {
                "account_id": account2_id,
                "amount": 45.00,
                "category_id": 1,  # طعام وشراب
                "description": "وجبة غداء في المطعم",
                "days_ago": 2
            },
            {
                "account_id": account2_id,
                "amount": 120.00,
                "category_id": 1,  # طعام وشراب
                "description": "مشتريات من البقالة",
                "days_ago": 4
            },
            # مصروفات المواصلات
            {
                "account_id": account2_id,
                "amount": 200.00,
                "category_id": 2,  # مواصلات
                "description": "وقود السيارة",
                "days_ago": 6
            },
            {
                "account_id": account2_id,
                "amount": 25.00,
                "category_id": 2,  # مواصلات
                "description": "أجرة تاكسي",
                "days_ago": 9
            },
            # مصروفات صحية
            {
                "account_id": account1_id,
                "amount": 150.00,
                "category_id": 5,  # صحة
                "description": "زيارة طبيب الأسنان",
                "days_ago": 14
            },
            {
                "account_id": account2_id,
                "amount": 65.00,
                "category_id": 5,  # صحة
                "description": "أدوية من الصيدلية",
                "days_ago": 11
            },
            # مصروفات التسوق
            {
                "account_id": account1_id,
                "amount": 320.00,
                "category_id": 8,  # تسوق
                "description": "ملابس شتوية جديدة",
                "days_ago": 18
            },
            {
                "account_id": account2_id,
                "amount": 75.00,
                "category_id": 8,  # تسوق
                "description": "أدوات منزلية",
                "days_ago": 13
            },
            # مصروفات الترفيه
            {
                "account_id": account2_id,
                "amount": 90.00,
                "category_id": 7,  # ترفيه
                "description": "تذاكر السينما مع الأصدقاء",
                "days_ago": 16
            },
            {
                "account_id": account2_id,
                "amount": 55.00,
                "category_id": 7,  # ترفيه
                "description": "ألعاب فيديو جديدة",
                "days_ago": 22
            },
            # مصروفات التعليم
            {
                "account_id": account1_id,
                "amount": 450.00,
                "category_id": 6,  # تعليم
                "description": "دورة تدريبية في البرمجة",
                "days_ago": 28
            }
        ]
        
        for expense in expense_transactions:
            transaction_date = (datetime.now() - timedelta(days=expense["days_ago"])).date()
            
            query = """
                INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_id, description, transaction_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            result = db.execute_insert(query, (
                user_id,
                expense["account_id"],
                "expense",
                expense["amount"],
                1,  # ريال سعودي
                expense["category_id"],
                expense["description"],
                transaction_date
            ))
            
            if result > 0:
                # تحديث رصيد الحساب
                db.execute_update(
                    "UPDATE accounts SET current_balance = current_balance - %s WHERE id = %s",
                    (expense["amount"], expense["account_id"])
                )
                print(f"  ✅ مصروف: {expense['amount']:,.2f} ر.س - {expense['description']}")
        
        print(f"✅ تم إضافة {len(expense_transactions)} مصروف بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المصروفات: {e}")

def add_practical_transfers(account1_id, account2_id):
    """إضافة تحويلات عملية بين الحسابين"""
    print("🔄 إضافة تحويلات عملية...")
    
    try:
        user_id = 1
        
        # قائمة التحويلات العملية
        transfers = [
            {
                "from_account_id": account1_id,
                "to_account_id": account2_id,
                "amount": 500.00,
                "description": "سحب نقدي للمصروفات اليومية",
                "days_ago": 5
            },
            {
                "from_account_id": account1_id,
                "to_account_id": account2_id,
                "amount": 300.00,
                "description": "نقد إضافي للطوارئ",
                "days_ago": 12
            },
            {
                "from_account_id": account2_id,
                "to_account_id": account1_id,
                "amount": 150.00,
                "description": "إيداع فائض النقد في البنك",
                "days_ago": 20
            },
            {
                "from_account_id": account1_id,
                "to_account_id": account2_id,
                "amount": 250.00,
                "description": "نقد لمصروفات نهاية الأسبوع",
                "days_ago": 8
            }
        ]
        
        for transfer in transfers:
            transfer_date = (datetime.now() - timedelta(days=transfer["days_ago"])).date()
            
            query = """
                INSERT INTO transfers (user_id, from_account_id, to_account_id, amount, currency_id, description, transfer_date)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            
            result = db.execute_insert(query, (
                user_id,
                transfer["from_account_id"],
                transfer["to_account_id"],
                transfer["amount"],
                1,  # ريال سعودي
                transfer["description"],
                transfer_date
            ))
            
            if result > 0:
                # تحديث أرصدة الحسابات
                # خصم من الحساب المرسل
                db.execute_update(
                    "UPDATE accounts SET current_balance = current_balance - %s WHERE id = %s",
                    (transfer["amount"], transfer["from_account_id"])
                )
                # إضافة للحساب المستقبل
                db.execute_update(
                    "UPDATE accounts SET current_balance = current_balance + %s WHERE id = %s",
                    (transfer["amount"], transfer["to_account_id"])
                )
                
                from_name = "الحساب الجاري" if transfer["from_account_id"] == account1_id else "المحفظة النقدية"
                to_name = "المحفظة النقدية" if transfer["to_account_id"] == account2_id else "الحساب الجاري"
                print(f"  ✅ تحويل: {transfer['amount']:,.2f} ر.س من {from_name} إلى {to_name}")
        
        print(f"✅ تم إضافة {len(transfers)} تحويل بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة التحويلات: {e}")

def display_final_summary():
    """عرض ملخص نهائي للبيانات المضافة"""
    print("\n" + "="*60)
    print("📊 ملخص البيانات المضافة")
    print("="*60)
    
    try:
        user_id = 1
        
        # إحصائيات الحسابات
        accounts = db.execute_query(
            "SELECT name, current_balance FROM accounts WHERE user_id = %s",
            (user_id,)
        )
        
        print("🏦 الحسابات:")
        total_balance = 0
        for account in accounts:
            print(f"   • {account['name']}: {account['current_balance']:,.2f} ر.س")
            total_balance += account['current_balance']
        print(f"   📊 إجمالي الأرصدة: {total_balance:,.2f} ر.س")
        
        # إحصائيات المعاملات
        income_total = db.execute_query(
            "SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE user_id = %s AND transaction_type = 'income'",
            (user_id,)
        )[0]['total']
        
        expense_total = db.execute_query(
            "SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE user_id = %s AND transaction_type = 'expense'",
            (user_id,)
        )[0]['total']
        
        transfers_count = db.execute_query(
            "SELECT COUNT(*) as count FROM transfers WHERE user_id = %s",
            (user_id,)
        )[0]['count']
        
        print(f"\n💰 المعاملات:")
        print(f"   • إجمالي الواردات: {income_total:,.2f} ر.س")
        print(f"   • إجمالي المصروفات: {expense_total:,.2f} ر.س")
        print(f"   • الصافي: {income_total - expense_total:,.2f} ر.س")
        print(f"   • عدد التحويلات: {transfers_count}")
        
        print(f"\n🎉 البرنامج جاهز للاستخدام العملي!")
        print(f"📋 بيانات تسجيل الدخول:")
        print(f"   اسم المستخدم: admin")
        print(f"   كلمة المرور: 123456")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الملخص: {e}")

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🏦 إضافة بيانات عملية لبرنامج إدارة الأموال")
    print("="*60)
    
    # الاتصال بقاعدة البيانات
    if not db.connect():
        print("❌ فشل الاتصال بقاعدة البيانات")
        return False
    
    # إضافة الحسابات
    account1_id, account2_id = add_practical_accounts()
    if not account1_id or not account2_id:
        print("❌ فشل في إضافة الحسابات")
        return False
    
    # إضافة الواردات
    add_practical_income_transactions(account1_id, account2_id)
    
    # إضافة المصروفات
    add_practical_expense_transactions(account1_id, account2_id)
    
    # إضافة التحويلات
    add_practical_transfers(account1_id, account2_id)
    
    # عرض الملخص النهائي
    display_final_summary()
    
    # إغلاق الاتصال
    db.close()
    
    return True

if __name__ == "__main__":
    main()
    input("\n🔄 اضغط Enter للخروج...")
