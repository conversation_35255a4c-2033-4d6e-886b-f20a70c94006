#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت سريع لإعادة تعيين البيانات التجريبية مع التحقق من المتطلبات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    try:
        # فحص الاتصال بقاعدة البيانات
        from database.connection import db
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        print("✅ الاتصال بقاعدة البيانات")
        
        # فحص تسجيل الدخول
        from utils.auth import auth_manager
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        print("✅ تسجيل الدخول كمدير")
        
        # فحص وجود الفئات
        income_cats = db.execute_query("SELECT COUNT(*) as count FROM income_categories")
        expense_cats = db.execute_query("SELECT COUNT(*) as count FROM expense_categories")
        
        income_count = income_cats[0]['count'] if income_cats else 0
        expense_count = expense_cats[0]['count'] if expense_cats else 0
        
        if income_count == 0 or expense_count == 0:
            print("⚠️ لا توجد فئات كافية")
            print("   سيتم إنشاء الفئات الافتراضية...")
            
            # تشغيل سكريبت التهيئة لإنشاء الفئات
            import subprocess
            result = subprocess.run([sys.executable, "database/init_db.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ تم إنشاء الفئات الافتراضية")
            else:
                print("❌ فشل في إنشاء الفئات الافتراضية")
                return False
        else:
            print(f"✅ الفئات متوفرة (دخل: {income_count}, مصروف: {expense_count})")
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص المتطلبات: {e}")
        return False

def create_backup():
    """إنشاء نسخة احتياطية قبل إعادة التعيين"""
    print("\n💾 إنشاء نسخة احتياطية...")
    
    try:
        from utils.backup import backup_manager
        from utils.auth import auth_manager
        
        # تسجيل الدخول
        auth_manager.login("admin", "123456")
        
        # إنشاء نسخة احتياطية
        success, message = backup_manager.create_backup("before_demo_reset")
        
        auth_manager.logout()
        
        if success:
            print("✅ تم إنشاء نسخة احتياطية بنجاح")
            return True
        else:
            print(f"⚠️ فشل في إنشاء نسخة احتياطية: {message}")
            return False
            
    except Exception as e:
        print(f"⚠️ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return False

def run_demo_reset():
    """تشغيل سكريبت إعادة تعيين البيانات التجريبية"""
    print("\n🚀 تشغيل سكريبت إعادة تعيين البيانات التجريبية...")
    
    try:
        from reset_demo_data import DemoDataReset
        
        demo_reset = DemoDataReset()
        success = demo_reset.run()
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل السكريبت: {e}")
        return False

def run_test():
    """تشغيل اختبار النتائج"""
    print("\n🧪 تشغيل اختبار النتائج...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_demo_data_reset.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ جميع الاختبارات نجحت")
            print(result.stdout)
            return True
        else:
            print("❌ بعض الاختبارات فشلت")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎯 سكريبت سريع لإعادة تعيين البيانات التجريبية")
    print("=" * 55)
    
    # تحذير للمستخدم
    print("\n⚠️ تحذير مهم:")
    print("   سيتم حذف جميع الحسابات والمعاملات الحالية للمستخدم admin")
    print("   سيتم إنشاء نسخة احتياطية تلقائياً قبل البدء")
    
    response = input("\nهل تريد المتابعة؟ (y/N): ").strip().lower()
    if response not in ['y', 'yes', 'نعم']:
        print("تم إلغاء العملية")
        return
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        print("💡 تأكد من:")
        print("   - تشغيل خادم MySQL")
        print("   - وجود قاعدة البيانات money_manager")
        print("   - وجود مستخدم admin بكلمة مرور 123456")
        return
    
    # إنشاء نسخة احتياطية
    backup_success = create_backup()
    if not backup_success:
        print("\n⚠️ لم يتم إنشاء نسخة احتياطية")
        response = input("هل تريد المتابعة بدون نسخة احتياطية؟ (y/N): ").strip().lower()
        if response not in ['y', 'yes', 'نعم']:
            print("تم إلغاء العملية")
            return
    
    # تشغيل إعادة التعيين
    if not run_demo_reset():
        print("\n❌ فشل في إعادة تعيين البيانات التجريبية")
        return
    
    # تشغيل الاختبار
    if not run_test():
        print("\n⚠️ فشل في اختبار النتائج")
        print("💡 جرب تشغيل: python test_demo_data_reset.py")
    
    print("\n🎉 تم إكمال إعادة تعيين البيانات التجريبية بنجاح!")
    
    print("\n📋 الخطوات التالية:")
    print("   1. شغل التطبيق: python main.py")
    print("   2. سجل الدخول: admin / 123456")
    print("   3. تحقق من:")
    print("      - الحسابات (5 حسابات)")
    print("      - الواردات (12 معاملة دخل)")
    print("      - المصروفات (20 معاملة مصروف)")
    print("      - التحويلات (5 تحويلات)")
    
    print("\n💾 لاستعادة البيانات السابقة:")
    print("   1. اذهب إلى 'إدارة قاعدة البيانات'")
    print("   2. انقر 'استعادة نسخة احتياطية'")
    print("   3. اختر النسخة المناسبة")

if __name__ == "__main__":
    main()
