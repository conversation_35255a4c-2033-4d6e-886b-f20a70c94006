# 🏦 برنامج إدارة الأموال الشخصية

نظام شامل لإدارة الأموال الشخصية مع دعم العملات المتعددة وواجهة مستخدم أنيقة باللون البنفسجي المتدرج.

## ✨ المميزات الرئيسية

### 👥 إدارة المستخدمين
- تسجيل دخول/تسجيل مستخدمين جدد
- صلاحيات مختلفة (مدير - مستخدم عادي)
- تعقب العمليات المالية حسب المستخدم
- نظام مصادقة آمن مع تشفير كلمات المرور

### 💱 نظام العملات المتعددة
- دعم إدخال بيانات مالية بعملات متعددة
- العملات المدعومة: SAR, USD, EUR, YER, AED, KWD, QAR, BHD, OMR, JOD, EGP
- عرض الأرصدة حسب كل عملة
- تحويل العملات التلقائي

### 💰 إدارة الواردات
- إدخال مصادر الدخل المختلفة
- اختيار العملة لكل وارد
- ربط الدخل بالمستخدم
- تصنيف الواردات (راتب، أعمال، استثمار، إلخ)

### 💸 إدارة المصروفات
- تسجيل المصروفات بتفاصيل كاملة
- اختيار العملة والتصنيف
- إرفاق صور للفواتير
- تصنيفات متنوعة (طعام، مواصلات، سكن، إلخ)

### 🏦 الحسابات والأرصدة
- تعريف أنواع مختلفة من الحسابات (صندوق، بنك، محفظة إلكترونية)
- تتبع الرصيد لكل حساب ولكل عملة
- العمليات بين الحسابات (تحويلات)
- إدارة متقدمة للحسابات

### 📊 التقارير والإحصائيات
- تقارير إجمالية حسب العملة والمستخدم والفترة
- تقرير بالأموال المتبقية
- رسوم بيانية تفاعلية
- إحصائيات مفصلة

### 🔍 نظام الفلاتر والبحث
- تصفية حسب المستخدم، العملة، نوع العملية
- تصفية حسب التاريخ والحساب
- بحث سريع حسب الوصف أو المبلغ

### ⚙️ إعدادات النظام
- إعداد صلاحيات المستخدمين
- النسخ الاحتياطي اليدوي والتلقائي
- إعدادات اللغة والتاريخ (هجري/ميلادي)
- تخصيص واجهة المستخدم

## 🎨 التصميم

- تصميم أنيق بألوان بنفسجية متدرجة
- واجهة مستخدم حديثة وسهلة الاستخدام
- تجربة مستخدم محسنة
- دعم كامل للغة العربية

## 🛠️ المتطلبات التقنية

### متطلبات النظام
- Python 3.8 أو أحدث
- MySQL Server 8.0 أو أحدث
- نظام التشغيل: Windows, macOS, Linux

### المكتبات المطلوبة
```
mysql-connector-python==8.2.0
customtkinter==5.2.2
Pillow==10.1.0
python-dateutil==2.8.2
bcrypt==4.1.2
reportlab==4.0.7
matplotlib==3.8.2
pandas==2.1.4
openpyxl==3.1.2
hijri-converter==2.3.1
```

## 📥 التثبيت والتشغيل

### 1. تحضير البيئة

```bash
# استنساخ المشروع
git clone <repository-url>
cd money-manager

# إنشاء بيئة افتراضية (اختياري)
python -m venv venv
source venv/bin/activate  # على Linux/Mac
# أو
venv\Scripts\activate     # على Windows

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. إعداد قاعدة البيانات

1. تأكد من تشغيل MySQL Server
2. قم بإنشاء قاعدة بيانات جديدة (اختياري - سيتم إنشاؤها تلقائياً)
3. تأكد من معرفة كلمة مرور MySQL root

### 3. تشغيل البرنامج

```bash
python main.py
```

### 4. الإعداد الأولي

1. عند التشغيل الأول، أدخل كلمة مرور MySQL
2. إذا لم توجد مستخدمين، سيطلب منك إنشاء حساب المدير الأول
3. أكمل بيانات المدير وابدأ الاستخدام

## 📁 هيكل المشروع

```
money-manager/
├── main.py                 # نقطة البداية الرئيسية
├── requirements.txt        # المتطلبات
├── README.md              # هذا الملف
├── config/                # ملفات الإعدادات
│   ├── colors.py          # ألوان التصميم
│   └── settings.py        # إعدادات التطبيق
├── database/              # قاعدة البيانات
│   ├── connection.py      # الاتصال بقاعدة البيانات
│   ├── models.py          # نماذج البيانات
│   └── schema.sql         # هيكل قاعدة البيانات
├── gui/                   # واجهة المستخدم
│   ├── login_window.py    # نافذة تسجيل الدخول
│   ├── register_window.py # نافذة التسجيل
│   ├── main_window.py     # النافذة الرئيسية
│   ├── income_window.py   # نافذة الواردات
│   ├── expense_window.py  # نافذة المصروفات
│   ├── reports_window.py  # نافذة التقارير
│   └── settings_window.py # نافذة الإعدادات
├── utils/                 # أدوات مساعدة
│   ├── auth.py           # نظام المصادقة
│   ├── currency.py       # إدارة العملات
│   ├── backup.py         # النسخ الاحتياطي
│   └── date_utils.py     # أدوات التاريخ
├── logs/                 # ملفات السجلات
├── backups/              # النسخ الاحتياطية
├── uploads/              # الملفات المرفوعة
├── reports/              # التقارير المُصدرة
└── assets/               # الموارد (صور، أيقونات)
```

## 🚀 الاستخدام

### تسجيل الدخول الأول
1. شغل البرنامج
2. أدخل كلمة مرور MySQL
3. أنشئ حساب المدير الأول
4. ابدأ استخدام النظام

### إضافة المعاملات
1. من القائمة الجانبية، اختر "الواردات" أو "المصروفات"
2. اضغط على "إضافة جديد"
3. أكمل البيانات المطلوبة
4. احفظ المعاملة

### عرض التقارير
1. اذهب إلى قسم "التقارير"
2. اختر نوع التقرير والفترة الزمنية
3. اعرض أو صدّر التقرير

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ في الاتصال بقاعدة البيانات:**
- تأكد من تشغيل MySQL Server
- تحقق من كلمة مرور MySQL
- تأكد من إعدادات الاتصال في `config/settings.py`

**خطأ في المتطلبات:**
- قم بتشغيل `pip install -r requirements.txt`
- تأكد من استخدام Python 3.8+

**مشاكل في الواجهة:**
- تأكد من تثبيت customtkinter بشكل صحيح
- أعد تشغيل البرنامج

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل أو لديك اقتراحات:
1. تحقق من ملفات السجل في مجلد `logs/`
2. راجع هذا الدليل
3. تواصل مع فريق التطوير

## 📄 الترخيص

هذا البرنامج مطور بواسطة Augment Agent لأغراض تعليمية وشخصية.

---

**استمتع بإدارة أموالك بطريقة احترافية! 💰✨**
