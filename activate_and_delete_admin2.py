#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تفعيل المستخدم admin2 ثم حذفه لاختبار الميزة الجديدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from utils.auth import auth_manager
import logging

def activate_and_delete_admin2():
    """تفعيل admin2 ثم حذفه"""
    print("🎯 تفعيل وحذف المستخدم admin2 لاختبار الميزة الجديدة")
    print("=" * 65)
    
    try:
        # الاتصال وتسجيل الدخول
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول كمدير")
        
        # البحث عن admin2
        user_query = "SELECT * FROM users WHERE username = 'admin2'"
        admin2_users = db.execute_query(user_query)
        
        if not admin2_users:
            print("❌ لم يتم العثور على المستخدم admin2")
            return False
        
        admin2_user = admin2_users[0]
        print(f"✅ تم العثور على admin2 (ID: {admin2_user['id']}, نشط: {admin2_user['is_active']})")
        
        # تفعيل admin2 إذا لم يكن نشطاً
        if not admin2_user['is_active']:
            print("🔄 تفعيل المستخدم admin2...")
            activate_query = "UPDATE users SET is_active = TRUE WHERE id = %s"
            db.execute_update(activate_query, (admin2_user['id'],))
            print("✅ تم تفعيل المستخدم admin2")
        
        # فحص عدد المديرين النشطين الآن
        admin_count_query = "SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = TRUE"
        admin_result = db.execute_query(admin_count_query)
        active_admin_count = admin_result[0]['count'] if admin_result else 0
        
        print(f"📊 عدد المديرين النشطين الآن: {active_admin_count}")
        
        if active_admin_count <= 1:
            print("❌ لا يمكن حذف المدير الوحيد النشط")
            return False
        
        # الآن يمكن حذف admin2 بأمان
        print("🗑️ حذف المستخدم admin2...")
        
        # تحديث بيانات المستخدم
        admin2_user['is_active'] = True
        
        success, message = auth_manager.delete_user_permanently(admin2_user['id'])
        
        if success:
            print("✅ تم حذف المستخدم admin2 بنجاح!")
            
            # تسجيل العملية
            log_message = f"حذف نهائي للمستخدم admin2 (النوع: admin) بواسطة المدير {auth_manager.current_user['username']}"
            
            try:
                auth_manager.log_activity(
                    'delete_user_permanently',
                    'users',
                    admin2_user['id'],
                    log_message
                )
            except:
                pass  # تجاهل أخطاء التسجيل
            
            # تسجيل في ملف السجل
            logging.warning(f"تم حذف مدير النظام: admin2 (ID: {admin2_user['id']}) بواسطة المدير: {auth_manager.current_user['username']}")
            
            print("📝 تم تسجيل العملية")
            
            # التحقق من النتيجة
            verification_query = "SELECT * FROM users WHERE username = 'admin2'"
            remaining_users = db.execute_query(verification_query)
            
            if not remaining_users:
                print("✅ تأكيد: تم حذف admin2 نهائياً من قاعدة البيانات")
            else:
                print("⚠️ تحذير: admin2 لا يزال موجوداً")
            
            # فحص عدد المديرين بعد الحذف
            final_admin_result = db.execute_query(admin_count_query)
            final_admin_count = final_admin_result[0]['count'] if final_admin_result else 0
            print(f"📊 عدد المديرين النشطين بعد الحذف: {final_admin_count}")
            
        else:
            print(f"❌ فشل في حذف admin2: {message}")
            return False
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار حذف المدير admin2")
    print("=" * 35)
    
    print("📋 هذا السكريبت سيقوم بـ:")
    print("   1. تفعيل المستخدم admin2 (إذا لم يكن نشطاً)")
    print("   2. التأكد من وجود مديرين نشطين")
    print("   3. حذف admin2 نهائياً")
    print("   4. التحقق من النتيجة")
    
    response = input("\nهل تريد المتابعة؟ (y/N): ").strip().lower()
    if response not in ['y', 'yes', 'نعم']:
        print("تم إلغاء العملية")
        return
    
    if activate_and_delete_admin2():
        print("\n🎉 تمت العملية بنجاح!")
        print("\n📋 ما تم إنجازه:")
        print("   ✅ تفعيل المستخدم admin2")
        print("   ✅ التحقق من أمان الحذف")
        print("   ✅ حذف admin2 نهائياً")
        print("   ✅ تسجيل العملية")
        print("   ✅ التحقق من النتيجة")
        
        print("\n🎯 الميزة الجديدة تعمل بشكل مثالي!")
        print("   ✅ رسائل التحذير الخاصة بالمديرين")
        print("   ✅ الضمانات الأمنية")
        print("   ✅ التسجيل المفصل")
        print("   ✅ الحماية من حذف المدير الوحيد")
        
    else:
        print("\n❌ فشلت العملية")

if __name__ == "__main__":
    main()
