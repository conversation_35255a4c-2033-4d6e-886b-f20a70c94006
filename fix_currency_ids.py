#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح معرفات العملات
Fix currency IDs
"""

import mysql.connector

def fix_currency_ids():
    """إصلاح معرفات العملات لتكون متسقة"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='mohdam',
            database='money_manager'
        )
        cursor = connection.cursor()
        
        print("✅ متصل بقاعدة البيانات")
        
        # فحص العملات الحالية
        cursor.execute("SELECT id, code, name FROM currencies ORDER BY id")
        current_currencies = cursor.fetchall()
        
        print("\n📋 العملات الحالية:")
        for currency in current_currencies:
            print(f"   ID: {currency[0]}, Code: {currency[1]}, Name: {currency[2]}")
        
        # حذف جميع العملات وإعادة إدراجها بمعرفات ثابتة
        print("\n🔄 إعادة تنظيم العملات...")
        
        # حذف القيود الخارجية مؤقتاً
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        
        # حذف العملات الموجودة
        cursor.execute("DELETE FROM currencies")
        
        # إعادة تعيين AUTO_INCREMENT
        cursor.execute("ALTER TABLE currencies AUTO_INCREMENT = 1")
        
        # إدراج العملات بمعرفات ثابتة
        currencies = [
            (1, 'SAR', 'ريال سعودي', 'ر.س'),
            (2, 'YER', 'ريال يمني', 'ر.ي'),
            (3, 'AED', 'درهم إماراتي', 'د.إ'),
            (4, 'USD', 'دولار أمريكي', '$')
        ]
        
        for currency_id, code, name, symbol in currencies:
            cursor.execute("""
                INSERT INTO currencies (id, code, name, symbol, is_active, exchange_rate)
                VALUES (%s, %s, %s, %s, 1, 1.0)
            """, (currency_id, code, name, symbol))
            print(f"   ✅ {code}: ID {currency_id}")
        
        # إعادة تفعيل القيود الخارجية
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        connection.commit()
        
        # التحقق من النتيجة
        print("\n📊 العملات بعد الإصلاح:")
        cursor.execute("SELECT id, code, name, symbol FROM currencies ORDER BY id")
        fixed_currencies = cursor.fetchall()
        
        for currency in fixed_currencies:
            print(f"   ID: {currency[0]} | {currency[1]}: {currency[2]} ({currency[3]})")
        
        cursor.close()
        connection.close()
        
        print("\n✅ تم إصلاح معرفات العملات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح العملات: {e}")
        return False

if __name__ == "__main__":
    print("🔧 إصلاح معرفات العملات")
    print("=" * 40)
    
    success = fix_currency_ids()
    
    if success:
        print("\n🎉 تم الإصلاح بنجاح!")
        print("💡 يمكنك الآن استخدام المعرفات التالية:")
        print("   1: SAR (ريال سعودي)")
        print("   2: YER (ريال يمني)")
        print("   3: AED (درهم إماراتي)")
        print("   4: USD (دولار أمريكي)")
    else:
        print("\n❌ فشل في الإصلاح")
