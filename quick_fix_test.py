#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 اختبار سريع لإصلاح مشكلة عرض المستخدمين...")

try:
    # 1. اختبار الاتصال
    print("1. اختبار الاتصال...")
    from database.connection import db
    if db.is_connected() or db.connect():
        print("✅ الاتصال ناجح")
    else:
        print("❌ فشل الاتصال")
        exit(1)
    
    # 2. اختبار تسجيل الدخول
    print("2. اختبار تسجيل الدخول...")
    from utils.auth import auth_manager
    success, message = auth_manager.login("admin", "123456")
    if success:
        print("✅ تسجيل الدخول ناجح")
    else:
        print(f"❌ فشل تسجيل الدخول: {message}")
        exit(1)
    
    # 3. اختبار جلب المستخدمين
    print("3. اختبار جلب المستخدمين...")
    from database.models import User
    users = User.get_all()
    if users:
        print(f"✅ تم جلب {len(users)} مستخدم")
        for user in users:
            print(f"   - {user['username']} ({user['role']})")
    else:
        print("❌ لا توجد مستخدمين")
    
    # 4. اختبار مكونات واجهة المستخدم
    print("4. اختبار مكونات واجهة المستخدم...")
    from config.colors import COLORS, BUTTON_STYLES
    from config.fonts import create_rtl_label, create_rtl_button
    print("✅ مكونات واجهة المستخدم متاحة")
    
    auth_manager.logout()
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("\n✨ الإصلاحات المطبقة:")
    print("   🔧 إزالة رسائل الخطأ غير الضرورية")
    print("   🛡️ تحسين معالجة الأخطاء")
    print("   📋 إضافة بطاقة مبسطة للمستخدمين بمشاكل")
    print("   ⚠️ تحويل رسائل الخطأ إلى تحذيرات")
    
    print("\n🚀 للاستخدام:")
    print("1. شغل التطبيق: python main.py")
    print("2. سجل الدخول: admin / 123456")
    print("3. اذهب إلى إدارة المستخدمين")
    print("4. يجب ألا تظهر رسالة 'خطأ في عرض بيانات المستخدم'")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
