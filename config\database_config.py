#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة إعدادات قاعدة البيانات
"""

import json
import os
import mysql.connector
from mysql.connector import Error

class DatabaseConfig:
    """مدير إعدادات قاعدة البيانات"""
    
    def __init__(self):
        self.config_file = "config/database_settings.json"
        self.default_config = {
            "host": "localhost",
            "port": 3306,
            "database": "money_manager",
            "user": "root",
            "password": "",  # كلمة مرور فارغة افتراضياً
            "charset": "utf8mb4",
            "autocommit": True
        }
        self.current_config = self.load_config()
    
    def load_config(self):
        """تحميل إعدادات قاعدة البيانات"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # التأكد من وجود جميع المفاتيح المطلوبة
                    for key, value in self.default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            else:
                # إنشاء ملف الإعدادات الافتراضي
                self.save_config(self.default_config)
                return self.default_config.copy()
        except Exception as e:
            print(f"خطأ في تحميل إعدادات قاعدة البيانات: {e}")
            return self.default_config.copy()
    
    def save_config(self, config):
        """حفظ إعدادات قاعدة البيانات"""
        try:
            # إنشاء مجلد config إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            self.current_config = config.copy()
            return True, "تم حفظ الإعدادات بنجاح"
        except Exception as e:
            return False, f"خطأ في حفظ الإعدادات: {e}"
    
    def test_connection(self, config=None):
        """اختبار الاتصال بقاعدة البيانات"""
        if config is None:
            config = self.current_config
        
        try:
            # محاولة الاتصال
            connection = mysql.connector.connect(
                host=config['host'],
                port=config['port'],
                database=config['database'],
                user=config['user'],
                password=config['password'],
                charset=config['charset'],
                autocommit=config.get('autocommit', True)
            )
            
            if connection.is_connected():
                # اختبار استعلام بسيط
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                connection.close()
                
                return True, "تم الاتصال بقاعدة البيانات بنجاح"
            else:
                return False, "فشل في الاتصال بقاعدة البيانات"
                
        except Error as e:
            error_messages = {
                1045: "خطأ في اسم المستخدم أو كلمة المرور",
                1049: "قاعدة البيانات غير موجودة",
                2003: "لا يمكن الوصول إلى الخادم",
                2005: "عنوان الخادم غير صحيح"
            }
            
            error_code = e.errno if hasattr(e, 'errno') else None
            error_message = error_messages.get(error_code, str(e))
            
            return False, f"خطأ في الاتصال: {error_message}"
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
    
    def get_config(self):
        """الحصول على الإعدادات الحالية"""
        return self.current_config.copy()
    
    def update_config(self, new_config):
        """تحديث الإعدادات"""
        # التحقق من صحة الإعدادات
        required_fields = ['host', 'port', 'database', 'user']
        for field in required_fields:
            if field not in new_config or not str(new_config[field]).strip():
                return False, f"الحقل '{field}' مطلوب"

        # كلمة المرور اختيارية
        if 'password' not in new_config:
            new_config['password'] = ''
        
        # التحقق من صحة المنفذ
        try:
            port = int(new_config['port'])
            if port < 1 or port > 65535:
                return False, "رقم المنفذ يجب أن يكون بين 1 و 65535"
            new_config['port'] = port
        except ValueError:
            return False, "رقم المنفذ يجب أن يكون رقماً صحيحاً"
        
        # إضافة الإعدادات الافتراضية المفقودة
        for key, value in self.default_config.items():
            if key not in new_config:
                new_config[key] = value
        
        return self.save_config(new_config)
    
    def reset_to_default(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        return self.save_config(self.default_config.copy())
    
    def validate_config(self, config):
        """التحقق من صحة الإعدادات"""
        errors = []
        
        # التحقق من الحقول المطلوبة
        required_fields = {
            'host': 'عنوان الخادم',
            'port': 'المنفذ',
            'database': 'اسم قاعدة البيانات',
            'user': 'اسم المستخدم'
        }

        for field, name in required_fields.items():
            if field not in config or not str(config[field]).strip():
                errors.append(f"{name} مطلوب")

        # كلمة المرور اختيارية (يمكن أن تكون فارغة)
        if 'password' not in config:
            config['password'] = ''
        
        # التحقق من المنفذ
        if 'port' in config:
            try:
                port = int(config['port'])
                if port < 1 or port > 65535:
                    errors.append("رقم المنفذ يجب أن يكون بين 1 و 65535")
            except (ValueError, TypeError):
                errors.append("رقم المنفذ يجب أن يكون رقماً صحيحاً")
        
        # التحقق من عنوان الخادم
        if 'host' in config and config['host']:
            host = config['host'].strip()
            if not host:
                errors.append("عنوان الخادم لا يمكن أن يكون فارغاً")
        
        return len(errors) == 0, errors
    
    def get_connection_string(self, config=None):
        """الحصول على نص الاتصال"""
        if config is None:
            config = self.current_config
        
        return f"mysql://{config['user']}:***@{config['host']}:{config['port']}/{config['database']}"

# إنشاء مثيل عام
db_config = DatabaseConfig()
