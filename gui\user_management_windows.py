#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نوافذ إدارة المستخدمين
"""

import customtkinter as ctk
from tkinter import messagebox
from config.colors import COLORS, BUTTON_STYLES, CARD_STYLES, ARABIC_TEXT_STYLES
from config.fonts import (
    create_rtl_label, create_rtl_button, create_rtl_entry
)
from utils.auth import auth_manager
import re

class AddUserWindow:
    """نافذة إضافة مستخدم جديد"""
    
    def __init__(self, parent, callback=None):
        self.parent = parent
        self.callback = callback
        self.window = None
        self.create_window()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = ctk.CTkToplevel(self.parent)
        self.window.title("إضافة مستخدم جديد")
        self.window.geometry("500x600")
        self.window.resizable(False, False)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء المحتوى
        self.create_content()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"500x600+{x}+{y}")
    
    def create_content(self):
        """إنشاء محتوى النافذة"""
        # إطار رئيسي
        main_frame = ctk.CTkFrame(
            self.window,
            fg_color=COLORS['bg_light'],
            corner_radius=15
        )
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان النافذة
        title_label = create_rtl_label(
            main_frame,
            text="إضافة مستخدم جديد",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 30))
        
        # إطار النموذج
        form_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        form_frame.pack(fill="both", expand=True, padx=30)
        
        # اسم المستخدم
        username_label = create_rtl_label(
            form_frame,
            text="اسم المستخدم:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        username_label.pack(anchor="e", pady=(0, 5))
        
        self.username_entry = create_rtl_entry(
            form_frame,
            placeholder_text="أدخل اسم المستخدم (3 أحرف على الأقل)"
        )
        self.username_entry.pack(fill="x", pady=(0, 15))
        
        # كلمة المرور
        password_label = create_rtl_label(
            form_frame,
            text="كلمة المرور:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        password_label.pack(anchor="e", pady=(0, 5))
        
        self.password_entry = create_rtl_entry(
            form_frame,
            placeholder_text="أدخل كلمة المرور (6 أحرف على الأقل)",
            show="*"
        )
        self.password_entry.pack(fill="x", pady=(0, 15))
        
        # تأكيد كلمة المرور
        confirm_password_label = create_rtl_label(
            form_frame,
            text="تأكيد كلمة المرور:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        confirm_password_label.pack(anchor="e", pady=(0, 5))
        
        self.confirm_password_entry = create_rtl_entry(
            form_frame,
            placeholder_text="أعد إدخال كلمة المرور",
            show="*"
        )
        self.confirm_password_entry.pack(fill="x", pady=(0, 15))
        
        # الاسم الكامل
        fullname_label = create_rtl_label(
            form_frame,
            text="الاسم الكامل:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        fullname_label.pack(anchor="e", pady=(0, 5))
        
        self.fullname_entry = create_rtl_entry(
            form_frame,
            placeholder_text="أدخل الاسم الكامل"
        )
        self.fullname_entry.pack(fill="x", pady=(0, 15))
        
        # الدور
        role_label = create_rtl_label(
            form_frame,
            text="الدور:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        role_label.pack(anchor="e", pady=(0, 5))
        
        self.role_var = ctk.StringVar(value="user")
        role_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        role_frame.pack(fill="x", pady=(0, 20))
        
        admin_radio = ctk.CTkRadioButton(
            role_frame,
            text="مدير",
            variable=self.role_var,
            value="admin",
            font=("Arial", 12)
        )
        admin_radio.pack(side="right", padx=10)
        
        user_radio = ctk.CTkRadioButton(
            role_frame,
            text="مستخدم",
            variable=self.role_var,
            value="user",
            font=("Arial", 12)
        )
        user_radio.pack(side="right", padx=10)
        
        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=30, pady=20)
        
        # زر الإلغاء
        cancel_button = create_rtl_button(
            buttons_frame,
            text="إلغاء",
            command=self.cancel,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="left", padx=10)
        
        # زر الحفظ
        save_button = create_rtl_button(
            buttons_frame,
            text="حفظ المستخدم",
            command=self.save_user,
            **BUTTON_STYLES['primary']
        )
        save_button.pack(side="right", padx=10)
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        confirm_password = self.confirm_password_entry.get()
        fullname = self.fullname_entry.get().strip()
        
        # التحقق من اسم المستخدم
        if not username or len(username) < 3:
            messagebox.showerror("خطأ", "اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
            return False
        
        # التحقق من أن اسم المستخدم يحتوي على أحرف وأرقام فقط
        if not re.match("^[a-zA-Z0-9_]+$", username):
            messagebox.showerror("خطأ", "اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط")
            return False
        
        # التحقق من كلمة المرور
        if not password or len(password) < 6:
            messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return False
        
        # التحقق من تطابق كلمة المرور
        if password != confirm_password:
            messagebox.showerror("خطأ", "كلمة المرور وتأكيد كلمة المرور غير متطابقتين")
            return False
        
        # التحقق من الاسم الكامل
        if not fullname or len(fullname) < 2:
            messagebox.showerror("خطأ", "الاسم الكامل يجب أن يكون حرفين على الأقل")
            return False
        
        return True
    
    def save_user(self):
        """حفظ المستخدم الجديد"""
        try:
            # التحقق من الصلاحيات
            if not auth_manager.can_manage_users():
                messagebox.showerror("غير مصرح", "ليس لديك صلاحية لإنشاء مستخدمين جدد")
                return

            if not self.validate_data():
                return

            username = self.username_entry.get().strip()
            password = self.password_entry.get()
            fullname = self.fullname_entry.get().strip()
            role = self.role_var.get()

            # تأكيد إنشاء المستخدم
            confirm_text = f"هل أنت متأكد من إنشاء المستخدم '{username}' بدور '{role}'؟"
            if role == 'admin':
                confirm_text += "\n\nتحذير: سيحصل هذا المستخدم على صلاحيات المدير الكاملة."

            result = messagebox.askyesno("تأكيد الإنشاء", confirm_text, icon='question')
            if not result:
                return

            # إنشاء المستخدم
            success, message = auth_manager.register_user(
                username=username,
                password=password,
                full_name=fullname,
                role=role
            )

            if success:
                messagebox.showinfo("نجح", message)
                # تسجيل العملية
                auth_manager.log_activity('create', 'users', None, f"إنشاء المستخدم {username}")
                if self.callback:
                    self.callback()  # تحديث قائمة المستخدمين
                self.window.destroy()
            else:
                messagebox.showerror("خطأ", message)

        except Exception as e:
            print(f"خطأ في حفظ المستخدم: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ المستخدم: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.window.destroy()


class EditUserWindow:
    """نافذة تعديل بيانات المستخدم - محسنة"""

    def __init__(self, parent, user_data, callback=None):
        self.parent = parent
        self.user_data = user_data
        self.callback = callback
        self.window = None
        self.create_window()

    def create_window(self):
        """إنشاء النافذة"""
        self.window = ctk.CTkToplevel(self.parent)
        self.window.title(f"تعديل المستخدم: {self.user_data['username']}")
        self.window.geometry("550x750")  # زيادة الحجم للحقول الإضافية
        self.window.resizable(False, False)

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

        # توسيط النافذة
        self.center_window()

        # إنشاء المحتوى
        self.create_content()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (550 // 2)
        y = (self.window.winfo_screenheight() // 2) - (750 // 2)
        self.window.geometry(f"550x750+{x}+{y}")

    def create_content(self):
        """إنشاء محتوى النافذة المحسن"""
        # إطار رئيسي
        main_frame = ctk.CTkFrame(
            self.window,
            fg_color=COLORS['bg_light'],
            corner_radius=15
        )
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = create_rtl_label(
            main_frame,
            text=f"تعديل المستخدم: {self.user_data['username']}",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 30))

        # إطار قابل للتمرير للنموذج
        scroll_frame = ctk.CTkScrollableFrame(
            main_frame,
            fg_color="transparent",
            height=500
        )
        scroll_frame.pack(fill="both", expand=True, padx=30)

        # اسم المستخدم (قابل للتعديل الآن)
        username_label = create_rtl_label(
            scroll_frame,
            text="اسم المستخدم:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        username_label.pack(anchor="e", pady=(0, 5))

        self.username_entry = create_rtl_entry(
            scroll_frame,
            placeholder_text="أدخل اسم المستخدم (3 أحرف على الأقل)"
        )
        self.username_entry.insert(0, self.user_data['username'])
        self.username_entry.pack(fill="x", pady=(0, 15))

        # الاسم الكامل
        fullname_label = create_rtl_label(
            scroll_frame,
            text="الاسم الكامل:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        fullname_label.pack(anchor="e", pady=(0, 5))

        self.fullname_entry = create_rtl_entry(
            scroll_frame,
            placeholder_text="أدخل الاسم الكامل"
        )
        self.fullname_entry.insert(0, self.user_data['full_name'])
        self.fullname_entry.pack(fill="x", pady=(0, 15))

        # الدور
        role_label = create_rtl_label(
            scroll_frame,
            text="الدور:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        role_label.pack(anchor="e", pady=(0, 5))

        self.role_var = ctk.StringVar(value=self.user_data['role'])
        role_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent")
        role_frame.pack(fill="x", pady=(0, 15))

        admin_radio = ctk.CTkRadioButton(
            role_frame,
            text="مدير",
            variable=self.role_var,
            value="admin",
            font=("Arial", 12)
        )
        admin_radio.pack(side="right", padx=10)

        user_radio = ctk.CTkRadioButton(
            role_frame,
            text="مستخدم",
            variable=self.role_var,
            value="user",
            font=("Arial", 12)
        )
        user_radio.pack(side="right", padx=10)

        # حالة المستخدم
        status_label = create_rtl_label(
            scroll_frame,
            text="حالة المستخدم:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        status_label.pack(anchor="e", pady=(0, 5))

        self.status_var = ctk.BooleanVar(value=self.user_data['is_active'])
        status_checkbox = ctk.CTkCheckBox(
            scroll_frame,
            text="المستخدم نشط",
            variable=self.status_var,
            font=("Arial", 12)
        )
        status_checkbox.pack(anchor="e", pady=(0, 20))

        # قسم تغيير كلمة المرور (اختياري)
        password_section_label = create_rtl_label(
            scroll_frame,
            text="تغيير كلمة المرور (اختياري):",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['subtitle']
        )
        password_section_label.pack(anchor="e", pady=(20, 10))

        # خط فاصل
        separator = ctk.CTkFrame(scroll_frame, height=2, fg_color=COLORS['border'])
        separator.pack(fill="x", pady=(0, 15))

        # كلمة المرور الجديدة
        new_password_label = create_rtl_label(
            scroll_frame,
            text="كلمة المرور الجديدة:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        new_password_label.pack(anchor="e", pady=(0, 5))

        self.new_password_entry = create_rtl_entry(
            scroll_frame,
            placeholder_text="اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور",
            show="*"
        )
        self.new_password_entry.pack(fill="x", pady=(0, 15))

        # تأكيد كلمة المرور الجديدة
        confirm_new_password_label = create_rtl_label(
            scroll_frame,
            text="تأكيد كلمة المرور الجديدة:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        confirm_new_password_label.pack(anchor="e", pady=(0, 5))

        self.confirm_new_password_entry = create_rtl_entry(
            scroll_frame,
            placeholder_text="أعد إدخال كلمة المرور الجديدة",
            show="*"
        )
        self.confirm_new_password_entry.pack(fill="x", pady=(0, 20))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=30, pady=20)

        # زر الإلغاء
        cancel_button = create_rtl_button(
            buttons_frame,
            text="إلغاء",
            command=self.cancel,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="left", padx=10)

        # زر الحفظ
        save_button = create_rtl_button(
            buttons_frame,
            text="حفظ التغييرات",
            command=self.save_changes,
            **BUTTON_STYLES['primary']
        )
        save_button.pack(side="right", padx=10)

    def validate_data(self):
        """التحقق من صحة البيانات المحسن"""
        username = self.username_entry.get().strip()
        fullname = self.fullname_entry.get().strip()
        new_password = self.new_password_entry.get()
        confirm_new_password = self.confirm_new_password_entry.get()

        # التحقق من اسم المستخدم
        if not username or len(username) < 3:
            messagebox.showerror("خطأ", "اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
            return False

        # التحقق من أن اسم المستخدم يحتوي على أحرف وأرقام فقط
        if not re.match("^[a-zA-Z0-9_]+$", username):
            messagebox.showerror("خطأ", "اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط")
            return False

        # التحقق من الاسم الكامل
        if not fullname or len(fullname) < 2:
            messagebox.showerror("خطأ", "الاسم الكامل يجب أن يكون حرفين على الأقل")
            return False

        # التحقق من كلمة المرور الجديدة (إذا تم إدخالها)
        if new_password or confirm_new_password:
            if new_password != confirm_new_password:
                messagebox.showerror("خطأ", "كلمة المرور الجديدة وتأكيدها غير متطابقتين")
                return False

        return True

    def save_changes(self):
        """حفظ التغييرات المحسن"""
        try:
            # التحقق من الصلاحيات
            if not auth_manager.can_manage_users():
                messagebox.showerror("غير مصرح", "ليس لديك صلاحية لتعديل المستخدمين")
                return

            if not self.validate_data():
                return

            username = self.username_entry.get().strip()
            fullname = self.fullname_entry.get().strip()
            role = self.role_var.get()
            is_active = self.status_var.get()
            new_password = self.new_password_entry.get()

            # منع المستخدم من تعطيل نفسه
            if not is_active and auth_manager.current_user['id'] == self.user_data['id']:
                messagebox.showerror("خطأ", "لا يمكنك تعطيل حسابك الخاص")
                return

            # التحقق من تغيير اسم المستخدم
            username_changed = username != self.user_data['username']
            if username_changed:
                # التحقق من عدم وجود اسم المستخدم الجديد
                from database.models import User
                existing_user = User.get_by_username(username)
                if existing_user and existing_user['id'] != self.user_data['id']:
                    messagebox.showerror("خطأ", f"اسم المستخدم '{username}' موجود مسبقاً")
                    return

                # تأكيد تغيير اسم المستخدم
                result = messagebox.askyesno(
                    "تأكيد تغيير اسم المستخدم",
                    f"هل أنت متأكد من تغيير اسم المستخدم من '{self.user_data['username']}' إلى '{username}'؟\n\nهذا التغيير قد يؤثر على تسجيل الدخول.",
                    icon='warning'
                )
                if not result:
                    return

            # تحذير عند تغيير الدور إلى مدير
            if role == 'admin' and self.user_data['role'] != 'admin':
                result = messagebox.askyesno(
                    "تحذير",
                    f"هل أنت متأكد من منح المستخدم '{username}' صلاحيات المدير؟\n\nسيحصل على إمكانية الوصول الكامل للنظام.",
                    icon='warning'
                )
                if not result:
                    return

            # تجميع التغييرات
            changes = []
            if username_changed:
                changes.append(f"اسم المستخدم: {self.user_data['username']} → {username}")
            if fullname != self.user_data['full_name']:
                changes.append(f"الاسم الكامل: {self.user_data['full_name']} → {fullname}")
            if role != self.user_data['role']:
                old_role = "مدير" if self.user_data['role'] == 'admin' else "مستخدم"
                new_role = "مدير" if role == 'admin' else "مستخدم"
                changes.append(f"الدور: {old_role} → {new_role}")
            if is_active != self.user_data['is_active']:
                old_status = "نشط" if self.user_data['is_active'] else "معطل"
                new_status = "نشط" if is_active else "معطل"
                changes.append(f"الحالة: {old_status} → {new_status}")
            if new_password:
                changes.append("كلمة المرور: سيتم تغييرها")

            # تأكيد التغييرات
            if changes:
                changes_text = "\n".join(changes)
                result = messagebox.askyesno(
                    "تأكيد التغييرات",
                    f"سيتم تطبيق التغييرات التالية:\n\n{changes_text}\n\nهل تريد المتابعة؟"
                )
                if not result:
                    return
            else:
                messagebox.showinfo("تنبيه", "لم يتم إجراء أي تغييرات")
                return

            # تحديث بيانات المستخدم
            update_params = {
                'user_id': self.user_data['id'],
                'username': username if username_changed else None,
                'full_name': fullname,
                'role': role,
                'is_active': is_active
            }

            success, message = auth_manager.update_user_data(**update_params)

            if success:
                # تغيير كلمة المرور إذا تم إدخالها
                if new_password:
                    password_success, password_message = auth_manager.reset_user_password(
                        self.user_data['id'], new_password
                    )
                    if not password_success:
                        messagebox.showerror("تحذير", f"تم تحديث البيانات ولكن فشل تغيير كلمة المرور: {password_message}")
                    else:
                        message += "\nتم تغيير كلمة المرور بنجاح"

                messagebox.showinfo("نجح", message)

                # تسجيل العملية
                auth_manager.log_activity('update', 'users', self.user_data['id'], f"تعديل المستخدم {username}")

                if self.callback:
                    self.callback()  # تحديث قائمة المستخدمين
                self.window.destroy()
            else:
                messagebox.showerror("خطأ", message)

        except Exception as e:
            print(f"خطأ في حفظ التغييرات: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ التغييرات: {str(e)}")

    def cancel(self):
        """إلغاء العملية"""
        self.window.destroy()


class ResetPasswordWindow:
    """نافذة إعادة تعيين كلمة المرور"""

    def __init__(self, parent, user_data, callback=None):
        self.parent = parent
        self.user_data = user_data
        self.callback = callback
        self.window = None
        self.create_window()

    def create_window(self):
        """إنشاء النافذة"""
        self.window = ctk.CTkToplevel(self.parent)
        self.window.title(f"إعادة تعيين كلمة مرور: {self.user_data['username']}")
        self.window.geometry("450x400")
        self.window.resizable(False, False)

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

        # توسيط النافذة
        self.center_window()

        # إنشاء المحتوى
        self.create_content()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.window.winfo_screenheight() // 2) - (400 // 2)
        self.window.geometry(f"450x400+{x}+{y}")

    def create_content(self):
        """إنشاء محتوى النافذة"""
        # إطار رئيسي
        main_frame = ctk.CTkFrame(
            self.window,
            fg_color=COLORS['bg_light'],
            corner_radius=15
        )
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = create_rtl_label(
            main_frame,
            text=f"إعادة تعيين كلمة مرور",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 10))

        # اسم المستخدم
        user_label = create_rtl_label(
            main_frame,
            text=f"المستخدم: {self.user_data['username']}",
            font_size='subtitle',
            text_color=COLORS['text_secondary']
        )
        user_label.pack(pady=(0, 30))

        # إطار النموذج
        form_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        form_frame.pack(fill="both", expand=True, padx=30)

        # كلمة المرور الجديدة
        password_label = create_rtl_label(
            form_frame,
            text="كلمة المرور الجديدة:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        password_label.pack(anchor="e", pady=(0, 5))

        self.password_entry = create_rtl_entry(
            form_frame,
            placeholder_text="أدخل كلمة المرور الجديدة (6 أحرف على الأقل)",
            show="*"
        )
        self.password_entry.pack(fill="x", pady=(0, 15))

        # تأكيد كلمة المرور
        confirm_password_label = create_rtl_label(
            form_frame,
            text="تأكيد كلمة المرور:",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        confirm_password_label.pack(anchor="e", pady=(0, 5))

        self.confirm_password_entry = create_rtl_entry(
            form_frame,
            placeholder_text="أعد إدخال كلمة المرور الجديدة",
            show="*"
        )
        self.confirm_password_entry.pack(fill="x", pady=(0, 30))

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=30, pady=20)

        # زر الإلغاء
        cancel_button = create_rtl_button(
            buttons_frame,
            text="إلغاء",
            command=self.cancel,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="left", padx=10)

        # زر الحفظ
        save_button = create_rtl_button(
            buttons_frame,
            text="إعادة تعيين كلمة المرور",
            command=self.reset_password,
            **BUTTON_STYLES['warning']
        )
        save_button.pack(side="right", padx=10)

    def validate_data(self):
        """التحقق من صحة البيانات"""
        password = self.password_entry.get()
        confirm_password = self.confirm_password_entry.get()

        # التحقق من كلمة المرور
        if not password or len(password) < 6:
            messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return False

        # التحقق من تطابق كلمة المرور
        if password != confirm_password:
            messagebox.showerror("خطأ", "كلمة المرور وتأكيد كلمة المرور غير متطابقتين")
            return False

        return True

    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        try:
            if not self.validate_data():
                return

            # تأكيد العملية
            result = messagebox.askyesno(
                "تأكيد",
                f"هل أنت متأكد من إعادة تعيين كلمة مرور المستخدم {self.user_data['username']}؟"
            )

            if not result:
                return

            password = self.password_entry.get()

            # إعادة تعيين كلمة المرور
            success, message = auth_manager.reset_user_password(
                user_id=self.user_data['id'],
                new_password=password
            )

            if success:
                messagebox.showinfo("نجح", message)
                if self.callback:
                    self.callback()  # تحديث قائمة المستخدمين
                self.window.destroy()
            else:
                messagebox.showerror("خطأ", message)

        except Exception as e:
            print(f"خطأ في إعادة تعيين كلمة المرور: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إعادة تعيين كلمة المرور: {str(e)}")

    def cancel(self):
        """إلغاء العملية"""
        self.window.destroy()
