# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('assets', 'assets'),
        ('config', 'config'),
        ('database', 'database'),
        ('gui', 'gui'),
        ('utils', 'utils'),
        ('sample_excel_files', 'sample_excel_files'),
        ('requirements.txt', '.'),
        ('README.md', '.'),
        ('accounts.json', '.'),
        ('users.json', '.'),
        ('transactions.json', '.'),
    ],
    hiddenimports=[
        'customtkinter',
        'mysql.connector',
        'bcrypt',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'reportlab',
        'matplotlib',
        'pandas',
        'openpyxl',
        'schedule',
        'psutil',
        'dateutil',
        'tkinter',
        'sqlite3',
        'json',
        'datetime',
        'logging',
        'threading',
        'queue',
        'csv',
        'io',
        'base64',
        'hashlib',
        'uuid',
        'random',
        'math',
        'calendar',
        'locale',
        'platform',
        'subprocess',
        'shutil',
        'tempfile',
        'zipfile',
        'ssl',
        'urllib',
        'email',
        'configparser',
        'pickle',
        'copy',
        'collections',
        'itertools',
        'functools',
        'operator',
        'typing',
        'warnings',
        'gc',
        'weakref',
        'ctypes',
        'struct',
        'array',
        'binascii',
        'zlib',
        'gzip',
        'tarfile',
        'zipimport',
        'importlib',
        'pkg_resources',
        'setuptools',
        'traceback',
        'inspect',
        'ast',
        'types',
        'builtins',
        'encodings',
        'codecs',
        'unicodedata',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MoneyManager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
