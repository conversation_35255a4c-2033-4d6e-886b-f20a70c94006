#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لنظام المعاملات متعددة العملات
Comprehensive test for multi-currency transaction system
"""

from database.models import Account, Transaction, Currency
from datetime import date

def test_multi_currency_transactions():
    """اختبار شامل للمعاملات متعددة العملات"""
    try:
        print("🧪 اختبار شامل للمعاملات متعددة العملات")
        print("=" * 60)
        
        # 1. إنشاء حساب اختبار
        print("\n1️⃣ إنشاء حساب اختبار متعدد العملات...")
        account_id = Account.create(
            user_id=1,
            name="حساب اختبار شامل",
            description="حساب لاختبار جميع العملات والمعاملات"
        )
        
        if account_id <= 0:
            print("❌ فشل في إنشاء الحساب")
            return False
        
        print(f"✅ تم إنشاء الحساب - ID: {account_id}")
        
        # 2. إضافة أرصدة ابتدائية بجميع العملات
        print("\n2️⃣ إضافة أرصدة ابتدائية...")
        initial_balances = [
            (1, 1000.0, "ريال سعودي"),  # SAR
            (2, 500.0, "ريال يمني"),    # YER
            (3, 200.0, "درهم إماراتي"), # AED
            (4, 100.0, "دولار أمريكي")  # USD
        ]
        
        for currency_id, amount, name in initial_balances:
            Account.add_currency_balance(account_id, currency_id, amount)
            print(f"   ✅ {name}: {amount}")
        
        # 3. اختبار إضافة واردات بعملات مختلفة
        print("\n3️⃣ اختبار إضافة واردات بعملات مختلفة...")
        income_tests = [
            (1, 500.0, "راتب شهري", "ريال سعودي"),
            (4, 50.0, "مكافأة", "دولار أمريكي"),
            (3, 100.0, "عمولة", "درهم إماراتي")
        ]
        
        for currency_id, amount, desc, currency_name in income_tests:
            income_id = Transaction.create(
                user_id=1,
                account_id=account_id,
                currency_id=currency_id,
                transaction_type='income',
                amount=amount,
                description=desc,
                transaction_date=date.today()
            )
            
            if income_id > 0:
                new_balance = Account.get_currency_balance(account_id, currency_id)
                print(f"   ✅ وارد {currency_name}: +{amount} (الرصيد الجديد: {new_balance})")
            else:
                print(f"   ❌ فشل في إضافة وارد {currency_name}")
                return False
        
        # 4. اختبار إضافة مصروفات بعملات مختلفة
        print("\n4️⃣ اختبار إضافة مصروفات بعملات مختلفة...")
        expense_tests = [
            (1, 200.0, "مصروفات متنوعة", "ريال سعودي"),
            (4, 25.0, "اشتراك", "دولار أمريكي"),
            (2, 100.0, "وقود", "ريال يمني")
        ]
        
        for currency_id, amount, desc, currency_name in expense_tests:
            expense_id = Transaction.create(
                user_id=1,
                account_id=account_id,
                currency_id=currency_id,
                transaction_type='expense',
                amount=amount,
                description=desc,
                transaction_date=date.today()
            )
            
            if expense_id > 0:
                new_balance = Account.get_currency_balance(account_id, currency_id)
                print(f"   ✅ مصروف {currency_name}: -{amount} (الرصيد الجديد: {new_balance})")
            else:
                print(f"   ❌ فشل في إضافة مصروف {currency_name}")
                return False
        
        # 5. عرض الأرصدة النهائية
        print("\n5️⃣ الأرصدة النهائية لجميع العملات:")
        balances = Account.get_all_balances(account_id)
        
        if balances:
            total_currencies = 0
            for balance in balances:
                if balance['balance'] > 0:
                    print(f"   💰 {balance['name']}: {balance['balance']} {balance['symbol']}")
                    total_currencies += 1
            
            print(f"\n📊 إجمالي العملات النشطة: {total_currencies}")
        else:
            print("   ⚠️ لا توجد أرصدة")
        
        # 6. اختبار التحقق من الرصيد الكافي
        print("\n6️⃣ اختبار التحقق من الرصيد الكافي...")
        
        # محاولة إضافة مصروف أكبر من الرصيد المتاح
        current_sar_balance = Account.get_currency_balance(account_id, 1)
        large_amount = current_sar_balance + 1000.0
        
        print(f"   الرصيد الحالي SAR: {current_sar_balance}")
        print(f"   محاولة مصروف: {large_amount}")
        
        # هذا يجب أن ينجح في النموذج ولكن يفشل في الواجهة
        large_expense_id = Transaction.create(
            user_id=1,
            account_id=account_id,
            currency_id=1,
            transaction_type='expense',
            amount=large_amount,
            description="مصروف كبير للاختبار",
            transaction_date=date.today()
        )
        
        if large_expense_id > 0:
            final_balance = Account.get_currency_balance(account_id, 1)
            print(f"   ⚠️ تم السماح بالمصروف الكبير (الرصيد الجديد: {final_balance})")
            print("   💡 ملاحظة: الواجهة ستمنع هذا، لكن النموذج يسمح به")
        else:
            print("   ❌ فشل في إضافة المصروف الكبير")
        
        print("\n🎉 اكتمل الاختبار الشامل بنجاح!")
        print("✅ نظام المعاملات متعددة العملات يعمل بشكل صحيح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار الشامل: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_currency_system():
    """اختبار نظام العملات"""
    try:
        print("\n🔍 اختبار نظام العملات...")
        
        currencies = Currency.get_all()
        print(f"📋 العملات المتاحة ({len(currencies)}):")
        
        for currency in currencies:
            print(f"   - {currency['code']}: {currency['name']} ({currency['symbol']})")
        
        # التحقق من العملات المطلوبة
        required_codes = ['SAR', 'YER', 'AED', 'USD']
        available_codes = [c['code'] for c in currencies]
        
        missing = [code for code in required_codes if code not in available_codes]
        
        if missing:
            print(f"⚠️ عملات مفقودة: {missing}")
            return False
        else:
            print("✅ جميع العملات المطلوبة متوفرة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار العملات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الاختبار الشامل لنظام المعاملات متعددة العملات")
    print("=" * 80)
    
    # اختبار نظام العملات
    currency_test = test_currency_system()
    
    # اختبار المعاملات
    transaction_test = test_multi_currency_transactions()
    
    print("\n" + "=" * 80)
    print("📋 ملخص النتائج:")
    print(f"   🔹 نظام العملات: {'✅ نجح' if currency_test else '❌ فشل'}")
    print(f"   🔹 نظام المعاملات: {'✅ نجح' if transaction_test else '❌ فشل'}")
    
    if currency_test and transaction_test:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز للاستخدام")
        print("\n💡 يمكنك الآن:")
        print("   - إضافة حسابات متعددة العملات")
        print("   - إضافة واردات ومصروفات بأي من العملات الأربع")
        print("   - عرض الأرصدة منفصلة لكل عملة")
    else:
        print("\n❌ بعض الاختبارات فشلت")
        print("💡 تحقق من الأخطاء أعلاه وأصلحها")

if __name__ == "__main__":
    main()
