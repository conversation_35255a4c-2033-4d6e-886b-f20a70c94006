#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة تعيين قاعدة البيانات - حذف وإعادة إنشاء
"""

import sys
import os
import mysql.connector
from mysql.connector import Error
import logging
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import DATABASE_CONFIG

def setup_logging():
    """إعداد نظام السجلات"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_mysql_connection():
    """اختبار الاتصال بـ MySQL"""
    try:
        print("🔄 اختبار الاتصال بـ MySQL...")
        
        # الاتصال بدون تحديد قاعدة بيانات
        config = DATABASE_CONFIG.copy()
        config.pop('database', None)  # إزالة اسم قاعدة البيانات
        
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            print("✅ تم الاتصال بـ MySQL بنجاح")
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"📋 إصدار MySQL: {version[0]}")
            
            cursor.close()
            connection.close()
            return True
        else:
            print("❌ فشل الاتصال بـ MySQL")
            return False
            
    except Error as e:
        print(f"❌ خطأ في الاتصال بـ MySQL: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def drop_database():
    """حذف قاعدة البيانات القديمة"""
    try:
        print("🗑️  حذف قاعدة البيانات القديمة...")
        
        # الاتصال بدون تحديد قاعدة بيانات
        config = DATABASE_CONFIG.copy()
        db_name = config.pop('database')
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # حذف قاعدة البيانات إذا كانت موجودة
        cursor.execute(f"DROP DATABASE IF EXISTS {db_name}")
        print(f"✅ تم حذف قاعدة البيانات '{db_name}' (إن وجدت)")
        
        cursor.close()
        connection.close()
        return True
        
    except Error as e:
        print(f"❌ خطأ في حذف قاعدة البيانات: {e}")
        return False

def create_database():
    """إنشاء قاعدة البيانات الجديدة"""
    try:
        print("🏗️  إنشاء قاعدة البيانات الجديدة...")
        
        # الاتصال بدون تحديد قاعدة بيانات
        config = DATABASE_CONFIG.copy()
        db_name = config.pop('database')
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # إنشاء قاعدة البيانات
        cursor.execute(f"""
            CREATE DATABASE {db_name} 
            CHARACTER SET utf8mb4 
            COLLATE utf8mb4_unicode_ci
        """)
        print(f"✅ تم إنشاء قاعدة البيانات '{db_name}' بنجاح")
        
        cursor.close()
        connection.close()
        return True
        
    except Error as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def create_tables():
    """إنشاء الجداول من ملف schema.sql"""
    try:
        print("📋 إنشاء الجداول...")
        
        # قراءة ملف schema.sql
        schema_path = os.path.join('database', 'schema.sql')
        if not os.path.exists(schema_path):
            print(f"❌ ملف {schema_path} غير موجود")
            return False
        
        with open(schema_path, 'r', encoding='utf-8') as file:
            schema_sql = file.read()
        
        # الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()
        
        # تقسيم الاستعلامات وتنفيذها
        statements = schema_sql.split(';')
        created_tables = 0
        
        for statement in statements:
            statement = statement.strip()
            if statement and not statement.startswith('--') and 'CREATE TABLE' in statement.upper():
                try:
                    cursor.execute(statement)
                    created_tables += 1
                    # استخراج اسم الجدول
                    table_name = statement.split('CREATE TABLE IF NOT EXISTS')[1].split('(')[0].strip()
                    print(f"  ✅ تم إنشاء الجدول: {table_name}")
                except Error as e:
                    print(f"  ❌ خطأ في إنشاء جدول: {e}")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print(f"✅ تم إنشاء {created_tables} جدول بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def insert_default_data():
    """إدراج البيانات الافتراضية"""
    try:
        print("📊 إدراج البيانات الافتراضية...")
        
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()
        
        # العملات
        currencies_data = [
            (1, 'SAR', 'ريال سعودي', 'ر.س', 1.0000, 1),
            (2, 'USD', 'دولار أمريكي', '$', 3.7500, 1),
            (3, 'EUR', 'يورو', '€', 4.0000, 1),
            (4, 'AED', 'درهم إماراتي', 'د.إ', 1.0200, 1),
            (5, 'KWD', 'دينار كويتي', 'د.ك', 12.3000, 1)
        ]
        
        cursor.executemany("""
            INSERT IGNORE INTO currencies (id, code, name, symbol, exchange_rate, is_active) 
            VALUES (%s, %s, %s, %s, %s, %s)
        """, currencies_data)
        
        # أنواع الحسابات
        account_types_data = [
            (1, 'صندوق نقدي', 'النقد المتوفر في اليد', '💰', 1),
            (2, 'حساب بنكي', 'حساب في البنك', '🏦', 1),
            (3, 'بطاقة ائتمان', 'بطاقة ائتمان', '💳', 1),
            (4, 'محفظة إلكترونية', 'محفظة رقمية', '📱', 1),
            (5, 'استثمار', 'حساب استثماري', '📈', 1),
            (6, 'حساب توفير', 'حساب توفير', '🏛️', 1)
        ]
        
        cursor.executemany("""
            INSERT IGNORE INTO account_types (id, name, description, icon, is_active) 
            VALUES (%s, %s, %s, %s, %s)
        """, account_types_data)
        
        # تصنيفات الواردات
        income_categories_data = [
            (1, 'راتب', 'الراتب الشهري', '💼', '#4CAF50', 1),
            (2, 'أعمال', 'دخل من الأعمال', '🏢', '#2196F3', 1),
            (3, 'استثمار', 'عوائد استثمارية', '📈', '#FF9800', 1),
            (4, 'عمل حر', 'دخل من العمل الحر', '💻', '#9C27B0', 1),
            (5, 'إيجار', 'دخل من الإيجار', '🏠', '#795548', 1),
            (6, 'هدية', 'هدايا ومكافآت', '🎁', '#E91E63', 1),
            (7, 'مكافأة', 'مكافآت العمل', '🏆', '#FF5722', 1),
            (8, 'استرداد', 'استرداد مبالغ', '↩️', '#607D8B', 1),
            (9, 'أخرى', 'واردات أخرى', '💰', '#9E9E9E', 1)
        ]
        
        cursor.executemany("""
            INSERT IGNORE INTO income_categories (id, name, description, icon, color, is_active) 
            VALUES (%s, %s, %s, %s, %s, %s)
        """, income_categories_data)
        
        # تصنيفات المصروفات
        expense_categories_data = [
            (1, 'طعام وشراب', 'مصروفات الطعام والشراب', '🍽️', '#F44336', 1),
            (2, 'مواصلات', 'مصروفات النقل والمواصلات', '🚗', '#FF5722', 1),
            (3, 'سكن', 'مصروفات السكن والإيجار', '🏠', '#795548', 1),
            (4, 'فواتير', 'الفواتير والخدمات', '📄', '#607D8B', 1),
            (5, 'صحة', 'المصروفات الطبية', '🏥', '#E91E63', 1),
            (6, 'تعليم', 'مصروفات التعليم', '📚', '#3F51B5', 1),
            (7, 'ترفيه', 'مصروفات الترفيه', '🎮', '#9C27B0', 1),
            (8, 'تسوق', 'مصروفات التسوق', '🛍️', '#FF9800', 1),
            (9, 'سفر', 'مصروفات السفر', '✈️', '#00BCD4', 1),
            (10, 'عائلة', 'مصروفات العائلة', '👨‍👩‍👧‍👦', '#4CAF50', 1),
            (11, 'صدقات', 'الصدقات والزكاة', '🤲', '#8BC34A', 1),
            (12, 'استثمار', 'مصروفات الاستثمار', '📊', '#2196F3', 1),
            (13, 'ديون', 'سداد الديون', '💳', '#FF9800', 1),
            (14, 'أخرى', 'مصروفات أخرى', '💸', '#9E9E9E', 1)
        ]
        
        cursor.executemany("""
            INSERT IGNORE INTO expense_categories (id, name, description, icon, color, is_active) 
            VALUES (%s, %s, %s, %s, %s, %s)
        """, expense_categories_data)
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("✅ تم إدراج البيانات الافتراضية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدراج البيانات الافتراضية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🔄 إعادة تعيين قاعدة بيانات مدير الأموال")
    print("=" * 70)
    
    setup_logging()
    
    # اختبار الاتصال بـ MySQL
    if not test_mysql_connection():
        print("\n❌ تأكد من تشغيل MySQL Server أولاً")
        return False
    
    print("\n⚠️  تحذير: سيتم حذف جميع البيانات الموجودة!")
    confirm = input("هل تريد المتابعة؟ (y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء العملية")
        return False
    
    success = True
    
    # حذف قاعدة البيانات القديمة
    if not drop_database():
        success = False
    
    # إنشاء قاعدة البيانات الجديدة
    if success and not create_database():
        success = False
    
    # إنشاء الجداول
    if success and not create_tables():
        success = False
    
    # إدراج البيانات الافتراضية
    if success and not insert_default_data():
        success = False
    
    if success:
        print("\n🎉 تم إعادة تعيين قاعدة البيانات بنجاح!")
        print("💡 يمكنك الآن تشغيل create_sample_data.py لإنشاء بيانات تجريبية")
    else:
        print("\n❌ حدثت أخطاء أثناء إعادة تعيين قاعدة البيانات")
    
    return success

if __name__ == "__main__":
    main()
    input("\n🔄 اضغط Enter للخروج...")
