import mysql.connector

def get_db_connection():
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='mohdam',
            database='money_manager'
        )
        return connection
    except mysql.connector.Error as err:
        print(f"Error connecting to database: {err}")
        return None

def find_and_drop_fk(connection):
    cursor = connection.cursor()
    fk_name = None
    try:
        # Find the foreign key constraint name
        cursor.execute('''
            SELECT CONSTRAINT_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = "money_manager"
              AND TABLE_NAME = "accounts"
              AND COLUMN_NAME = "currency_id"
              AND REFERENCED_TABLE_NAME = "currencies";
        ''')
        result = cursor.fetchone()
        if result:
            fk_name = result[0]
            print(f"Found foreign key: {fk_name}")

            # Drop the foreign key
            cursor.execute(f"ALTER TABLE accounts DROP FOREIGN KEY {fk_name}")
            print(f"Successfully dropped foreign key: {fk_name}")
            connection.commit()
        else:
            print("Foreign key on 'accounts.currency_id' not found or already dropped.")

    except mysql.connector.Error as err:
        print(f"Error during foreign key operation: {err}")
        connection.rollback()
    finally:
        cursor.close()

if __name__ == "__main__":
    db_connection = get_db_connection()
    if db_connection:
        find_and_drop_fk(db_connection)
        db_connection.close()