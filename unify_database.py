#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
توحيد قاعدة البيانات - ترحيل البيانات من SQLite إلى MySQL
"""

import mysql.connector
import sqlite3
import json
import os
from datetime import datetime

def create_mysql_backup():
    """إنشاء نسخة احتياطية من MySQL قبل التعديل"""
    print("💾 إنشاء نسخة احتياطية من MySQL...")
    
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        
        # تصدير البيانات الحالية
        backup_data = {}
        tables = ['users', 'currencies', 'accounts', 'account_balances', 'transactions', 'transfers']
        
        for table in tables:
            try:
                cursor.execute(f"SELECT * FROM {table}")
                data = cursor.fetchall()
                backup_data[table] = data
                print(f"✅ تم تصدير {table}: {len(data)} سجل")
            except Exception as e:
                print(f"⚠️ تعذر تصدير {table}: {e}")
                backup_data[table] = []
        
        # حفظ النسخة الاحتياطية
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"mysql_backup_before_unification_{timestamp}.json"
        
        with open(backup_filename, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
        
        cursor.close()
        connection.close()
        
        print(f"✅ تم حفظ النسخة الاحتياطية: {backup_filename}")
        return backup_filename
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def ensure_mysql_schema():
    """التأكد من وجود مخطط MySQL الصحيح"""
    print("🔧 التحقق من مخطط MySQL...")
    
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # التحقق من وجود الجداول المطلوبة
        required_tables = [
            'users', 'currencies', 'account_types', 'accounts', 
            'account_balances', 'income_categories', 'expense_categories',
            'transactions', 'transfers'
        ]
        
        cursor.execute("SHOW TABLES")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = [table for table in required_tables if table not in existing_tables]
        
        if missing_tables:
            print(f"⚠️ جداول مفقودة: {missing_tables}")
            print("🔧 إنشاء الجداول المفقودة...")
            
            # قراءة وتنفيذ مخطط قاعدة البيانات
            if os.path.exists("database/schema.sql"):
                with open("database/schema.sql", 'r', encoding='utf-8') as f:
                    schema_sql = f.read()
                
                # تقسيم وتنفيذ الاستعلامات
                statements = schema_sql.split(';')
                for statement in statements:
                    statement = statement.strip()
                    if statement and not statement.startswith('--') and 'CREATE TABLE' in statement:
                        try:
                            cursor.execute(statement)
                            print(f"✅ تم إنشاء جدول")
                        except Exception as e:
                            print(f"⚠️ خطأ في إنشاء جدول: {e}")
                
                connection.commit()
        else:
            print("✅ جميع الجداول المطلوبة موجودة")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من المخطط: {e}")
        return False

def migrate_sqlite_to_mysql():
    """ترحيل البيانات من SQLite إلى MySQL"""
    print("🔄 ترحيل البيانات من SQLite إلى MySQL...")
    
    if not os.path.exists("money_manager.db"):
        print("❌ ملف SQLite غير موجود")
        return False
    
    try:
        # الاتصال بـ SQLite
        sqlite_conn = sqlite3.connect("money_manager.db")
        sqlite_conn.row_factory = sqlite3.Row
        sqlite_cursor = sqlite_conn.cursor()
        
        # الاتصال بـ MySQL
        mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        mysql_conn = mysql.connector.connect(**mysql_config)
        mysql_cursor = mysql_conn.cursor()
        
        # ترحيل المستخدمين
        print("👥 ترحيل المستخدمين...")
        sqlite_cursor.execute("SELECT * FROM users")
        users = sqlite_cursor.fetchall()
        
        for user in users:
            # التحقق من وجود المستخدم
            mysql_cursor.execute("SELECT id FROM users WHERE username = %s", (user['username'],))
            existing_user = mysql_cursor.fetchone()
            
            if not existing_user:
                insert_query = """
                    INSERT INTO users (username, password_hash, full_name, role, is_active, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                mysql_cursor.execute(insert_query, (
                    user['username'], user['password_hash'], user['full_name'],
                    user['role'], user['is_active'], user['created_at']
                ))
                print(f"✅ تم ترحيل المستخدم: {user['username']}")
            else:
                print(f"⚠️ المستخدم موجود مسبقاً: {user['username']}")
        
        # ترحيل العملات
        print("💰 ترحيل العملات...")
        sqlite_cursor.execute("SELECT * FROM currencies")
        currencies = sqlite_cursor.fetchall()
        
        for currency in currencies:
            mysql_cursor.execute("SELECT id FROM currencies WHERE code = %s", (currency['code'],))
            existing_currency = mysql_cursor.fetchone()
            
            if not existing_currency:
                insert_query = """
                    INSERT INTO currencies (code, name, symbol, exchange_rate, is_active)
                    VALUES (%s, %s, %s, %s, %s)
                """
                mysql_cursor.execute(insert_query, (
                    currency['code'], currency['name'], currency['symbol'],
                    currency['exchange_rate'], currency['is_active']
                ))
                print(f"✅ تم ترحيل العملة: {currency['code']}")
            else:
                print(f"⚠️ العملة موجودة مسبقاً: {currency['code']}")
        
        # ترحيل الحسابات
        print("🏦 ترحيل الحسابات...")
        sqlite_cursor.execute("SELECT * FROM accounts")
        accounts = sqlite_cursor.fetchall()
        
        for account in accounts:
            mysql_cursor.execute("SELECT id FROM accounts WHERE name = %s AND user_id = %s", 
                                (account['name'], account['user_id']))
            existing_account = mysql_cursor.fetchone()
            
            if not existing_account:
                insert_query = """
                    INSERT INTO accounts (user_id, name, account_type_id, description, is_active, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                mysql_cursor.execute(insert_query, (
                    account['user_id'], account['name'], account['account_type_id'],
                    account['description'], account['is_active'], account['created_at']
                ))
                account_id = mysql_cursor.lastrowid
                print(f"✅ تم ترحيل الحساب: {account['name']} (ID: {account_id})")
                
                # ترحيل رصيد الحساب إذا كان موجوداً
                if 'current_balance' in account and account['current_balance'] != 0:
                    # الحصول على ID العملة الافتراضية (SAR)
                    mysql_cursor.execute("SELECT id FROM currencies WHERE code = 'SAR'")
                    currency_result = mysql_cursor.fetchone()
                    if currency_result:
                        currency_id = currency_result[0]
                        balance_query = """
                            INSERT INTO account_balances (account_id, currency_id, balance)
                            VALUES (%s, %s, %s)
                        """
                        mysql_cursor.execute(balance_query, (account_id, currency_id, account['current_balance']))
                        print(f"✅ تم ترحيل رصيد الحساب: {account['current_balance']} SAR")
            else:
                print(f"⚠️ الحساب موجود مسبقاً: {account['name']}")
        
        mysql_conn.commit()
        
        sqlite_cursor.close()
        sqlite_conn.close()
        mysql_cursor.close()
        mysql_conn.close()
        
        print("✅ تم ترحيل البيانات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ترحيل البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔄 بدء عملية توحيد قاعدة البيانات")
    print("=" * 50)
    
    # 1. إنشاء نسخة احتياطية من MySQL
    backup_file = create_mysql_backup()
    if not backup_file:
        print("❌ فشل في إنشاء النسخة الاحتياطية - توقف العملية")
        return
    
    # 2. التأكد من مخطط MySQL
    if not ensure_mysql_schema():
        print("❌ فشل في التحقق من مخطط MySQL - توقف العملية")
        return
    
    # 3. ترحيل البيانات
    if migrate_sqlite_to_mysql():
        print("\n🎉 تم توحيد قاعدة البيانات بنجاح!")
        print(f"💾 النسخة الاحتياطية محفوظة في: {backup_file}")
    else:
        print("\n❌ فشل في توحيد قاعدة البيانات")

if __name__ == "__main__":
    main()
