#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل لميزة إدارة المستخدمين بعد الإصلاح
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def run_comprehensive_test():
    """تشغيل اختبار شامل لميزة إدارة المستخدمين"""
    print("🎯 اختبار نهائي شامل لميزة إدارة المستخدمين")
    print("=" * 60)
    
    test_results = {
        'database_connection': False,
        'users_exist': False,
        'admin2_exists': False,
        'login_works': False,
        'permissions_work': False,
        'user_model_works': False,
        'ui_components_work': False,
        'load_users_simulation': False
    }
    
    # 1. اختبار الاتصال بقاعدة البيانات
    print("\n1️⃣ اختبار الاتصال بقاعدة البيانات...")
    try:
        from database.connection import db
        if db.is_connected() or db.connect():
            print("✅ الاتصال بقاعدة البيانات ناجح")
            test_results['database_connection'] = True
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return test_results
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return test_results
    
    # 2. فحص المستخدمين الموجودين
    print("\n2️⃣ فحص المستخدمين الموجودين...")
    try:
        users = db.execute_query("SELECT username, full_name, role, is_active FROM users")
        print(f"📊 عدد المستخدمين: {len(users)}")
        
        if len(users) > 0:
            test_results['users_exist'] = True
            print("✅ يوجد مستخدمين في النظام")
            
            for user in users:
                status = "نشط" if user['is_active'] else "معطل"
                print(f"   - {user['username']} ({user['full_name']}) - {user['role']} - {status}")
                
                if user['username'] == 'admin2':
                    test_results['admin2_exists'] = True
                    print("✅ المستخدم admin2 موجود")
        else:
            print("❌ لا يوجد مستخدمين في النظام")
            
    except Exception as e:
        print(f"❌ خطأ في فحص المستخدمين: {e}")
    
    # 3. إنشاء admin2 إذا لم يكن موجوداً
    if not test_results['admin2_exists']:
        print("\n🔧 إنشاء المستخدم admin2...")
        try:
            import bcrypt
            from datetime import datetime
            
            password_hash = bcrypt.hashpw("123456".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            insert_query = """
                INSERT INTO users (username, password_hash, full_name, role, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            
            result = db.execute_insert(insert_query, (
                'admin2', password_hash, 'المدير الجديد', 'admin', True, datetime.now()
            ))
            
            if result > 0:
                print("✅ تم إنشاء المستخدم admin2 بنجاح")
                test_results['admin2_exists'] = True
            else:
                print("❌ فشل في إنشاء المستخدم admin2")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء admin2: {e}")
    
    # 4. اختبار تسجيل الدخول
    print("\n3️⃣ اختبار تسجيل الدخول...")
    try:
        from utils.auth import auth_manager
        
        # جرب admin2 أولاً
        success, message = auth_manager.login("admin2", "123456")
        if success:
            print("✅ تسجيل الدخول بـ admin2 ناجح")
            test_results['login_works'] = True
            current_user = "admin2"
        else:
            print(f"⚠️ فشل تسجيل الدخول بـ admin2: {message}")
            
            # جرب admin
            success, message = auth_manager.login("admin", "123456")
            if success:
                print("✅ تسجيل الدخول بـ admin ناجح")
                test_results['login_works'] = True
                current_user = "admin"
            else:
                print(f"❌ فشل تسجيل الدخول بـ admin: {message}")
                current_user = None
        
        # 5. اختبار الصلاحيات
        if test_results['login_works']:
            print("\n4️⃣ اختبار الصلاحيات...")
            if auth_manager.can_manage_users():
                print("✅ صلاحيات إدارة المستخدمين متاحة")
                test_results['permissions_work'] = True
            else:
                print("❌ صلاحيات إدارة المستخدمين غير متاحة")
            
            auth_manager.logout()
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
    
    # 6. اختبار User.get_all()
    print("\n5️⃣ اختبار User.get_all()...")
    try:
        from database.models import User
        users = User.get_all()
        
        if users and len(users) > 0:
            print(f"✅ User.get_all() تعمل بشكل صحيح - {len(users)} مستخدم")
            test_results['user_model_works'] = True
            
            for user in users:
                print(f"   - {user['username']} ({user['full_name']})")
        else:
            print("❌ User.get_all() لا تعمل بشكل صحيح")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار User.get_all(): {e}")
    
    # 7. اختبار مكونات واجهة المستخدم
    print("\n6️⃣ اختبار مكونات واجهة المستخدم...")
    try:
        from config.colors import COLORS, BUTTON_STYLES
        from config.fonts import create_rtl_label, create_rtl_button
        from gui.user_management_windows import AddUserWindow, EditUserWindow, ResetPasswordWindow
        print("✅ جميع مكونات واجهة المستخدم متاحة")
        test_results['ui_components_work'] = True
    except Exception as e:
        print(f"❌ خطأ في مكونات واجهة المستخدم: {e}")
    
    # 8. محاكاة دالة load_users_list المحسنة
    print("\n7️⃣ محاكاة دالة load_users_list المحسنة...")
    try:
        from database.models import User
        
        print("   🔄 بدء تحميل قائمة المستخدمين...")
        print("   📊 جلب المستخدمين من قاعدة البيانات...")
        
        users = User.get_all()
        print(f"   📋 تم جلب {len(users)} مستخدم")
        
        if users:
            print("   🎨 إنشاء بطاقات المستخدمين...")
            for i, user in enumerate(users):
                username = user.get('username', 'غير محدد')
                fullname = user.get('full_name', 'غير محدد')
                role = user.get('role', 'user')
                is_active = user.get('is_active', True)
                
                print(f"      📋 بطاقة {i+1}: {username}")
                print(f"         - الاسم الكامل: {fullname}")
                print(f"         - الدور: {'مدير' if role == 'admin' else 'مستخدم'}")
                print(f"         - الحالة: {'نشط' if is_active else 'معطل'}")
            
            print("   ✅ تم تحميل قائمة المستخدمين بنجاح")
            test_results['load_users_simulation'] = True
        else:
            print("   ❌ لا توجد مستخدمين للعرض")
            
    except Exception as e:
        print(f"❌ خطأ في محاكاة load_users_list: {e}")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار النهائي:")
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    print(f"\n🎯 الاختبارات الناجحة: {passed_tests}/{total_tests}")
    
    for test_name, result in test_results.items():
        status = "✅" if result else "❌"
        test_display_name = {
            'database_connection': 'الاتصال بقاعدة البيانات',
            'users_exist': 'وجود المستخدمين',
            'admin2_exists': 'وجود المستخدم admin2',
            'login_works': 'تسجيل الدخول',
            'permissions_work': 'الصلاحيات',
            'user_model_works': 'نموذج المستخدم',
            'ui_components_work': 'مكونات واجهة المستخدم',
            'load_users_simulation': 'محاكاة تحميل المستخدمين'
        }
        print(f"   {status} {test_display_name.get(test_name, test_name)}")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! ميزة إدارة المستخدمين تعمل بشكل مثالي!")
        
        print(f"\n🚀 تعليمات الاستخدام:")
        print(f"1. شغل التطبيق: python main.py")
        print(f"2. سجل الدخول باستخدام: {current_user if test_results['login_works'] else 'admin2'} / 123456")
        print(f"3. انقر على '👥 إدارة المستخدمين' في الشريط الجانبي")
        print(f"4. يجب أن تظهر {len(users) if test_results['user_model_works'] else 'جميع'} المستخدمين في بطاقات منظمة")
        
        print(f"\n✨ الميزات المتاحة:")
        print(f"   • عرض جميع المستخدمين في بطاقات منظمة")
        print(f"   • إضافة مستخدم جديد")
        print(f"   • تعديل بيانات المستخدم (اسم المستخدم، الاسم الكامل، الدور)")
        print(f"   • تغيير كلمة المرور")
        print(f"   • تفعيل/تعطيل المستخدمين")
        print(f"   • إعادة تعيين كلمة المرور")
        
        return True
    else:
        print(f"\n⚠️ {total_tests - passed_tests} اختبارات فشلت. راجع الأخطاء أعلاه.")
        print(f"\n🔧 اقتراحات الإصلاح:")
        
        if not test_results['database_connection']:
            print("   - تحقق من إعدادات قاعدة البيانات")
            print("   - تأكد من تشغيل MySQL Server")
        
        if not test_results['login_works']:
            print("   - تحقق من كلمات المرور")
            print("   - شغل سكريبت إنشاء المستخدمين")
        
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    
    if success:
        print("\n🎊 تم إصلاح ميزة إدارة المستخدمين بنجاح!")
        print("يمكنك الآن استخدام التطبيق بثقة تامة.")
    else:
        print("\n🔧 يرجى مراجعة الأخطاء وتطبيق الإصلاحات المقترحة.")
        print("يمكنك تشغيل سكريبت الإصلاح الشامل: python fix_user_management_complete.py")
