#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي للتحسينات على نافذة تعديل المستخدم
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_all_enhancements():
    """اختبار جميع التحسينات"""
    print("🎯 اختبار نهائي للتحسينات على نافذة تعديل المستخدم")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 8
    
    # 1. اختبار استيراد الوحدات
    print("\n1. اختبار استيراد الوحدات...")
    try:
        from gui.user_management_windows import EditUserWindow
        from utils.auth import auth_manager
        from database.models import User
        import re
        print("✅ تم استيراد جميع الوحدات بنجاح")
        tests_passed += 1
    except Exception as e:
        print(f"❌ فشل استيراد الوحدات: {e}")
    
    # 2. اختبار تسجيل الدخول
    print("\n2. اختبار تسجيل الدخول...")
    try:
        success, message = auth_manager.login("admin2", "123456")
        if success:
            print("✅ تم تسجيل الدخول بنجاح")
            tests_passed += 1
        else:
            print(f"❌ فشل تسجيل الدخول: {message}")
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
    
    # 3. اختبار regex للتحقق من اسم المستخدم
    print("\n3. اختبار regex للتحقق من اسم المستخدم...")
    try:
        valid_usernames = ["user123", "admin_user", "test_123"]
        invalid_usernames = ["user-123", "user@name", "user name"]
        
        all_valid = all(re.match("^[a-zA-Z0-9_]+$", name) for name in valid_usernames)
        all_invalid = all(not re.match("^[a-zA-Z0-9_]+$", name) for name in invalid_usernames)
        
        if all_valid and all_invalid:
            print("✅ regex للتحقق من اسم المستخدم يعمل بشكل صحيح")
            tests_passed += 1
        else:
            print("❌ regex للتحقق من اسم المستخدم لا يعمل بشكل صحيح")
    except Exception as e:
        print(f"❌ خطأ في اختبار regex: {e}")
    
    # 4. اختبار دالة update_user
    print("\n4. اختبار دالة update_user...")
    try:
        user = User.get_by_username("admin2")
        if user:
            # اختبار تحديث الاسم الكامل
            success, message = User.update_user(user['id'], full_name="اختبار تحديث")
            if success:
                # إعادة الاسم الأصلي
                User.update_user(user['id'], full_name="المدير الجديد")
                print("✅ دالة update_user تعمل بشكل صحيح")
                tests_passed += 1
            else:
                print(f"❌ فشل في دالة update_user: {message}")
        else:
            print("❌ لم يتم العثور على المستخدم")
    except Exception as e:
        print(f"❌ خطأ في اختبار update_user: {e}")
    
    # 5. اختبار دالة reset_user_password
    print("\n5. اختبار دالة reset_user_password...")
    try:
        user = User.get_by_username("admin2")
        if user:
            # تغيير كلمة المرور ثم إعادتها
            success1, message1 = auth_manager.reset_user_password(user['id'], "test123")
            success2, message2 = auth_manager.reset_user_password(user['id'], "123456")
            
            if success1 and success2:
                print("✅ دالة reset_user_password تعمل بشكل صحيح")
                tests_passed += 1
            else:
                print(f"❌ فشل في دالة reset_user_password: {message1}, {message2}")
        else:
            print("❌ لم يتم العثور على المستخدم")
    except Exception as e:
        print(f"❌ خطأ في اختبار reset_user_password: {e}")
    
    # 6. اختبار إنشاء نافذة EditUserWindow
    print("\n6. اختبار إنشاء نافذة EditUserWindow...")
    try:
        import customtkinter as ctk
        
        # إنشاء نافذة مؤقتة
        test_window = ctk.CTk()
        test_window.withdraw()
        
        # الحصول على بيانات المستخدم
        user_data = User.get_by_username("admin2")
        
        # إنشاء نافذة التعديل
        edit_window = EditUserWindow(test_window, user_data)
        edit_window.window.withdraw()
        
        # التحقق من الحقول الجديدة
        required_attributes = [
            'username_entry',
            'new_password_entry', 
            'confirm_new_password_entry',
            'fullname_entry',
            'role_var',
            'status_var'
        ]
        
        all_present = all(hasattr(edit_window, attr) for attr in required_attributes)
        
        # إغلاق النوافذ
        edit_window.window.destroy()
        test_window.destroy()
        
        if all_present:
            print("✅ نافذة EditUserWindow تحتوي على جميع الحقول المطلوبة")
            tests_passed += 1
        else:
            print("❌ نافذة EditUserWindow تفتقر لبعض الحقول")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء نافذة EditUserWindow: {e}")
    
    # 7. اختبار دالة validate_data (محاكاة)
    print("\n7. اختبار دالة validate_data...")
    try:
        # محاكاة التحقق من البيانات
        def mock_validate(username, fullname, password, confirm_password):
            # التحقق من اسم المستخدم
            if not username or len(username) < 3:
                return False, "اسم المستخدم قصير"
            if not re.match("^[a-zA-Z0-9_]+$", username):
                return False, "اسم المستخدم يحتوي على أحرف غير مسموحة"
            
            # التحقق من الاسم الكامل
            if not fullname or len(fullname) < 2:
                return False, "الاسم الكامل قصير"
            
            # التحقق من كلمة المرور
            if password or confirm_password:
                if password != confirm_password:
                    return False, "كلمة المرور غير متطابقة"
            
            return True, "صحيح"
        
        # اختبار حالات مختلفة
        test_cases = [
            ("user123", "اسم كامل", "", "", True),
            ("user123", "اسم كامل", "pass123", "pass123", True),
            ("us", "اسم كامل", "", "", False),  # اسم مستخدم قصير
            ("user-123", "اسم كامل", "", "", False),  # أحرف غير مسموحة
            ("user123", "ا", "", "", False),  # اسم كامل قصير
            ("user123", "اسم كامل", "pass123", "pass456", False),  # كلمة مرور غير متطابقة
        ]
        
        all_correct = True
        for username, fullname, password, confirm_password, expected in test_cases:
            result, message = mock_validate(username, fullname, password, confirm_password)
            if result != expected:
                all_correct = False
                break
        
        if all_correct:
            print("✅ دالة validate_data تعمل بشكل صحيح")
            tests_passed += 1
        else:
            print("❌ دالة validate_data لا تعمل بشكل صحيح")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار validate_data: {e}")
    
    # 8. اختبار التحقق من الصلاحيات
    print("\n8. اختبار التحقق من الصلاحيات...")
    try:
        if auth_manager.can_manage_users():
            print("✅ التحقق من الصلاحيات يعمل بشكل صحيح")
            tests_passed += 1
        else:
            print("❌ المستخدم لا يمكنه إدارة المستخدمين")
    except Exception as e:
        print(f"❌ خطأ في اختبار الصلاحيات: {e}")
    
    # النتائج النهائية
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار النهائي: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 جميع التحسينات تعمل بشكل مثالي!")
        print("\n✅ الميزات المحسنة:")
        print("  • إمكانية تعديل اسم المستخدم")
        print("  • إمكانية تغيير كلمة المرور (اختياري)")
        print("  • تحسين واجهة المستخدم مع إطار قابل للتمرير")
        print("  • تحسين دوال التحقق من البيانات")
        print("  • إضافة مربعات حوار التأكيد")
        print("  • تحسين إجراءات الأمان")
        
        print("\n🚀 للاستخدام:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول: admin2 / 123456")
        print("3. اذهب إلى إدارة المستخدمين")
        print("4. انقر على 'تعديل' لأي مستخدم")
        
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    test_all_enhancements()
