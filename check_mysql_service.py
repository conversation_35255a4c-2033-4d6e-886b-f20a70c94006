#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص وبدء تشغيل خدمة MySQL
"""

import subprocess
import time
import mysql.connector
import os

def check_mysql_service():
    """فحص حالة خدمة MySQL"""
    print("🔍 فحص حالة خدمة MySQL...")
    
    try:
        # فحص الخدمات النشطة
        result = subprocess.run(['net', 'start'], capture_output=True, text=True, shell=True)
        
        if result.returncode == 0:
            services = result.stdout
            mysql_services = [line.strip() for line in services.split('\n') if 'mysql' in line.lower()]
            
            if mysql_services:
                print("✅ خدمة MySQL نشطة:")
                for service in mysql_services:
                    print(f"   - {service}")
                return True
            else:
                print("❌ خدمة MySQL غير نشطة")
                return False
        else:
            print("❌ فشل في فحص الخدمات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص خدمة MySQL: {e}")
        return False

def start_mysql_service():
    """بدء تشغيل خدمة MySQL"""
    print("🚀 محاولة بدء تشغيل خدمة MySQL...")
    
    mysql_service_names = ['MySQL80', 'MySQL', 'mysqld', 'mysql']
    
    for service_name in mysql_service_names:
        try:
            print(f"🔄 محاولة بدء {service_name}...")
            result = subprocess.run(['net', 'start', service_name], 
                                  capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                print(f"✅ تم بدء تشغيل {service_name} بنجاح")
                time.sleep(3)  # انتظار لبدء الخدمة
                return True
            else:
                print(f"❌ فشل في بدء {service_name}: {result.stderr}")
                
        except Exception as e:
            print(f"❌ خطأ في بدء {service_name}: {e}")
    
    print("❌ فشل في بدء تشغيل خدمة MySQL")
    return False

def check_xampp_mysql():
    """فحص MySQL في XAMPP"""
    print("🔍 فحص MySQL في XAMPP...")
    
    xampp_paths = [
        r"C:\xampp\mysql\bin\mysqld.exe",
        r"C:\Program Files\XAMPP\mysql\bin\mysqld.exe",
        r"D:\xampp\mysql\bin\mysqld.exe"
    ]
    
    for path in xampp_paths:
        if os.path.exists(path):
            print(f"✅ تم العثور على MySQL في XAMPP: {path}")
            
            # محاولة بدء تشغيل XAMPP MySQL
            try:
                xampp_control = path.replace(r"\mysql\bin\mysqld.exe", r"\xampp-control.exe")
                if os.path.exists(xampp_control):
                    print("🚀 محاولة بدء تشغيل XAMPP...")
                    subprocess.Popen([xampp_control])
                    time.sleep(5)
                    return True
            except Exception as e:
                print(f"❌ خطأ في بدء XAMPP: {e}")
    
    print("❌ لم يتم العثور على XAMPP MySQL")
    return False

def test_mysql_connection():
    """اختبار الاتصال بـ MySQL"""
    print("🔗 اختبار الاتصال بـ MySQL...")
    
    configs = [
        {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager'
        },
        {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '',
            'database': 'money_manager'
        },
        {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager'
        }
    ]
    
    for i, config in enumerate(configs, 1):
        try:
            print(f"🔄 محاولة الاتصال {i}...")
            connection = mysql.connector.connect(**config)
            
            if connection.is_connected():
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                connection.close()
                
                print(f"✅ نجح الاتصال بـ MySQL!")
                print(f"   المضيف: {config['host']}:{config['port']}")
                print(f"   المستخدم: {config['user']}")
                print(f"   قاعدة البيانات: {config['database']}")
                
                # حفظ التكوين الناجح
                save_working_config(config)
                return True
                
        except mysql.connector.Error as e:
            print(f"❌ فشل الاتصال {i}: {e}")
        except Exception as e:
            print(f"❌ خطأ في الاتصال {i}: {e}")
    
    print("❌ فشل في جميع محاولات الاتصال")
    return False

def save_working_config(config):
    """حفظ التكوين الناجح"""
    try:
        import json
        
        config_file = "config/database_settings.json"
        
        # إنشاء مجلد config إذا لم يكن موجوداً
        os.makedirs("config", exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        
        print(f"✅ تم حفظ التكوين الناجح في: {config_file}")
        
    except Exception as e:
        print(f"❌ خطأ في حفظ التكوين: {e}")

def create_database_if_not_exists():
    """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
    print("🗄️ التحقق من وجود قاعدة البيانات...")
    
    try:
        # الاتصال بدون تحديد قاعدة بيانات
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # إنشاء قاعدة البيانات
        cursor.execute("CREATE DATABASE IF NOT EXISTS money_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ تم التأكد من وجود قاعدة البيانات")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 فحص وإعداد خدمة MySQL")
    print("=" * 40)
    
    # 1. فحص حالة الخدمة
    if check_mysql_service():
        print("✅ خدمة MySQL نشطة")
    else:
        # 2. محاولة بدء الخدمة
        if not start_mysql_service():
            # 3. فحص XAMPP
            if not check_xampp_mysql():
                print("❌ فشل في بدء تشغيل MySQL")
                print("💡 يرجى بدء تشغيل XAMPP أو MySQL يدوياً")
                return False
    
    # 4. اختبار الاتصال
    if test_mysql_connection():
        # 5. إنشاء قاعدة البيانات
        if create_database_if_not_exists():
            print("\n🎉 MySQL جاهز للاستخدام!")
            return True
    
    print("\n❌ فشل في إعداد MySQL")
    return False

if __name__ == "__main__":
    main()
