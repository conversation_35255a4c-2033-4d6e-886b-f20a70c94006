import mysql.connector

try:
    connection = mysql.connector.connect(
        host='localhost',
        user='root',
        password='mohdam',
        database='money_manager'
    )
    cursor = connection.cursor()
    
    print("✅ متصل بقاعدة البيانات")
    
    # فحص هيكل جدول transactions
    cursor.execute("DESCRIBE transactions")
    columns = cursor.fetchall()
    
    print("📊 هيكل جدول transactions:")
    for col in columns:
        print(f"   - {col[0]}: {col[1]} {col[2]} {col[3]} {col[4]} {col[5]}")
    
    cursor.close()
    connection.close()
    
except Exception as e:
    print(f"❌ خطأ: {e}")
