#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملفات Excel محسنة للنصوص العربية
"""

import pandas as pd
import os
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows

def create_arabic_friendly_excel():
    """إنشاء ملفات Excel محسنة للنصوص العربية"""
    
    print("🚀 إنشاء ملفات Excel محسنة للنصوص العربية...")
    
    # إنشاء مجلد للملفات المحسنة
    output_dir = 'arabic_friendly_excel'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # بيانات نموذجية للواردات (محسنة)
    income_data = [
        {
            'المبلغ': 5000.00,
            'الحساب': 'المحفظة النقدية',
            'العملة': 'SAR',
            'التاريخ': '2024-01-15',
            'الوصف': 'راتب شهر يناير'
        },
        {
            'المبلغ': 1500.50,
            'الحساب': 'معاذ',
            'العملة': 'SAR',
            'التاريخ': '2024-01-20',
            'الوصف': 'مكافأة إضافية'
        },
        {
            'المبلغ': 800.00,
            'الحساب': 'محفظة متعددة العملات',
            'العملة': 'USD',
            'التاريخ': '2024-01-25',
            'الوصف': 'عمولة مشروع'
        }
    ]
    
    # بيانات نموذجية للمصروفات (محسنة)
    expense_data = [
        {
            'المبلغ': 1200.00,
            'الحساب': 'المحفظة النقدية',
            'العملة': 'SAR',
            'التاريخ': '2024-01-16',
            'الوصف': 'إيجار المنزل'
        },
        {
            'المبلغ': 450.75,
            'الحساب': 'البشرية',
            'العملة': 'SAR',
            'التاريخ': '2024-01-18',
            'الوصف': 'فاتورة الكهرباء'
        }
    ]
    
    # 1. إنشاء ملف بالطريقة الأساسية المحسنة
    try:
        df_income = pd.DataFrame(income_data)
        basic_file = os.path.join(output_dir, 'واردات_محسن_أساسي.xlsx')
        
        # استخدام ExcelWriter مع خيارات محسنة
        with pd.ExcelWriter(basic_file, engine='openpyxl', options={'strings_to_urls': False}) as writer:
            df_income.to_excel(writer, sheet_name='الواردات', index=False)
        
        print(f"✅ تم إنشاء الملف الأساسي المحسن: {basic_file}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف الأساسي: {e}")
    
    # 2. إنشاء ملف بالطريقة المتقدمة مع تنسيق
    try:
        advanced_file = os.path.join(output_dir, 'واردات_محسن_متقدم.xlsx')
        
        # إنشاء workbook جديد
        wb = Workbook()
        ws = wb.active
        ws.title = "الواردات"
        
        # إضافة البيانات
        df_income = pd.DataFrame(income_data)
        for r in dataframe_to_rows(df_income, index=False, header=True):
            ws.append(r)
        
        # تنسيق الخط للعربية
        arabic_font = Font(name='Arial Unicode MS', size=12)
        header_font = Font(name='Arial Unicode MS', size=14, bold=True)
        
        # تطبيق التنسيق على الرؤوس
        for cell in ws[1]:
            cell.font = header_font
            cell.alignment = Alignment(horizontal='center')
        
        # تطبيق التنسيق على البيانات
        for row in ws.iter_rows(min_row=2):
            for cell in row:
                cell.font = arabic_font
                if isinstance(cell.value, str):
                    cell.alignment = Alignment(horizontal='right')
                else:
                    cell.alignment = Alignment(horizontal='center')
        
        # ضبط عرض الأعمدة
        column_widths = [15, 25, 10, 15, 30]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[ws.cell(row=1, column=i).column_letter].width = width
        
        # حفظ الملف
        wb.save(advanced_file)
        print(f"✅ تم إنشاء الملف المتقدم المحسن: {advanced_file}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف المتقدم: {e}")
    
    # 3. إنشاء ملف للمصروفات
    try:
        df_expense = pd.DataFrame(expense_data)
        expense_file = os.path.join(output_dir, 'مصروفات_محسن.xlsx')
        
        with pd.ExcelWriter(expense_file, engine='openpyxl', options={'strings_to_urls': False}) as writer:
            df_expense.to_excel(writer, sheet_name='المصروفات', index=False)
        
        print(f"✅ تم إنشاء ملف المصروفات المحسن: {expense_file}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف المصروفات: {e}")
    
    # 4. إنشاء ملف قالب فارغ
    try:
        template_data = [
            {
                'المبلغ': 'أدخل المبلغ هنا',
                'الحساب': 'اختر من الحسابات المتاحة',
                'العملة': 'SAR أو USD أو AED أو YER',
                'التاريخ': 'YYYY-MM-DD',
                'الوصف': 'وصف المعاملة'
            }
        ]
        
        df_template = pd.DataFrame(template_data)
        template_file = os.path.join(output_dir, 'قالب_فارغ.xlsx')
        
        with pd.ExcelWriter(template_file, engine='openpyxl', options={'strings_to_urls': False}) as writer:
            df_template.to_excel(writer, sheet_name='القالب', index=False)
        
        print(f"✅ تم إنشاء القالب الفارغ: {template_file}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء القالب: {e}")
    
    # 5. اختبار قراءة الملفات المنشأة
    print(f"\n📖 اختبار قراءة الملفات المنشأة:")
    
    test_files = [
        'واردات_محسن_أساسي.xlsx',
        'واردات_محسن_متقدم.xlsx',
        'مصروفات_محسن.xlsx'
    ]
    
    for filename in test_files:
        filepath = os.path.join(output_dir, filename)
        if os.path.exists(filepath):
            try:
                df_test = pd.read_excel(filepath, engine='openpyxl')
                print(f"✅ {filename}: {len(df_test)} صف، أعمدة: {list(df_test.columns)}")
                
                # فحص عينة من البيانات
                if not df_test.empty:
                    first_row = df_test.iloc[0]
                    account = first_row.get('الحساب', 'غير محدد')
                    amount = first_row.get('المبلغ', 0)
                    print(f"   عينة: {account} - {amount}")
                    
            except Exception as e:
                print(f"❌ خطأ في قراءة {filename}: {e}")
        else:
            print(f"⚠️ الملف غير موجود: {filename}")
    
    print(f"\n📁 جميع الملفات في المجلد: {output_dir}")
    print("\n💡 نصائح لاستخدام الملفات:")
    print("1. استخدم الملفات المحسنة بدلاً من الملفات العادية")
    print("2. احفظ ملفاتك بصيغة .xlsx (وليس .xls)")
    print("3. تأكد من أن برنامج Excel يدعم UTF-8")
    print("4. استخدم خط Arial Unicode MS للنصوص العربية")
    
    print("\n✨ تم الانتهاء من إنشاء الملفات المحسنة!")

if __name__ == "__main__":
    create_arabic_friendly_excel()
