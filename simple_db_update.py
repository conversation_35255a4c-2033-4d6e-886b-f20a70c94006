#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث مبسط لقاعدة البيانات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def update_db_simple():
    """تحديث قاعدة البيانات بطريقة مبسطة"""
    try:
        from database.connection import db
        
        print("🔧 تحديث قاعدة البيانات...")
        
        # التحقق من الاتصال
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return False
        
        print("✅ الاتصال بقاعدة البيانات نشط")
        
        # إضافة عمود category_name إلى جدول transactions
        try:
            print("➕ إضافة عمود category_name...")
            db.execute_update("""
                ALTER TABLE transactions 
                ADD COLUMN category_name VARCHAR(100) NULL
            """)
            print("✅ تم إضافة عمود category_name")
        except Exception as e:
            if "Duplicate column name" in str(e):
                print("✅ عمود category_name موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إضافة عمود category_name: {e}")
        
        # إضافة عمود account_type_name إلى جدول accounts
        try:
            print("➕ إضافة عمود account_type_name...")
            db.execute_update("""
                ALTER TABLE accounts 
                ADD COLUMN account_type_name VARCHAR(100) NULL
            """)
            print("✅ تم إضافة عمود account_type_name")
        except Exception as e:
            if "Duplicate column name" in str(e):
                print("✅ عمود account_type_name موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إضافة عمود account_type_name: {e}")
        
        print("\n🎉 تم تحديث قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 تحديث قاعدة البيانات")
    print("=" * 30)
    
    if update_db_simple():
        print("\n✅ تم التحديث بنجاح!")
        print("\n💡 الآن يمكنك:")
        print("   • إدخال التصنيفات يدوياً")
        print("   • إدخال أنواع الحسابات يدوياً")
    else:
        print("\n❌ فشل في التحديث")
    
    print("\n🏁 انتهى")
