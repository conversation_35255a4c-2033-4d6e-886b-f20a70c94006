{"sqlite_data": {"users": [{"id": 1, "username": "admin", "email": null, "password_hash": "$2b$12$afmkUyzP2676PFmk311T7O.rKpsWQYzVZBe7/0BlXBoyI.Zy9tb.e", "full_name": "المدير العام", "role": "admin", "is_active": 1, "created_at": "2025-06-28 12:43:57", "updated_at": "2025-06-28 12:43:57", "last_login": null, "profile_image": null}, {"id": 2, "username": "user", "email": null, "password_hash": "$2b$12$afmkUyzP2676PFmk311T7O.rKpsWQYzVZBe7/0BlXBoyI.Zy9tb.e", "full_name": "مستخدم تجريبي", "role": "user", "is_active": 1, "created_at": "2025-06-28 12:43:57", "updated_at": "2025-06-28 12:43:57", "last_login": null, "profile_image": null}], "currencies": [{"id": 10, "code": "SAR", "name": "ريال سعودي", "symbol": "ر.س", "is_active": 1, "exchange_rate": 1.0, "created_at": "2025-07-15 03:57:54"}, {"id": 11, "code": "YER", "name": "ريال يمني", "symbol": "ر.ي", "is_active": 1, "exchange_rate": 1.0, "created_at": "2025-07-15 03:57:54"}, {"id": 12, "code": "AED", "name": "درهم إماراتي", "symbol": "د.إ", "is_active": 1, "exchange_rate": 1.0, "created_at": "2025-07-15 03:57:54"}, {"id": 13, "code": "USD", "name": "دولار أمريكي", "symbol": "$", "is_active": 1, "exchange_rate": 1.0, "created_at": "2025-07-15 03:57:54"}], "accounts": [{"id": 1, "user_id": 1, "name": "الصندوق النقدي", "account_type_id": 1, "description": "النقد المتوفر في اليد", "is_active": 1, "created_at": "2025-06-28 12:43:57", "updated_at": "2025-06-28 12:43:57"}, {"id": 2, "user_id": 1, "name": "حسا<PERSON> البنك", "account_type_id": 2, "description": "الحساب الجاري في البنك", "is_active": 1, "created_at": "2025-06-28 12:43:57", "updated_at": "2025-06-28 12:43:57"}, {"id": 3, "user_id": 1, "name": "محفظة إلكترونية", "account_type_id": 4, "description": "محفظة رقمية بالدولار", "is_active": 1, "created_at": "2025-06-28 12:43:57", "updated_at": "2025-06-28 12:43:57"}], "account_balances": [{"id": 1, "account_id": 1, "currency_id": 1, "balance": 5000.0, "created_at": "2025-07-15 03:55:12", "updated_at": "2025-07-15 03:55:12"}, {"id": 2, "account_id": 2, "currency_id": 1, "balance": 25000.0, "created_at": "2025-07-15 03:55:12", "updated_at": "2025-07-15 03:55:12"}, {"id": 3, "account_id": 3, "currency_id": 2, "balance": 500.0, "created_at": "2025-07-15 03:55:12", "updated_at": "2025-07-15 03:55:12"}], "transactions": [{"id": 1, "user_id": 1, "account_id": 1, "type": "income", "amount": 8000.0, "currency_id": 1, "category_id": 1, "description": "راتب شهر ديسمبر", "transaction_date": "2024-12-20", "created_at": "2025-06-28 12:43:57", "updated_at": "2025-06-28 12:43:57", "reference_number": null, "notes": null, "is_recurring": 0, "recurring_frequency": null}, {"id": 2, "user_id": 1, "account_id": 1, "type": "expense", "amount": 300.0, "currency_id": 1, "category_id": 1, "description": "تسوق من السوبر ماركت", "transaction_date": "2024-12-25", "created_at": "2025-06-28 12:43:57", "updated_at": "2025-06-28 12:43:57", "reference_number": null, "notes": null, "is_recurring": 0, "recurring_frequency": null}, {"id": 3, "user_id": 1, "account_id": 2, "type": "expense", "amount": 1200.0, "currency_id": 1, "category_id": 3, "description": "إيجار الشقة", "transaction_date": "2024-12-01", "created_at": "2025-06-28 12:43:57", "updated_at": "2025-06-28 12:43:57", "reference_number": null, "notes": null, "is_recurring": 0, "recurring_frequency": null}, {"id": 4, "user_id": 1, "account_id": 1, "type": "expense", "amount": 150.0, "currency_id": 1, "category_id": 2, "description": "وقود السيارة", "transaction_date": "2024-12-24", "created_at": "2025-06-28 12:43:57", "updated_at": "2025-06-28 12:43:57", "reference_number": null, "notes": null, "is_recurring": 0, "recurring_frequency": null}]}, "json_data": {"users": {"admin": {"password_hash": "$2b$12$hokgAU1FOQkM1fiiwIZ23.0XfK/3ffVRwOUUL59pPdHXqqt8Oei5O", "full_name": "المدير العام", "role": "admin", "is_active": true, "created_at": "2025-06-28T16:20:06.217676", "last_login": "2025-06-28T16:58:08.197052"}, "user": {"password_hash": "$2b$12$hokgAU1FOQkM1fiiwIZ23.0XfK/3ffVRwOUUL59pPdHXqqt8Oei5O", "full_name": "مستخدم تجريبي", "role": "user", "is_active": true, "created_at": "2025-06-28T16:20:06.217698", "last_login": null}}, "accounts": [{"id": 1, "user_id": 1, "name": "الصندوق النقدي", "type": "نقدي", "currency": "ريال سعودي", "balance": 5000.0, "description": "النقد المتوفر في اليد"}, {"id": 2, "user_id": 1, "name": "حساب الراجحي", "type": "بنكي", "currency": "ريال سعودي", "balance": 25000.0, "description": "الحساب الجاري في بنك الراجحي"}, {"id": 3, "user_id": 1, "name": "حساب التوفير", "type": "توفير", "currency": "ريال سعودي", "balance": 50000.0, "description": "حساب التوفير طويل المدى"}], "transactions": [{"id": 1, "user_id": 1, "account_id": 2, "type": "دخل", "amount": 8000.0, "category": "راتب", "description": "راتب شهر ديسمبر", "date": "2025-06-25", "created_at": "2025-06-29T16:56:06.906188"}, {"id": 2, "user_id": 1, "account_id": 1, "type": "مصروف", "amount": 300.0, "category": "طعام وشراب", "description": "تسوق من السوبر ماركت", "date": "2025-06-28", "created_at": "2025-06-29T16:56:06.906200"}, {"id": 3, "user_id": 1, "account_id": 2, "type": "مصروف", "amount": 1200.0, "category": "سكن", "description": "إيجار الشقة", "date": "2025-06-26", "created_at": "2025-06-29T16:56:06.906204"}, {"id": 4, "user_id": 1, "account_id": 1, "type": "دخل", "amount": 1500.0, "category": "أعمال", "description": "دخل من مشروع جانبي", "date": "2025-06-20", "created_at": "2025-06-29T16:56:06.906208"}]}, "analysis_timestamp": "2025-07-20"}