"""
نماذج البيانات لقاعدة بيانات إدارة الأموال (متعددة العملات)
"""

from datetime import datetime
from database.connection import db
from utils.auth import auth_manager
import logging
from decimal import Decimal

class User:
    """نموذج المستخدم - محدث لدعم إدارة المستخدمين المتعددين"""

    @staticmethod
    def get_by_id(user_id):
        """الحصول على مستخدم بواسطة ID"""
        query = "SELECT * FROM users WHERE id = %s"
        result = db.execute_query(query, (user_id,))
        return result[0] if result else None

    @staticmethod
    def get_by_username(username):
        """الحصول على مستخدم بواسطة اسم المستخدم"""
        query = "SELECT * FROM users WHERE username = %s"
        result = db.execute_query(query, (username,))
        return result[0] if result else None

    @staticmethod
    def get_all():
        """الحصول على جميع المستخدمين"""
        query = """
            SELECT u.*, creator.username as created_by_username
            FROM users u
            LEFT JOIN users creator ON u.created_by = creator.id
            ORDER BY u.created_at DESC
        """
        return db.execute_query(query)

    @staticmethod
    def get_active_users():
        """الحصول على المستخدمين النشطين فقط"""
        query = """
            SELECT u.*, creator.username as created_by_username
            FROM users u
            LEFT JOIN users creator ON u.created_by = creator.id
            WHERE u.is_active = TRUE
            ORDER BY u.created_at DESC
        """
        return db.execute_query(query)

    @staticmethod
    def create_user(username, password_hash, full_name, role='user', created_by=None):
        """إنشاء مستخدم جديد"""
        try:
            # التحقق من عدم وجود اسم المستخدم
            if User.get_by_username(username):
                return False, "اسم المستخدم موجود مسبقاً"

            query = """
                INSERT INTO users (username, password_hash, full_name, role, created_by, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            user_id = db.execute_insert(query, (
                username, password_hash, full_name, role, created_by, datetime.now()
            ))

            if user_id > 0:
                logging.info(f"تم إنشاء المستخدم {username} بنجاح (ID: {user_id})")
                return True, user_id
            else:
                return False, "خطأ في حفظ بيانات المستخدم"

        except Exception as e:
            logging.error(f"خطأ في إنشاء المستخدم: {e}")
            return False, f"خطأ في إنشاء المستخدم: {str(e)}"

    @staticmethod
    def update_user(user_id, username=None, full_name=None, role=None, is_active=None):
        """تحديث بيانات المستخدم"""
        try:
            # بناء الاستعلام ديناميكياً
            update_fields = []
            params = []

            if username is not None:
                # التحقق من عدم وجود اسم المستخدم لدى مستخدم آخر
                existing_user = User.get_by_username(username)
                if existing_user and existing_user['id'] != user_id:
                    return False, "اسم المستخدم موجود مسبقاً"
                update_fields.append("username = %s")
                params.append(username)

            if full_name is not None:
                update_fields.append("full_name = %s")
                params.append(full_name)

            if role is not None:
                update_fields.append("role = %s")
                params.append(role)

            if is_active is not None:
                update_fields.append("is_active = %s")
                params.append(is_active)

            if not update_fields:
                return False, "لا توجد بيانات للتحديث"

            # إضافة تاريخ التحديث
            update_fields.append("updated_at = %s")
            params.append(datetime.now())

            # إضافة معرف المستخدم
            params.append(user_id)

            query = f"""
                UPDATE users
                SET {', '.join(update_fields)}
                WHERE id = %s
            """

            result = db.execute_update(query, params)
            if result:
                logging.info(f"تم تحديث المستخدم {user_id} بنجاح")
                return True, "تم تحديث البيانات بنجاح"
            else:
                return False, "خطأ في تحديث البيانات"

        except Exception as e:
            logging.error(f"خطأ في تحديث المستخدم: {e}")
            return False, f"خطأ في تحديث المستخدم: {str(e)}"

    @staticmethod
    def delete_user(user_id):
        """حذف مستخدم (حذف ناعم - تعطيل الحساب)"""
        try:
            # التحقق من وجود المستخدم
            user = User.get_by_id(user_id)
            if not user:
                return False, "المستخدم غير موجود"

            # منع حذف المدير الوحيد
            admin_count = db.execute_query(
                "SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = TRUE"
            )[0]['count']

            if user['role'] == 'admin' and admin_count <= 1:
                return False, "لا يمكن حذف المدير الوحيد في النظام"

            # تعطيل الحساب بدلاً من الحذف
            query = """
                UPDATE users
                SET is_active = FALSE, updated_at = %s
                WHERE id = %s
            """
            result = db.execute_update(query, (datetime.now(), user_id))

            if result:
                logging.info(f"تم تعطيل المستخدم {user['username']} (ID: {user_id})")
                return True, "تم تعطيل المستخدم بنجاح"
            else:
                return False, "خطأ في تعطيل المستخدم"

        except Exception as e:
            logging.error(f"خطأ في حذف المستخدم: {e}")
            return False, f"خطأ في حذف المستخدم: {str(e)}"

    @staticmethod
    def delete_user_permanently(user_id):
        """حذف المستخدم نهائياً من قاعدة البيانات"""
        try:
            # التحقق من وجود المستخدم
            user = User.get_by_id(user_id)
            if not user:
                return False, "المستخدم غير موجود"

            # منع حذف المدير الوحيد
            admin_count = db.execute_query(
                "SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = TRUE"
            )[0]['count']

            if user['role'] == 'admin' and admin_count <= 1:
                return False, "لا يمكن حذف المدير الوحيد في النظام"

            # حذف المستخدم نهائياً من قاعدة البيانات
            query = "DELETE FROM users WHERE id = %s"
            result = db.execute_update(query, (user_id,))

            if result:
                logging.info(f"تم حذف المستخدم {user['username']} نهائياً (ID: {user_id})")
                return True, f"تم حذف المستخدم '{user['username']}' نهائياً من قاعدة البيانات"
            else:
                return False, "خطأ في حذف المستخدم من قاعدة البيانات"

        except Exception as e:
            logging.error(f"خطأ في الحذف النهائي للمستخدم: {e}")
            return False, f"خطأ في الحذف النهائي: {str(e)}"

    @staticmethod
    def activate_user(user_id):
        """تفعيل مستخدم"""
        try:
            query = """
                UPDATE users
                SET is_active = TRUE, updated_at = %s
                WHERE id = %s
            """
            result = db.execute_update(query, (datetime.now(), user_id))

            if result:
                logging.info(f"تم تفعيل المستخدم (ID: {user_id})")
                return True, "تم تفعيل المستخدم بنجاح"
            else:
                return False, "خطأ في تفعيل المستخدم"

        except Exception as e:
            logging.error(f"خطأ في تفعيل المستخدم: {e}")
            return False, f"خطأ في تفعيل المستخدم: {str(e)}"

    @staticmethod
    def change_password(user_id, new_password_hash):
        """تغيير كلمة مرور المستخدم"""
        try:
            query = """
                UPDATE users
                SET password_hash = %s, updated_at = %s
                WHERE id = %s
            """
            result = db.execute_update(query, (new_password_hash, datetime.now(), user_id))

            if result:
                logging.info(f"تم تغيير كلمة مرور المستخدم (ID: {user_id})")
                return True, "تم تغيير كلمة المرور بنجاح"
            else:
                return False, "خطأ في تغيير كلمة المرور"

        except Exception as e:
            logging.error(f"خطأ في تغيير كلمة المرور: {e}")
            return False, f"خطأ في تغيير كلمة المرور: {str(e)}"

    @staticmethod
    def update_last_login(user_id):
        """تحديث آخر تسجيل دخول"""
        try:
            query = "UPDATE users SET last_login = %s WHERE id = %s"
            db.execute_update(query, (datetime.now(), user_id))
        except Exception as e:
            logging.error(f"خطأ في تحديث آخر تسجيل دخول: {e}")

    @staticmethod
    def get_user_statistics():
        """إحصائيات المستخدمين"""
        try:
            query = """
                SELECT
                    COUNT(*) as total_users,
                    SUM(CASE WHEN is_active = TRUE THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_users,
                    SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as regular_users
                FROM users
            """
            result = db.execute_query(query)
            return result[0] if result else None
        except Exception as e:
            logging.error(f"خطأ في جلب إحصائيات المستخدمين: {e}")
            return None

    @staticmethod
    def update_profile(user_id, full_name):
        """تحديث ملف المستخدم (للتوافق مع الكود الحالي)"""
        return User.update_user(user_id, full_name=full_name)

class Currency:
    """نموذج العملة"""

    @staticmethod
    def get_all():
        """الحصول على جميع العملات"""
        query = "SELECT * FROM currencies WHERE is_active = TRUE ORDER BY name"
        return db.execute_query(query)

    @staticmethod
    def get_by_id(currency_id):
        """الحصول على عملة بواسطة ID"""
        query = "SELECT * FROM currencies WHERE id = %s"
        result = db.execute_query(query, (currency_id,))
        return result[0] if result else None

    @staticmethod
    def get_by_code(code):
        """الحصول على عملة بواسطة الرمز"""
        query = "SELECT * FROM currencies WHERE code = %s"
        result = db.execute_query(query, (code,))
        return result[0] if result else None

class Account:
    """نموذج الحساب (متعدد العملات)"""

    @staticmethod
    def create(user_id, name, description="", account_type_id=1):
        """إنشاء حساب جديد متعدد العملات"""
        query = """
            INSERT INTO accounts (user_id, name, account_type_id, description)
            VALUES (%s, %s, %s, %s)
        """
        account_id = db.execute_insert(query, (user_id, name, account_type_id, description))
        if account_id > 0:
            auth_manager.log_activity('create_account', 'accounts', account_id)
        return account_id

    @staticmethod
    def update_balance_for_currency(account_id, currency_id, amount_change):
        """
        تحديث رصيد عملة معينة في حساب معين.
        amount_change: يمكن أن يكون موجبًا (إضافة) أو سالبًا (خصم).
        """
        # التحقق من وجود السجل أولاً
        existing_balance = Account.get_currency_balance(account_id, currency_id)
        new_balance = float(existing_balance) + float(amount_change)

        # إدراج أو تحديث الرصيد (MySQL syntax)
        query = """
            INSERT INTO account_balances (account_id, currency_id, balance, updated_at)
            VALUES (%s, %s, %s, CURRENT_TIMESTAMP)
            ON DUPLICATE KEY UPDATE
            balance = VALUES(balance),
            updated_at = CURRENT_TIMESTAMP
        """
        return db.execute_update(query, (account_id, currency_id, new_balance))

    @staticmethod
    def get_balances(account_id):
        """الحصول على جميع أرصدة العملات لحساب معين"""
        query = """
            SELECT ab.currency_id, ab.balance, c.code, c.name, c.symbol
            FROM account_balances ab
            JOIN currencies c ON ab.currency_id = c.id
            WHERE ab.account_id = %s AND ab.balance > 0
            ORDER BY c.name
        """
        balances = db.execute_query(query, (account_id,))
        return balances if balances is not None else []

    @staticmethod
    def get_all_balances(account_id):
        """الحصول على جميع أرصدة العملات لحساب معين (حتى الصفرية)"""
        query = """
            SELECT ab.currency_id, ab.balance, c.code, c.name, c.symbol
            FROM account_balances ab
            JOIN currencies c ON ab.currency_id = c.id
            WHERE ab.account_id = %s
            ORDER BY c.name
        """
        balances = db.execute_query(query, (account_id,))
        return balances if balances is not None else []

    @staticmethod
    def get_by_user(user_id):
        """الحصول على حسابات المستخدم مع أرصدتها متعددة العملات"""
        query = """
            SELECT a.*
            FROM accounts a
            WHERE a.user_id = %s
            ORDER BY a.is_active DESC, a.created_at DESC
        """
        accounts = db.execute_query(query, (user_id,))
        if not accounts:
            return []

        # جلب الأرصدة لكل حساب
        for account in accounts:
            account['balances'] = Account.get_balances(account['id'])
            account['all_balances'] = Account.get_all_balances(account['id'])

        return accounts

    @staticmethod
    def add_currency_balance(account_id, currency_id, initial_balance=0.0):
        """إضافة عملة جديدة لحساب مع رصيد ابتدائي"""
        query = """
            INSERT INTO account_balances (account_id, currency_id, balance)
            VALUES (%s, %s, %s)
            ON DUPLICATE KEY UPDATE balance = VALUES(balance)
        """
        return db.execute_update(query, (account_id, currency_id, initial_balance))

    @staticmethod
    def get_currency_balance(account_id, currency_id):
        """الحصول على رصيد عملة معينة في حساب"""
        query = """
            SELECT balance FROM account_balances
            WHERE account_id = %s AND currency_id = %s
        """
        result = db.execute_query(query, (account_id, currency_id))
        return result[0]['balance'] if result else 0.0

    @staticmethod
    def get_by_id(account_id):
        """الحصول على حساب بواسطة ID مع أرصدته"""
        query = """
            SELECT a.*
            FROM accounts a
            WHERE a.id = %s
        """
        result = db.execute_query(query, (account_id,))
        if not result:
            return None

        account = result[0]
        account['balances'] = Account.get_balances(account_id)
        return account

    @staticmethod
    def get_total_balance_by_currency(user_id):
        """الحصول على إجمالي الرصيد المجمع لكل عملة"""
        query = """
            SELECT c.code, c.name, c.symbol, SUM(ab.balance) as total_balance
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            JOIN currencies c ON ab.currency_id = c.id
            WHERE a.user_id = %s AND a.is_active = TRUE
            GROUP BY c.id, c.code, c.name, c.symbol
            HAVING total_balance != 0
            ORDER BY total_balance DESC
        """
        balances = db.execute_query(query, (user_id,))
        return balances if balances is not None else []

    @staticmethod
    def update(account_id, name, description=None):
        """تحديث بيانات حساب موجود (بدون نوع حساب)"""
        query = """
            UPDATE accounts
            SET name = %s,
                description = %s,
                updated_at = %s
            WHERE id = %s
        """
        result = db.execute_update(query, (name, description, datetime.now(), account_id))
        if result > 0:
            auth_manager.log_activity('update_account', 'accounts', account_id)
        return result > 0

    @staticmethod
    def activate(account_id):
        """تفعيل حساب"""
        query = "UPDATE accounts SET is_active = TRUE, updated_at = %s WHERE id = %s"
        result = db.execute_update(query, (datetime.now(), account_id))
        if result > 0:
            auth_manager.log_activity('activate_account', 'accounts', account_id)
        return result > 0

    @staticmethod
    def deactivate(account_id):
        """إلغاء تفعيل حساب"""
        query = "UPDATE accounts SET is_active = FALSE, updated_at = %s WHERE id = %s"
        result = db.execute_update(query, (datetime.now(), account_id))
        if result > 0:
            auth_manager.log_activity('deactivate_account', 'accounts', account_id)
        return result > 0

    @staticmethod
    def delete(account_id):
        """حذف حساب مع التحقق من الشروط"""
        account = Account.get_by_id(account_id)
        if not account:
            return False, "الحساب غير موجود"

        # التحقق مما إذا كانت هناك أرصدة غير صفرية
        if account.get('balances'):
            if any(b.get('balance', 0) != 0 for b in account['balances']):
                return False, "لا يمكن حذف الحساب لأنه يحتوي على أرصدة غير صفرية. يرجى تصفية الأرصدة أولاً."

        # التحقق من وجود معاملات مرتبطة
        trans_query = "SELECT 1 FROM transactions WHERE account_id = %s LIMIT 1"
        if db.execute_query(trans_query, (account_id,)):
            return False, "لا يمكن حذف الحساب لوجود معاملات مرتبطة به. يمكنك إلغاء تفعيله بدلاً من ذلك."

        # التحقق من وجود تحويلات مرتبطة
        transfer_query = "SELECT 1 FROM transfers WHERE from_account_id = %s OR to_account_id = %s LIMIT 1"
        if db.execute_query(transfer_query, (account_id, account_id)):
            return False, "لا يمكن حذف الحساب لوجود تحويلات مرتبطة به. يمكنك إلغاء تفعيله بدلاً من ذلك."

        # إذا مرت كل التحققات، يمكن الحذف
        try:
            # أولاً، حذف الأرصدة (التي يجب أن تكون صفرية)
            db.execute_update("DELETE FROM account_balances WHERE account_id = %s", (account_id,))
            
            # ثانياً، حذف الحساب نفسه
            query = "DELETE FROM accounts WHERE id = %s"
            result = db.execute_update(query, (account_id,))
            
            if result > 0:
                auth_manager.log_activity('delete_account', 'accounts', account_id)
                return True, "تم حذف الحساب بنجاح"
            else:
                return False, "فشل حذف الحساب من قاعدة البيانات."
        except Exception as e:
            logging.error(f"خطأ أثناء حذف الحساب {account_id}: {e}")
            return False, f"حدث خطأ أثناء الحذف: {e}"

class Transaction:
    """نموذج المعاملة (متعدد العملات)"""

    @staticmethod
    def create(user_id, account_id, currency_id, transaction_type, amount,
               description="", transaction_date=None):
        """إنشاء معاملة جديدة وتحديث رصيد العملة المحدد"""
        if transaction_date is None:
            transaction_date = datetime.now().date()

        query = """
            INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id,
                                    description, transaction_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        transaction_id = db.execute_insert(query, (
            user_id, account_id, transaction_type, amount, currency_id,
            description, transaction_date
        ))

        if transaction_id > 0:
            # تحديد قيمة التغيير في الرصيد
            amount_change = Decimal(amount) if transaction_type == 'income' else -Decimal(amount)
            
            # تحديث رصيد العملة في الحساب
            Account.update_balance_for_currency(account_id, currency_id, amount_change)

            auth_manager.log_activity(f'create_{transaction_type}', 'transactions', transaction_id)

        return transaction_id

    @staticmethod
    def delete(transaction_id):
        """حذف معاملة وعكس تأثيرها على رصيد العملة"""
        transaction = Transaction.get_by_id(transaction_id)
        if not transaction:
            return False

        # عكس تأثير المعاملة على الرصيد
        amount_change = -Decimal(transaction['amount']) if transaction['transaction_type'] == 'income' else Decimal(transaction['amount'])
        Account.update_balance_for_currency(transaction['account_id'], transaction['currency_id'], amount_change)

        # حذف المعاملة
        query = "DELETE FROM transactions WHERE id = %s"
        result = db.execute_update(query, (transaction_id,))

        if result > 0:
            auth_manager.log_activity('delete_transaction', 'transactions', transaction_id)

        return result > 0

    @staticmethod
    def update(transaction_id, amount=None, account_id=None, currency_id=None,
               description=None, transaction_date=None):
        """تحديث معاملة مع إعادة حساب الأرصدة"""
        try:
            # الحصول على المعاملة الحالية
            current_transaction = Transaction.get_by_id(transaction_id)
            if not current_transaction:
                logging.error(f"لم يتم العثور على المعاملة {transaction_id} للتحديث.")
                return False

            # عكس تأثير المعاملة القديمة على الرصيد
            old_amount_change = -Decimal(current_transaction['amount']) if current_transaction['transaction_type'] == 'income' else Decimal(current_transaction['amount'])
            update_success = Account.update_balance_for_currency(
                current_transaction['account_id'], 
                current_transaction['currency_id'], 
                old_amount_change
            )
            if not update_success:
                logging.warning(f"فشل عكس المعاملة القديمة {transaction_id}")

            # إعداد القيم الجديدة
            new_amount = Decimal(amount if amount is not None else current_transaction['amount'])
            new_account_id = account_id if account_id is not None else current_transaction['account_id']
            new_currency_id = currency_id if currency_id is not None else current_transaction['currency_id']
            new_description = description if description is not None else current_transaction['description']
            new_transaction_date = transaction_date if transaction_date is not None else current_transaction['transaction_date']

            # تحديث المعاملة في قاعدة البيانات
            query = """
                UPDATE transactions
                SET amount = %s, account_id = %s, currency_id = %s,
                    description = %s, transaction_date = %s,
                    updated_at = %s
                WHERE id = %s
            """
            result = db.execute_update(query, (
                new_amount, new_account_id, new_currency_id,
                new_description, new_transaction_date,
                datetime.now(), transaction_id
            ))

            if result > 0:
                # تطبيق تأثير المعاملة الجديدة على الرصيد
                new_amount_change = new_amount if current_transaction['transaction_type'] == 'income' else -new_amount
                update_success = Account.update_balance_for_currency(
                    new_account_id, 
                    new_currency_id, 
                    new_amount_change
                )
                if not update_success:
                    logging.warning(f"فشل تطبيق تأثير المعاملة الجديدة {transaction_id}")

                auth_manager.log_activity('update_transaction', 'transactions', transaction_id)
                return True
            else:
                # إذا فشل التحديث، يجب إعادة تطبيق تأثير المعاملة القديمة
                # لمنع عدم تطابق الأرصدة
                rerevert_success = Account.update_balance_for_currency(
                    current_transaction['account_id'], 
                    current_transaction['currency_id'], 
                    -old_amount_change  # عكس العكس
                )
                if not rerevert_success:
                    logging.error(f"خطر: فشل تحديث المعاملة {transaction_id} وفشل استعادة الرصيد الأصلي.")
                return False

        except Exception as e:
            logging.error(f"خطأ في تحديث المعاملة {transaction_id}: {e}", exc_info=True)
            # قد تحتاج إلى منطق أكثر تعقيدًا هنا لاستعادة الحالة إذا لزم الأمر
            return False

    @staticmethod
    def get_by_id(transaction_id):
        """الحصول على معاملة بواسطة ID"""
        query = """
            SELECT t.*, a.name as account_name, c.symbol as currency_symbol,
                   c.name as currency_name, c.code as currency_code
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.id = %s
        """
        result = db.execute_query(query, (transaction_id,))
        return result[0] if result else None

class Transfer:
    """نموذج التحويل (متعدد العملات)"""

    @staticmethod
    def create(user_id, from_account_id, to_account_id, from_amount, from_currency_id,
               to_amount, to_currency_id, description="", transfer_date=None):
        """إنشاء تحويل جديد بين حسابين (و/أو عملتين)"""
        if transfer_date is None:
            transfer_date = datetime.now().date()

        # حساب سعر الصرف ضمنيًا
        exchange_rate = Decimal(to_amount) / Decimal(from_amount) if from_amount != 0 else 0

        query = """
            INSERT INTO transfers (user_id, from_account_id, to_account_id, 
                                 from_amount, from_currency_id, to_amount, to_currency_id, 
                                 exchange_rate, description, transfer_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        transfer_id = db.execute_insert(query, (
            user_id, from_account_id, to_account_id, 
            from_amount, from_currency_id, to_amount, to_currency_id,
            exchange_rate, description, transfer_date
        ))

        if transfer_id > 0:
            # خصم المبلغ من الحساب المصدر
            Account.update_balance_for_currency(from_account_id, from_currency_id, -Decimal(from_amount))
            
            # إضافة المبلغ إلى الحساب الهدف
            Account.update_balance_for_currency(to_account_id, to_currency_id, Decimal(to_amount))

            auth_manager.log_activity('create_transfer', 'transfers', transfer_id)

        return transfer_id

    @staticmethod
    def delete(transfer_id):
        """حذف تحويل وعكس تأثيره على أرصدة الحسابات"""
        transfer = Transfer.get_by_id(transfer_id)
        if not transfer:
            return False

        # عكس التحويل
        # إضافة المبلغ مرة أخرى إلى الحساب المصدر
        Account.update_balance_for_currency(transfer['from_account_id'], transfer['from_currency_id'], Decimal(transfer['from_amount']))
        
        # خصم المبلغ من الحساب الهدف
        Account.update_balance_for_currency(transfer['to_account_id'], transfer['to_currency_id'], -Decimal(transfer['to_amount']))

        # حذف سجل التحويل
        query = "DELETE FROM transfers WHERE id = %s"
        result = db.execute_update(query, (transfer_id,))

        if result > 0:
            auth_manager.log_activity('delete_transfer', 'transfers', transfer_id)

        return result > 0

    @staticmethod
    def get_by_id(transfer_id):
        """الحصول على تفاصيل تحويل معين"""
        query = """
            SELECT t.*,
                   fa.name as from_account_name, ta.name as to_account_name,
                   fc.name as from_currency_name, fc.code as from_currency_code,
                   tc.name as to_currency_name, tc.code as to_currency_code
            FROM transfers t
            JOIN accounts fa ON t.from_account_id = fa.id
            JOIN accounts ta ON t.to_account_id = ta.id
            JOIN currencies fc ON t.from_currency_id = fc.id
            JOIN currencies tc ON t.to_currency_id = tc.id
            WHERE t.id = %s
        """
        result = db.execute_query(query, (transfer_id,))
        return result[0] if result else None

    @staticmethod
    def get_by_user(user_id, limit=None):
        """الحصول على تحويلات المستخدم"""
        try:
            query = """
                SELECT t.*,
                       fa.name as from_account_name, ta.name as to_account_name,
                       fc.name as from_currency_name, fc.code as from_currency_code, fc.symbol as from_currency_symbol,
                       tc.name as to_currency_name, tc.code as to_currency_code, tc.symbol as to_currency_symbol
                FROM transfers t
                JOIN accounts fa ON t.from_account_id = fa.id
                JOIN accounts ta ON t.to_account_id = ta.id
                JOIN currencies fc ON t.from_currency_id = fc.id
                JOIN currencies tc ON t.to_currency_id = tc.id
                WHERE t.user_id = %s
                ORDER BY t.transfer_date DESC, t.created_at DESC
            """

            if limit:
                query += f" LIMIT {limit}"

            result = db.execute_query(query, (user_id,))
            return result if result else []

        except Exception as e:
            print(f"❌ خطأ في Transfer.get_by_user: {e}")
            import traceback
            traceback.print_exc()
            return []

# Note: The remaining classes (Category, AccountType) do not need major changes
# for this multi-currency refactoring, so they are omitted for brevity but should be kept in the file.
class Category:
    """نموذج التصنيف"""

    @staticmethod
    def get_income_categories():
        """الحصول على تصنيفات الواردات"""
        query = "SELECT * FROM income_categories WHERE is_active = TRUE ORDER BY name"
        return db.execute_query(query)

    @staticmethod
    def get_expense_categories():
        """الحصول على تصنيفات المصروفات"""
        query = "SELECT * FROM expense_categories WHERE is_active = TRUE ORDER BY name"
        return db.execute_query(query)

class AccountType:
    """نموذج نوع الحساب"""

    @staticmethod
    def get_all():
        """الحصول على جميع أنواع الحسابات"""
        query = "SELECT * FROM account_types ORDER BY name"
        return db.execute_query(query)
