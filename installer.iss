[Setup]
AppName=برنامج إدارة الأموال الشخصية
AppVersion=1.0
AppPublisher=mohdam
AppPublisherURL=https://github.com/mohdam
WizardStyle=modern
DefaultDirName={autopf}\Money Manager
DefaultGroupName=Money Manager
OutputDir=installer_output
OutputBaseFilename=MoneyManagerSetup
Compression=lzma
SolidCompression=yes
ArchitecturesAllowed=x64compatible
ArchitecturesInstallIn64BitMode=x64compatible
SetupIconFile=assets\icon.ico
UninstallDisplayIcon={app}\Money Manager.exe

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: checkablealone

[Files]
Source: "dist\Money Manager.exe"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\Money Manager"; Filename: "{app}\Money Manager.exe"
Name: "{autodesktop}\Money Manager"; Filename: "{app}\Money Manager.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\Money Manager.exe"; Description: "{cm:LaunchProgram,Money Manager}"; Flags: nowait postinstall skipifsilent 