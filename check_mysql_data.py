#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص البيانات في MySQL
"""

import mysql.connector
import json

def check_mysql_connection():
    """فحص الاتصال بـ MySQL"""
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        
        print("✅ تم الاتصال بـ MySQL بنجاح")
        
        # فحص الجداول
        cursor.execute("SHOW TABLES")
        tables = [row[list(row.keys())[0]] for row in cursor.fetchall()]
        print(f"📋 الجداول في MySQL: {', '.join(tables)}")
        
        mysql_data = {}
        
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                count = cursor.fetchone()['count']
                print(f"   {table}: {count} سجل")
                mysql_data[table] = count
            except Exception as e:
                print(f"   ❌ خطأ في {table}: {e}")
        
        # فحص البيانات المهمة
        print("\n📊 تفاصيل البيانات المهمة:")
        
        # المستخدمين
        cursor.execute("SELECT username, role, is_active FROM users")
        users = cursor.fetchall()
        print(f"👥 المستخدمين:")
        for user in users:
            status = "نشط" if user['is_active'] else "غير نشط"
            print(f"   - {user['username']} ({user['role']}) - {status}")
        
        # العملات
        cursor.execute("SELECT code, name, symbol FROM currencies WHERE is_active = TRUE")
        currencies = cursor.fetchall()
        print(f"💰 العملات النشطة:")
        for currency in currencies:
            print(f"   - {currency['code']}: {currency['name']} ({currency['symbol']})")
        
        # الحسابات
        cursor.execute("""
            SELECT a.name, u.username, a.is_active 
            FROM accounts a 
            JOIN users u ON a.user_id = u.id
        """)
        accounts = cursor.fetchall()
        print(f"🏦 الحسابات:")
        for account in accounts:
            status = "نشط" if account['is_active'] else "غير نشط"
            print(f"   - {account['name']} (المستخدم: {account['username']}) - {status}")
        
        # أرصدة الحسابات
        cursor.execute("""
            SELECT a.name as account_name, c.code as currency, ab.balance
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            JOIN currencies c ON ab.currency_id = c.id
            WHERE ab.balance != 0
        """)
        balances = cursor.fetchall()
        print(f"💵 الأرصدة غير الصفرية:")
        for balance in balances:
            print(f"   - {balance['account_name']}: {balance['balance']} {balance['currency']}")
        
        cursor.close()
        connection.close()
        
        return mysql_data
        
    except mysql.connector.Error as e:
        print(f"❌ خطأ في الاتصال بـ MySQL: {e}")
        return {}
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return {}

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص البيانات في MySQL")
    print("=" * 40)
    
    mysql_data = check_mysql_connection()
    
    if mysql_data:
        print(f"\n✅ تم فحص {len(mysql_data)} جدول في MySQL")
        
        # حفظ النتائج
        with open('mysql_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(mysql_data, f, ensure_ascii=False, indent=2)
        
        print("💾 تم حفظ النتائج في: mysql_analysis.json")
    else:
        print("❌ فشل في فحص بيانات MySQL")

if __name__ == "__main__":
    main()
