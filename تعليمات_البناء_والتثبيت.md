# تعليمات بناء وتثبيت برنامج مدير الأموال

هذا الملف يشرح كيفية تحويل برنامج مدير الأموال إلى ملف تنفيذي (exe) وإنشاء حزمة تثبيت قابلة للتوزيع على أجهزة أخرى.

## المتطلبات الأساسية

1. **بايثون (Python)**: يجب أن يكون مثبتًا على جهازك (الإصدار 3.8 أو أحدث)
2. **PyInstaller**: أداة لتحويل برامج بايثون إلى ملفات تنفيذية
3. **Inno Setup**: أداة لإنشاء حزم التثبيت لنظام ويندوز

## طريقة البناء التلقائي

يمكنك استخدام ملف البناء التلقائي الذي يقوم بكل الخطوات المطلوبة:

1. انقر بزر الماوس الأيمن على ملف `build_installer.bat` واختر "تشغيل كمسؤول" (Run as administrator)
2. انتظر حتى تكتمل العملية
3. ستجد الملف التنفيذي في مجلد `dist\Money Manager`
4. ستجد حزمة التثبيت في مجلد `installer_output`

## طريقة البناء اليدوي

إذا كنت ترغب في تنفيذ الخطوات يدويًا، اتبع التالي:

### 1. تثبيت الأدوات المطلوبة

```
pip install pyinstaller
```

قم بتثبيت Inno Setup من الموقع الرسمي: https://jrsoftware.org/isdl.php

### 2. بناء الملف التنفيذي

```
pyinstaller --noconfirm "Money Manager.spec"
```

### 3. إنشاء حزمة التثبيت

```
iscc installer.iss
```

## الملفات الناتجة

1. **الملف التنفيذي**: `dist\Money Manager\Money Manager.exe`
2. **حزمة التثبيت**: `installer_output\MoneyManagerSetup.exe`

## استكشاف الأخطاء وإصلاحها

### مشاكل PyInstaller

- تأكد من تثبيت جميع المكتبات المطلوبة في ملف `requirements.txt`
- تحقق من وجود ملف `Money Manager.spec` في المجلد الرئيسي
- إذا واجهت مشاكل، جرب استخدام ملف `.spec` آخر مثل `money_manager.spec`

### مشاكل Inno Setup

- تأكد من إضافة مسار تثبيت Inno Setup إلى متغير PATH في النظام
- تحقق من صحة ملف `installer.iss`
- تأكد من وجود الملفات المشار إليها في ملف `installer.iss`

## تخصيص عملية البناء

### تعديل ملف .spec

يمكنك تعديل ملف `Money Manager.spec` لتغيير إعدادات بناء الملف التنفيذي مثل:
- إضافة ملفات إضافية
- تغيير أيقونة البرنامج
- إضافة مكتبات مخفية

### تعديل ملف .iss

يمكنك تعديل ملف `installer.iss` لتخصيص حزمة التثبيت مثل:
- تغيير اسم البرنامج أو الإصدار
- تعديل مسار التثبيت الافتراضي
- إضافة خطوات تثبيت مخصصة
- تغيير لغة واجهة التثبيت

## ملاحظات هامة

- تأكد من اختبار البرنامج التنفيذي قبل إنشاء حزمة التثبيت
- قم بتوزيع حزمة التثبيت `MoneyManagerSetup.exe` وليس الملف التنفيذي المباشر
- تأكد من تضمين جميع الملفات المطلوبة في حزمة التثبيت