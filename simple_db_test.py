#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للاتصال بقاعدة البيانات
"""

import mysql.connector
from mysql.connector import Error

def test_connection():
    try:
        print("🔄 محاولة الاتصال بـ MySQL...")
        
        # محاولة الاتصال
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='mohdam',
            charset='utf8mb4'
        )
        
        if connection.is_connected():
            print("✅ تم الاتصال بـ MySQL بنجاح!")
            
            cursor = connection.cursor()
            
            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            cursor.execute("CREATE DATABASE IF NOT EXISTS money_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ تم إنشاء/التحقق من قاعدة البيانات")
            
            # الاتصال بقاعدة البيانات
            cursor.execute("USE money_manager")
            print("✅ تم الاتصال بقاعدة البيانات money_manager")
            
            # اختبار استعلام
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            print(f"✅ نتيجة الاختبار: {result}")
            
            cursor.close()
            connection.close()
            print("✅ تم إغلاق الاتصال بنجاح")
            
            return True
            
    except Error as e:
        print(f"❌ خطأ في MySQL: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    test_connection()
