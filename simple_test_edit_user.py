#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للتحسينات على نافذة تعديل المستخدم
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 اختبار مبسط للتحسينات على نافذة تعديل المستخدم")
print("=" * 60)

# 1. اختبار استيراد الوحدات
print("\n1. اختبار استيراد الوحدات...")
try:
    from gui.user_management_windows import EditUserWindow
    print("✅ تم استيراد EditUserWindow")
except Exception as e:
    print(f"❌ فشل استيراد EditUserWindow: {e}")
    exit(1)

try:
    from utils.auth import auth_manager
    print("✅ تم استيراد auth_manager")
except Exception as e:
    print(f"❌ فشل استيراد auth_manager: {e}")
    exit(1)

try:
    from database.models import User
    print("✅ تم استيراد User model")
except Exception as e:
    print(f"❌ فشل استيراد User model: {e}")
    exit(1)

# 2. اختبار تسجيل الدخول
print("\n2. اختبار تسجيل الدخول...")
try:
    success, message = auth_manager.login("admin2", "123456")
    if success:
        print("✅ تم تسجيل الدخول بنجاح")
        print(f"   المستخدم: {auth_manager.current_user['username']}")
    else:
        print(f"❌ فشل تسجيل الدخول: {message}")
        exit(1)
except Exception as e:
    print(f"❌ خطأ في تسجيل الدخول: {e}")
    exit(1)

# 3. اختبار دالة update_user في models
print("\n3. اختبار دالة update_user...")
try:
    # الحصول على المستخدم الحالي
    current_user = User.get_by_username("admin2")
    if current_user:
        print(f"✅ تم العثور على المستخدم: {current_user['username']}")
        
        # اختبار تحديث الاسم الكامل
        success, message = User.update_user(
            current_user['id'],
            full_name="المدير الجديد المحدث"
        )
        
        if success:
            print("✅ تم تحديث الاسم الكامل بنجاح")
            
            # إعادة الاسم الأصلي
            User.update_user(current_user['id'], full_name="المدير الجديد")
        else:
            print(f"❌ فشل تحديث الاسم الكامل: {message}")
    else:
        print("❌ لم يتم العثور على المستخدم admin2")
        exit(1)
except Exception as e:
    print(f"❌ خطأ في اختبار update_user: {e}")
    exit(1)

# 4. اختبار regex للتحقق من اسم المستخدم
print("\n4. اختبار regex للتحقق من اسم المستخدم...")
try:
    import re
    
    # أسماء صحيحة
    valid_names = ["user123", "admin_user", "test_123"]
    for name in valid_names:
        if re.match("^[a-zA-Z0-9_]+$", name):
            print(f"✅ اسم صحيح: {name}")
        else:
            print(f"❌ اسم خاطئ: {name}")
    
    # أسماء خاطئة
    invalid_names = ["user-123", "user@name", "user name"]
    for name in invalid_names:
        if not re.match("^[a-zA-Z0-9_]+$", name):
            print(f"✅ تم رفض اسم خاطئ: {name}")
        else:
            print(f"❌ لم يتم رفض اسم خاطئ: {name}")
            
except Exception as e:
    print(f"❌ خطأ في اختبار regex: {e}")

# 5. اختبار إنشاء نافذة التعديل (بدون عرض)
print("\n5. اختبار إنشاء نافذة التعديل...")
try:
    import customtkinter as ctk
    
    # إنشاء نافذة مؤقتة
    test_window = ctk.CTk()
    test_window.withdraw()  # إخفاء النافذة
    
    # الحصول على بيانات المستخدم
    user_data = User.get_by_username("admin2")
    
    # إنشاء نافذة التعديل
    edit_window = EditUserWindow(test_window, user_data)
    edit_window.window.withdraw()  # إخفاء النافذة
    
    # التحقق من الحقول الجديدة
    checks = [
        ("username_entry", "حقل اسم المستخدم"),
        ("new_password_entry", "حقل كلمة المرور الجديدة"),
        ("confirm_new_password_entry", "حقل تأكيد كلمة المرور"),
        ("fullname_entry", "حقل الاسم الكامل"),
        ("role_var", "متغير الدور"),
        ("status_var", "متغير الحالة")
    ]
    
    for attr, desc in checks:
        if hasattr(edit_window, attr):
            print(f"✅ {desc} موجود")
        else:
            print(f"❌ {desc} مفقود")
    
    # إغلاق النوافذ
    edit_window.window.destroy()
    test_window.destroy()
    
    print("✅ تم إنشاء نافذة التعديل المحسنة بنجاح")
    
except Exception as e:
    print(f"❌ خطأ في إنشاء نافذة التعديل: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 60)
print("🎉 انتهى الاختبار المبسط!")
print("\n📋 التحسينات المطبقة:")
print("✅ إمكانية تعديل اسم المستخدم")
print("✅ إمكانية تغيير كلمة المرور (اختياري)")
print("✅ تحسين واجهة المستخدم مع إطار قابل للتمرير")
print("✅ تحسين دوال التحقق من البيانات")
print("✅ إضافة مربعات حوار التأكيد")

print("\n🚀 لاختبار النافذة بصرياً:")
print("1. شغل التطبيق: python main.py")
print("2. سجل الدخول باستخدام: admin2 / 123456")
print("3. انقر على '👥 إدارة المستخدمين'")
print("4. انقر على 'تعديل' بجانب أي مستخدم")
