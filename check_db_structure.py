#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys

def check_database_structure():
    """فحص هيكل قاعدة البيانات الحالية"""
    try:
        conn = sqlite3.connect('money_manager.db')
        cursor = conn.cursor()
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("📊 جداول قاعدة البيانات الحالية:")
        print("=" * 50)
        for table in tables:
            table_name = table[0]
            print(f"\n🔹 جدول: {table_name}")
            
            # الحصول على معلومات الأعمدة
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            for col in columns:
                col_id, name, data_type, not_null, default_val, pk = col
                pk_str = " (PRIMARY KEY)" if pk else ""
                not_null_str = " NOT NULL" if not_null else ""
                default_str = f" DEFAULT {default_val}" if default_val else ""
                print(f"    - {name}: {data_type}{not_null_str}{default_str}{pk_str}")
        
        # فحص وجود جدول account_balances
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='account_balances'")
        account_balances_exists = cursor.fetchone()
        
        print(f"\n🔍 جدول account_balances موجود: {'نعم' if account_balances_exists else 'لا'}")
        
        # فحص العملات الموجودة
        cursor.execute("SELECT * FROM currencies")
        currencies = cursor.fetchall()
        
        print(f"\n💰 العملات المتاحة ({len(currencies)}):")
        for currency in currencies:
            print(f"    - {currency}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

if __name__ == "__main__":
    check_database_structure()
