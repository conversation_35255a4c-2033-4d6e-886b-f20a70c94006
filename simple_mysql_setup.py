#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector
from config.settings import DATABASE_CONFIG

def setup_database():
    """إعداد قاعدة البيانات بشكل مبسط"""
    try:
        print("🚀 بدء إعداد قاعدة البيانات...")
        
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()
        
        print("✅ تم الاتصال بنجاح")
        
        # إنشاء جدول account_balances
        print("📊 إنشاء جدول account_balances...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_balances (
                id INT AUTO_INCREMENT PRIMARY KEY,
                account_id INT NOT NULL,
                currency_id INT NOT NULL,
                balance DECIMAL(15, 2) DEFAULT 0.00,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_account_currency (account_id, currency_id)
            )
        """)
        
        # إضافة عمود exchange_rate إذا لم يكن موجوداً
        try:
            cursor.execute("ALTER TABLE currencies ADD COLUMN exchange_rate DECIMAL(10, 4) DEFAULT 1.0000")
            print("   - تم إضافة عمود exchange_rate")
        except:
            print("   - عمود exchange_rate موجود")
        
        # تحديث العملات
        print("💰 تحديث العملات...")
        cursor.execute("DELETE FROM currencies WHERE code NOT IN ('SAR', 'YER', 'AED', 'USD')")
        
        currencies = [
            ('SAR', 'ريال سعودي', 'ر.س'),
            ('YER', 'ريال يمني', 'ر.ي'),
            ('AED', 'درهم إماراتي', 'د.إ'),
            ('USD', 'دولار أمريكي', '$')
        ]
        
        for code, name, symbol in currencies:
            cursor.execute("""
                INSERT INTO currencies (code, name, symbol, is_active, exchange_rate)
                VALUES (%s, %s, %s, 1, 1.0)
                ON DUPLICATE KEY UPDATE 
                name = VALUES(name), 
                symbol = VALUES(symbol),
                is_active = 1
            """, (code, name, symbol))
        
        # إنشاء جدول activity_log
        print("📝 إنشاء جدول activity_log...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                action VARCHAR(100) NOT NULL,
                table_name VARCHAR(50) NOT NULL,
                record_id INT,
                details TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول transfers
        print("🔄 إنشاء جدول transfers...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transfers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                from_account_id INT NOT NULL,
                to_account_id INT NOT NULL,
                from_amount DECIMAL(15, 2) NOT NULL,
                to_amount DECIMAL(15, 2) NOT NULL,
                from_currency_id INT NOT NULL,
                to_currency_id INT NOT NULL,
                exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
                description TEXT,
                transfer_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reference_number VARCHAR(50)
            )
        """)
        
        # إضافة أنواع الحسابات
        print("🏦 إضافة أنواع الحسابات...")
        try:
            cursor.execute("ALTER TABLE account_types ADD COLUMN icon VARCHAR(50)")
        except:
            pass
        
        account_types = [
            ('حساب عام', 'حساب عام للاستخدام المتنوع'),
            ('نقدي', 'صندوق نقدي'),
            ('بنكي', 'حساب بنكي'),
            ('محفظة إلكترونية', 'محفظة إلكترونية')
        ]
        
        for name, description in account_types:
            cursor.execute("""
                INSERT IGNORE INTO account_types (name, description)
                VALUES (%s, %s)
            """, (name, description))
        
        connection.commit()
        
        print("\n✅ تم إعداد قاعدة البيانات بنجاح!")
        print("🎉 يمكنك الآن استخدام ميزة الحسابات متعددة العملات")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    setup_database()
