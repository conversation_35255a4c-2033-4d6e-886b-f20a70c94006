# دليل الإصلاح السريع - تطبيق إدارة الأموال

## المشاكل المحددة
1. ❌ جميع البيانات السابقة مفقودة/لا يتم تحميلها
2. ❌ فشل إضافة حسابات مالية جديدة

## الحل السريع (5 خطوات)

### الخطوة 1: التحقق من خدمة MySQL
```cmd
# فتح Command Prompt كمدير
# تشغيل الأمر التالي:
sc query mysql

# إذا كانت الخدمة متوقفة، شغلها:
sc start mysql
```

### الخطوة 2: تشغيل الإصلاح الشامل
```cmd
# في مجلد المشروع، شغل:
python database_master_fix.py
```

### الخطوة 3: إذا فشلت الخطوة 2، شغل السكريپتات منفردة
```cmd
# إصلاح قاعدة البيانات
python comprehensive_database_fix.py

# إصلاح مشكلة الحسابات
python fix_account_creation_issue.py
```

### الخطوة 4: اختبار التطبيق
```cmd
# تشغيل التطبيق
python main.py

# أو
python launcher.py
```

### الخطوة 5: التحقق من النتائج
- تسجيل الدخول بـ: admin / 123456
- التحقق من ظهور الحسابات السابقة (3 حسابات)
- اختبار إضافة حساب جديد

## البيانات المتوقع استعادتها

### المستخدمون:
- **admin** - المدير العام
- **user** - مستخدم تجريبي

### الحسابات المالية:
1. **الصندوق النقدي** - 5,000 ر.س
2. **حساب الراجحي** - 25,000 ر.س  
3. **حساب التوفير** - 50,000 ر.س

### المعاملات:
- راتب شهر ديسمبر: +8,000 ر.س
- تسوق من السوبر ماركت: -300 ر.س
- إيجار الشقة: -1,200 ر.س
- دخل من مشروع جانبي: +1,500 ر.س

## في حالة استمرار المشاكل

### مشكلة: خدمة MySQL لا تعمل
**الحل:**
1. تأكد من تثبيت MySQL
2. أعد تشغيل الكمبيوتر
3. شغل خدمة MySQL يدوياً من Services

### مشكلة: خطأ في كلمة المرور
**الحل:**
1. افتح ملف `config/database_settings.json`
2. تأكد من كلمة المرور: `"password": "mohdam"`
3. إذا كانت مختلفة، غيرها للصحيحة

### مشكلة: قاعدة البيانات غير موجودة
**الحل:**
```sql
-- اتصل بـ MySQL وشغل:
CREATE DATABASE money_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### مشكلة: Python لا يعمل
**الحل:**
1. تأكد من تثبيت Python
2. شغل من PowerShell:
```powershell
python --version
pip install mysql-connector-python
```

## ملفات مهمة

### ملفات الإصلاح:
- `database_master_fix.py` - الإصلاح الشامل
- `comprehensive_database_fix.py` - إصلاح قاعدة البيانات
- `fix_account_creation_issue.py` - إصلاح مشكلة الحسابات

### ملفات البيانات:
- `cleanup_backup_20250720_021829/users.json` - المستخدمون
- `cleanup_backup_20250720_021829/accounts.json` - الحسابات
- `cleanup_backup_20250720_021829/transactions.json` - المعاملات

### ملفات الإعدادات:
- `config/database_settings.json` - إعدادات قاعدة البيانات
- `config/settings.py` - إعدادات التطبيق

## رسائل النجاح المتوقعة

عند نجاح الإصلاح، ستظهر رسائل مثل:
```
✓ تم الاتصال بخادم MySQL بنجاح
✓ تم إنشاء قاعدة البيانات 'money_manager'
✓ تم إنشاء جميع الجداول
✓ تم استعادة المستخدم: admin
✓ تم استعادة الحساب: الصندوق النقدي
✓ تم استعادة المعاملة: راتب شهر ديسمبر
✓ تم إصلاح جميع المشاكل بنجاح!
```

## الدعم الإضافي

إذا استمرت المشاكل:
1. راجع ملف `database_analysis_report.md` للتفاصيل الكاملة
2. تحقق من ملفات السجلات في مجلد `logs/`
3. شغل `comprehensive_database_audit.py` للفحص التفصيلي

---
**آخر تحديث**: 2025-07-20
