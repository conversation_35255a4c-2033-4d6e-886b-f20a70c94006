#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشاكل استيراد Excel
"""

import pandas as pd
import os
from datetime import datetime

def debug_excel_import():
    """تشخيص مشاكل استيراد Excel"""
    
    print("🔍 تشخيص مشاكل استيراد Excel...")
    print("=" * 50)
    
    # 1. فحص الاتصال بقاعدة البيانات
    print("\n1️⃣ فحص الاتصال بقاعدة البيانات:")
    try:
        from database.connection import db
        from utils.auth import auth_manager
        
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # تسجيل دخول تجريبي
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل في تسجيل الدخول: {message}")
            return
        
        print("✅ تم تسجيل الدخول بنجاح")
        user_id = auth_manager.current_user['id']
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return
    
    # 2. فحص الحسابات الموجودة
    print("\n2️⃣ فحص الحسابات الموجودة في قاعدة البيانات:")
    try:
        accounts_query = "SELECT id, name FROM accounts WHERE user_id = %s AND is_active = 1"
        accounts = db.execute_query(accounts_query, (user_id,))
        
        if not accounts:
            print("❌ لا توجد حسابات في قاعدة البيانات!")
            print("💡 تحتاج لإنشاء حسابات أولاً أو تشغيل add_demo_accounts.py")
            return
        
        print(f"✅ تم العثور على {len(accounts)} حساب:")
        accounts_dict = {}
        for acc in accounts:
            accounts_dict[acc['name']] = acc['id']
            print(f"   • {acc['name']} (ID: {acc['id']})")
        
    except Exception as e:
        print(f"❌ خطأ في جلب الحسابات: {e}")
        return
    
    # 3. فحص العملات المدعومة
    print("\n3️⃣ فحص العملات المدعومة:")
    try:
        currencies_query = "SELECT id, code, symbol FROM currencies WHERE is_active = 1"
        currencies = db.execute_query(currencies_query)
        
        if not currencies:
            print("❌ لا توجد عملات مدعومة!")
            return
        
        print(f"✅ تم العثور على {len(currencies)} عملة:")
        currencies_dict = {}
        for curr in currencies:
            currencies_dict[curr['code']] = curr['id']
            currencies_dict[curr['symbol']] = curr['id']
            print(f"   • {curr['code']} ({curr['symbol']}) - ID: {curr['id']}")
        
    except Exception as e:
        print(f"❌ خطأ في جلب العملات: {e}")
        return
    
    # 4. فحص ملف Excel النموذجي
    print("\n4️⃣ فحص ملف Excel النموذجي:")
    sample_file = "sample_excel_files/نموذج_واردات.xlsx"
    
    if not os.path.exists(sample_file):
        print(f"❌ الملف غير موجود: {sample_file}")
        return
    
    try:
        df = pd.read_excel(sample_file)
        print(f"✅ تم قراءة الملف بنجاح - {len(df)} صف")
        print(f"📋 الأعمدة: {list(df.columns)}")
        
        # فحص كل صف
        print("\n🔍 فحص تفصيلي لكل صف:")
        for index, row in df.iterrows():
            print(f"\n   الصف {index + 1}:")
            
            # فحص المبلغ
            amount_raw = row.get('المبلغ', 0)
            try:
                amount = float(str(amount_raw).replace(',', '').replace('ر.س', '').replace('$', '').strip())
                print(f"   ✅ المبلغ: {amount}")
            except:
                print(f"   ❌ المبلغ غير صحيح: {amount_raw}")
                continue
            
            # فحص الحساب
            account_name = str(row.get('الحساب', '')).strip()
            if account_name in accounts_dict:
                print(f"   ✅ الحساب: {account_name} (موجود)")
            else:
                print(f"   ❌ الحساب: {account_name} (غير موجود)")
                print(f"      الحسابات المتاحة: {list(accounts_dict.keys())}")
            
            # فحص العملة
            currency_code = str(row.get('العملة', '')).strip()
            if currency_code in currencies_dict:
                print(f"   ✅ العملة: {currency_code} (مدعومة)")
            else:
                print(f"   ❌ العملة: {currency_code} (غير مدعومة)")
                print(f"      العملات المدعومة: {list(set([k for k in currencies_dict.keys() if len(k) <= 3]))}")
            
            # فحص التاريخ
            date_raw = str(row.get('التاريخ', ''))
            try:
                date_formats = ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y', '%Y/%m/%d']
                date_parsed = None
                for date_format in date_formats:
                    try:
                        date_parsed = datetime.strptime(date_raw, date_format).date()
                        break
                    except:
                        continue
                
                if date_parsed:
                    print(f"   ✅ التاريخ: {date_parsed}")
                else:
                    print(f"   ❌ التاريخ غير صحيح: {date_raw}")
            except:
                print(f"   ❌ التاريخ غير صحيح: {date_raw}")
            
            # فحص الوصف
            description = str(row.get('الوصف', '')).strip()
            print(f"   📝 الوصف: {description}")
        
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف Excel: {e}")
        return
    
    # 5. اقتراحات الإصلاح
    print("\n5️⃣ اقتراحات الإصلاح:")
    print("💡 لحل مشاكل الاستيراد:")
    print("   1. تأكد من أن أسماء الحسابات في Excel تطابق تماماً الحسابات في قاعدة البيانات")
    print("   2. استخدم رموز العملات الصحيحة: SAR, YER, AED, USD")
    print("   3. تأكد من تنسيق التواريخ (YYYY-MM-DD مفضل)")
    print("   4. تأكد من أن المبالغ أرقام صحيحة")
    
    print("\n✅ انتهى التشخيص!")

if __name__ == "__main__":
    debug_excel_import()
