# دليل استيراد البيانات من Excel

## 🎯 نظرة عامة
تم إضافة ميزة جديدة لاستيراد المعاملات (الواردات والمصروفات) من ملفات Excel بشكل تلقائي. هذه الميزة تسمح لك بإضافة مجموعة كبيرة من المعاملات دفعة واحدة بدلاً من إدخالها يدوياً واحدة تلو الأخرى.

## 📋 متطلبات ملف Excel

### الأعمدة المطلوبة (بالترتيب):
1. **المبلغ** - رقم عشري (مثل: 1500.50)
2. **الحساب** - اسم الحساب الموجود في النظام
3. **العملة** - رمز العملة المدعومة
4. **التاريخ** - تاريخ المعاملة
5. **الوصف** - وصف المعاملة

### العملات المدعومة:
- **SAR** - ريال سعودي (ر.س)
- **YER** - ريال يمني (ر.ي)
- **AED** - درهم إماراتي (د.إ)
- **USD** - دولار أمريكي ($)

### تنسيقات التاريخ المدعومة:
- `YYYY-MM-DD` (مثل: 2024-01-15)
- `DD/MM/YYYY` (مثل: 15/01/2024)
- `MM/DD/YYYY` (مثل: 01/15/2024)
- `DD-MM-YYYY` (مثل: 15-01-2024)
- `YYYY/MM/DD` (مثل: 2024/01/15)

## 🚀 كيفية الاستخدام

### الخطوة 1: تحضير ملف Excel
1. أنشئ ملف Excel جديد
2. أضف الأعمدة الخمسة المطلوبة في الصف الأول
3. أدخل بيانات المعاملات في الصفوف التالية

### الخطوة 2: استيراد البيانات
1. افتح تطبيق مدير الأموال
2. انتقل إلى قسم **الواردات** أو **المصروفات**
3. اضغط على زر **📂 استيراد من Excel**
4. اختر ملف Excel المحضر
5. راجع البيانات في نافذة المعاينة
6. اضغط على **✅ تأكيد الاستيراد**

### الخطوة 3: مراجعة النتائج
- ستظهر نافذة تعرض عدد المعاملات المستوردة بنجاح
- إذا كانت هناك أخطاء، ستظهر تفاصيلها
- سيتم تحديث أرصدة الحسابات تلقائياً

## 📊 مثال على ملف Excel صحيح

| المبلغ | الحساب | العملة | التاريخ | الوصف |
|--------|---------|---------|----------|--------|
| 5000.00 | الصندوق النقدي | SAR | 2024-01-15 | راتب شهر يناير |
| 1500.50 | حساب الراجحي | SAR | 2024-01-20 | مكافأة إضافية |
| 800.00 | الصندوق النقدي | USD | 2024-01-25 | عمولة مشروع |
| 2000.00 | حساب التوفير | AED | 2024-01-30 | أرباح استثمار |

## ⚠️ أخطاء شائعة وحلولها

### 1. "الأعمدة التالية مفقودة"
**السبب:** أسماء الأعمدة غير صحيحة أو مفقودة
**الحل:** تأكد من وجود الأعمدة الخمسة بالأسماء الصحيحة

### 2. "مبلغ غير صحيح"
**السبب:** المبلغ يحتوي على نص أو فارغ
**الحل:** تأكد من أن جميع المبالغ أرقام صحيحة

### 3. "حساب غير موجود"
**السبب:** اسم الحساب غير مطابق للحسابات الموجودة
**الحل:** تأكد من أن أسماء الحسابات مطابقة تماماً للحسابات في النظام

### 4. "عملة غير مدعومة"
**السبب:** رمز العملة غير صحيح
**الحل:** استخدم فقط الرموز المدعومة: SAR, YER, AED, USD

### 5. "تاريخ غير صحيح"
**السبب:** تنسيق التاريخ غير مدعوم
**الحل:** استخدم أحد التنسيقات المدعومة المذكورة أعلاه

## 🔧 إنشاء ملفات نموذجية للاختبار

يمكنك تشغيل الملف `create_sample_excel.py` لإنشاء ملفات Excel نموذجية:

```bash
python create_sample_excel.py
```

سيتم إنشاء المجلد `sample_excel_files` مع الملفات التالية:
- `نموذج_واردات.xlsx` - مثال على الواردات
- `نموذج_مصروفات.xlsx` - مثال على المصروفات
- `نموذج_مختلط.xlsx` - واردات ومصروفات معاً
- `نموذج_بأخطاء.xlsx` - للاختبار مع أخطاء

## 💡 نصائح مهمة

1. **تأكد من الحسابات:** تأكد من إنشاء الحسابات في النظام قبل الاستيراد
2. **راجع البيانات:** استخدم نافذة المعاينة لمراجعة البيانات قبل الاستيراد
3. **النسخ الاحتياطية:** أنشئ نسخة احتياطية قبل استيراد كمية كبيرة من البيانات
4. **اختبر أولاً:** جرب الاستيراد مع ملف صغير أولاً
5. **أسماء الحسابات:** تأكد من مطابقة أسماء الحسابات تماماً (حساس للأحرف الكبيرة والصغيرة)

## 🎉 الميزات المتقدمة

- **معاينة البيانات:** مراجعة البيانات قبل الاستيراد
- **التحقق من الصحة:** فحص تلقائي للبيانات وعرض الأخطاء
- **تقرير النتائج:** عرض مفصل لنتائج الاستيراد
- **تحديث الأرصدة:** تحديث تلقائي لأرصدة الحسابات
- **دعم متعدد العملات:** استيراد معاملات بعملات مختلفة
- **مرونة في التواريخ:** دعم تنسيقات تاريخ متعددة

---

**ملاحظة:** هذه الميزة تدعم ملفات Excel (.xlsx و .xls) فقط. تأكد من حفظ ملفك بأحد هذين التنسيقين.
