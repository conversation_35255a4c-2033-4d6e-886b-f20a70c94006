#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 اختبار سريع لإصلاح مشكلة إدارة قاعدة البيانات...")

try:
    # 1. اختبار استيراد الأنماط
    print("1. اختبار استيراد الأنماط...")
    from config.colors import BUTTON_STYLES
    
    # التحقق من وجود الأنماط الجديدة
    required_styles = ['primary', 'secondary', 'success', 'danger', 'warning', 'info']
    for style in required_styles:
        if style in BUTTON_STYLES:
            height = BUTTON_STYLES[style].get('height', 'غير محدد')
            print(f"✅ نمط {style}: height={height}")
        else:
            print(f"❌ نمط {style} مفقود")
    
    # 2. فحص تضارب المعاملات في الكود
    print("\n2. فحص تضارب المعاملات في الكود...")
    with open('gui/main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن تضارب المعاملات
    problematic_patterns = [
        'height=40,\n            **BUTTON_STYLES',
        'height=35,\n            **BUTTON_STYLES',
        'height=45,\n            **BUTTON_STYLES',
        'height=50,\n            **BUTTON_STYLES'
    ]
    
    conflicts_found = []
    for pattern in problematic_patterns:
        if pattern in content:
            conflicts_found.append(pattern)
    
    if conflicts_found:
        print("❌ تم العثور على تضارب في المعاملات:")
        for conflict in conflicts_found:
            print(f"   - {conflict}")
    else:
        print("✅ لا توجد تضاربات في معاملات الأزرار")
    
    # 3. اختبار إنشاء أزرار بدون تضارب
    print("\n3. اختبار إنشاء أزرار بدون تضارب...")
    import customtkinter as ctk
    from config.fonts import create_rtl_button
    
    # إنشاء نافذة تجريبية
    test_window = ctk.CTk()
    test_window.withdraw()
    
    test_frame = ctk.CTkFrame(test_window)
    
    # اختبار إنشاء أزرار مع الأنماط المختلفة
    styles_to_test = ['primary', 'secondary', 'success', 'warning', 'info']
    
    for style in styles_to_test:
        try:
            test_button = create_rtl_button(
                test_frame,
                text=f"اختبار {style}",
                **BUTTON_STYLES[style]
            )
            print(f"✅ تم إنشاء زر بنمط {style} بدون تضارب")
        except Exception as e:
            print(f"❌ خطأ في إنشاء زر بنمط {style}: {e}")
    
    test_window.destroy()
    
    # 4. اختبار استيراد MainWindow
    print("\n4. اختبار استيراد MainWindow...")
    try:
        from gui.main_window import MainWindow
        print("✅ تم استيراد MainWindow بنجاح")
    except Exception as e:
        print(f"❌ فشل استيراد MainWindow: {e}")
        exit(1)
    
    # 5. اختبار الاتصال والتسجيل
    print("\n5. اختبار الاتصال والتسجيل...")
    try:
        from database.connection import db
        from utils.auth import auth_manager
        
        if db.is_connected() or db.connect():
            print("✅ الاتصال بقاعدة البيانات ناجح")
            
            success, message = auth_manager.login("admin", "123456")
            if success and auth_manager.is_admin():
                print("✅ تسجيل الدخول كمدير ناجح")
                
                # اختبار إنشاء واجهة إدارة قاعدة البيانات
                print("\n6. اختبار إنشاء واجهة إدارة قاعدة البيانات...")
                test_window = ctk.CTk()
                test_window.withdraw()
                
                try:
                    main_window = MainWindow(test_window)
                    print("✅ تم إنشاء MainWindow")
                    
                    # محاولة استدعاء دالة show_database_management
                    main_window.show_database_management()
                    print("✅ تم استدعاء show_database_management بدون أخطاء")
                    
                except Exception as e:
                    print(f"❌ خطأ في إنشاء واجهة إدارة قاعدة البيانات: {e}")
                    print("تفاصيل الخطأ:")
                    import traceback
                    traceback.print_exc()
                finally:
                    test_window.destroy()
                
                auth_manager.logout()
            else:
                print(f"❌ فشل تسجيل الدخول: {message}")
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
    
    print("\n🎉 تم إصلاح مشكلة ميزة إدارة قاعدة البيانات!")
    
    print("\n✨ الإصلاحات المطبقة:")
    print("   🔧 إزالة تضارب معاملات height في create_rtl_button")
    print("   🎨 إضافة أنماط أزرار جديدة (warning, info)")
    print("   🛠️ إصلاح جميع الأزرار في قسمي إدارة قاعدة البيانات")
    print("   🧹 تنظيف الكود وإزالة المتغيرات غير المستخدمة")
    
    print("\n🚀 للاستخدام:")
    print("1. شغل التطبيق: python main.py")
    print("2. سجل الدخول كمدير: admin / 123456")
    print("3. انقر على زر '🗄️ إدارة قاعدة البيانات' في الشريط الجانبي")
    print("4. يجب أن تظهر الصفحة بالمحتوى كاملاً بدون أخطاء")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
