#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة "خطأ في عرض بيانات المستخدم"
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_user_display_issue():
    """تشخيص مشكلة عرض المستخدمين"""
    print("🔍 تشخيص مشكلة 'خطأ في عرض بيانات المستخدم'")
    print("=" * 60)
    
    try:
        # 1. اختبار الاتصال بقاعدة البيانات
        print("1. اختبار الاتصال بقاعدة البيانات...")
        from database.connection import db
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        print("✅ الاتصال بقاعدة البيانات ناجح")
        
        # 2. اختبار تسجيل الدخول
        print("\n2. اختبار تسجيل الدخول...")
        from utils.auth import auth_manager
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        print("✅ تسجيل الدخول ناجح")
        
        # 3. اختبار جلب المستخدمين
        print("\n3. اختبار جلب المستخدمين...")
        from database.models import User
        
        users = User.get_all()
        if not users:
            print("❌ لا توجد مستخدمين")
            return False
        
        print(f"✅ تم جلب {len(users)} مستخدم")
        
        # 4. فحص بيانات كل مستخدم
        print("\n4. فحص بيانات كل مستخدم...")
        problematic_users = []
        
        for i, user in enumerate(users):
            print(f"\n   المستخدم {i+1}:")
            
            # فحص البيانات الأساسية
            user_id = user.get('id')
            username = user.get('username')
            full_name = user.get('full_name')
            role = user.get('role')
            is_active = user.get('is_active')
            created_at = user.get('created_at')
            
            print(f"      - ID: {user_id}")
            print(f"      - اسم المستخدم: {username}")
            print(f"      - الاسم الكامل: {full_name}")
            print(f"      - الدور: {role}")
            print(f"      - نشط: {is_active}")
            print(f"      - تاريخ الإنشاء: {created_at}")
            
            # فحص المشاكل المحتملة
            issues = []
            
            if not user_id:
                issues.append("معرف المستخدم مفقود")
            
            if not username:
                issues.append("اسم المستخدم مفقود")
            
            if not isinstance(user, dict):
                issues.append("بيانات المستخدم ليست dictionary")
            
            if role not in ['admin', 'user']:
                issues.append(f"دور غير صحيح: {role}")
            
            if is_active not in [True, False, 0, 1]:
                issues.append(f"حالة النشاط غير صحيحة: {is_active}")
            
            if issues:
                print(f"      ⚠️ مشاكل: {', '.join(issues)}")
                problematic_users.append({
                    'index': i+1,
                    'user': user,
                    'issues': issues
                })
            else:
                print(f"      ✅ البيانات صحيحة")
        
        # 5. محاكاة إنشاء بطاقات المستخدمين
        print("\n5. محاكاة إنشاء بطاقات المستخدمين...")
        
        successful_cards = 0
        failed_cards = 0
        
        for i, user in enumerate(users):
            try:
                print(f"   محاكاة بطاقة المستخدم {i+1}...")
                
                # محاكاة التحقق من البيانات
                if not user or not isinstance(user, dict):
                    print(f"      ❌ بيانات غير صحيحة")
                    failed_cards += 1
                    continue
                
                # محاكاة استخراج البيانات
                username = user.get('username', 'غير محدد')
                fullname = user.get('full_name', 'غير محدد')
                role = user.get('role', 'user')
                is_active = user.get('is_active', True)
                
                # محاكاة معالجة تاريخ الإنشاء
                created_at = user.get('created_at')
                if created_at:
                    try:
                        if hasattr(created_at, 'strftime'):
                            created_at_text = created_at.strftime("%Y-%m-%d %H:%M")
                        else:
                            created_at_text = str(created_at)
                    except:
                        created_at_text = "غير محدد"
                else:
                    created_at_text = "غير محدد"
                
                print(f"      ✅ البطاقة: {username} - {fullname}")
                successful_cards += 1
                
            except Exception as e:
                print(f"      ❌ خطأ في إنشاء البطاقة: {e}")
                failed_cards += 1
        
        print(f"\n   📊 النتائج: {successful_cards} نجح، {failed_cards} فشل")
        
        # 6. اختبار مكونات واجهة المستخدم
        print("\n6. اختبار مكونات واجهة المستخدم...")
        try:
            from config.colors import COLORS, BUTTON_STYLES, ARABIC_TEXT_STYLES
            from config.fonts import create_rtl_label, create_rtl_button
            
            # التحقق من وجود الألوان المطلوبة
            required_colors = ['bg_card', 'border', 'text_primary', 'text_secondary', 'success', 'error', 'bg_light']
            missing_colors = []
            
            for color in required_colors:
                if color not in COLORS:
                    missing_colors.append(color)
            
            if missing_colors:
                print(f"      ⚠️ ألوان مفقودة: {missing_colors}")
            else:
                print("      ✅ جميع الألوان متاحة")
            
            # التحقق من وجود الأنماط المطلوبة
            if 'label' not in ARABIC_TEXT_STYLES:
                print("      ⚠️ نمط label مفقود في ARABIC_TEXT_STYLES")
            else:
                print("      ✅ أنماط النص العربي متاحة")
            
            print("✅ مكونات واجهة المستخدم متاحة")
            
        except Exception as e:
            print(f"❌ خطأ في مكونات واجهة المستخدم: {e}")
            return False
        
        # 7. تقرير المشاكل
        print("\n7. تقرير المشاكل:")
        if problematic_users:
            print(f"⚠️ تم العثور على {len(problematic_users)} مستخدم بمشاكل:")
            for prob_user in problematic_users:
                print(f"   - المستخدم {prob_user['index']}: {', '.join(prob_user['issues'])}")
        else:
            print("✅ لا توجد مشاكل في بيانات المستخدمين")
        
        if failed_cards > 0:
            print(f"⚠️ فشل في إنشاء {failed_cards} بطاقة مستخدم")
        else:
            print("✅ جميع بطاقات المستخدمين يمكن إنشاؤها بنجاح")
        
        auth_manager.logout()
        
        # النتيجة النهائية
        if problematic_users or failed_cards > 0:
            print(f"\n❌ تم العثور على مشاكل تحتاج إلى إصلاح")
            return False
        else:
            print(f"\n✅ لا توجد مشاكل واضحة في البيانات")
            return True
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_components():
    """اختبار مكونات واجهة المستخدم بشكل منفصل"""
    print("\n🎨 اختبار مكونات واجهة المستخدم")
    print("=" * 40)
    
    try:
        import customtkinter as ctk
        from config.colors import COLORS, BUTTON_STYLES, ARABIC_TEXT_STYLES
        from config.fonts import create_rtl_label, create_rtl_button
        
        # إنشاء نافذة تجريبية
        test_window = ctk.CTk()
        test_window.withdraw()
        
        # اختبار إنشاء إطار
        test_frame = ctk.CTkFrame(test_window, fg_color="transparent")
        
        # اختبار إنشاء label
        test_label = create_rtl_label(
            test_frame,
            text="اختبار",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        
        # اختبار إنشاء زر
        test_button = create_rtl_button(
            test_frame,
            text="اختبار",
            **BUTTON_STYLES['primary']
        )
        
        print("✅ جميع مكونات واجهة المستخدم تعمل بشكل صحيح")
        
        # إغلاق النافذة التجريبية
        test_window.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مكونات واجهة المستخدم: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 تشخيص شامل لمشكلة عرض المستخدمين")
    print("=" * 70)
    
    # تشخيص مشكلة عرض المستخدمين
    diagnosis_result = diagnose_user_display_issue()
    
    # اختبار مكونات واجهة المستخدم
    ui_test_result = test_ui_components()
    
    print("\n" + "=" * 70)
    print("📊 نتائج التشخيص:")
    
    if diagnosis_result:
        print("✅ تشخيص بيانات المستخدمين: لا توجد مشاكل")
    else:
        print("❌ تشخيص بيانات المستخدمين: توجد مشاكل")
    
    if ui_test_result:
        print("✅ اختبار مكونات واجهة المستخدم: يعمل بشكل صحيح")
    else:
        print("❌ اختبار مكونات واجهة المستخدم: توجد مشاكل")
    
    if diagnosis_result and ui_test_result:
        print("\n🎉 لا توجد مشاكل واضحة. المشكلة قد تكون في الكود نفسه.")
        print("💡 التوصية: فحص دالة create_user_card وإزالة معالجة الأخطاء غير الضرورية")
    else:
        print("\n⚠️ تم العثور على مشاكل تحتاج إلى إصلاح")
    
    return diagnosis_result and ui_test_result

if __name__ == "__main__":
    main()
