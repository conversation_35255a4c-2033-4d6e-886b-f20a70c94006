-- تحديث جدول المعاملات لإضافة عمود category_name
USE money_manager;

-- إضافة عمود category_name إذا لم يكن موجوداً
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS category_name VARCHAR(100) NULL 
COMMENT 'اسم التصنيف المدخل يدوياً';

-- إنشاء فهرس للبحث السريع في التصنيفات
CREATE INDEX IF NOT EXISTS idx_category_name ON transactions(category_name);

-- عرض هيكل الجدول المحدث
DESCRIBE transactions;

SELECT 'تم تحديث جدول transactions بنجاح!' as message;
