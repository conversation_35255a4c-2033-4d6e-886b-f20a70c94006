#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة المستخدم admin2 وإنشاؤه إذا لم يكن موجوداً
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import mysql.connector
import bcrypt
from datetime import datetime
from config.settings import DATABASE_CONFIG

def fix_admin_user():
    """إصلاح مشكلة المستخدم admin2"""
    print("🔧 إصلاح مشكلة المستخدم admin2")
    print("=" * 40)
    
    try:
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor(dictionary=True)
        
        # البحث عن المستخدم admin2
        cursor.execute("SELECT * FROM users WHERE username = 'admin2'")
        admin2_user = cursor.fetchone()
        
        if admin2_user:
            print("✅ المستخدم admin2 موجود مسبقاً")
            print(f"   الاسم الكامل: {admin2_user['full_name']}")
            print(f"   الدور: {admin2_user['role']}")
            print(f"   نشط: {'نعم' if admin2_user['is_active'] else 'لا'}")
        else:
            print("⚠️ المستخدم admin2 غير موجود - سيتم إنشاؤه...")
            
            # تشفير كلمة المرور
            password = "123456"
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # إنشاء المستخدم admin2
            insert_query = """
                INSERT INTO users (username, password_hash, full_name, role, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(insert_query, (
                'admin2',
                password_hash,
                'المدير الجديد',
                'admin',
                True,
                datetime.now()
            ))
            
            user_id = cursor.lastrowid
            print(f"✅ تم إنشاء المستخدم admin2 بنجاح (ID: {user_id})")
        
        # التحقق من وجود المستخدم admin القديم
        cursor.execute("SELECT * FROM users WHERE username = 'admin'")
        admin_user = cursor.fetchone()
        
        if admin_user:
            print(f"\n📋 المستخدم admin الموجود:")
            print(f"   - ID: {admin_user['id']}")
            print(f"   - الاسم الكامل: {admin_user['full_name']}")
            print(f"   - الدور: {admin_user['role']}")
            print(f"   - نشط: {'نعم' if admin_user['is_active'] else 'لا'}")
            
            # اختياري: تحديث كلمة مرور المستخدم admin إلى 123456 أيضاً
            print("\n🔑 تحديث كلمة مرور المستخدم admin...")
            password_hash = bcrypt.hashpw("123456".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            cursor.execute(
                "UPDATE users SET password_hash = %s WHERE username = 'admin'",
                (password_hash,)
            )
            print("✅ تم تحديث كلمة مرور المستخدم admin")
        
        # عرض جميع المستخدمين
        print("\n👥 جميع المستخدمين في النظام:")
        cursor.execute("SELECT username, full_name, role, is_active FROM users ORDER BY created_at")
        all_users = cursor.fetchall()
        
        for user in all_users:
            status = "نشط" if user['is_active'] else "معطل"
            print(f"   - {user['username']} ({user['full_name']}) - {user['role']} - {status}")
        
        connection.commit()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح المستخدم admin2: {e}")
        return False

def test_login():
    """اختبار تسجيل الدخول بعد الإصلاح"""
    print("\n🔍 اختبار تسجيل الدخول...")
    
    try:
        from utils.auth import auth_manager
        
        # اختبار تسجيل الدخول بـ admin2
        print("   اختبار admin2...")
        success, message = auth_manager.login("admin2", "123456")
        if success:
            print("✅ تسجيل الدخول بـ admin2 ناجح")
            auth_manager.logout()
        else:
            print(f"❌ فشل تسجيل الدخول بـ admin2: {message}")
        
        # اختبار تسجيل الدخول بـ admin
        print("   اختبار admin...")
        success, message = auth_manager.login("admin", "123456")
        if success:
            print("✅ تسجيل الدخول بـ admin ناجح")
            auth_manager.logout()
        else:
            print(f"❌ فشل تسجيل الدخول بـ admin: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    if fix_admin_user():
        if test_login():
            print("\n🎉 تم إصلاح المشكلة بنجاح!")
            print("\n📋 بيانات الاعتماد المتاحة:")
            print("1. admin2 / 123456 (المستخدم المطلوب)")
            print("2. admin / 123456 (المستخدم الأصلي)")
            print("\n🚀 يمكنك الآن تسجيل الدخول واستخدام ميزة إدارة المستخدمين")
        else:
            print("\n⚠️ تم الإصلاح ولكن فشل اختبار تسجيل الدخول")
    else:
        print("\n❌ فشل في إصلاح المشكلة")

if __name__ == "__main__":
    main()
