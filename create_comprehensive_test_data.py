#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية شاملة للنظام
إنشاء حسابات ومعاملات متنوعة لاختبار جميع وظائف النظام
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from database.models import Account, Currency, Transaction, Transfer
from utils.auth import auth_manager
from datetime import datetime, timedelta
import random

class ComprehensiveTestDataCreator:
    """منشئ البيانات التجريبية الشاملة"""
    
    def __init__(self):
        self.created_accounts = []
        self.created_transactions = []
        self.created_transfers = []
        
        # تسجيل دخول تلقائي
        users = db.execute_query("SELECT * FROM users LIMIT 1")
        if users:
            auth_manager.current_user = users[0]
            print(f"✅ تم تسجيل الدخول كـ: {users[0].get('username')}")
        else:
            print("❌ لا يوجد مستخدمين في قاعدة البيانات")
            return
    
    def run_full_creation(self):
        """تشغيل إنشاء البيانات التجريبية الشاملة"""
        print("🏗️ بدء إنشاء البيانات التجريبية الشاملة...")
        print("=" * 60)
        
        # 1. إنشاء الحسابات الجديدة
        self.create_test_accounts()
        
        # 2. إنشاء معاملات الواردات
        self.create_income_transactions()
        
        # 3. إنشاء معاملات المصروفات
        self.create_expense_transactions()
        
        # 4. إنشاء التحويلات
        self.create_transfers()
        
        # 5. عرض ملخص البيانات المُنشأة
        self.display_summary()
        
        print("\n" + "=" * 60)
        print("🎉 تم إنشاء البيانات التجريبية بنجاح!")
    
    def create_test_accounts(self):
        """إنشاء 3 حسابات جديدة بعملات مختلفة"""
        print("🏦 إنشاء الحسابات التجريبية...")
        
        # بيانات الحسابات الجديدة
        accounts_data = [
            {
                'name': 'حساب الراتب',
                'currency_id': 1,  # ريال سعودي
                'initial_balance': 15000.0,
                'description': 'حساب استقبال الراتب الشهري'
            },
            {
                'name': 'حساب المصروفات',
                'currency_id': 2,  # دولار أمريكي
                'initial_balance': 2500.0,
                'description': 'حساب المصروفات اليومية والشخصية'
            },
            {
                'name': 'حساب الاستثمار',
                'currency_id': 5,  # درهم إماراتي
                'initial_balance': 50000.0,
                'description': 'حساب الاستثمارات والمدخرات طويلة المدى'
            }
        ]
        
        user_id = auth_manager.current_user['id']
        
        for account_data in accounts_data:
            try:
                # إنشاء الحساب
                account_id = Account.create(
                    user_id=user_id,
                    name=account_data['name'],
                    currency_id=account_data['currency_id'],
                    description=account_data['description']
                )
                
                if account_id > 0:
                    print(f"   ✅ تم إنشاء حساب: {account_data['name']} (ID: {account_id})")
                    
                    # إضافة الرصيد الابتدائي
                    if account_data['initial_balance'] > 0:
                        balance_result = Account.update_balance_for_currency(
                            account_id, 
                            account_data['currency_id'], 
                            account_data['initial_balance']
                        )
                        
                        if balance_result > 0:
                            # الحصول على رمز العملة
                            currency = db.execute_query(
                                "SELECT symbol FROM currencies WHERE id = %s", 
                                (account_data['currency_id'],)
                            )
                            symbol = currency[0]['symbol'] if currency else ''
                            
                            print(f"      💰 رصيد ابتدائي: {account_data['initial_balance']} {symbol}")
                            
                            self.created_accounts.append({
                                'id': account_id,
                                'name': account_data['name'],
                                'currency_id': account_data['currency_id'],
                                'balance': account_data['initial_balance'],
                                'symbol': symbol
                            })
                        else:
                            print(f"      ❌ فشل في إضافة الرصيد الابتدائي")
                else:
                    print(f"   ❌ فشل في إنشاء حساب: {account_data['name']}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في إنشاء حساب {account_data['name']}: {e}")
        
        print(f"✅ تم إنشاء {len(self.created_accounts)} حساب جديد")
    
    def create_income_transactions(self):
        """إنشاء معاملات واردات متنوعة"""
        print("\n💰 إنشاء معاملات الواردات...")
        
        if not self.created_accounts:
            print("❌ لا توجد حسابات لإنشاء معاملات")
            return
        
        # بيانات الواردات التجريبية
        income_data = [
            {'amount': 12000, 'description': 'راتب شهر ديسمبر', 'days_ago': 5},
            {'amount': 3000, 'description': 'مكافأة نهاية السنة', 'days_ago': 10},
            {'amount': 1500, 'description': 'عمولة مبيعات', 'days_ago': 15},
            {'amount': 800, 'description': 'أرباح استثمار', 'days_ago': 20},
            {'amount': 2200, 'description': 'عمل إضافي', 'days_ago': 25},
            {'amount': 500, 'description': 'هدية نقدية', 'days_ago': 30},
            {'amount': 1200, 'description': 'استرداد ضريبي', 'days_ago': 35},
            {'amount': 4000, 'description': 'عائد إيجار', 'days_ago': 40}
        ]
        
        user_id = auth_manager.current_user['id']
        
        for i, income in enumerate(income_data):
            try:
                # اختيار حساب عشوائي
                account = random.choice(self.created_accounts)
                
                # حساب تاريخ المعاملة
                transaction_date = datetime.now() - timedelta(days=income['days_ago'])
                
                # إنشاء المعاملة
                transaction_id = Transaction.create(
                    user_id=user_id,
                    account_id=account['id'],
                    currency_id=account['currency_id'],
                    transaction_type='income',
                    amount=income['amount'],
                    description=income['description'],
                    transaction_date=transaction_date.date()
                )
                
                if transaction_id > 0:
                    print(f"   ✅ وارد: {income['amount']} {account['symbol']} - {income['description']}")
                    
                    self.created_transactions.append({
                        'id': transaction_id,
                        'type': 'income',
                        'amount': income['amount'],
                        'account': account['name'],
                        'description': income['description']
                    })
                else:
                    print(f"   ❌ فشل في إنشاء وارد: {income['description']}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في إنشاء وارد {income['description']}: {e}")
        
        income_count = len([t for t in self.created_transactions if t['type'] == 'income'])
        print(f"✅ تم إنشاء {income_count} معاملة وارد")
    
    def create_expense_transactions(self):
        """إنشاء معاملات مصروفات متنوعة"""
        print("\n💸 إنشاء معاملات المصروفات...")
        
        if not self.created_accounts:
            print("❌ لا توجد حسابات لإنشاء معاملات")
            return
        
        # بيانات المصروفات التجريبية
        expense_data = [
            {'amount': 2500, 'description': 'إيجار الشقة', 'days_ago': 3},
            {'amount': 800, 'description': 'فاتورة الكهرباء', 'days_ago': 7},
            {'amount': 1200, 'description': 'تسوق البقالة', 'days_ago': 12},
            {'amount': 300, 'description': 'وقود السيارة', 'days_ago': 18},
            {'amount': 150, 'description': 'اشتراك الإنترنت', 'days_ago': 22},
            {'amount': 450, 'description': 'عشاء في مطعم', 'days_ago': 28},
            {'amount': 200, 'description': 'صيانة السيارة', 'days_ago': 33},
            {'amount': 600, 'description': 'ملابس جديدة', 'days_ago': 38},
            {'amount': 100, 'description': 'اشتراك الجيم', 'days_ago': 42},
            {'amount': 350, 'description': 'أدوية وصيدلية', 'days_ago': 45}
        ]
        
        user_id = auth_manager.current_user['id']
        
        for expense in expense_data:
            try:
                # اختيار حساب عشوائي
                account = random.choice(self.created_accounts)
                
                # حساب تاريخ المعاملة
                transaction_date = datetime.now() - timedelta(days=expense['days_ago'])
                
                # إنشاء المعاملة
                transaction_id = Transaction.create(
                    user_id=user_id,
                    account_id=account['id'],
                    currency_id=account['currency_id'],
                    transaction_type='expense',
                    amount=expense['amount'],
                    description=expense['description'],
                    transaction_date=transaction_date.date()
                )
                
                if transaction_id > 0:
                    print(f"   ✅ مصروف: {expense['amount']} {account['symbol']} - {expense['description']}")
                    
                    self.created_transactions.append({
                        'id': transaction_id,
                        'type': 'expense',
                        'amount': expense['amount'],
                        'account': account['name'],
                        'description': expense['description']
                    })
                else:
                    print(f"   ❌ فشل في إنشاء مصروف: {expense['description']}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في إنشاء مصروف {expense['description']}: {e}")
        
        expense_count = len([t for t in self.created_transactions if t['type'] == 'expense'])
        print(f"✅ تم إنشاء {expense_count} معاملة مصروف")
    
    def create_transfers(self):
        """إنشاء تحويلات بين الحسابات"""
        print("\n🔄 إنشاء التحويلات...")
        
        if len(self.created_accounts) < 2:
            print("❌ يجب وجود حسابين على الأقل لإنشاء تحويلات")
            return
        
        # بيانات التحويلات التجريبية
        transfer_data = [
            {'amount': 5000, 'description': 'تحويل للاستثمار', 'days_ago': 8},
            {'amount': 1500, 'description': 'تحويل للمصروفات', 'days_ago': 16},
            {'amount': 3000, 'description': 'توفير شهري', 'days_ago': 24},
            {'amount': 800, 'description': 'تحويل طارئ', 'days_ago': 32},
            {'amount': 2000, 'description': 'إعادة توزيع الأموال', 'days_ago': 40}
        ]
        
        user_id = auth_manager.current_user['id']
        
        for transfer in transfer_data:
            try:
                # اختيار حسابين مختلفين عشوائياً
                from_account = random.choice(self.created_accounts)
                to_accounts = [acc for acc in self.created_accounts if acc['id'] != from_account['id']]
                to_account = random.choice(to_accounts)
                
                # حساب تاريخ التحويل
                transfer_date = datetime.now() - timedelta(days=transfer['days_ago'])
                
                # إنشاء التحويل
                transfer_id = Transfer.create(
                    user_id=user_id,
                    from_account_id=from_account['id'],
                    to_account_id=to_account['id'],
                    from_amount=transfer['amount'],
                    from_currency_id=from_account['currency_id'],
                    to_amount=transfer['amount'],  # نفس المبلغ للتبسيط
                    to_currency_id=to_account['currency_id'],
                    description=transfer['description'],
                    transfer_date=transfer_date.date()
                )
                
                if transfer_id > 0:
                    print(f"   ✅ تحويل: {transfer['amount']} من {from_account['name']} إلى {to_account['name']}")
                    print(f"      📝 {transfer['description']}")
                    
                    self.created_transfers.append({
                        'id': transfer_id,
                        'amount': transfer['amount'],
                        'from_account': from_account['name'],
                        'to_account': to_account['name'],
                        'description': transfer['description']
                    })
                else:
                    print(f"   ❌ فشل في إنشاء تحويل: {transfer['description']}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في إنشاء تحويل {transfer['description']}: {e}")
        
        print(f"✅ تم إنشاء {len(self.created_transfers)} تحويل")
    
    def display_summary(self):
        """عرض ملخص البيانات المُنشأة"""
        print("\n📊 ملخص البيانات التجريبية المُنشأة:")
        
        # الحسابات
        print(f"\n🏦 الحسابات ({len(self.created_accounts)}):")
        for account in self.created_accounts:
            print(f"   - {account['name']}: {account['balance']} {account['symbol']}")
        
        # المعاملات
        income_count = len([t for t in self.created_transactions if t['type'] == 'income'])
        expense_count = len([t for t in self.created_transactions if t['type'] == 'expense'])
        
        print(f"\n💰 المعاملات:")
        print(f"   - الواردات: {income_count}")
        print(f"   - المصروفات: {expense_count}")
        print(f"   - إجمالي المعاملات: {len(self.created_transactions)}")
        
        # التحويلات
        print(f"\n🔄 التحويلات: {len(self.created_transfers)}")
        
        # إحصائيات إجمالية
        total_income = sum(t['amount'] for t in self.created_transactions if t['type'] == 'income')
        total_expense = sum(t['amount'] for t in self.created_transactions if t['type'] == 'expense')
        
        print(f"\n📈 الإحصائيات:")
        print(f"   - إجمالي الواردات: {total_income:,.2f}")
        print(f"   - إجمالي المصروفات: {total_expense:,.2f}")
        print(f"   - الفرق: {total_income - total_expense:,.2f}")

def main():
    """الدالة الرئيسية"""
    print("🏗️ منشئ البيانات التجريبية الشاملة")
    print("=" * 60)
    print("الهدف: إنشاء حسابات ومعاملات متنوعة لاختبار جميع وظائف النظام")
    print()
    
    try:
        # إنشاء البيانات التجريبية
        creator = ComprehensiveTestDataCreator()
        creator.run_full_creation()
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
