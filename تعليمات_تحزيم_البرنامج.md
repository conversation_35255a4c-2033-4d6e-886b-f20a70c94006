# تعليمات تحزيم برنامج مدير الأموال

## المتطلبات الأساسية

1. **تثبيت PyInstaller**
   - للتثبيت، افتح موجه الأوامر (Command Prompt) وقم بتنفيذ:
   ```
   pip install pyinstaller
   ```

2. **تثبيت Inno Setup**
   - قم بتحميل وتثبيت Inno Setup من الموقع الرسمي: [https://jrsoftware.org/isdl.php](https://jrsoftware.org/isdl.php)
   - تأكد من إضافته إلى متغير PATH في النظام أثناء التثبيت

## خطوات تحزيم البرنامج

### الخطوة 1: بناء الملف التنفيذي باستخدام PyInstaller

1. افتح موجه الأوامر (Command Prompt)
2. انتقل إلى مجلد البرنامج:
   ```
   cd "c:\Users\<USER>\Desktop\money manager"
   ```
3. قم بتنفيذ أمر PyInstaller باستخدام ملف المواصفات الموجود:
   ```
   pyinstaller --noconfirm "Money Manager.spec"
   ```
4. إذا فشل الأمر السابق، جرب استخدام ملف المواصفات البديل:
   ```
   pyinstaller --noconfirm "money_manager.spec"
   ```
5. تحقق من وجود الملف التنفيذي في المجلد `dist\Money Manager\Money Manager.exe`

### الخطوة 2: إنشاء حزمة التثبيت باستخدام Inno Setup

1. تأكد من وجود مجلد الإخراج للمثبت، وإذا لم يكن موجوداً قم بإنشائه:
   ```
   if not exist "installer_output" mkdir "installer_output"
   ```
2. قم بتشغيل Inno Setup Compiler (ISCC) لإنشاء حزمة التثبيت:
   ```
   "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" installer.iss
   ```
   أو
   ```
   "C:\Program Files\Inno Setup 6\ISCC.exe" installer.iss
   ```
   (قد يختلف المسار حسب مكان تثبيت Inno Setup على جهازك)

3. بعد اكتمال العملية، ستجد ملف التثبيت في مجلد `installer_output\MoneyManagerSetup.exe`

## استخدام ملف build_installer.bat

يمكنك أيضاً استخدام ملف `build_installer.bat` الذي قمنا بإنشائه لتنفيذ جميع الخطوات السابقة تلقائياً:

1. انقر نقراً مزدوجاً على ملف `build_installer.bat`
2. انتظر حتى تكتمل العملية
3. ستجد الملف التنفيذي في مجلد `dist\Money Manager` وحزمة التثبيت في مجلد `installer_output`

## حل المشكلات الشائعة

### مشكلة: عدم العثور على PyInstaller

- قم بتثبيته يدوياً باستخدام: `pip install pyinstaller`

### مشكلة: عدم العثور على Inno Setup Compiler

- تأكد من تثبيت Inno Setup بشكل صحيح
- تحقق من إضافته إلى متغير PATH في النظام
- استخدم المسار الكامل لـ ISCC.exe كما هو موضح أعلاه

### مشكلة: فشل بناء الملف التنفيذي

- تحقق من ملفات .spec والمتطلبات
- تأكد من تثبيت جميع المكتبات المطلوبة: `pip install -r requirements.txt`

### مشكلة: فشل إنشاء حزمة التثبيت

- تحقق من ملف installer.iss
- تأكد من وجود الملف التنفيذي في المسار الصحيح

## ملاحظات هامة

- تأكد من تحديث إصدار البرنامج في ملف installer.iss قبل إنشاء حزمة تثبيت جديدة
- يمكنك تخصيص إعدادات المثبت عن طريق تعديل ملف installer.iss
- احرص على اختبار حزمة التثبيت قبل توزيعها على المستخدمين