@echo off
chcp 65001 > nul
title برنامج إدارة الأموال الشخصية

echo ========================================
echo 🏦 برنامج إدارة الأموال الشخصية
echo ========================================
echo 📅 التاريخ: %date% %time%
echo 💻 نظام التشغيل: Windows
echo ========================================
echo.

echo 🔍 فحص Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo.
    echo 💡 خيارات الحل:
    echo    1. تحميل Python من: https://python.org/downloads/
    echo    2. تأكد من إضافة Python إلى PATH أثناء التثبيت
    echo    3. أعد تشغيل Command Prompt بعد التثبيت
    echo.
    echo ❓ هل تريد فتح صفحة تحميل Python؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" start https://python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python %PYTHON_VERSION% موجود
echo.

echo 🔍 فحص pip...
python -m pip --version > nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo 💡 محاولة إصلاح pip...
    python -m ensurepip --upgrade
    if errorlevel 1 (
        echo ❌ فشل في إصلاح pip
        pause
        exit /b 1
    )
)
echo ✅ pip متوفر
echo.

echo 🔍 فحص المتطلبات الأساسية...
python -c "import sys; print('Python version check:', sys.version_info >= (3, 8))" 2>nul
python -c "import customtkinter, mysql.connector, bcrypt" > nul 2>&1
if errorlevel 1 (
    echo ⚠️  بعض المتطلبات غير مثبتة
    echo.
    echo ❓ هل تريد تثبيت المتطلبات تلقائياً؟ (y/n)
    set /p install_choice=
    if /i "%install_choice%"=="y" (
        echo.
        echo 📦 بدء تثبيت المتطلبات...
        echo ⏳ هذا قد يستغرق بضع دقائق...
        echo.

        if exist requirements.txt (
            echo 📋 تثبيت من requirements.txt...
            python -m pip install -r requirements.txt
        ) else (
            echo 📦 تثبيت المكتبات الأساسية...
            python -m pip install mysql-connector-python customtkinter Pillow bcrypt python-dateutil
        )

        if errorlevel 1 (
            echo.
            echo ❌ فشل في تثبيت بعض المتطلبات
            echo 💡 جرب تشغيل: python install_requirements.py
            echo 💡 أو راجع ملف INSTALLATION_GUIDE.md
            pause
            exit /b 1
        )

        echo ✅ تم تثبيت المتطلبات بنجاح
    ) else (
        echo ⚠️  لن يتم تثبيت المتطلبات
        echo 💡 لتثبيتها لاحقاً: python install_requirements.py
        echo.
    )
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo 🔍 فحص ملفات المشروع...
if not exist main.py (
    echo ❌ ملف main.py غير موجود
    echo 💡 تأكد من وجود جميع ملفات المشروع
    pause
    exit /b 1
)

if not exist config\settings.py (
    echo ❌ ملفات الإعدادات غير موجودة
    echo 💡 تأكد من وجود مجلد config وملفاته
    pause
    exit /b 1
)

echo ✅ ملفات المشروع موجودة
echo.

echo 🚀 بدء تشغيل البرنامج...
echo 💡 إذا كانت هذه المرة الأولى، ستحتاج إلى:
echo    • كلمة مرور MySQL
echo    • إنشاء حساب المدير الأول
echo.
echo ⏳ جاري التشغيل...
echo.

python main.py

set EXIT_CODE=%errorlevel%

echo.
if %EXIT_CODE% equ 0 (
    echo ✅ تم إغلاق البرنامج بنجاح
) else (
    echo ❌ حدث خطأ أثناء تشغيل البرنامج (رمز الخطأ: %EXIT_CODE%)
    echo.
    echo 🔍 خطوات استكشاف الأخطاء:
    echo    1. راجع ملفات السجل في مجلد logs\
    echo    2. تأكد من تشغيل MySQL Server
    echo    3. تحقق من كلمة مرور MySQL
    echo    4. شغل: python check_requirements.py
    echo.
    if exist logs\ (
        echo 📋 آخر ملفات السجل:
        dir /b /o-d logs\*.log 2>nul | head -3
    )
)

echo.
echo 👋 شكراً لاستخدام برنامج إدارة الأموال
echo 💡 للمساعدة: راجع README.md أو INSTALLATION_GUIDE.md
pause
