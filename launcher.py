#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل برنامج إدارة الأموال الشخصية
Personal Money Manager Launcher
"""

import sys
import os
import subprocess
import platform
from datetime import datetime

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 70)
    print("🏦 مشغل برنامج إدارة الأموال الشخصية")
    print("   Personal Money Manager Launcher")
    print("=" * 70)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💻 نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"🐍 Python: {sys.version.split()[0]}")
    print("=" * 70)

def check_python_version():
    """فحص إصدار Python"""
    print("\n🔍 فحص إصدار Python...")
    
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} متوافق")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} غير متوافق")
        print("💡 يتطلب Python 3.8 أو أحدث")
        print("🔗 تحميل من: https://python.org/downloads/")
        return False

def check_main_file():
    """فحص وجود الملف الرئيسي"""
    print("\n🔍 فحص ملفات المشروع...")
    
    required_files = [
        'main.py',
        'config/settings.py',
        'database/connection.py',
        'gui/login_window.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  ملفات مفقودة: {len(missing_files)}")
        print("💡 تأكد من نسخ جميع ملفات المشروع")
        return False
    
    print("✅ جميع الملفات الأساسية موجودة")
    return True

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("\n🔍 فحص المتطلبات الأساسية...")
    
    required_modules = [
        ('mysql.connector', 'mysql-connector-python'),
        ('customtkinter', 'customtkinter'),
        ('PIL', 'Pillow'),
        ('bcrypt', 'bcrypt')
    ]
    
    missing_modules = []
    
    for module_name, package_name in required_modules:
        try:
            if module_name == 'mysql.connector':
                import mysql.connector
            elif module_name == 'PIL':
                from PIL import Image
            else:
                __import__(module_name)
            
            print(f"✅ {package_name}")
            
        except ImportError:
            print(f"❌ {package_name}")
            missing_modules.append(package_name)
        except Exception as e:
            print(f"⚠️  {package_name}: {e}")
            missing_modules.append(package_name)
    
    return missing_modules

def install_requirements(missing_modules):
    """تثبيت المتطلبات المفقودة"""
    if not missing_modules:
        return True
    
    print(f"\n⚠️  مكتبات مفقودة: {len(missing_modules)}")
    print("📦 المكتبات المطلوبة:")
    for module in missing_modules:
        print(f"   • {module}")
    
    print("\n❓ خيارات التثبيت:")
    print("1. تثبيت تلقائي من requirements.txt")
    print("2. تثبيت المكتبات الأساسية فقط")
    print("3. تشغيل مثبت المتطلبات المتقدم")
    print("4. تخطي وتشغيل البرنامج")
    print("5. إلغاء")
    
    try:
        choice = input("\nاختر رقم الخيار (1-5): ").strip()
        
        if choice == "1":
            return install_from_requirements_file()
        elif choice == "2":
            return install_basic_modules(missing_modules)
        elif choice == "3":
            return run_advanced_installer()
        elif choice == "4":
            print("⚠️  سيتم تشغيل البرنامج بدون تثبيت المتطلبات")
            return True
        elif choice == "5":
            print("❌ تم إلغاء التشغيل")
            return False
        else:
            print("❌ اختيار غير صحيح")
            return False
            
    except KeyboardInterrupt:
        print("\n❌ تم إلغاء التشغيل")
        return False

def install_from_requirements_file():
    """تثبيت من ملف requirements.txt"""
    if not os.path.exists('requirements.txt'):
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    print("\n📋 تثبيت من requirements.txt...")
    print("⏳ قد يستغرق هذا بضع دقائق...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ تم تثبيت جميع المتطلبات بنجاح")
            return True
        else:
            print("❌ فشل في تثبيت بعض المتطلبات")
            print(f"خطأ: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ انتهت مهلة التثبيت")
        return False
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False

def install_basic_modules(missing_modules):
    """تثبيت المكتبات الأساسية فقط"""
    print("\n📦 تثبيت المكتبات الأساسية...")
    
    basic_modules = [
        'mysql-connector-python',
        'customtkinter', 
        'Pillow',
        'bcrypt',
        'python-dateutil'
    ]
    
    # تثبيت المكتبات المفقودة من القائمة الأساسية
    modules_to_install = [m for m in basic_modules if m in missing_modules]
    
    if not modules_to_install:
        print("✅ جميع المكتبات الأساسية مثبتة")
        return True
    
    for module in modules_to_install:
        print(f"📦 تثبيت {module}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", module
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ تم تثبيت {module}")
            else:
                print(f"❌ فشل تثبيت {module}")
                
        except Exception as e:
            print(f"❌ خطأ في تثبيت {module}: {e}")
    
    return True

def run_advanced_installer():
    """تشغيل مثبت المتطلبات المتقدم"""
    if os.path.exists('install_requirements.py'):
        print("\n🛠️  تشغيل مثبت المتطلبات المتقدم...")
        try:
            subprocess.run([sys.executable, 'install_requirements.py'])
            return True
        except Exception as e:
            print(f"❌ خطأ في تشغيل المثبت المتقدم: {e}")
            return False
    else:
        print("❌ ملف install_requirements.py غير موجود")
        return False

def run_main_program():
    """تشغيل البرنامج الرئيسي"""
    print("\n🚀 بدء تشغيل برنامج إدارة الأموال...")
    print("💡 إذا كانت هذه المرة الأولى:")
    print("   • ستحتاج كلمة مرور MySQL")
    print("   • سيتم إنشاء قاعدة البيانات تلقائياً")
    print("   • ستحتاج إنشاء حساب المدير الأول")
    print("\n⏳ جاري التشغيل...")
    
    try:
        # تشغيل البرنامج الرئيسي
        result = subprocess.run([sys.executable, 'main.py'])
        
        if result.returncode == 0:
            print("\n✅ تم إغلاق البرنامج بنجاح")
        else:
            print(f"\n⚠️  البرنامج أُغلق برمز خطأ: {result.returncode}")
            print("💡 راجع ملفات السجل في مجلد logs/ للتفاصيل")
            
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البرنامج: {e}")

def show_help():
    """عرض المساعدة"""
    print("\n💡 للمساعدة والدعم:")
    print("   📖 README.md - الدليل الشامل")
    print("   🛠️  INSTALLATION_GUIDE.md - دليل التثبيت")
    print("   ⚡ QUICK_START.md - دليل البدء السريع")
    print("   🔍 python check_requirements.py - فحص المتطلبات")
    print("   📦 python install_requirements.py - تثبيت المتطلبات")

def main():
    """الدالة الرئيسية"""
    try:
        print_header()
        
        # فحص إصدار Python
        if not check_python_version():
            return
        
        # فحص ملفات المشروع
        if not check_main_file():
            return
        
        # فحص المتطلبات
        missing_modules = check_requirements()
        
        # تثبيت المتطلبات إذا لزم الأمر
        if missing_modules:
            if not install_requirements(missing_modules):
                return
        else:
            print("✅ جميع المتطلبات الأساسية متوفرة")
        
        # تشغيل البرنامج
        run_main_program()
        
        # عرض المساعدة
        show_help()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف المشغل بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    finally:
        print("\n👋 شكراً لاستخدام برنامج إدارة الأموال")

if __name__ == "__main__":
    main()
    input("\n🔄 اضغط Enter للخروج...")
