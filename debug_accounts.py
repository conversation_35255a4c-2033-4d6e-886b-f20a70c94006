#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from database.models import Account

try:
    print("🔍 اختبار استرجاع الحسابات...")
    
    user_id = 1
    accounts = Account.get_by_user(user_id)
    
    print(f"✅ تم العثور على {len(accounts)} حساب")
    
    for account in accounts:
        print(f"\n🏦 حساب: {account['name']}")
        print(f"   - المعرف: {account['id']}")
        print(f"   - الأرصدة: {len(account.get('balances', []))}")
        
        for balance in account.get('balances', []):
            print(f"     • {balance['name']}: {balance['balance']} {balance['symbol']}")

except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
