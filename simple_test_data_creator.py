#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
منشئ بيانات تجريبية مبسط
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from database.models import Account
from utils.auth import auth_manager

def create_test_accounts():
    """إنشاء حسابات تجريبية"""
    print("🏦 إنشاء الحسابات التجريبية...")
    
    # تسجيل دخول تلقائي
    users = db.execute_query("SELECT * FROM users LIMIT 1")
    if users:
        auth_manager.current_user = users[0]
        user_id = users[0]['id']
        print(f"✅ تم تسجيل الدخول كـ: {users[0].get('username')}")
    else:
        print("❌ لا يوجد مستخدمين")
        return
    
    # بيانات الحسابات
    accounts_data = [
        {
            'name': 'حساب الراتب',
            'currency_id': 1,  # ريال سعودي
            'initial_balance': 15000.0,
            'description': 'حساب استقبال الراتب الشهري'
        },
        {
            'name': 'حساب المصروفات',
            'currency_id': 2,  # دولار أمريكي
            'initial_balance': 2500.0,
            'description': 'حساب المصروفات اليومية والشخصية'
        },
        {
            'name': 'حساب الاستثمار',
            'currency_id': 5,  # درهم إماراتي
            'initial_balance': 50000.0,
            'description': 'حساب الاستثمارات والمدخرات طويلة المدى'
        }
    ]
    
    created_accounts = []
    
    for account_data in accounts_data:
        try:
            print(f"   🔄 إنشاء حساب: {account_data['name']}")
            
            # إنشاء الحساب
            account_id = Account.create(
                user_id=user_id,
                name=account_data['name'],
                currency_id=account_data['currency_id'],
                description=account_data['description']
            )
            
            if account_id > 0:
                print(f"   ✅ تم إنشاء الحساب (ID: {account_id})")
                
                # إضافة الرصيد الابتدائي
                if account_data['initial_balance'] > 0:
                    balance_result = Account.update_balance_for_currency(
                        account_id, 
                        account_data['currency_id'], 
                        account_data['initial_balance']
                    )
                    
                    if balance_result > 0:
                        # الحصول على رمز العملة
                        currency = db.execute_query(
                            "SELECT symbol FROM currencies WHERE id = %s", 
                            (account_data['currency_id'],)
                        )
                        symbol = currency[0]['symbol'] if currency else ''
                        
                        print(f"   💰 رصيد ابتدائي: {account_data['initial_balance']} {symbol}")
                        
                        created_accounts.append({
                            'id': account_id,
                            'name': account_data['name'],
                            'currency_id': account_data['currency_id'],
                            'balance': account_data['initial_balance'],
                            'symbol': symbol
                        })
                    else:
                        print(f"   ❌ فشل في إضافة الرصيد الابتدائي")
            else:
                print(f"   ❌ فشل في إنشاء الحساب")
                
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء حساب {account_data['name']}: {e}")
    
    print(f"\n✅ تم إنشاء {len(created_accounts)} حساب جديد")
    
    # عرض الحسابات المُنشأة
    for account in created_accounts:
        print(f"   - {account['name']}: {account['balance']} {account['symbol']}")
    
    return created_accounts

def create_sample_transactions():
    """إنشاء معاملات تجريبية مبسطة"""
    print("\n💰 إنشاء معاملات تجريبية...")
    
    try:
        # الحصول على الحسابات الموجودة
        user_id = auth_manager.current_user['id']
        accounts = db.execute_query("SELECT * FROM accounts WHERE user_id = %s", (user_id,))
        
        if not accounts:
            print("❌ لا توجد حسابات لإنشاء معاملات")
            return
        
        print(f"📋 وجدت {len(accounts)} حساب")
        
        # إنشاء معاملات واردات مبسطة
        income_data = [
            {'amount': 12000, 'description': 'راتب شهر ديسمبر'},
            {'amount': 3000, 'description': 'مكافأة نهاية السنة'},
            {'amount': 1500, 'description': 'عمولة مبيعات'}
        ]
        
        for income in income_data:
            try:
                account = accounts[0]  # استخدام أول حساب
                
                # إدراج مباشر في قاعدة البيانات
                query = """
                    INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, description, transaction_date)
                    VALUES (%s, %s, %s, %s, %s, %s, CURDATE())
                """
                
                result = db.execute_insert(query, (
                    user_id, account['id'], 'income', income['amount'], 
                    account['currency_id'], income['description']
                ))
                
                if result > 0:
                    print(f"   ✅ وارد: {income['amount']} - {income['description']}")
                    
                    # تحديث الرصيد
                    Account.update_balance_for_currency(
                        account['id'], account['currency_id'], income['amount']
                    )
                else:
                    print(f"   ❌ فشل في إنشاء وارد: {income['description']}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في إنشاء وارد {income['description']}: {e}")
        
        # إنشاء معاملات مصروفات مبسطة
        expense_data = [
            {'amount': 2500, 'description': 'إيجار الشقة'},
            {'amount': 800, 'description': 'فاتورة الكهرباء'},
            {'amount': 1200, 'description': 'تسوق البقالة'}
        ]
        
        for expense in expense_data:
            try:
                account = accounts[1] if len(accounts) > 1 else accounts[0]  # استخدام ثاني حساب
                
                # إدراج مباشر في قاعدة البيانات
                query = """
                    INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, description, transaction_date)
                    VALUES (%s, %s, %s, %s, %s, %s, CURDATE())
                """
                
                result = db.execute_insert(query, (
                    user_id, account['id'], 'expense', expense['amount'], 
                    account['currency_id'], expense['description']
                ))
                
                if result > 0:
                    print(f"   ✅ مصروف: {expense['amount']} - {expense['description']}")
                    
                    # تحديث الرصيد (خصم)
                    Account.update_balance_for_currency(
                        account['id'], account['currency_id'], -expense['amount']
                    )
                else:
                    print(f"   ❌ فشل في إنشاء مصروف: {expense['description']}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في إنشاء مصروف {expense['description']}: {e}")
        
        print("✅ تم إنشاء المعاملات التجريبية")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المعاملات: {e}")

def display_final_summary():
    """عرض ملخص البيانات النهائية"""
    print("\n📊 ملخص البيانات النهائية:")
    
    try:
        user_id = auth_manager.current_user['id']
        
        # الحسابات
        accounts = db.execute_query("SELECT * FROM accounts WHERE user_id = %s", (user_id,))
        print(f"\n🏦 الحسابات ({len(accounts)}):")
        
        for account in accounts:
            # الحصول على الأرصدة
            balances = db.execute_query("""
                SELECT ab.balance, c.symbol 
                FROM account_balances ab 
                JOIN currencies c ON ab.currency_id = c.id 
                WHERE ab.account_id = %s
            """, (account['id'],))
            
            balance_text = ", ".join([f"{b['balance']} {b['symbol']}" for b in balances]) if balances else "0"
            print(f"   - {account['name']}: {balance_text}")
        
        # المعاملات
        transactions = db.execute_query("SELECT * FROM transactions WHERE user_id = %s", (user_id,))
        income_count = len([t for t in transactions if t['transaction_type'] == 'income'])
        expense_count = len([t for t in transactions if t['transaction_type'] == 'expense'])
        
        print(f"\n💰 المعاملات:")
        print(f"   - الواردات: {income_count}")
        print(f"   - المصروفات: {expense_count}")
        print(f"   - إجمالي المعاملات: {len(transactions)}")
        
        # التحويلات
        transfers = db.execute_query("SELECT * FROM transfers WHERE user_id = %s", (user_id,))
        print(f"   - التحويلات: {len(transfers)}")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الملخص: {e}")

def main():
    """الدالة الرئيسية"""
    print("🏗️ منشئ البيانات التجريبية المبسط")
    print("=" * 50)
    
    try:
        # إنشاء الحسابات
        created_accounts = create_test_accounts()
        
        if created_accounts:
            # إنشاء المعاملات
            create_sample_transactions()
        
        # عرض الملخص النهائي
        display_final_summary()
        
        print("\n🎉 تم إنشاء البيانات التجريبية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
