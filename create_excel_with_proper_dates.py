#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملفات Excel نموذجية مع تنسيق التاريخ الصحيح لاختبار وظيفة الاستيراد
"""

import pandas as pd
from datetime import datetime, date
from openpyxl import Workbook
from openpyxl.styles import NamedStyle
import os

def create_excel_with_date_format():
    """إنشاء ملفات Excel مع تنسيق التاريخ الصحيح"""
    
    print("🚀 إنشاء ملفات Excel مع تنسيق التاريخ الصحيح...")
    print("="*60)
    
    # بيانات الواردات مع كائنات التاريخ الفعلية
    income_data = [
        {
            'التاريخ': date(2024, 1, 15),
            'الوصف': 'راتب شهر يناير',
            'المبلغ': 15000.00,
            'العملة': 'SAR',
            'الحساب': 'البنك الأهلي'
        },
        {
            'التاريخ': date(2024, 1, 20),
            'الوصف': 'مكافأة أداء',
            'المبلغ': 3000.00,
            'العملة': 'SAR',
            'الحساب': 'البنك الأهلي'
        },
        {
            'التاريخ': date(2024, 1, 25),
            'الوصف': 'عمولة مبيعات',
            'المبلغ': 2500.00,
            'العملة': 'USD',
            'الحساب': 'حساب الدولار'
        },
        {
            'التاريخ': date(2024, 2, 1),
            'الوصف': 'راتب شهر فبراير',
            'المبلغ': 15000.00,
            'العملة': 'SAR',
            'الحساب': 'البنك الأهلي'
        },
        {
            'التاريخ': date(2024, 2, 5),
            'الوصف': 'إيجار عقار',
            'المبلغ': 8000.00,
            'العملة': 'AED',
            'الحساب': 'بنك الإمارات'
        },
        {
            'التاريخ': date(2024, 2, 10),
            'الوصف': 'استثمار أرباح',
            'المبلغ': 1200.00,
            'العملة': 'USD',
            'الحساب': 'حساب الدولار'
        },
        {
            'التاريخ': date(2024, 2, 15),
            'الوصف': 'مشروع تجاري',
            'المبلغ': 450000.00,
            'العملة': 'YER',
            'الحساب': 'البنك اليمني'
        },
        {
            'التاريخ': date(2024, 2, 20),
            'الوصف': 'هدية نقدية',
            'المبلغ': 5000.00,
            'العملة': 'SAR',
            'الحساب': 'محفظة نقدية'
        }
    ]
    
    # بيانات المصروفات مع كائنات التاريخ الفعلية
    expenses_data = [
        {
            'التاريخ': date(2024, 1, 16),
            'الوصف': 'فاتورة كهرباء',
            'المبلغ': 450.00,
            'العملة': 'SAR',
            'الحساب': 'البنك الأهلي'
        },
        {
            'التاريخ': date(2024, 1, 18),
            'الوصف': 'تسوق بقالة',
            'المبلغ': 320.00,
            'العملة': 'SAR',
            'الحساب': 'محفظة نقدية'
        },
        {
            'التاريخ': date(2024, 1, 22),
            'الوصف': 'وقود السيارة',
            'المبلغ': 180.00,
            'العملة': 'SAR',
            'الحساب': 'البنك الأهلي'
        },
        {
            'التاريخ': date(2024, 1, 28),
            'الوصف': 'مطعم وعشاء',
            'المبلغ': 150.00,
            'العملة': 'USD',
            'الحساب': 'حساب الدولار'
        },
        {
            'التاريخ': date(2024, 2, 2),
            'الوصف': 'فاتورة إنترنت',
            'المبلغ': 200.00,
            'العملة': 'SAR',
            'الحساب': 'البنك الأهلي'
        },
        {
            'التاريخ': date(2024, 2, 8),
            'الوصف': 'صيانة السيارة',
            'المبلغ': 800.00,
            'العملة': 'AED',
            'الحساب': 'بنك الإمارات'
        },
        {
            'التاريخ': date(2024, 2, 12),
            'الوصف': 'أدوية وعلاج',
            'المبلغ': 25000.00,
            'العملة': 'YER',
            'الحساب': 'البنك اليمني'
        },
        {
            'التاريخ': date(2024, 2, 18),
            'الوصف': 'ملابس وأحذية',
            'المبلغ': 1200.00,
            'العملة': 'SAR',
            'الحساب': 'البنك الأهلي'
        }
    ]
    
    # إنشاء ملف الواردات مع تنسيق التاريخ الصحيح
    create_formatted_excel_file(income_data, 'sample_income_formatted.xlsx', 'الواردات')
    
    # إنشاء ملف المصروفات مع تنسيق التاريخ الصحيح
    create_formatted_excel_file(expenses_data, 'sample_expenses_formatted.xlsx', 'المصروفات')
    
    # إنشاء ملف مختلط للاختبار الشامل
    create_mixed_format_test_file()
    
    print("\n✅ تم إنشاء جميع الملفات بنجاح!")

def create_formatted_excel_file(data, filename, sheet_name):
    """إنشاء ملف Excel مع تنسيق التاريخ الصحيح"""
    
    # إنشاء DataFrame
    df = pd.DataFrame(data)
    
    # إنشاء ملف Excel مع تنسيق مخصص
    with pd.ExcelWriter(filename, engine='openpyxl', date_format='DD/MM/YYYY') as writer:
        df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # الحصول على workbook و worksheet
        workbook = writer.book
        worksheet = writer.sheets[sheet_name]
        
        # تطبيق تنسيق التاريخ على عمود التاريخ
        date_style = NamedStyle(name='date_style', number_format='DD/MM/YYYY')
        
        # العثور على عمود التاريخ
        date_col = None
        for col_num, col_name in enumerate(df.columns, 1):
            if col_name == 'التاريخ':
                date_col = col_num
                break
        
        if date_col:
            # تطبيق تنسيق التاريخ على العمود
            for row in range(2, len(df) + 2):  # بدءاً من الصف الثاني (بعد العناوين)
                cell = worksheet.cell(row=row, column=date_col)
                cell.number_format = 'DD/MM/YYYY'
        
        # تعديل عرض الأعمدة
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    print(f"✅ تم إنشاء ملف {filename} مع تنسيق التاريخ الصحيح")

def create_mixed_format_test_file():
    """إنشاء ملف اختبار بتنسيقات تاريخ مختلطة"""
    
    # بيانات بتنسيقات تاريخ مختلفة
    mixed_data = [
        {
            'التاريخ': date(2024, 3, 1),  # كائن تاريخ
            'الوصف': 'معاملة بتاريخ كائن',
            'المبلغ': 1000.00,
            'العملة': 'SAR',
            'الحساب': 'البنك الأهلي'
        },
        {
            'التاريخ': '2024-03-02',  # نص بتنسيق ISO
            'الوصف': 'معاملة بتاريخ ISO',
            'المبلغ': 1500.00,
            'العملة': 'SAR',
            'الحساب': 'البنك الأهلي'
        },
        {
            'التاريخ': '02/03/2024',  # نص بتنسيق DD/MM/YYYY
            'الوصف': 'معاملة بتاريخ DD/MM/YYYY',
            'المبلغ': 2000.00,
            'العملة': 'USD',
            'الحساب': 'حساب الدولار'
        },
        {
            'التاريخ': datetime(2024, 3, 4, 14, 30),  # كائن datetime
            'الوصف': 'معاملة بتاريخ ووقت',
            'المبلغ': 2500.00,
            'العملة': 'AED',
            'الحساب': 'بنك الإمارات'
        }
    ]
    
    # إنشاء الملف
    df = pd.DataFrame(mixed_data)
    df.to_excel('sample_mixed_dates.xlsx', index=False, engine='openpyxl')
    
    print("✅ تم إنشاء ملف sample_mixed_dates.xlsx للاختبار المختلط")

def test_date_reading():
    """اختبار قراءة التواريخ من الملفات المنشأة"""
    
    print("\n🧪 اختبار قراءة التواريخ من الملفات المنشأة...")
    print("="*60)
    
    test_files = [
        'sample_income_formatted.xlsx',
        'sample_expenses_formatted.xlsx', 
        'sample_mixed_dates.xlsx'
    ]
    
    for filename in test_files:
        if os.path.exists(filename):
            print(f"\n📊 اختبار ملف: {filename}")
            try:
                df = pd.read_excel(filename, engine='openpyxl')
                print(f"   ✅ تم قراءة الملف - {len(df)} صف")
                
                # فحص عمود التاريخ
                if 'التاريخ' in df.columns:
                    print("   📅 فحص عمود التاريخ:")
                    for idx, date_val in enumerate(df['التاريخ'].head(3)):
                        print(f"      الصف {idx+1}: {date_val} (نوع: {type(date_val)})")
                        
                        # محاولة تحليل التاريخ
                        parsed_date = parse_date_test(date_val)
                        if parsed_date:
                            print(f"         ✅ تم تحليله إلى: {parsed_date}")
                        else:
                            print(f"         ❌ فشل في التحليل")
                else:
                    print("   ❌ عمود التاريخ غير موجود")
                    
            except Exception as e:
                print(f"   ❌ خطأ في قراءة الملف: {e}")
        else:
            print(f"❌ الملف {filename} غير موجود")

def parse_date_test(date_value):
    """دالة اختبار لتحليل التاريخ (نسخة مبسطة من الدالة المحسنة)"""
    try:
        if pd.isna(date_value):
            return None

        if isinstance(date_value, datetime):
            return date_value.date()
        
        if hasattr(date_value, 'date') and callable(getattr(date_value, 'date')):
            return date_value.date()
        
        if hasattr(date_value, 'to_pydatetime'):
            return date_value.to_pydatetime().date()

        date_str = str(date_value).strip()
        
        date_formats = [
            '%Y-%m-%d',
            '%d/%m/%Y',
            '%m/%d/%Y',
            '%d-%m-%Y',
            '%Y/%m/%d'
        ]

        for date_format in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, date_format)
                return parsed_date.date()
            except:
                continue

        try:
            parsed_date = pd.to_datetime(date_value, errors='coerce')
            if not pd.isna(parsed_date):
                return parsed_date.date()
        except:
            pass

        return None
    except:
        return None

def main():
    """الدالة الرئيسية"""
    create_excel_with_date_format()
    test_date_reading()
    
    print("\n" + "="*60)
    print("📋 ملخص الملفات المنشأة:")
    print("   • sample_income_formatted.xlsx - واردات مع تنسيق تاريخ صحيح")
    print("   • sample_expenses_formatted.xlsx - مصروفات مع تنسيق تاريخ صحيح")
    print("   • sample_mixed_dates.xlsx - اختبار تنسيقات تاريخ مختلطة")
    print("\n🔍 يمكنك الآن اختبار هذه الملفات مع وظيفة الاستيراد المحسنة")

if __name__ == "__main__":
    main()
