import mysql.connector

try:
    connection = mysql.connector.connect(
        host='localhost',
        user='root',
        password='mohdam',
        database='money_manager'
    )
    cursor = connection.cursor()
    
    print("✅ متصل بقاعدة البيانات")
    
    # فحص العملات الموجودة
    cursor.execute("SELECT id, code, name, symbol FROM currencies ORDER BY id")
    currencies = cursor.fetchall()
    
    print(f"\n📋 العملات الموجودة ({len(currencies)}):")
    for currency in currencies:
        print(f"   ID: {currency[0]} | Code: {currency[1]} | Name: {currency[2]} | Symbol: {currency[3]}")
    
    # إنشاء خريطة للعملات
    currency_map = {}
    for currency in currencies:
        currency_map[currency[1]] = currency[0]  # code -> id
    
    print(f"\n🗺️ خريطة العملات:")
    for code, currency_id in currency_map.items():
        print(f"   {code}: {currency_id}")
    
    cursor.close()
    connection.close()
    
except Exception as e:
    print(f"❌ خطأ: {e}")
