#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض محتويات ملفات Excel النموذجية لاختبار وظيفة الاستيراد
"""

import pandas as pd
import os

def display_file_info(filename, title, description):
    """عرض معلومات ومحتوى ملف Excel"""
    print("\n" + "="*80)
    print(f"📊 {title}")
    print("="*80)
    print(f"📁 اسم الملف: {filename}")
    print(f"📝 الوصف: {description}")
    
    if os.path.exists(filename):
        try:
            df = pd.read_excel(filename)
            print(f"📈 عدد الصفوف: {len(df)}")
            print(f"📋 الأعمدة: {', '.join(df.columns.tolist())}")
            
            if 'المبلغ' in df.columns:
                print(f"💰 إجمالي المبلغ: {df['المبلغ'].sum():,.2f}")
            elif 'Amount' in df.columns:
                print(f"💰 إجمالي المبلغ: {df['Amount'].sum():,.2f}")
            
            print("\n📋 محتوى الملف:")
            print("-" * 80)
            print(df.to_string(index=False))
            
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {e}")
    else:
        print("❌ الملف غير موجود")

def main():
    """الدالة الرئيسية"""
    print("🚀 عرض ملفات Excel النموذجية لاختبار وظيفة الاستيراد")
    print("="*80)
    
    # عرض الملفات الصحيحة
    display_file_info(
        "sample_income.xlsx",
        "ملف الواردات النموذجي",
        "يحتوي على 8 معاملات واردة بعملات مختلفة (SAR, USD, AED, YER)"
    )
    
    display_file_info(
        "sample_expenses.xlsx", 
        "ملف المصروفات النموذجي",
        "يحتوي على 8 معاملات مصروفات بعملات مختلفة (SAR, USD, AED, YER)"
    )
    
    # عرض ملفات اختبار الأخطاء
    display_file_info(
        "sample_invalid.xlsx",
        "ملف بتنسيق خاطئ (لاختبار معالجة الأخطاء)",
        "يحتوي على أعمدة بالإنجليزية وبيانات غير صحيحة"
    )
    
    display_file_info(
        "sample_incomplete.xlsx",
        "ملف بأعمدة ناقصة (لاختبار معالجة الأخطاء)",
        "يحتوي على صفوف ببيانات مفقودة أو غير مكتملة"
    )
    
    print("\n" + "="*80)
    print("✅ تم عرض جميع الملفات النموذجية")
    print("="*80)
    print("🔍 يمكنك الآن استخدام هذه الملفات لاختبار وظيفة الاستيراد في التطبيق")
    print("📖 راجع ملف IMPORT_TEST_SUMMARY.md للحصول على دليل الاختبار الشامل")

if __name__ == "__main__":
    main()
