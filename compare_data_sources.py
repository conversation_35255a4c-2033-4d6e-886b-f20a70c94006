#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مقارنة البيانات بين SQLite وملفات JSON
"""

import sqlite3
import json
import os

def get_sqlite_data():
    """استخراج البيانات من SQLite"""
    print("🔍 استخراج البيانات من SQLite...")
    
    if not os.path.exists("money_manager.db"):
        print("❌ ملف SQLite غير موجود")
        return {}
    
    try:
        conn = sqlite3.connect("money_manager.db")
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        data = {}
        
        # المستخدمين
        cursor.execute("SELECT * FROM users")
        users = [dict(row) for row in cursor.fetchall()]
        data['users'] = users
        print(f"👥 المستخدمين في SQLite: {len(users)}")
        for user in users:
            print(f"   - {user['username']} ({user['role']})")
        
        # العملات
        cursor.execute("SELECT * FROM currencies")
        currencies = [dict(row) for row in cursor.fetchall()]
        data['currencies'] = currencies
        print(f"💰 العملات في SQLite: {len(currencies)}")
        for currency in currencies:
            print(f"   - {currency['code']}: {currency['name']}")
        
        # الحسابات
        cursor.execute("SELECT * FROM accounts")
        accounts = [dict(row) for row in cursor.fetchall()]
        data['accounts'] = accounts
        print(f"🏦 الحسابات في SQLite: {len(accounts)}")
        for account in accounts:
            print(f"   - {account['name']}")
        
        # أرصدة الحسابات
        cursor.execute("SELECT * FROM account_balances")
        balances = [dict(row) for row in cursor.fetchall()]
        data['account_balances'] = balances
        print(f"💵 أرصدة الحسابات في SQLite: {len(balances)}")
        
        # المعاملات
        cursor.execute("SELECT * FROM transactions")
        transactions = [dict(row) for row in cursor.fetchall()]
        data['transactions'] = transactions
        print(f"📊 المعاملات في SQLite: {len(transactions)}")
        
        conn.close()
        return data
        
    except Exception as e:
        print(f"❌ خطأ في قراءة SQLite: {e}")
        return {}

def get_json_data():
    """استخراج البيانات من ملفات JSON"""
    print("\n📄 استخراج البيانات من ملفات JSON...")
    
    data = {}
    
    # ملف المستخدمين
    if os.path.exists("users.json"):
        with open("users.json", 'r', encoding='utf-8') as f:
            users_json = json.load(f)
            data['users'] = users_json
            print(f"👥 المستخدمين في JSON: {len(users_json)}")
            for username, user_data in users_json.items():
                print(f"   - {username} ({user_data.get('role', 'غير محدد')})")
    
    # ملف الحسابات
    if os.path.exists("accounts.json"):
        with open("accounts.json", 'r', encoding='utf-8') as f:
            accounts_json = json.load(f)
            data['accounts'] = accounts_json
            print(f"🏦 الحسابات في JSON: {len(accounts_json)}")
            for account in accounts_json:
                print(f"   - {account.get('name', 'غير محدد')} ({account.get('currency', 'غير محدد')})")
    
    # ملف المعاملات
    if os.path.exists("transactions.json"):
        with open("transactions.json", 'r', encoding='utf-8') as f:
            transactions_json = json.load(f)
            data['transactions'] = transactions_json
            print(f"📊 المعاملات في JSON: {len(transactions_json)}")
    
    return data

def compare_users(sqlite_data, json_data):
    """مقارنة بيانات المستخدمين"""
    print("\n👥 مقارنة المستخدمين:")
    print("-" * 30)
    
    sqlite_users = sqlite_data.get('users', [])
    json_users = json_data.get('users', {})
    
    print(f"SQLite: {len(sqlite_users)} مستخدم")
    print(f"JSON: {len(json_users)} مستخدم")
    
    # مقارنة أسماء المستخدمين
    sqlite_usernames = {user['username'] for user in sqlite_users}
    json_usernames = set(json_users.keys())
    
    common_users = sqlite_usernames & json_usernames
    sqlite_only = sqlite_usernames - json_usernames
    json_only = json_usernames - sqlite_usernames
    
    print(f"مشتركين: {len(common_users)} - {list(common_users)}")
    print(f"في SQLite فقط: {len(sqlite_only)} - {list(sqlite_only)}")
    print(f"في JSON فقط: {len(json_only)} - {list(json_only)}")

def compare_accounts(sqlite_data, json_data):
    """مقارنة بيانات الحسابات"""
    print("\n🏦 مقارنة الحسابات:")
    print("-" * 30)
    
    sqlite_accounts = sqlite_data.get('accounts', [])
    json_accounts = json_data.get('accounts', [])
    
    print(f"SQLite: {len(sqlite_accounts)} حساب")
    print(f"JSON: {len(json_accounts)} حساب")
    
    # مقارنة أسماء الحسابات
    sqlite_names = {acc['name'] for acc in sqlite_accounts}
    json_names = {acc.get('name', '') for acc in json_accounts}
    
    common_accounts = sqlite_names & json_names
    sqlite_only = sqlite_names - json_names
    json_only = json_names - sqlite_names
    
    print(f"مشتركة: {len(common_accounts)} - {list(common_accounts)}")
    print(f"في SQLite فقط: {len(sqlite_only)} - {list(sqlite_only)}")
    print(f"في JSON فقط: {len(json_only)} - {list(json_only)}")

def compare_transactions(sqlite_data, json_data):
    """مقارنة بيانات المعاملات"""
    print("\n📊 مقارنة المعاملات:")
    print("-" * 30)
    
    sqlite_transactions = sqlite_data.get('transactions', [])
    json_transactions = json_data.get('transactions', [])
    
    print(f"SQLite: {len(sqlite_transactions)} معاملة")
    print(f"JSON: {len(json_transactions)} معاملة")
    
    # تحليل أنواع المعاملات
    if sqlite_transactions:
        sqlite_types = {}
        for trans in sqlite_transactions:
            trans_type = trans.get('type', 'غير محدد')
            sqlite_types[trans_type] = sqlite_types.get(trans_type, 0) + 1
        print(f"أنواع المعاملات في SQLite: {sqlite_types}")
    
    if json_transactions:
        json_types = {}
        for trans in json_transactions:
            trans_type = trans.get('type', 'غير محدد')
            json_types[trans_type] = json_types.get(trans_type, 0) + 1
        print(f"أنواع المعاملات في JSON: {json_types}")

def analyze_currencies(sqlite_data):
    """تحليل العملات في SQLite"""
    print("\n💰 تحليل العملات:")
    print("-" * 30)
    
    currencies = sqlite_data.get('currencies', [])
    print(f"العملات المتاحة: {len(currencies)}")
    
    for currency in currencies:
        status = "نشط" if currency.get('is_active', 0) else "غير نشط"
        rate = currency.get('exchange_rate', 1.0)
        print(f"   - {currency['code']}: {currency['name']} (سعر الصرف: {rate}) - {status}")

def main():
    """الدالة الرئيسية"""
    print("🔍 مقارنة البيانات بين SQLite وملفات JSON")
    print("=" * 60)
    
    # استخراج البيانات
    sqlite_data = get_sqlite_data()
    json_data = get_json_data()
    
    if not sqlite_data and not json_data:
        print("❌ لا توجد بيانات للمقارنة")
        return
    
    # المقارنات
    compare_users(sqlite_data, json_data)
    compare_accounts(sqlite_data, json_data)
    compare_transactions(sqlite_data, json_data)
    analyze_currencies(sqlite_data)
    
    # حفظ النتائج
    comparison_result = {
        'sqlite_data': sqlite_data,
        'json_data': json_data,
        'analysis_timestamp': '2025-07-20'
    }
    
    with open('data_comparison_result.json', 'w', encoding='utf-8') as f:
        json.dump(comparison_result, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n✅ تم حفظ نتائج المقارنة في: data_comparison_result.json")

if __name__ == "__main__":
    main()
