#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف Excel نموذجي لاختبار وظيفة الاستيراد
"""

import pandas as pd
from datetime import datetime, timedelta
import os

def create_sample_excel():
    """إنشاء ملف Excel نموذجي للواردات والمصروفات"""

    # أسماء الحسابات الصحيحة في قاعدة البيانات الحالية:
    # - البشرية
    # - معاذ
    # - حساب التوفير
    # - بطاقة ائتمانية
    # - المحفظة النقدية
    # - ابراهيم
    # - احمد
    # - محفظة متعددة العملات
    # - فضل
    
    # بيانات نموذجية للواردات (أسماء الحسابات تطابق قاعدة البيانات الحالية)
    income_data = [
        {
            'المبلغ': 5000.00,
            'الحساب': 'المحفظة النقدية',
            'العملة': 'SAR',
            'التاريخ': '2024-01-15',
            'الوصف': 'راتب شهر يناير'
        },
        {
            'المبلغ': 1500.50,
            'الحساب': 'معاذ',
            'العملة': 'SAR',
            'التاريخ': '2024-01-20',
            'الوصف': 'مكافأة إضافية'
        },
        {
            'المبلغ': 800.00,
            'الحساب': 'محفظة متعددة العملات',
            'العملة': 'USD',
            'التاريخ': '2024-01-25',
            'الوصف': 'عمولة مشروع'
        },
        {
            'المبلغ': 2000.00,
            'الحساب': 'حساب التوفير',
            'العملة': 'AED',
            'التاريخ': '2024-01-30',
            'الوصف': 'أرباح استثمار'
        },
        {
            'المبلغ': 50000.00,
            'الحساب': 'فضل',
            'العملة': 'YER',
            'التاريخ': '2024-02-01',
            'الوصف': 'تحويل من الأهل'
        }
    ]
    
    # بيانات نموذجية للمصروفات (أسماء الحسابات تطابق قاعدة البيانات الحالية)
    expense_data = [
        {
            'المبلغ': 1200.00,
            'الحساب': 'المحفظة النقدية',
            'العملة': 'SAR',
            'التاريخ': '2024-01-16',
            'الوصف': 'إيجار المنزل'
        },
        {
            'المبلغ': 450.75,
            'الحساب': 'البشرية',
            'العملة': 'SAR',
            'التاريخ': '2024-01-18',
            'الوصف': 'فاتورة الكهرباء'
        },
        {
            'المبلغ': 200.00,
            'الحساب': 'محفظة متعددة العملات',
            'العملة': 'USD',
            'التاريخ': '2024-01-22',
            'الوصف': 'اشتراك نتفليكس'
        },
        {
            'المبلغ': 300.50,
            'الحساب': 'احمد',
            'العملة': 'AED',
            'التاريخ': '2024-01-28',
            'الوصف': 'وقود السيارة'
        },
        {
            'المبلغ': 15000.00,
            'الحساب': 'ابراهيم',
            'العملة': 'YER',
            'التاريخ': '2024-02-02',
            'الوصف': 'مشتريات البقالة'
        }
    ]
    
    # إنشاء مجلد للملفات النموذجية
    samples_dir = 'sample_excel_files'
    if not os.path.exists(samples_dir):
        os.makedirs(samples_dir)
    
    # إنشاء ملف Excel للواردات
    income_df = pd.DataFrame(income_data)
    income_file = os.path.join(samples_dir, 'نموذج_واردات.xlsx')
    income_df.to_excel(income_file, index=False, engine='openpyxl')
    print(f"✅ تم إنشاء ملف الواردات النموذجي: {income_file}")
    
    # إنشاء ملف Excel للمصروفات
    expense_df = pd.DataFrame(expense_data)
    expense_file = os.path.join(samples_dir, 'نموذج_مصروفات.xlsx')
    expense_df.to_excel(expense_file, index=False, engine='openpyxl')
    print(f"✅ تم إنشاء ملف المصروفات النموذجي: {expense_file}")
    
    # إنشاء ملف مختلط (واردات ومصروفات معاً)
    mixed_data = income_data + expense_data
    mixed_df = pd.DataFrame(mixed_data)
    mixed_file = os.path.join(samples_dir, 'نموذج_مختلط.xlsx')
    mixed_df.to_excel(mixed_file, index=False, engine='openpyxl')
    print(f"✅ تم إنشاء ملف البيانات المختلطة: {mixed_file}")
    
    # إنشاء ملف بأخطاء للاختبار
    error_data = [
        {
            'المبلغ': 'غير صحيح',  # خطأ في المبلغ
            'الحساب': 'الصندوق النقدي',
            'العملة': 'SAR',
            'التاريخ': '2024-01-15',
            'الوصف': 'بيانات خاطئة - مبلغ'
        },
        {
            'المبلغ': 1000.00,
            'الحساب': 'حساب غير موجود',  # خطأ في الحساب
            'العملة': 'SAR',
            'التاريخ': '2024-01-16',
            'الوصف': 'بيانات خاطئة - حساب'
        },
        {
            'المبلغ': 500.00,
            'الحساب': 'الصندوق النقدي',
            'العملة': 'XYZ',  # خطأ في العملة
            'التاريخ': '2024-01-17',
            'الوصف': 'بيانات خاطئة - عملة'
        },
        {
            'المبلغ': 750.00,
            'الحساب': 'الصندوق النقدي',
            'العملة': 'SAR',
            'التاريخ': 'تاريخ خاطئ',  # خطأ في التاريخ
            'الوصف': 'بيانات خاطئة - تاريخ'
        },
        {
            'المبلغ': 2000.00,
            'الحساب': 'الصندوق النقدي',
            'العملة': 'SAR',
            'التاريخ': '2024-01-19',
            'الوصف': 'بيانات صحيحة'  # هذا الصف صحيح
        }
    ]
    
    error_df = pd.DataFrame(error_data)
    error_file = os.path.join(samples_dir, 'نموذج_بأخطاء.xlsx')
    error_df.to_excel(error_file, index=False, engine='openpyxl')
    print(f"✅ تم إنشاء ملف الاختبار بالأخطاء: {error_file}")
    
    print(f"\n📁 جميع الملفات النموذجية في المجلد: {samples_dir}")
    print("\n📋 تعليمات الاستخدام:")
    print("1. افتح التطبيق وانتقل إلى قسم الواردات أو المصروفات")
    print("2. اضغط على زر 'استيراد من Excel'")
    print("3. اختر أحد الملفات النموذجية")
    print("4. راجع البيانات في نافذة المعاينة")
    print("5. اضغط على 'تأكيد الاستيراد'")
    
    return samples_dir

if __name__ == "__main__":
    print("🚀 إنشاء ملفات Excel النموذجية...")
    create_sample_excel()
    print("\n✨ تم الانتهاء بنجاح!")
