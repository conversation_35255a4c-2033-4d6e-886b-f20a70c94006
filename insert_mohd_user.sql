
-- إنشاء حساب مستخدم mohd
USE money_manager;

-- التحقق من وجود المستخدم
SELECT COUNT(*) as existing_count FROM users WHERE username = 'mohd';

-- إدراج المستخدم الجديد
INSERT INTO users (username, password_hash, full_name, role, is_active, created_at, updated_at)
SELECT 'mohd', '$2b$12$LyXgW.5ZuilS6.8V66StIue6MCYZWIj4oJrVOrQCjvO9rkNBwnZTC', 'محمد', 'user', TRUE, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'mohd');

-- الحصول على معرف المستخدم الجديد
SET @new_user_id = (SELECT id FROM users WHERE username = 'mohd');

-- تسجيل العملية في activity_log
INSERT INTO activity_log (user_id, action, table_name, record_id, details, created_at)
SELECT 1, 'create_user', 'users', @new_user_id, 'تم إنشاء حساب مستخدم جديد: mohd', NOW()
WHERE @new_user_id IS NOT NULL;

-- عرض النتيجة
SELECT 'تم إنشاء المستخدم بنجاح!' as result;
SELECT id, username, full_name, role, is_active, created_at 
FROM users WHERE username = 'mohd';
