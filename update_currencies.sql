-- تحديث العملات لتشمل العملات المطلوبة فقط
USE money_manager;

-- إلغاء تفعيل جميع العملات أولاً
UPDATE currencies SET is_active = FALSE;

-- حذف العملات غير المطلوبة (إذا لم تكن مستخدمة)
-- سنقوم بإلغاء تفعيلها فقط للحفاظ على البيانات الموجودة

-- تحديث/إضافة العملات المطلوبة
INSERT INTO currencies (id, code, name, symbol, exchange_rate, is_active) VALUES
(1, 'SAR', 'ريال سعودي', 'ر.س', 1.0000, TRUE),
(2, 'YER', 'ريال يمني', 'ر.ي', 0.0040, TRUE),
(3, 'AED', 'درهم إماراتي', 'د.إ', 1.0200, TRUE),
(4, 'USD', 'دولار أمريكي', '$', 3.7500, TRUE)
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    symbol = VALUES(symbol),
    exchange_rate = VALUES(exchange_rate),
    is_active = VALUES(is_active);

-- التأكد من أن العملات المطلوبة فقط نشطة
UPDATE currencies SET is_active = TRUE WHERE code IN ('SAR', 'YER', 'AED', 'USD');
UPDATE currencies SET is_active = FALSE WHERE code NOT IN ('SAR', 'YER', 'AED', 'USD');

-- عرض النتيجة النهائية
SELECT * FROM currencies ORDER BY is_active DESC, name;
