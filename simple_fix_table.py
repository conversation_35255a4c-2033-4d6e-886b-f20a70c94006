import mysql.connector

connection = mysql.connector.connect(
    host='localhost',
    user='root',
    password='mohdam',
    database='money_manager'
)
cursor = connection.cursor()

print("✅ متصل بقاعدة البيانات")

# إضافة عمود type
try:
    cursor.execute("ALTER TABLE transactions ADD COLUMN type ENUM('income', 'expense', 'transfer') NOT NULL DEFAULT 'income'")
    print("✅ تم إضافة عمود type")
except Exception as e:
    print(f"عمود type: {e}")

connection.commit()
cursor.close()
connection.close()

print("✅ تم الانتهاء")
