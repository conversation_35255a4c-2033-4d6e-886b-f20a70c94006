#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث مبسط للعملات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def update_currencies_simple():
    """تحديث العملات بطريقة مبسطة"""
    try:
        from database.connection import db
        
        print("💱 تحديث العملات...")
        
        # التحقق من الاتصال
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return False
        
        print("✅ الاتصال بقاعدة البيانات نشط")
        
        # إلغاء تفعيل جميع العملات
        print("🚫 إلغاء تفعيل جميع العملات...")
        db.execute_update("UPDATE currencies SET is_active = FALSE")
        
        # العملات المطلوبة
        currencies_data = [
            ('SAR', 'ريال سعودي', 'ر.س', 1.0000),
            ('YER', 'ريال يمني', 'ر.ي', 0.0040),
            ('AED', 'درهم إماراتي', 'د.إ', 1.0200),
            ('USD', 'دولار أمريكي', '$', 3.7500)
        ]
        
        print("✅ تحديث العملات المطلوبة...")
        
        for code, name, symbol, rate in currencies_data:
            # محاولة التحديث أولاً
            result = db.execute_update("""
                UPDATE currencies 
                SET name = %s, symbol = %s, exchange_rate = %s, is_active = TRUE 
                WHERE code = %s
            """, (name, symbol, rate, code))
            
            # إذا لم يتم التحديث، أدرج جديد
            if result == 0:
                db.execute_insert("""
                    INSERT INTO currencies (code, name, symbol, exchange_rate, is_active) 
                    VALUES (%s, %s, %s, %s, TRUE)
                """, (code, name, symbol, rate))
                print(f"   ➕ تم إضافة {name}")
            else:
                print(f"   🔄 تم تحديث {name}")
        
        # التحقق من النتيجة
        print("\n📊 العملات النشطة:")
        currencies = db.execute_query("""
            SELECT code, name, symbol, exchange_rate 
            FROM currencies 
            WHERE is_active = TRUE 
            ORDER BY name
        """)
        
        if currencies:
            for curr in currencies:
                print(f"   ✅ {curr['name']} ({curr['code']}) - {curr['symbol']} - {curr['exchange_rate']}")
        
        print(f"\n🎉 تم تحديث {len(currencies)} عملة بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("💱 تحديث العملات المدعومة")
    print("=" * 40)
    
    if update_currencies_simple():
        print("\n✅ تم التحديث بنجاح!")
        print("\n💡 العملات المتاحة الآن:")
        print("   • ريال سعودي")
        print("   • ريال يمني") 
        print("   • درهم إماراتي")
        print("   • دولار أمريكي")
    else:
        print("\n❌ فشل في التحديث")
    
    print("\n🏁 انتهى")
