#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل البيانات الحالية في التطبيق
"""

import mysql.connector
import sqlite3
import json
import os
from datetime import datetime

def analyze_mysql_data():
    """تحليل البيانات في MySQL"""
    print("🔍 تحليل بيانات MySQL...")
    
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        
        mysql_data = {}
        
        # تحليل المستخدمين
        cursor.execute("SELECT * FROM users WHERE is_active = TRUE")
        users = cursor.fetchall()
        mysql_data['users'] = users
        print(f"👥 المستخدمين النشطين: {len(users)}")
        for user in users:
            print(f"   - {user['username']} ({user['role']})")
        
        # تحليل العملات
        cursor.execute("SELECT * FROM currencies WHERE is_active = TRUE")
        currencies = cursor.fetchall()
        mysql_data['currencies'] = currencies
        print(f"💰 العملات المدعومة: {len(currencies)}")
        for currency in currencies:
            print(f"   - {currency['code']}: {currency['name']} ({currency['symbol']})")
        
        # تحليل الحسابات
        cursor.execute("""
            SELECT a.*, u.username 
            FROM accounts a 
            JOIN users u ON a.user_id = u.id 
            WHERE a.is_active = TRUE
        """)
        accounts = cursor.fetchall()
        mysql_data['accounts'] = accounts
        print(f"🏦 الحسابات النشطة: {len(accounts)}")
        for account in accounts:
            print(f"   - {account['name']} (المستخدم: {account['username']})")
        
        # تحليل أرصدة الحسابات
        cursor.execute("""
            SELECT ab.*, a.name as account_name, c.code as currency_code
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            JOIN currencies c ON ab.currency_id = c.id
            WHERE ab.balance != 0
        """)
        balances = cursor.fetchall()
        mysql_data['account_balances'] = balances
        print(f"💵 أرصدة الحسابات غير الصفرية: {len(balances)}")
        for balance in balances:
            print(f"   - {balance['account_name']}: {balance['balance']} {balance['currency_code']}")
        
        # تحليل المعاملات
        cursor.execute("""
            SELECT COUNT(*) as count, type 
            FROM transactions 
            GROUP BY type
        """)
        transaction_stats = cursor.fetchall()
        mysql_data['transaction_stats'] = transaction_stats
        print(f"📊 إحصائيات المعاملات:")
        for stat in transaction_stats:
            print(f"   - {stat['type']}: {stat['count']} معاملة")
        
        # تحليل التحويلات
        cursor.execute("SELECT COUNT(*) as count FROM transfers")
        transfer_count = cursor.fetchone()['count']
        mysql_data['transfer_count'] = transfer_count
        print(f"🔄 التحويلات: {transfer_count}")
        
        # تحليل التصنيفات
        cursor.execute("SELECT * FROM income_categories")
        income_categories = cursor.fetchall()
        mysql_data['income_categories'] = income_categories
        print(f"📈 تصنيفات الواردات: {len(income_categories)}")
        
        cursor.execute("SELECT * FROM expense_categories")
        expense_categories = cursor.fetchall()
        mysql_data['expense_categories'] = expense_categories
        print(f"📉 تصنيفات المصروفات: {len(expense_categories)}")
        
        cursor.close()
        connection.close()
        
        return mysql_data
        
    except Exception as e:
        print(f"❌ خطأ في تحليل بيانات MySQL: {e}")
        return {}

def analyze_sqlite_data():
    """تحليل البيانات في SQLite"""
    print("\n🔍 تحليل بيانات SQLite...")
    
    if not os.path.exists("money_manager.db"):
        print("⚠️ ملف SQLite غير موجود")
        return {}
    
    try:
        connection = sqlite3.connect("money_manager.db")
        connection.row_factory = sqlite3.Row
        cursor = connection.cursor()
        
        sqlite_data = {}
        
        # فحص الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📋 الجداول الموجودة: {', '.join(tables)}")
        
        # تحليل البيانات في كل جدول
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   - {table}: {count} سجل")
                
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table} LIMIT 5")
                    sample_data = [dict(row) for row in cursor.fetchall()]
                    sqlite_data[table] = sample_data
            except Exception as e:
                print(f"   ❌ خطأ في جدول {table}: {e}")
        
        connection.close()
        return sqlite_data
        
    except Exception as e:
        print(f"❌ خطأ في تحليل بيانات SQLite: {e}")
        return {}

def analyze_json_files():
    """تحليل ملفات JSON"""
    print("\n🔍 تحليل ملفات JSON...")
    
    json_data = {}
    json_files = ["accounts.json", "users.json", "transactions.json"]
    
    for json_file in json_files:
        if os.path.exists(json_file):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    json_data[json_file] = data
                    
                    if isinstance(data, list):
                        print(f"📄 {json_file}: {len(data)} عنصر")
                    elif isinstance(data, dict):
                        print(f"📄 {json_file}: {len(data)} مفتاح")
                    else:
                        print(f"📄 {json_file}: نوع البيانات {type(data)}")
                        
            except Exception as e:
                print(f"❌ خطأ في قراءة {json_file}: {e}")
        else:
            print(f"⚠️ {json_file} غير موجود")
    
    return json_data

def compare_data_sources(mysql_data, sqlite_data, json_data):
    """مقارنة مصادر البيانات المختلفة"""
    print("\n📊 مقارنة مصادر البيانات:")
    print("=" * 50)
    
    # مقارنة المستخدمين
    mysql_users = len(mysql_data.get('users', []))
    sqlite_users = len(sqlite_data.get('users', []))
    json_users = len(json_data.get('users.json', {}))
    
    print(f"👥 المستخدمين:")
    print(f"   MySQL: {mysql_users}")
    print(f"   SQLite: {sqlite_users}")
    print(f"   JSON: {json_users}")
    
    # مقارنة العملات
    mysql_currencies = len(mysql_data.get('currencies', []))
    sqlite_currencies = len(sqlite_data.get('currencies', []))
    
    print(f"💰 العملات:")
    print(f"   MySQL: {mysql_currencies}")
    print(f"   SQLite: {sqlite_currencies}")
    
    # مقارنة الحسابات
    mysql_accounts = len(mysql_data.get('accounts', []))
    sqlite_accounts = len(sqlite_data.get('accounts', []))
    json_accounts = len(json_data.get('accounts.json', []))
    
    print(f"🏦 الحسابات:")
    print(f"   MySQL: {mysql_accounts}")
    print(f"   SQLite: {sqlite_accounts}")
    print(f"   JSON: {json_accounts}")
    
    # مقارنة المعاملات
    mysql_transactions = sum(stat['count'] for stat in mysql_data.get('transaction_stats', []))
    sqlite_transactions = len(sqlite_data.get('transactions', []))
    json_transactions = len(json_data.get('transactions.json', []))
    
    print(f"📊 المعاملات:")
    print(f"   MySQL: {mysql_transactions}")
    print(f"   SQLite: {sqlite_transactions}")
    print(f"   JSON: {json_transactions}")

def main():
    """الدالة الرئيسية"""
    print("🔍 تحليل البيانات الحالية في التطبيق")
    print("=" * 60)
    
    # تحليل البيانات من جميع المصادر
    mysql_data = analyze_mysql_data()
    sqlite_data = analyze_sqlite_data()
    json_data = analyze_json_files()
    
    # مقارنة البيانات
    compare_data_sources(mysql_data, sqlite_data, json_data)
    
    # حفظ التحليل في ملف
    analysis_result = {
        'timestamp': datetime.now().isoformat(),
        'mysql_data': mysql_data,
        'sqlite_data': sqlite_data,
        'json_data': json_data
    }
    
    with open('data_analysis_result.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n✅ تم حفظ نتائج التحليل في: data_analysis_result.json")

if __name__ == "__main__":
    main()
