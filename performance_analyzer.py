#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تحليل أداء التطبيق - تشخيص أسباب البطء في التنقل
"""

import sys
import os
import time
import threading
from functools import wraps
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from utils.auth import auth_manager

class PerformanceAnalyzer:
    """محلل الأداء"""
    
    def __init__(self):
        self.measurements = {}
        self.query_times = []
        self.ui_creation_times = []
    
    def measure_time(self, func_name):
        """ديكوريتر لقياس وقت تنفيذ الدوال"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()
                execution_time = end_time - start_time
                
                if func_name not in self.measurements:
                    self.measurements[func_name] = []
                self.measurements[func_name].append(execution_time)
                
                print(f"⏱️ {func_name}: {execution_time:.3f} ثانية")
                return result
            return wrapper
        return decorator
    
    def analyze_database_queries(self):
        """تحليل أداء استعلامات قاعدة البيانات"""
        print("🔍 تحليل أداء استعلامات قاعدة البيانات...")
        
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return
        
        user_id = auth_manager.current_user['id']
        
        # قائمة الاستعلامات المهمة
        queries = {
            "currency_balances_summary": """
                SELECT
                    c.code, c.name, c.symbol,
                    COALESCE(SUM(ab.balance), 0) as total_balance,
                    COUNT(DISTINCT a.id) as accounts_count
                FROM currencies c
                LEFT JOIN account_balances ab ON c.id = ab.currency_id
                INNER JOIN accounts a ON ab.account_id = a.id
                WHERE c.is_active = TRUE
                AND a.user_id = %s
                AND a.is_active = TRUE
                GROUP BY c.id, c.code, c.name, c.symbol
                HAVING total_balance > 0 OR accounts_count > 0
                ORDER BY total_balance DESC
            """,
            
            "accounts_by_currency": """
                SELECT
                    c.code as currency_code, c.name as currency_name,
                    c.symbol as currency_symbol, a.id as account_id,
                    a.name as account_name, COALESCE(ab.balance, 0) as balance
                FROM currencies c
                LEFT JOIN account_balances ab ON c.id = ab.currency_id
                INNER JOIN accounts a ON ab.account_id = a.id
                WHERE c.is_active = TRUE
                AND a.user_id = %s
                AND a.is_active = TRUE
                ORDER BY c.code, a.name
            """,
            
            "income_transactions": """
                SELECT t.*, a.name as account_name, c.symbol as currency_symbol
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = %s AND t.transaction_type = 'income'
                ORDER BY t.transaction_date DESC, t.created_at DESC
            """,
            
            "expense_transactions": """
                SELECT t.*, a.name as account_name, c.symbol as currency_symbol
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = %s AND t.transaction_type = 'expense'
                ORDER BY t.transaction_date DESC, t.created_at DESC
            """,
            
            "recent_transactions": """
                SELECT t.*, a.name as account_name, c.symbol as currency_symbol
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = %s
                ORDER BY t.transaction_date DESC, t.created_at DESC
                LIMIT 10
            """,
            
            "monthly_stats": """
                SELECT 
                    DATE_FORMAT(t.transaction_date, '%Y-%m') as month,
                    t.transaction_type,
                    SUM(t.amount) as total_amount,
                    COUNT(*) as transaction_count
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                WHERE a.user_id = %s
                AND t.transaction_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                GROUP BY month, t.transaction_type
                ORDER BY month DESC
            """
        }
        
        # قياس وقت تنفيذ كل استعلام
        for query_name, query in queries.items():
            print(f"\n📊 اختبار استعلام: {query_name}")
            
            # قياس الوقت
            start_time = time.time()
            try:
                results = db.execute_query(query, (user_id,))
                end_time = time.time()
                execution_time = end_time - start_time
                
                result_count = len(results) if results else 0
                print(f"   ⏱️ الوقت: {execution_time:.3f} ثانية")
                print(f"   📄 النتائج: {result_count} سجل")
                
                self.query_times.append({
                    'name': query_name,
                    'time': execution_time,
                    'count': result_count
                })
                
                # تحليل الاستعلام إذا كان بطيئاً
                if execution_time > 0.1:  # أكثر من 100ms
                    print(f"   ⚠️ استعلام بطيء! يحتاج تحسين")
                    
            except Exception as e:
                print(f"   ❌ خطأ في الاستعلام: {e}")
        
        auth_manager.logout()
    
    def analyze_ui_creation(self):
        """تحليل أداء إنشاء واجهة المستخدم"""
        print("\n🎨 تحليل أداء إنشاء واجهة المستخدم...")
        
        try:
            import customtkinter as ctk
            from gui.components.rtl_components import create_rtl_label
            from config.ui_config import COLORS, ARABIC_TEXT_STYLES
            
            # إنشاء نافذة اختبار
            test_window = ctk.CTk()
            test_window.withdraw()
            
            # اختبار إنشاء عناصر مختلفة
            ui_tests = {
                "create_label": lambda: create_rtl_label(
                    test_window, 
                    text="نص تجريبي", 
                    font_size='header'
                ),
                "create_frame": lambda: ctk.CTkFrame(test_window),
                "create_button": lambda: ctk.CTkButton(test_window, text="زر تجريبي"),
                "create_scrollable_frame": lambda: ctk.CTkScrollableFrame(test_window),
                "create_entry": lambda: ctk.CTkEntry(test_window)
            }
            
            for test_name, test_func in ui_tests.items():
                start_time = time.time()
                
                # إنشاء 10 عناصر لقياس الأداء
                elements = []
                for i in range(10):
                    elements.append(test_func())
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                print(f"   ⏱️ {test_name}: {execution_time:.3f} ثانية (10 عناصر)")
                
                self.ui_creation_times.append({
                    'name': test_name,
                    'time': execution_time,
                    'per_element': execution_time / 10
                })
                
                # تنظيف العناصر
                for element in elements:
                    element.destroy()
            
            test_window.destroy()
            
        except Exception as e:
            print(f"❌ خطأ في اختبار واجهة المستخدم: {e}")
    
    def simulate_page_loading(self):
        """محاكاة تحميل الصفحات"""
        print("\n📱 محاكاة تحميل الصفحات...")
        
        try:
            import customtkinter as ctk
            from gui.main_window import MainWindow
            
            # إنشاء نافذة اختبار
            test_window = ctk.CTk()
            test_window.withdraw()
            
            # تسجيل الدخول
            success, message = auth_manager.login("admin", "123456")
            if not success:
                print(f"❌ فشل تسجيل الدخول: {message}")
                return
            
            # إنشاء MainWindow
            main_window = MainWindow(test_window)
            
            # اختبار تحميل الصفحات المختلفة
            page_tests = {
                "dashboard": main_window.load_dashboard,
                "show_income": main_window.show_income,
                "show_expense": main_window.show_expense,
                "show_accounts": main_window.show_accounts
            }
            
            for page_name, page_func in page_tests.items():
                print(f"\n📄 اختبار تحميل صفحة: {page_name}")
                
                start_time = time.time()
                try:
                    page_func()
                    end_time = time.time()
                    execution_time = end_time - start_time
                    
                    print(f"   ⏱️ وقت التحميل: {execution_time:.3f} ثانية")
                    
                    if execution_time > 1.0:  # أكثر من ثانية
                        print(f"   ⚠️ صفحة بطيئة! تحتاج تحسين")
                    elif execution_time > 0.5:  # أكثر من نصف ثانية
                        print(f"   ⚡ صفحة متوسطة السرعة")
                    else:
                        print(f"   ✅ صفحة سريعة")
                        
                except Exception as e:
                    print(f"   ❌ خطأ في تحميل الصفحة: {e}")
            
            test_window.destroy()
            auth_manager.logout()
            
        except Exception as e:
            print(f"❌ خطأ في محاكاة تحميل الصفحات: {e}")
    
    def generate_report(self):
        """إنشاء تقرير شامل للأداء"""
        print("\n" + "="*60)
        print("📊 تقرير تحليل الأداء")
        print("="*60)
        
        # تحليل استعلامات قاعدة البيانات
        if self.query_times:
            print("\n🗄️ أداء استعلامات قاعدة البيانات:")
            sorted_queries = sorted(self.query_times, key=lambda x: x['time'], reverse=True)
            
            for query in sorted_queries:
                status = "🔴" if query['time'] > 0.1 else "🟡" if query['time'] > 0.05 else "🟢"
                print(f"   {status} {query['name']}: {query['time']:.3f}s ({query['count']} سجل)")
        
        # تحليل إنشاء واجهة المستخدم
        if self.ui_creation_times:
            print("\n🎨 أداء إنشاء واجهة المستخدم:")
            sorted_ui = sorted(self.ui_creation_times, key=lambda x: x['time'], reverse=True)
            
            for ui_test in sorted_ui:
                per_element_ms = ui_test['per_element'] * 1000
                status = "🔴" if per_element_ms > 10 else "🟡" if per_element_ms > 5 else "🟢"
                print(f"   {status} {ui_test['name']}: {per_element_ms:.1f}ms لكل عنصر")
        
        # توصيات التحسين
        print("\n💡 توصيات التحسين:")
        
        # توصيات قاعدة البيانات
        slow_queries = [q for q in self.query_times if q['time'] > 0.1]
        if slow_queries:
            print("   🗄️ قاعدة البيانات:")
            for query in slow_queries:
                print(f"      - تحسين استعلام {query['name']}")
                print(f"        • إضافة فهارس للجداول")
                print(f"        • تحسين شروط WHERE")
                print(f"        • استخدام LIMIT للنتائج الكبيرة")
        
        # توصيات واجهة المستخدم
        slow_ui = [ui for ui in self.ui_creation_times if ui['per_element'] > 0.01]
        if slow_ui:
            print("   🎨 واجهة المستخدم:")
            for ui_test in slow_ui:
                print(f"      - تحسين إنشاء {ui_test['name']}")
                print(f"        • التحميل التدريجي")
                print(f"        • إعادة استخدام العناصر")
                print(f"        • التحميل في خيوط منفصلة")
        
        # توصيات عامة
        print("   🚀 تحسينات عامة:")
        print("      - استخدام التخزين المؤقت للبيانات")
        print("      - التحميل التدريجي للصفحات")
        print("      - تحسين استعلامات قاعدة البيانات")
        print("      - استخدام خيوط منفصلة للعمليات الثقيلة")

def main():
    """الدالة الرئيسية"""
    print("🚀 أداة تحليل أداء التطبيق")
    print("=" * 40)
    
    analyzer = PerformanceAnalyzer()
    
    # تحليل أداء قاعدة البيانات
    analyzer.analyze_database_queries()
    
    # تحليل أداء واجهة المستخدم
    analyzer.analyze_ui_creation()
    
    # محاكاة تحميل الصفحات
    analyzer.simulate_page_loading()
    
    # إنشاء التقرير
    analyzer.generate_report()

if __name__ == "__main__":
    main()
