#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات الخطوط والنصوص العربية
تحتوي على جميع إعدادات الخطوط ودعم RTL للنصوص العربية
"""

import customtkinter as ctk
import tkinter as tk
from .settings import ARABIC_FONT_CONFIG

class ArabicFontManager:
    """مدير الخطوط العربية ودعم RTL"""
    
    def __init__(self):
        self.available_fonts = self._get_available_fonts()
        self.selected_font = self._select_best_font()
        
    def _get_available_fonts(self):
        """الحصول على الخطوط المتاحة في النظام"""
        try:
            import tkinter.font as tkfont
            available = tkfont.families()
            return [font for font in ARABIC_FONT_CONFIG['font_families'] if font in available]
        except:
            return ['Arial']  # خط احتياطي
    
    def _select_best_font(self):
        """اختيار أفضل خط متاح للعربية"""
        if self.available_fonts:
            return self.available_fonts[0]
        return 'Arial'
    
    def get_font(self, size_key='body', weight='normal'):
        """الحصول على خط مع الحجم والوزن المحددين"""
        size = ARABIC_FONT_CONFIG['font_sizes'].get(size_key, 14)
        return ctk.CTkFont(family=self.selected_font, size=size, weight=weight)
    
    def get_rtl_label_config(self):
        """إعدادات التسميات مع دعم RTL"""
        return {
            'anchor': ARABIC_FONT_CONFIG['rtl_settings']['default_anchor'],
            'justify': ARABIC_FONT_CONFIG['rtl_settings']['label_justify']
        }
    
    def get_rtl_button_config(self):
        """إعدادات الأزرار مع دعم RTL"""
        return {
            'anchor': ARABIC_FONT_CONFIG['rtl_settings']['button_anchor']
        }
    
    def get_rtl_entry_config(self):
        """إعدادات حقول الإدخال مع دعم RTL"""
        return {
            'justify': ARABIC_FONT_CONFIG['rtl_settings']['input_justify']
        }

# إنشاء مثيل عام لمدير الخطوط
font_manager = ArabicFontManager()

# دوال مساعدة للحصول على الخطوط بسهولة
def get_arabic_font(size_key='body', weight='normal'):
    """دالة مساعدة للحصول على خط عربي"""
    return font_manager.get_font(size_key, weight)

def get_title_font():
    """خط العناوين الرئيسية"""
    return get_arabic_font('title', 'bold')

def get_subtitle_font():
    """خط العناوين الفرعية"""
    return get_arabic_font('subtitle', 'bold')

def get_header_font():
    """خط رؤوس الأقسام"""
    return get_arabic_font('header', 'bold')

def get_body_font():
    """خط النص العادي"""
    return get_arabic_font('body')

def get_small_font():
    """خط النص الصغير"""
    return get_arabic_font('small')

def get_button_font():
    """خط الأزرار"""
    return get_arabic_font('button', 'bold')

def get_input_font():
    """خط حقول الإدخال"""
    return get_arabic_font('input')

# إعدادات RTL للمكونات المختلفة
RTL_LABEL_CONFIG = font_manager.get_rtl_label_config()
RTL_BUTTON_CONFIG = font_manager.get_rtl_button_config()
RTL_ENTRY_CONFIG = font_manager.get_rtl_entry_config()

# دوال لإنشاء مكونات مع دعم RTL
def create_rtl_label(parent, text, font_size='body', **kwargs):
    """إنشاء تسمية مع دعم RTL"""
    config = RTL_LABEL_CONFIG.copy()
    config.update(kwargs)
    
    return ctk.CTkLabel(
        parent,
        text=text,
        font=get_arabic_font(font_size),
        **config
    )

def create_rtl_button(parent, text, command=None, **kwargs):
    """إنشاء زر مع دعم RTL"""
    config = RTL_BUTTON_CONFIG.copy()
    config.update(kwargs)
    
    return ctk.CTkButton(
        parent,
        text=text,
        font=get_button_font(),
        command=command,
        **config
    )

def create_rtl_entry(parent, placeholder_text="", **kwargs):
    """إنشاء حقل إدخال مع دعم RTL"""
    config = RTL_ENTRY_CONFIG.copy()
    config.update(kwargs)

    return ctk.CTkEntry(
        parent,
        placeholder_text=placeholder_text,
        font=get_input_font(),
        **config
    )

def create_rtl_textbox(parent, **kwargs):
    """إنشاء صندوق نص متعدد الأسطر مع دعم RTL"""
    # إعدادات RTL للـ textbox
    config = {
        'font': get_input_font(),
        'wrap': 'word',  # التفاف الكلمات
    }
    config.update(kwargs)

    # إنشاء الـ textbox
    textbox = ctk.CTkTextbox(parent, **config)

    # تطبيق إعدادات RTL على الـ textbox الداخلي
    try:
        # الوصول للـ textbox الداخلي وتطبيق إعدادات RTL
        textbox._textbox.configure(
            justify='right',  # محاذاة النص لليمين
            insertofftime=300,  # تحسين المؤشر
            insertontime=600
        )

        # ربط أحداث لضمان RTL عند الكتابة
        def on_key_press(event):
            # التأكد من أن المؤشر في الموضع الصحيح للنص العربي
            textbox._textbox.mark_set("insert", "insert")

        textbox._textbox.bind('<KeyPress>', on_key_press)

    except Exception as e:
        print(f"تحذير: لا يمكن تطبيق إعدادات RTL على textbox: {e}")

    return textbox

# معلومات الخطوط المتاحة
def get_font_info():
    """الحصول على معلومات الخطوط المتاحة"""
    return {
        'selected_font': font_manager.selected_font,
        'available_fonts': font_manager.available_fonts,
        'font_config': ARABIC_FONT_CONFIG
    }

def print_font_info():
    """طباعة معلومات الخطوط للتشخيص"""
    info = get_font_info()
    print("🔤 معلومات الخطوط العربية:")
    print(f"   الخط المحدد: {info['selected_font']}")
    print(f"   الخطوط المتاحة: {', '.join(info['available_fonts'])}")
    print(f"   دعم RTL: مفعل")
