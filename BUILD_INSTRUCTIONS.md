# تعليمات بناء برنامج إدارة الأموال

هذا الملف يشرح كيفية تحويل برنامج إدارة الأموال إلى ملف تنفيذي (.exe) وإنشاء حزمة تثبيت قابلة للتوزيع.

## المتطلبات الأساسية

1. **Python 3.8+** مثبت على جهازك
2. **PyInstaller** لإنشاء الملف التنفيذي
3. **Inno Setup** لإنشاء حزمة التثبيت

## خطوات البناء التلقائية

1. قم بتشغيل ملف `build_installer.bat` بالنقر المزدوج عليه
2. سيقوم الملف تلقائياً بالتحقق من وجود PyInstaller وتثبيته إذا لم يكن موجوداً
3. سيقوم ببناء الملف التنفيذي باستخدام PyInstaller
4. سيتحقق من وجود Inno Setup
5. سيقوم بإنشاء حزمة التثبيت باستخدام Inno Setup

## الملفات الناتجة

- **الملف التنفيذي**: `dist\Money Manager\Money Manager.exe`
- **حزمة التثبيت**: `installer_output\MoneyManagerSetup.exe`

## خطوات البناء اليدوية

إذا واجهتك مشكلة مع الملف التلقائي، يمكنك اتباع هذه الخطوات يدوياً:

### 1. بناء الملف التنفيذي

```
pyinstaller --noconfirm "Money Manager.spec"
```

### 2. بناء حزمة التثبيت

```
iscc installer.iss
```

## تثبيت المتطلبات

### تثبيت PyInstaller

```
pip install pyinstaller
```

### تثبيت Inno Setup

1. قم بتنزيل Inno Setup من الموقع الرسمي: https://jrsoftware.org/isdl.php
2. قم بتثبيته باتباع تعليمات المثبت

## استكشاف الأخطاء وإصلاحها

### مشاكل PyInstaller

- تأكد من تثبيت جميع المكتبات المطلوبة في `requirements.txt`
- تحقق من أن ملف `.spec` يحتوي على جميع الملفات والمجلدات المطلوبة

### مشاكل Inno Setup

- تأكد من إضافة Inno Setup إلى متغير PATH
- تحقق من أن ملف `installer.iss` يشير إلى المسار الصحيح للملفات التنفيذية

## ملاحظات إضافية

- يمكنك تعديل ملف `Money Manager.spec` لتخصيص إعدادات الملف التنفيذي
- يمكنك تعديل ملف `installer.iss` لتخصيص إعدادات حزمة التثبيت