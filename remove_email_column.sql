-- إزالة عمود البريد الإلكتروني من جدول المستخدمين
-- تشغيل هذا الملف لتحديث قاعدة البيانات الموجودة

USE money_manager;

-- التحقق من وجود عمود البريد الإلكتروني قبل إزالته
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'money_manager'
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'email'
);

-- إزالة عمود البريد الإلكتروني إذا كان موجوداً
SET @sql = IF(@column_exists > 0, 
    'ALTER TABLE users DROP COLUMN email;', 
    'SELECT "عمود البريد الإلكتروني غير موجود" as message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- عرض رسالة تأكيد
SELECT 'تم تحديث جدول المستخدمين بنجاح - تم إزالة عمود البريد الإلكتروني' as result;

-- عرض هيكل الجدول المحدث
DESCRIBE users;
