#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث قاعدة البيانات لإزالة حقل البريد الإلكتروني
"""

import sys
import os
import logging

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db

def setup_logging():
    """إعداد نظام السجلات"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('database_update.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def check_email_column_exists():
    """التحقق من وجود عمود البريد الإلكتروني"""
    try:
        query = """
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = 'money_manager'
            AND TABLE_NAME = 'users'
            AND COLUMN_NAME = 'email'
        """
        result = db.execute_query(query)
        return result[0]['count'] > 0 if result else False
    except Exception as e:
        logging.error(f"خطأ في التحقق من وجود عمود البريد الإلكتروني: {e}")
        return False

def remove_email_column():
    """إزالة عمود البريد الإلكتروني"""
    try:
        if not check_email_column_exists():
            logging.info("عمود البريد الإلكتروني غير موجود - لا حاجة للتحديث")
            return True
        
        logging.info("إزالة عمود البريد الإلكتروني...")
        query = "ALTER TABLE users DROP COLUMN email"
        db.execute_update(query)
        
        logging.info("تم إزالة عمود البريد الإلكتروني بنجاح")
        return True
        
    except Exception as e:
        logging.error(f"خطأ في إزالة عمود البريد الإلكتروني: {e}")
        return False

def verify_table_structure():
    """التحقق من هيكل الجدول بعد التحديث"""
    try:
        logging.info("التحقق من هيكل الجدول...")
        columns = db.execute_query("DESCRIBE users")
        
        if not columns:
            logging.error("لا يمكن الحصول على هيكل الجدول")
            return False
        
        logging.info("هيكل جدول المستخدمين:")
        for col in columns:
            logging.info(f"  - {col['Field']}: {col['Type']} {'NULL' if col['Null'] == 'YES' else 'NOT NULL'}")
        
        # التحقق من عدم وجود عمود البريد الإلكتروني
        column_names = [col['Field'] for col in columns]
        if 'email' in column_names:
            logging.error("عمود البريد الإلكتروني ما زال موجوداً!")
            return False
        
        logging.info("✅ تم التحقق من إزالة عمود البريد الإلكتروني بنجاح")
        return True
        
    except Exception as e:
        logging.error(f"خطأ في التحقق من هيكل الجدول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔄 تحديث قاعدة البيانات - إزالة حقل البريد الإلكتروني")
    print("=" * 60)
    
    setup_logging()
    
    try:
        # الاتصال بقاعدة البيانات
        if not db.connect():
            logging.error("فشل في الاتصال بقاعدة البيانات")
            return False
        
        logging.info("تم الاتصال بقاعدة البيانات بنجاح")
        
        # إزالة عمود البريد الإلكتروني
        if not remove_email_column():
            logging.error("فشل في إزالة عمود البريد الإلكتروني")
            return False
        
        # التحقق من هيكل الجدول
        if not verify_table_structure():
            logging.error("فشل في التحقق من هيكل الجدول")
            return False
        
        print("\n🎉 تم تحديث قاعدة البيانات بنجاح!")
        print("✅ تم إزالة حقل البريد الإلكتروني من جدول المستخدمين")
        print("✅ النظام الآن يدعم تسجيل الدخول للمدير فقط باستخدام اسم المستخدم")
        
        return True
        
    except Exception as e:
        logging.error(f"خطأ عام في تحديث قاعدة البيانات: {e}")
        return False
    
    finally:
        if db.connection:
            db.close()

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n❌ فشل في تحديث قاعدة البيانات!")
        print("راجع ملف database_update.log للتفاصيل")
    
    input("\nاضغط Enter للخروج...")
