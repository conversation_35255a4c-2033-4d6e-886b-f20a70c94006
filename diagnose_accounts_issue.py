#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص مشكلة عدم ظهور الحسابات
تفحص قاعدة البيانات ووظائف تحميل الحسابات لتحديد سبب المشكلة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from database.models import Account, User, Currency, AccountType
from utils.auth import auth_manager

class AccountsDiagnostic:
    """أداة تشخيص مشاكل الحسابات"""
    
    def __init__(self):
        self.issues_found = []
        self.recommendations = []
    
    def run_full_diagnostic(self):
        """تشغيل تشخيص شامل"""
        print("🔍 بدء تشخيص مشكلة عدم ظهور الحسابات...")
        print("=" * 70)
        
        # 1. فحص الاتصال بقاعدة البيانات
        self.check_database_connection()
        
        # 2. فحص جداول قاعدة البيانات
        self.check_database_tables()
        
        # 3. فحص المستخدم الحالي
        self.check_current_user()
        
        # 4. فحص الحسابات في قاعدة البيانات
        self.check_accounts_in_database()
        
        # 5. فحص أنواع الحسابات
        self.check_account_types()
        
        # 6. فحص العملات
        self.check_currencies()
        
        # 7. فحص وظائف تحميل الحسابات
        self.check_account_loading_functions()
        
        # 8. عرض النتائج والتوصيات
        self.display_results()
    
    def check_database_connection(self):
        """فحص الاتصال بقاعدة البيانات"""
        print("1️⃣ فحص الاتصال بقاعدة البيانات...")
        try:
            # اختبار استعلام بسيط
            result = db.execute_query("SELECT 1 as test")
            if result and result[0]['test'] == 1:
                print("   ✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح")
            else:
                print("   ❌ مشكلة في الاتصال بقاعدة البيانات")
                self.issues_found.append("مشكلة في الاتصال بقاعدة البيانات")
        except Exception as e:
            print(f"   ❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            self.issues_found.append(f"خطأ في الاتصال: {e}")
    
    def check_database_tables(self):
        """فحص وجود الجداول المطلوبة"""
        print("\n2️⃣ فحص جداول قاعدة البيانات...")
        required_tables = ['users', 'accounts', 'account_types', 'currencies', 'account_balances']
        
        try:
            for table in required_tables:
                result = db.execute_query(f"SHOW TABLES LIKE '{table}'")
                if result:
                    print(f"   ✅ جدول {table} موجود")
                else:
                    print(f"   ❌ جدول {table} غير موجود")
                    self.issues_found.append(f"جدول {table} غير موجود")
        except Exception as e:
            print(f"   ❌ خطأ في فحص الجداول: {e}")
            self.issues_found.append(f"خطأ في فحص الجداول: {e}")
    
    def check_current_user(self):
        """فحص المستخدم الحالي"""
        print("\n3️⃣ فحص المستخدم الحالي...")
        
        if not auth_manager.current_user:
            print("   ❌ لا يوجد مستخدم مسجل دخول")
            self.issues_found.append("لا يوجد مستخدم مسجل دخول")
            return
        
        user = auth_manager.current_user
        print(f"   ✅ المستخدم الحالي: {user.get('username', 'غير محدد')}")
        print(f"   📋 معرف المستخدم: {user.get('id', 'غير محدد')}")
        print(f"   📋 الاسم الكامل: {user.get('full_name', 'غير محدد')}")
        
        # التحقق من وجود المستخدم في قاعدة البيانات
        try:
            db_user = User.get_by_id(user['id'])
            if db_user:
                print("   ✅ المستخدم موجود في قاعدة البيانات")
            else:
                print("   ❌ المستخدم غير موجود في قاعدة البيانات")
                self.issues_found.append("المستخدم غير موجود في قاعدة البيانات")
        except Exception as e:
            print(f"   ❌ خطأ في التحقق من المستخدم: {e}")
            self.issues_found.append(f"خطأ في التحقق من المستخدم: {e}")
    
    def check_accounts_in_database(self):
        """فحص الحسابات في قاعدة البيانات"""
        print("\n4️⃣ فحص الحسابات في قاعدة البيانات...")
        
        if not auth_manager.current_user:
            print("   ⚠️ تخطي فحص الحسابات - لا يوجد مستخدم مسجل دخول")
            return
        
        user_id = auth_manager.current_user['id']
        
        try:
            # فحص جميع الحسابات للمستخدم
            all_accounts_query = "SELECT * FROM accounts WHERE user_id = %s"
            all_accounts = db.execute_query(all_accounts_query, (user_id,))
            
            print(f"   📊 إجمالي الحسابات للمستخدم: {len(all_accounts) if all_accounts else 0}")
            
            if all_accounts:
                active_accounts = [acc for acc in all_accounts if acc.get('is_active', False)]
                inactive_accounts = [acc for acc in all_accounts if not acc.get('is_active', True)]
                
                print(f"   ✅ الحسابات النشطة: {len(active_accounts)}")
                print(f"   ⚠️ الحسابات غير النشطة: {len(inactive_accounts)}")
                
                print("\n   📋 تفاصيل الحسابات:")
                for i, account in enumerate(all_accounts, 1):
                    status = "نشط" if account.get('is_active', False) else "غير نشط"
                    print(f"      {i}. {account.get('name', 'بدون اسم')} - {status}")
                    print(f"         المعرف: {account.get('id')}")
                    print(f"         نوع الحساب: {account.get('account_type_id')}")
                    print(f"         العملة: {account.get('currency_id', 'غير محدد')}")
                    print(f"         تاريخ الإنشاء: {account.get('created_at')}")
                
                if not active_accounts:
                    self.issues_found.append("جميع الحسابات غير نشطة (is_active = FALSE)")
                    self.recommendations.append("تفعيل الحسابات المطلوبة باستخدام Account.activate(account_id)")
            else:
                print("   ❌ لا توجد حسابات للمستخدم الحالي")
                self.issues_found.append("لا توجد حسابات للمستخدم الحالي")
                self.recommendations.append("إضافة حسابات جديدة للمستخدم")
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص الحسابات: {e}")
            self.issues_found.append(f"خطأ في فحص الحسابات: {e}")
    
    def check_account_types(self):
        """فحص أنواع الحسابات"""
        print("\n5️⃣ فحص أنواع الحسابات...")
        
        try:
            account_types = AccountType.get_all()
            print(f"   📊 عدد أنواع الحسابات المتاحة: {len(account_types) if account_types else 0}")
            
            if account_types:
                print("   📋 أنواع الحسابات المتاحة:")
                for acc_type in account_types:
                    print(f"      - {acc_type.get('id')}: {acc_type.get('name')}")
            else:
                print("   ❌ لا توجد أنواع حسابات")
                self.issues_found.append("لا توجد أنواع حسابات في قاعدة البيانات")
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص أنواع الحسابات: {e}")
            self.issues_found.append(f"خطأ في فحص أنواع الحسابات: {e}")
    
    def check_currencies(self):
        """فحص العملات"""
        print("\n6️⃣ فحص العملات...")
        
        try:
            currencies = Currency.get_all()
            print(f"   📊 عدد العملات المتاحة: {len(currencies) if currencies else 0}")
            
            if currencies:
                print("   📋 العملات المتاحة:")
                for currency in currencies:
                    print(f"      - {currency.get('id')}: {currency.get('name')} ({currency.get('code')})")
            else:
                print("   ❌ لا توجد عملات")
                self.issues_found.append("لا توجد عملات في قاعدة البيانات")
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص العملات: {e}")
            self.issues_found.append(f"خطأ في فحص العملات: {e}")
    
    def check_account_loading_functions(self):
        """فحص وظائف تحميل الحسابات"""
        print("\n7️⃣ فحص وظائف تحميل الحسابات...")
        
        if not auth_manager.current_user:
            print("   ⚠️ تخطي فحص وظائف التحميل - لا يوجد مستخدم مسجل دخول")
            return
        
        user_id = auth_manager.current_user['id']
        
        try:
            # اختبار Account.get_by_user()
            print("   🔍 اختبار Account.get_by_user()...")
            accounts = Account.get_by_user(user_id)
            print(f"      📊 عدد الحسابات المُحملة: {len(accounts) if accounts else 0}")
            
            if accounts:
                print("      📋 الحسابات المُحملة:")
                for account in accounts:
                    print(f"         - {account.get('name')} (ID: {account.get('id')})")
                    print(f"           النوع: {account.get('account_type_name')}")
                    print(f"           نشط: {account.get('is_active')}")
                    balances = account.get('balances', [])
                    print(f"           الأرصدة: {len(balances)} عملة")
            else:
                print("      ❌ لم يتم تحميل أي حسابات")
                self.issues_found.append("وظيفة Account.get_by_user() لا تُرجع أي حسابات")
            
            # اختبار الاستعلام المباشر للحسابات النشطة
            print("\n   🔍 اختبار الاستعلام المباشر للحسابات النشطة...")
            direct_query = "SELECT id, name FROM accounts WHERE user_id = %s AND is_active = TRUE"
            direct_accounts = db.execute_query(direct_query, (user_id,))
            print(f"      📊 عدد الحسابات النشطة (استعلام مباشر): {len(direct_accounts) if direct_accounts else 0}")
            
            if direct_accounts:
                print("      📋 الحسابات النشطة:")
                for account in direct_accounts:
                    print(f"         - {account.get('name')} (ID: {account.get('id')})")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار وظائف التحميل: {e}")
            self.issues_found.append(f"خطأ في وظائف التحميل: {e}")
    
    def display_results(self):
        """عرض النتائج والتوصيات"""
        print("\n" + "=" * 70)
        print("📊 نتائج التشخيص")
        print("=" * 70)
        
        if not self.issues_found:
            print("✅ لم يتم العثور على مشاكل! الحسابات يجب أن تظهر بشكل صحيح.")
        else:
            print(f"❌ تم العثور على {len(self.issues_found)} مشكلة:")
            for i, issue in enumerate(self.issues_found, 1):
                print(f"   {i}. {issue}")
        
        if self.recommendations:
            print(f"\n💡 التوصيات ({len(self.recommendations)}):")
            for i, rec in enumerate(self.recommendations, 1):
                print(f"   {i}. {rec}")
        
        print("\n" + "=" * 70)
        print("🔧 خطوات الإصلاح المقترحة:")
        print("1. تأكد من تسجيل الدخول بالمستخدم الصحيح")
        print("2. تحقق من أن الحسابات مُفعلة (is_active = TRUE)")
        print("3. تأكد من وجود أنواع حسابات وعملات في قاعدة البيانات")
        print("4. أعد تشغيل التطبيق وتحقق من نافذة الحسابات")
        print("5. إذا استمرت المشكلة، تحقق من سجلات الأخطاء")

def main():
    """الدالة الرئيسية"""
    try:
        # محاولة تسجيل دخول تلقائي للاختبار
        print("🔐 محاولة تسجيل دخول تلقائي للاختبار...")
        
        # الحصول على أول مستخدم متاح
        users = User.get_all()
        if users:
            auth_manager.current_user = users[0]
            print(f"✅ تم تسجيل الدخول كـ: {users[0].get('username')}")
        else:
            print("❌ لا توجد مستخدمين في قاعدة البيانات")
        
        # تشغيل التشخيص
        diagnostic = AccountsDiagnostic()
        diagnostic.run_full_diagnostic()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التشخيص: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
