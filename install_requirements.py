#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تثبيت تلقائي لمتطلبات برنامج إدارة الأموال
"""

import sys
import subprocess
import os
import platform
from datetime import datetime

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 70)
    print("📦 تثبيت متطلبات برنامج إدارة الأموال")
    print("=" * 70)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💻 نظام التشغيل: {platform.system()} {platform.release()}")
    print("=" * 70)

def check_python():
    """فحص Python"""
    print("\n🐍 فحص Python...")
    
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} متوافق")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} غير متوافق")
        print("💡 يتطلب Python 3.8 أو أحدث")
        return False

def check_pip():
    """فحص وترقية pip"""
    print("\n📦 فحص pip...")
    
    try:
        # فحص pip
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0:
            print("❌ pip غير متوفر")
            return False
        
        print("✅ pip متوفر")
        
        # ترقية pip
        print("🔄 ترقية pip...")
        upgrade_result = subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], capture_output=True, text=True, timeout=60)
        
        if upgrade_result.returncode == 0:
            print("✅ تم ترقية pip بنجاح")
        else:
            print("⚠️  تعذر ترقية pip، لكن يمكن المتابعة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص pip: {e}")
        return False

def install_package(package_name, package_version=None):
    """تثبيت حزمة واحدة"""
    try:
        if package_version:
            package_spec = f"{package_name}=={package_version}"
        else:
            package_spec = package_name
        
        print(f"📦 تثبيت {package_spec}...")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_spec
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ تم تثبيت {package_name} بنجاح")
            return True
        else:
            print(f"❌ فشل تثبيت {package_name}")
            print(f"خطأ: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ انتهت مهلة تثبيت {package_name}")
        return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت {package_name}: {e}")
        return False

def install_requirements_file():
    """تثبيت من ملف requirements.txt"""
    if not os.path.exists('requirements.txt'):
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    print("\n📋 تثبيت من ملف requirements.txt...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ تم تثبيت جميع المتطلبات من requirements.txt")
            return True
        else:
            print("❌ فشل تثبيت بعض المتطلبات من requirements.txt")
            print(f"خطأ: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ انتهت مهلة تثبيت المتطلبات")
        return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def install_packages_individually():
    """تثبيت الحزم منفصلة"""
    print("\n📦 تثبيت الحزم منفصلة...")
    
    packages = [
        ("mysql-connector-python", "8.2.0"),
        ("customtkinter", "5.2.2"),
        ("Pillow", "10.1.0"),
        ("python-dateutil", "2.8.2"),
        ("bcrypt", "4.1.2"),
        ("reportlab", "4.0.7"),
        ("matplotlib", "3.8.2"),
        ("pandas", "2.1.4"),
        ("openpyxl", "3.1.2"),
        ("hijri-converter", "2.3.1"),
        ("schedule", "1.2.0")
    ]
    
    successful = 0
    failed = 0
    
    for package_name, version in packages:
        if install_package(package_name, version):
            successful += 1
        else:
            failed += 1
            # محاولة تثبيت بدون تحديد الإصدار
            print(f"🔄 محاولة تثبيت {package_name} بدون تحديد الإصدار...")
            if install_package(package_name):
                successful += 1
                failed -= 1
    
    print(f"\n📊 النتائج: {successful} نجح، {failed} فشل")
    return failed == 0

def verify_installation():
    """التحقق من نجاح التثبيت"""
    print("\n🔍 التحقق من التثبيت...")
    
    modules_to_check = [
        ('mysql.connector', 'mysql-connector-python'),
        ('customtkinter', 'customtkinter'),
        ('PIL', 'Pillow'),
        ('dateutil', 'python-dateutil'),
        ('bcrypt', 'bcrypt'),
        ('reportlab', 'reportlab'),
        ('matplotlib', 'matplotlib'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('schedule', 'schedule')
    ]
    
    working_modules = 0
    total_modules = len(modules_to_check)
    
    for module_name, package_name in modules_to_check:
        try:
            if module_name == 'mysql.connector':
                import mysql.connector
            elif module_name == 'PIL':
                from PIL import Image
            elif module_name == 'dateutil':
                import dateutil
            else:
                __import__(module_name)
            
            print(f"✅ {package_name}: يعمل بشكل صحيح")
            working_modules += 1
            
        except ImportError:
            print(f"❌ {package_name}: غير مثبت أو لا يعمل")
        except Exception as e:
            print(f"⚠️  {package_name}: خطأ - {e}")
    
    # فحص hijri-converter (اختياري)
    try:
        import hijri_converter
        print("✅ hijri-converter: يعمل بشكل صحيح (اختياري)")
    except ImportError:
        print("⚠️  hijri-converter: غير مثبت (اختياري)")
    
    success_rate = (working_modules / total_modules) * 100
    print(f"\n📈 معدل النجاح: {success_rate:.1f}% ({working_modules}/{total_modules})")
    
    return working_modules >= (total_modules * 0.8)  # 80% على الأقل

def create_virtual_environment():
    """إنشاء بيئة افتراضية"""
    print("\n🏗️  إنشاء بيئة افتراضية...")
    
    venv_name = "money_manager_env"
    
    if os.path.exists(venv_name):
        print(f"ℹ️  البيئة الافتراضية {venv_name} موجودة مسبقاً")
        return True
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "venv", venv_name
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"✅ تم إنشاء البيئة الافتراضية: {venv_name}")
            
            # إرشادات التفعيل
            if platform.system() == "Windows":
                activate_cmd = f"{venv_name}\\Scripts\\activate"
            else:
                activate_cmd = f"source {venv_name}/bin/activate"
            
            print(f"💡 لتفعيل البيئة الافتراضية:")
            print(f"   {activate_cmd}")
            
            return True
        else:
            print("❌ فشل إنشاء البيئة الافتراضية")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيئة الافتراضية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص Python
    if not check_python():
        print("\n❌ يرجى تثبيت Python 3.8 أو أحدث أولاً")
        return False
    
    # فحص pip
    if not check_pip():
        print("\n❌ pip غير متوفر")
        return False
    
    # سؤال المستخدم عن البيئة الافتراضية
    print("\n❓ هل تريد إنشاء بيئة افتراضية؟ (موصى به)")
    create_venv = input("اكتب 'y' للموافقة أو أي شيء آخر للتخطي: ").lower().strip()
    
    if create_venv == 'y':
        create_virtual_environment()
        print("\n💡 تذكر تفعيل البيئة الافتراضية قبل تشغيل البرنامج")
    
    # محاولة التثبيت من requirements.txt أولاً
    success = False
    
    if os.path.exists('requirements.txt'):
        success = install_requirements_file()
    
    # إذا فشل، جرب التثبيت منفصل
    if not success:
        print("\n🔄 محاولة التثبيت منفصل...")
        success = install_packages_individually()
    
    # التحقق من التثبيت
    if success:
        verification_success = verify_installation()
        
        if verification_success:
            print("\n🎉 تم تثبيت جميع المتطلبات بنجاح!")
            print("🚀 يمكنك الآن تشغيل البرنامج:")
            print("   python main.py")
        else:
            print("\n⚠️  تم التثبيت لكن بعض المكتبات قد لا تعمل بشكل صحيح")
            print("💡 جرب تشغيل البرنامج وراجع الأخطاء إن وجدت")
    else:
        print("\n❌ فشل في تثبيت بعض المتطلبات")
        print("💡 جرب:")
        print("   1. تحديث pip: python -m pip install --upgrade pip")
        print("   2. التثبيت اليدوي: pip install package_name")
        print("   3. استخدام بيئة افتراضية")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        
        print("\n" + "=" * 70)
        if success:
            print("✅ اكتمل التثبيت بنجاح!")
        else:
            print("⚠️  التثبيت اكتمل مع بعض المشاكل")
        print("=" * 70)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف التثبيت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    finally:
        input("\n👋 اضغط Enter للخروج...")
