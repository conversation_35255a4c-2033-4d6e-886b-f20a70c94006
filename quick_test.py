#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 اختبار سريع...")

try:
    print("1. اختبار استيراد EditUserWindow...")
    from gui.user_management_windows import EditUserWindow
    print("✅ نجح استيراد EditUserWindow")
    
    print("2. اختبار استيراد auth_manager...")
    from utils.auth import auth_manager
    print("✅ نجح استيراد auth_manager")
    
    print("3. اختبار تسجيل الدخول...")
    success, message = auth_manager.login("admin2", "123456")
    if success:
        print("✅ نجح تسجيل الدخول")
    else:
        print(f"❌ فشل تسجيل الدخول: {message}")
    
    print("4. اختبار User model...")
    from database.models import User
    users = User.get_all()
    print(f"✅ تم جلب {len(users)} مستخدم")
    
    print("\n🎉 جميع الاختبارات نجحت!")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
