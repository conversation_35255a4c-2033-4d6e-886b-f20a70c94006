import mysql.connector
from config.settings import DATABASE_CONFIG

try:
    connection = mysql.connector.connect(**DATABASE_CONFIG)
    cursor = connection.cursor()
    
    print("💰 إضافة العملات المطلوبة...")
    
    # حذف العملات الموجودة وإعادة إدراجها
    cursor.execute("DELETE FROM currencies")
    
    currencies = [
        (1, 'SAR', 'ريال سعودي', 'ر.س'),
        (2, 'YER', 'ريال يمني', 'ر.ي'),
        (3, 'AED', 'درهم إماراتي', 'د.إ'),
        (4, 'USD', 'دولار أمريكي', '$')
    ]
    
    for currency_id, code, name, symbol in currencies:
        cursor.execute("""
            INSERT INTO currencies (id, code, name, symbol, is_active, exchange_rate)
            VALUES (%s, %s, %s, %s, 1, 1.0)
        """, (currency_id, code, name, symbol))
        print(f"   ✅ تم إضافة {name} ({code})")
    
    connection.commit()
    
    # عرض العملات
    cursor.execute("SELECT * FROM currencies")
    currencies = cursor.fetchall()
    
    print(f"\n📊 العملات الموجودة ({len(currencies)}):")
    for currency in currencies:
        print(f"   ID: {currency[0]} | Code: {currency[1]} | Name: {currency[2]} | Symbol: {currency[3]}")
    
    cursor.close()
    connection.close()
    
    print("\n✅ تم إصلاح العملات بنجاح!")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
