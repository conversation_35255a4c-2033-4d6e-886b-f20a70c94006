#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث العملات في قاعدة البيانات لتشمل العملات المطلوبة فقط
"""

import sys
import os
import mysql.connector

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def update_currencies():
    """تحديث العملات في قاعدة البيانات"""
    print("💱 تحديث العملات في قاعدة البيانات")
    print("=" * 50)
    
    try:
        # إعدادات الاتصال بقاعدة البيانات
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        # الاتصال بقاعدة البيانات
        print("🔌 الاتصال بقاعدة البيانات...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("✅ تم الاتصال بنجاح")
        
        # العملات المطلوبة
        required_currencies = [
            (1, 'SAR', 'ريال سعودي', 'ر.س', 1.0000, True),
            (2, 'YER', 'ريال يمني', 'ر.ي', 0.0040, True),
            (3, 'AED', 'درهم إماراتي', 'د.إ', 1.0200, True),
            (4, 'USD', 'دولار أمريكي', '$', 3.7500, True)
        ]
        
        print("\n📋 العملات المطلوبة:")
        for _, code, name, symbol, rate, _ in required_currencies:
            print(f"   • {name} ({code}) - {symbol} - سعر الصرف: {rate}")
        
        # إلغاء تفعيل جميع العملات أولاً
        print("\n🚫 إلغاء تفعيل جميع العملات الموجودة...")
        cursor.execute("UPDATE currencies SET is_active = FALSE")
        
        # تحديث/إضافة العملات المطلوبة
        print("✅ تحديث/إضافة العملات المطلوبة...")
        
        for currency_data in required_currencies:
            cursor.execute("""
                INSERT INTO currencies (id, code, name, symbol, exchange_rate, is_active) 
                VALUES (%s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    name = VALUES(name),
                    symbol = VALUES(symbol),
                    exchange_rate = VALUES(exchange_rate),
                    is_active = VALUES(is_active)
            """, currency_data)
        
        # التأكد من أن العملات المطلوبة فقط نشطة
        print("🔄 التأكد من تفعيل العملات المطلوبة فقط...")
        cursor.execute("""
            UPDATE currencies 
            SET is_active = TRUE 
            WHERE code IN ('SAR', 'YER', 'AED', 'USD')
        """)
        
        cursor.execute("""
            UPDATE currencies 
            SET is_active = FALSE 
            WHERE code NOT IN ('SAR', 'YER', 'AED', 'USD')
        """)
        
        # حفظ التغييرات
        connection.commit()
        
        # عرض النتيجة النهائية
        print("\n📊 العملات النشطة الآن:")
        cursor.execute("""
            SELECT code, name, symbol, exchange_rate, is_active 
            FROM currencies 
            WHERE is_active = TRUE 
            ORDER BY name
        """)
        
        active_currencies = cursor.fetchall()
        for code, name, symbol, rate, is_active in active_currencies:
            status = "نشط" if is_active else "غير نشط"
            print(f"   ✅ {name} ({code}) - {symbol} - {rate} - {status}")
        
        # عرض العملات غير النشطة
        print("\n📋 العملات غير النشطة:")
        cursor.execute("""
            SELECT code, name, symbol 
            FROM currencies 
            WHERE is_active = FALSE 
            ORDER BY name
        """)
        
        inactive_currencies = cursor.fetchall()
        if inactive_currencies:
            for code, name, symbol in inactive_currencies:
                print(f"   🚫 {name} ({code}) - {symbol}")
        else:
            print("   لا توجد عملات غير نشطة")
        
        # إغلاق الاتصال
        cursor.close()
        connection.close()
        
        print("\n" + "=" * 50)
        print("🎉 تم تحديث العملات بنجاح!")
        print("\n💡 العملات المتاحة الآن في قسم الحسابات:")
        print("   • ريال سعودي (ر.س)")
        print("   • ريال يمني (ر.ي)")
        print("   • درهم إماراتي (د.إ)")
        print("   • دولار أمريكي ($)")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_currencies_update():
    """اختبار تحديث العملات"""
    print("\n🧪 اختبار العملات المحدثة...")
    
    try:
        # استيراد النماذج
        from database.models import Currency
        
        # الحصول على العملات النشطة
        currencies = Currency.get_all()
        
        print(f"📊 عدد العملات النشطة: {len(currencies)}")
        
        expected_codes = ['SAR', 'YER', 'AED', 'USD']
        actual_codes = [curr['code'] for curr in currencies]
        
        print("🔍 التحقق من العملات المطلوبة:")
        for code in expected_codes:
            if code in actual_codes:
                print(f"   ✅ {code} موجود")
            else:
                print(f"   ❌ {code} غير موجود")
        
        # التحقق من عدم وجود عملات إضافية
        extra_codes = [code for code in actual_codes if code not in expected_codes]
        if extra_codes:
            print(f"⚠️ عملات إضافية غير مطلوبة: {extra_codes}")
        else:
            print("✅ لا توجد عملات إضافية")
        
        return len(currencies) == 4 and set(actual_codes) == set(expected_codes)
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    print("💱 تحديث العملات المدعومة")
    print("=" * 60)
    
    # تحديث العملات
    if update_currencies():
        # اختبار التحديث
        if test_currencies_update():
            print("\n🎯 تم التحديث والاختبار بنجاح!")
        else:
            print("\n⚠️ التحديث تم لكن الاختبار فشل")
    else:
        print("\n❌ فشل في تحديث العملات")
    
    print("\n🏁 انتهى التحديث")
