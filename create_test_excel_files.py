#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملفات Excel تجريبية لاختبار إصلاحات RTL في استيراد Excel
"""

import pandas as pd
import os
from datetime import datetime, timedelta

def create_test_excel_file():
    """إنشاء ملف Excel تجريبي لاختبار الاستيراد"""
    
    # بيانات تجريبية للواردات
    income_data = {
        'المبلغ': [1000, 1500, 2000, 500, 750],
        'الحساب': ['الحساب الرئيسي', 'حساب التوفير', 'الحساب الرئيسي', 'حساب التوفير', 'الحساب الرئيسي'],
        'العملة': ['SAR', 'USD', 'SAR', 'AED', 'YER'],
        'التاريخ': [
            datetime.now().strftime('%Y-%m-%d'),
            (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
            (datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d'),
            (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d'),
            (datetime.now() - timedelta(days=4)).strftime('%Y-%m-%d')
        ],
        'الوصف': [
            'راتب شهري',
            'مكافأة إضافية',
            'عمولة مبيعات',
            'استثمار',
            'دخل إضافي'
        ]
    }
    
    # بيانات تجريبية للمصروفات
    expense_data = {
        'المبلغ': [200, 150, 300, 100, 250],
        'الحساب': ['الحساب الرئيسي', 'حساب التوفير', 'الحساب الرئيسي', 'حساب التوفير', 'الحساب الرئيسي'],
        'العملة': ['SAR', 'USD', 'SAR', 'AED', 'YER'],
        'التاريخ': [
            datetime.now().strftime('%Y-%m-%d'),
            (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
            (datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d'),
            (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d'),
            (datetime.now() - timedelta(days=4)).strftime('%Y-%m-%d')
        ],
        'الوصف': [
            'مشتريات منزلية',
            'وقود السيارة',
            'فاتورة كهرباء',
            'مطعم',
            'صيانة السيارة'
        ]
    }
    
    # إنشاء DataFrames
    income_df = pd.DataFrame(income_data)
    expense_df = pd.DataFrame(expense_data)
    
    # حفظ الملفات
    income_file = 'test_income_data.xlsx'
    expense_file = 'test_expense_data.xlsx'
    
    try:
        income_df.to_excel(income_file, index=False, engine='openpyxl')
        expense_df.to_excel(expense_file, index=False, engine='openpyxl')
        
        print("✅ تم إنشاء ملفات Excel التجريبية بنجاح:")
        print(f"   📊 ملف الواردات: {income_file}")
        print(f"   📊 ملف المصروفات: {expense_file}")
        print()
        print("📋 محتوى ملف الواردات:")
        print(income_df.to_string(index=False))
        print()
        print("📋 محتوى ملف المصروفات:")
        print(expense_df.to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملفات Excel: {e}")
        return False

def create_test_with_errors():
    """إنشاء ملف Excel يحتوي على أخطاء لاختبار عرض الأخطاء"""
    
    # بيانات تحتوي على أخطاء متعمدة
    error_data = {
        'المبلغ': [1000, 'نص خاطئ', -500, 0, 2000],  # أخطاء في المبلغ
        'الحساب': ['الحساب الرئيسي', 'حساب غير موجود', '', 'حساب التوفير', 'حساب خاطئ'],  # أخطاء في الحساب
        'العملة': ['SAR', 'XYZ', '', 'USD', 'ABC'],  # أخطاء في العملة
        'التاريخ': [
            datetime.now().strftime('%Y-%m-%d'),
            'تاريخ خاطئ',
            '',
            '2023-13-45',  # تاريخ غير صحيح
            datetime.now().strftime('%Y-%m-%d')
        ],
        'الوصف': [
            'معاملة صحيحة',
            'معاملة بأخطاء',
            'معاملة بأخطاء أخرى',
            'معاملة بتاريخ خاطئ',
            'معاملة بعملة خاطئة'
        ]
    }
    
    error_df = pd.DataFrame(error_data)
    error_file = 'test_error_data.xlsx'
    
    try:
        error_df.to_excel(error_file, index=False, engine='openpyxl')
        print(f"✅ تم إنشاء ملف الأخطاء التجريبي: {error_file}")
        print("📋 محتوى ملف الأخطاء:")
        print(error_df.to_string(index=False))
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف الأخطاء: {e}")
        return False

if __name__ == "__main__":
    print("🧪 إنشاء ملفات Excel التجريبية لاختبار إصلاحات RTL")
    print("=" * 60)
    
    # إنشاء ملفات البيانات الصحيحة
    create_test_excel_file()
    print()
    
    # إنشاء ملف الأخطاء
    create_test_with_errors()
    print()
    
    print("🔍 لاختبار الاستيراد:")
    print("1. شغل التطبيق: python main.py")
    print("2. اذهب إلى قسم الواردات أو المصروفات")
    print("3. اضغط على 'استيراد من Excel'")
    print("4. اختر أحد الملفين المُنشأين")
    print("5. تحقق من عرض RTL في نوافذ الحوار")
    print()
    print("✨ تم الانتهاء من إنشاء جميع ملفات الاختبار!")
