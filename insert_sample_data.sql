-- إدراج البيانات التجريبية لمدير الأموال
USE money_manager;

-- إدراج المستخدم الافتراضي
-- كلمة المرور المشفرة لـ "123456"
INSERT INTO users (username, password_hash, full_name, role, is_active)
VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSn9Uy/S', 'المدير الافتراضي', 'admin', 1);

-- الحصول على معرف المستخدم
SET @user_id = LAST_INSERT_ID();

-- إدراج حسابات تجريبية
INSERT INTO accounts (user_id, name, account_type_id, currency_id, initial_balance, current_balance, description) VALUES
(@user_id, 'الصندوق النقدي', 1, 1, 5000.00, 5000.00, 'النقد المتوفر في اليد'),
(@user_id, 'حساب الراجحي', 2, 1, 25000.00, 25000.00, 'الحساب الجاري في بنك الراجحي'),
(@user_id, 'حساب التوفير', 6, 1, 50000.00, 50000.00, 'حساب التوفير طويل المدى'),
(@user_id, 'محفظة PayPal', 4, 2, 1200.00, 1200.00, 'محفظة PayPal للمعاملات الدولية'),
(@user_id, 'بطاقة فيزا', 3, 1, -2500.00, -2500.00, 'بطاقة ائتمان فيزا');

-- إدراج معاملات تجريبية (واردات)
INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_id, description, transaction_date) VALUES
(@user_id, 2, 'income', 8000.00, 1, 1, 'راتب شهر ديسمبر', '2025-06-25'),
(@user_id, 1, 'income', 1500.00, 1, 2, 'دخل من مشروع جانبي', '2025-06-20'),
(@user_id, 4, 'income', 500.00, 2, 4, 'مشروع تطوير موقع', '2025-06-15'),
(@user_id, 3, 'income', 200.00, 1, 3, 'أرباح استثمارية', '2025-06-10');

-- إدراج معاملات تجريبية (مصروفات)
INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_id, description, transaction_date) VALUES
(@user_id, 1, 'expense', 300.00, 1, 1, 'تسوق من السوبر ماركت', '2025-06-28'),
(@user_id, 2, 'expense', 1200.00, 1, 3, 'إيجار الشقة', '2025-06-26'),
(@user_id, 5, 'expense', 150.00, 1, 2, 'وقود السيارة', '2025-06-27'),
(@user_id, 2, 'expense', 400.00, 1, 4, 'فاتورة الكهرباء والماء', '2025-06-22'),
(@user_id, 1, 'expense', 80.00, 1, 7, 'تذاكر السينما', '2025-06-25'),
(@user_id, 2, 'expense', 250.00, 1, 8, 'ملابس جديدة', '2025-06-23');

-- إدراج تحويلات تجريبية
INSERT INTO transfers (user_id, from_account_id, to_account_id, amount, currency_id, description, transfer_date) VALUES
(@user_id, 2, 1, 1000.00, 1, 'سحب نقدي من البنك', '2025-06-21'),
(@user_id, 2, 3, 2000.00, 1, 'تحويل للتوفير', '2025-06-17');

-- تحديث أرصدة الحسابات بناءً على المعاملات
UPDATE accounts SET current_balance = initial_balance + 
    (SELECT COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END), 0) 
     FROM transactions WHERE account_id = accounts.id)
WHERE user_id = @user_id;

-- تحديث أرصدة الحسابات بناءً على التحويلات
UPDATE accounts SET current_balance = current_balance - 
    (SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE from_account_id = accounts.id)
WHERE user_id = @user_id;

UPDATE accounts SET current_balance = current_balance + 
    (SELECT COALESCE(SUM(amount), 0) FROM transfers WHERE to_account_id = accounts.id)
WHERE user_id = @user_id;

-- عرض ملخص البيانات المُدرجة
SELECT 'تم إنشاء البيانات التجريبية بنجاح!' as message;
SELECT CONCAT('المستخدم: ', username, ' | كلمة المرور: 123456') as login_info FROM users WHERE username = 'admin';
SELECT CONCAT('عدد الحسابات: ', COUNT(*)) as accounts_count FROM accounts WHERE user_id = @user_id;
SELECT CONCAT('عدد المعاملات: ', COUNT(*)) as transactions_count FROM transactions WHERE user_id = @user_id;
SELECT CONCAT('عدد التحويلات: ', COUNT(*)) as transfers_count FROM transfers WHERE user_id = @user_id;
