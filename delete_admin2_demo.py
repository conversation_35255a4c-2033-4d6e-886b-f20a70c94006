#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محاكاة عملية حذف المستخدم admin2 باستخدام الميزة المحدثة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from utils.auth import auth_manager
import logging

def simulate_admin2_deletion():
    """محاكاة عملية حذف المستخدم admin2"""
    print("🎯 محاكاة حذف المستخدم admin2 باستخدام الميزة المحدثة")
    print("=" * 65)
    
    try:
        # الخطوة 1: الاتصال بقاعدة البيانات
        print("1️⃣ الاتصال بقاعدة البيانات...")
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # الخطوة 2: تسجيل الدخول كمدير
        print("\n2️⃣ تسجيل الدخول كمدير...")
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        print(f"✅ تم تسجيل الدخول بنجاح كمدير: {auth_manager.current_user['username']}")
        
        # الخطوة 3: البحث عن المستخدم admin2
        print("\n3️⃣ البحث عن المستخدم admin2...")
        user_query = "SELECT * FROM users WHERE username = 'admin2'"
        admin2_users = db.execute_query(user_query)
        
        if not admin2_users:
            print("❌ لم يتم العثور على المستخدم admin2")
            return False
        
        admin2_user = admin2_users[0]
        print(f"✅ تم العثور على المستخدم admin2:")
        print(f"   - ID: {admin2_user['id']}")
        print(f"   - اسم المستخدم: {admin2_user['username']}")
        print(f"   - الدور: {admin2_user['role']}")
        print(f"   - نشط: {'نعم' if admin2_user['is_active'] else 'لا'}")
        print(f"   - تاريخ الإنشاء: {admin2_user['created_at']}")
        
        # الخطوة 4: فحص عدد المديرين قبل الحذف
        print("\n4️⃣ فحص عدد المديرين في النظام...")
        admin_count_query = "SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = TRUE"
        admin_result = db.execute_query(admin_count_query)
        active_admin_count = admin_result[0]['count'] if admin_result else 0
        
        total_admin_query = "SELECT COUNT(*) as count FROM users WHERE role = 'admin'"
        total_admin_result = db.execute_query(total_admin_query)
        total_admin_count = total_admin_result[0]['count'] if total_admin_result else 0
        
        print(f"   📊 إجمالي المديرين: {total_admin_count}")
        print(f"   📊 المديرين النشطين: {active_admin_count}")
        
        # الخطوة 5: محاكاة فحص أمان الحذف
        print("\n5️⃣ فحص أمان حذف المدير...")
        
        # محاكاة دالة check_admin_deletion_safety
        if admin2_user.get('role') != 'admin':
            can_delete = True
            safety_message = "مستخدم عادي - يمكن حذفه بأمان"
        else:
            if total_admin_count <= 1:
                can_delete = False
                safety_message = "لا يمكن حذف المدير الوحيد في النظام"
            else:
                can_delete = True
                safety_message = f"يوجد {total_admin_count} مديرين في النظام، يمكن حذف هذا المدير بأمان"
        
        print(f"   🔒 أمان الحذف: {'✅ آمن' if can_delete else '❌ غير آمن'}")
        print(f"   💬 رسالة الأمان: {safety_message}")
        
        if not can_delete:
            print("❌ لا يمكن المتابعة - الحذف غير آمن")
            return False
        
        # الخطوة 6: محاكاة رسائل التحذير
        print("\n6️⃣ عرض رسائل التحذير...")
        
        is_admin_user = admin2_user.get('role') == 'admin'
        user_type_text = "المدير" if is_admin_user else "المستخدم"
        
        if is_admin_user:
            print("🚨 رسالة التحذير الأولى للمدير:")
            print("=" * 50)
            print(f"""🚨 تحذير خطير: حذف مدير النظام 🚨

أنت على وشك حذف المستخدم المدير '{admin2_user['username']}' نهائياً!

⚠️ تحذيرات مهمة:
• هذا مستخدم مدير له صلاحيات كاملة في النظام
• سيفقد جميع صلاحياته الإدارية فوراً
• لن يتمكن من الوصول لإدارة النظام مرة أخرى
• سيتم حذف جميع بياناته وسجلاته نهائياً
• لا يمكن التراجع عن هذا الإجراء أبداً

🔒 تأكد من وجود مديرين آخرين في النظام قبل المتابعة

هل أنت متأكد من حذف هذا المدير نهائياً؟""")
            print("=" * 50)
            
            print("\n🚨 رسالة التأكيد النهائي للمدير:")
            print("=" * 50)
            print(f"""🚨 التأكيد الأخير لحذف المدير 🚨

هذا هو التأكيد الأخير لحذف المدير '{admin2_user['username']}'!

⚠️ تذكير أخير:
• سيفقد جميع صلاحياته الإدارية
• لن يتمكن من إدارة النظام مرة أخرى
• سيتم حذف جميع بياناته نهائياً
• لا يمكن التراجع عن هذا الإجراء

هل أنت متأكد 100% من حذف هذا المدير؟""")
            print("=" * 50)
        
        # الخطوة 7: تأكيد المستخدم (محاكاة)
        print(f"\n7️⃣ تأكيد حذف {user_type_text}...")
        response = input(f"هل تريد المتابعة مع حذف {user_type_text} '{admin2_user['username']}'؟ (y/N): ").strip().lower()
        
        if response not in ['y', 'yes', 'نعم']:
            print("❌ تم إلغاء عملية الحذف بناءً على طلب المستخدم")
            return False
        
        # الخطوة 8: تنفيذ الحذف الفعلي
        print(f"\n8️⃣ تنفيذ حذف {user_type_text} '{admin2_user['username']}'...")
        
        # استخدام دالة الحذف من auth_manager
        success, message = auth_manager.delete_user_permanently(admin2_user['id'])
        
        if success:
            print(f"✅ تم حذف {user_type_text} '{admin2_user['username']}' نهائياً بنجاح!")
            
            # تسجيل العملية
            log_message = f"حذف نهائي للمستخدم {admin2_user['username']} (النوع: {admin2_user.get('role', 'user')}) بواسطة المدير {auth_manager.current_user['username']}"
            
            auth_manager.log_activity(
                'delete_user_permanently',
                'users',
                admin2_user['id'],
                log_message
            )
            
            # تسجيل إضافي في ملف السجل
            if is_admin_user:
                logging.warning(f"تم حذف مدير النظام: {admin2_user['username']} (ID: {admin2_user['id']}) بواسطة المدير: {auth_manager.current_user['username']} (ID: {auth_manager.current_user['id']})")
            else:
                logging.info(f"تم حذف المستخدم: {admin2_user['username']} (ID: {admin2_user['id']}) بواسطة المدير: {auth_manager.current_user['username']} (ID: {auth_manager.current_user['id']})")
            
            print("📝 تم تسجيل العملية في ملف السجل")
            
        else:
            print(f"❌ فشل في حذف {user_type_text}: {message}")
            
            # تسجيل محاولة الحذف الفاشلة
            logging.error(f"فشل في حذف المستخدم {admin2_user['username']} (ID: {admin2_user['id']}) بواسطة المدير {auth_manager.current_user['username']}: {message}")
            return False
        
        # الخطوة 9: التحقق من النتيجة
        print(f"\n9️⃣ التحقق من نتيجة الحذف...")
        
        # فحص ما إذا كان المستخدم لا يزال موجوداً
        verification_query = "SELECT * FROM users WHERE username = 'admin2'"
        remaining_users = db.execute_query(verification_query)
        
        if not remaining_users:
            print("✅ تأكيد: تم حذف المستخدم admin2 نهائياً من قاعدة البيانات")
        else:
            print("⚠️ تحذير: المستخدم admin2 لا يزال موجوداً في قاعدة البيانات")
        
        # فحص عدد المديرين بعد الحذف
        final_admin_result = db.execute_query(admin_count_query)
        final_admin_count = final_admin_result[0]['count'] if final_admin_result else 0
        
        print(f"📊 عدد المديرين النشطين بعد الحذف: {final_admin_count}")
        
        auth_manager.logout()
        
        print("\n" + "=" * 65)
        print("🎉 تمت عملية الحذف بنجاح!")
        print("📋 ملخص العملية:")
        print(f"   👤 المستخدم المحذوف: {admin2_user['username']}")
        print(f"   🔐 نوع المستخدم: {user_type_text}")
        print(f"   📝 تم التسجيل: نعم")
        print(f"   🔒 أمان العملية: تم التحقق")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عملية الحذف: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 تطبيق عملي لحذف المستخدم admin2")
    print("=" * 45)
    
    print("📋 هذا السكريبت سيقوم بـ:")
    print("   1. محاكاة الخطوات المطلوبة لحذف admin2")
    print("   2. عرض رسائل التحذير الفعلية")
    print("   3. تنفيذ الحذف الفعلي إذا تم التأكيد")
    print("   4. التحقق من نتيجة العملية")
    
    print("\n⚠️ تحذير: هذا سيحذف المستخدم admin2 نهائياً!")
    
    if simulate_admin2_deletion():
        print("\n✅ تمت العملية بنجاح!")
        print("\n📋 الخطوات التي تمت:")
        print("   ✅ تسجيل الدخول كمدير")
        print("   ✅ البحث عن المستخدم admin2")
        print("   ✅ فحص أمان الحذف")
        print("   ✅ عرض رسائل التحذير")
        print("   ✅ تنفيذ الحذف")
        print("   ✅ تسجيل العملية")
        print("   ✅ التحقق من النتيجة")
        
        print("\n🎯 الميزة الجديدة تعمل بشكل مثالي!")
        
    else:
        print("\n❌ فشلت العملية أو تم إلغاؤها")

if __name__ == "__main__":
    main()
