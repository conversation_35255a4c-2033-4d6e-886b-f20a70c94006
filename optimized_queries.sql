-- dashboard_summary

                SELECT
                    c.code, c.name, c.symbol,
                    COALESCE(SUM(ab.balance), 0) as total_balance,
                    COUNT(DISTINCT a.id) as accounts_count
                FROM currencies c
                LEFT JOIN account_balances ab ON c.id = ab.currency_id
                INNER JOIN accounts a ON ab.account_id = a.id
                WHERE c.is_active = 1
                AND a.user_id = %s
                AND a.is_active = 1
                GROUP BY c.id, c.code, c.name, c.symbol
                HAVING total_balance > 0 OR accounts_count > 0
                ORDER BY total_balance DESC
                LIMIT 5
            ;

-- recent_transactions

                SELECT t.id, t.description, t.amount, t.transaction_date,
                       t.transaction_type, a.name as account_name, 
                       c.symbol as currency_symbol
                FROM transactions t
                FORCE INDEX (idx_transactions_user_type)
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = %s
                ORDER BY t.transaction_date DESC, t.id DESC
                LIMIT 10
            ;

-- income_page

                SELECT t.id, t.description, t.amount, t.transaction_date,
                       a.name as account_name, c.symbol as currency_symbol
                FROM transactions t
                FORCE INDEX (idx_transactions_user_type)
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = %s AND t.transaction_type = 'income'
                ORDER BY t.transaction_date DESC, t.id DESC
                LIMIT 20
            ;

-- expense_page

                SELECT t.id, t.description, t.amount, t.transaction_date,
                       a.name as account_name, c.symbol as currency_symbol
                FROM transactions t
                FORCE INDEX (idx_transactions_user_type)
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = %s AND t.transaction_type = 'expense'
                ORDER BY t.transaction_date DESC, t.id DESC
                LIMIT 20
            ;

