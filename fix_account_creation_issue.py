#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة إضافة الحسابات الجديدة في تطبيق إدارة الأموال
"""

import os
import sys
import json
import mysql.connector
from mysql.connector import Error
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class AccountCreationFixer:
    """فئة إصلاح مشكلة إنشاء الحسابات"""
    
    def __init__(self):
        self.connection = None
        self.cursor = None
        self.config = self.load_config()
        
    def load_config(self):
        """تحميل إعدادات قاعدة البيانات"""
        config_file = "config/database_settings.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return {
                "host": "localhost",
                "port": 3306,
                "user": "root",
                "password": "mohdam",
                "database": "money_manager"
            }
    
    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            self.cursor = self.connection.cursor(dictionary=True)
            print("✓ تم الاتصال بقاعدة البيانات")
            return True
        except Exception as e:
            print(f"✗ خطأ في الاتصال: {e}")
            return False
    
    def diagnose_account_creation_issues(self):
        """تشخيص مشاكل إنشاء الحسابات"""
        print("=" * 50)
        print("تشخيص مشاكل إنشاء الحسابات")
        print("=" * 50)
        
        issues = []
        
        try:
            # 1. فحص وجود جدول الحسابات
            self.cursor.execute("SHOW TABLES LIKE 'accounts'")
            if not self.cursor.fetchone():
                issues.append("جدول الحسابات غير موجود")
                print("✗ جدول الحسابات غير موجود")
            else:
                print("✓ جدول الحسابات موجود")
            
            # 2. فحص بنية جدول الحسابات
            self.cursor.execute("DESCRIBE accounts")
            columns = {col['Field']: col for col in self.cursor.fetchall()}
            
            required_columns = ['id', 'user_id', 'name', 'account_type_id', 'description', 'is_active']
            missing_columns = []
            
            for col in required_columns:
                if col not in columns:
                    missing_columns.append(col)
            
            if missing_columns:
                issues.append(f"أعمدة مفقودة في جدول الحسابات: {', '.join(missing_columns)}")
                print(f"✗ أعمدة مفقودة: {', '.join(missing_columns)}")
            else:
                print("✓ جميع الأعمدة المطلوبة موجودة")
            
            # 3. فحص وجود جدول أنواع الحسابات
            self.cursor.execute("SHOW TABLES LIKE 'account_types'")
            if not self.cursor.fetchone():
                issues.append("جدول أنواع الحسابات غير موجود")
                print("✗ جدول أنواع الحسابات غير موجود")
            else:
                print("✓ جدول أنواع الحسابات موجود")
                
                # فحص وجود بيانات في جدول أنواع الحسابات
                self.cursor.execute("SELECT COUNT(*) as count FROM account_types")
                count = self.cursor.fetchone()['count']
                
                if count == 0:
                    issues.append("لا توجد أنواع حسابات في قاعدة البيانات")
                    print("✗ لا توجد أنواع حسابات")
                else:
                    print(f"✓ توجد {count} أنواع حسابات")
            
            # 4. فحص وجود جدول العملات
            self.cursor.execute("SHOW TABLES LIKE 'currencies'")
            if not self.cursor.fetchone():
                issues.append("جدول العملات غير موجود")
                print("✗ جدول العملات غير موجود")
            else:
                print("✓ جدول العملات موجود")
                
                # فحص وجود بيانات في جدول العملات
                self.cursor.execute("SELECT COUNT(*) as count FROM currencies")
                count = self.cursor.fetchone()['count']
                
                if count == 0:
                    issues.append("لا توجد عملات في قاعدة البيانات")
                    print("✗ لا توجد عملات")
                else:
                    print(f"✓ توجد {count} عملات")
            
            # 5. فحص وجود جدول أرصدة الحسابات
            self.cursor.execute("SHOW TABLES LIKE 'account_balances'")
            if not self.cursor.fetchone():
                issues.append("جدول أرصدة الحسابات غير موجود")
                print("✗ جدول أرصدة الحسابات غير موجود")
            else:
                print("✓ جدول أرصدة الحسابات موجود")
            
            # 6. فحص المفاتيح الخارجية
            self.cursor.execute("""
                SELECT 
                    CONSTRAINT_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = %s 
                AND TABLE_NAME = 'accounts' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            """, (self.config['database'],))
            
            foreign_keys = self.cursor.fetchall()
            
            if not foreign_keys:
                issues.append("لا توجد مفاتيح خارجية في جدول الحسابات")
                print("✗ لا توجد مفاتيح خارجية")
            else:
                print(f"✓ توجد {len(foreign_keys)} مفاتيح خارجية")
            
            # 7. اختبار إنشاء حساب تجريبي
            print("\nاختبار إنشاء حساب تجريبي...")
            test_result = self.test_account_creation()
            
            if not test_result:
                issues.append("فشل في اختبار إنشاء حساب تجريبي")
            
            return issues
            
        except Exception as e:
            print(f"✗ خطأ في التشخيص: {e}")
            issues.append(f"خطأ في التشخيص: {e}")
            return issues
    
    def test_account_creation(self):
        """اختبار إنشاء حساب تجريبي"""
        try:
            # التحقق من وجود مستخدم للاختبار
            self.cursor.execute("SELECT id FROM users LIMIT 1")
            user = self.cursor.fetchone()
            
            if not user:
                print("✗ لا يوجد مستخدمين للاختبار")
                return False
            
            user_id = user['id']
            
            # التحقق من وجود نوع حساب للاختبار
            self.cursor.execute("SELECT id FROM account_types LIMIT 1")
            account_type = self.cursor.fetchone()
            
            if not account_type:
                print("✗ لا توجد أنواع حسابات للاختبار")
                return False
            
            account_type_id = account_type['id']
            
            # محاولة إنشاء حساب تجريبي
            test_account_name = f"حساب تجريبي {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            insert_query = """
            INSERT INTO accounts (user_id, name, account_type_id, description, is_active)
            VALUES (%s, %s, %s, %s, %s)
            """
            
            values = (user_id, test_account_name, account_type_id, "حساب تجريبي للاختبار", True)
            
            self.cursor.execute(insert_query, values)
            account_id = self.cursor.lastrowid
            
            if account_id > 0:
                print(f"✓ تم إنشاء حساب تجريبي بنجاح (ID: {account_id})")
                
                # حذف الحساب التجريبي
                self.cursor.execute("DELETE FROM accounts WHERE id = %s", (account_id,))
                self.connection.commit()
                
                print("✓ تم حذف الحساب التجريبي")
                return True
            else:
                print("✗ فشل في إنشاء الحساب التجريبي")
                return False
                
        except Exception as e:
            print(f"✗ خطأ في اختبار إنشاء الحساب: {e}")
            return False
    
    def fix_account_creation_issues(self):
        """إصلاح مشاكل إنشاء الحسابات"""
        print("\n" + "=" * 50)
        print("إصلاح مشاكل إنشاء الحسابات")
        print("=" * 50)
        
        try:
            # 1. إنشاء جدول الحسابات إذا لم يكن موجوداً
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS accounts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    name VARCHAR(100) NOT NULL,
                    account_type_id INT NOT NULL,
                    description TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (account_type_id) REFERENCES account_types(id)
                )
            """)
            print("✓ تم التأكد من وجود جدول الحسابات")
            
            # 2. إنشاء جدول أنواع الحسابات إذا لم يكن موجوداً
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS account_types (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(50) NOT NULL,
                    description TEXT,
                    icon VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✓ تم التأكد من وجود جدول أنواع الحسابات")
            
            # 3. إدراج أنواع الحسابات الأساسية
            self.cursor.execute("SELECT COUNT(*) as count FROM account_types")
            if self.cursor.fetchone()['count'] == 0:
                account_types = [
                    ('صندوق نقدي', 'نقد في اليد', 'cash'),
                    ('حساب بنكي', 'حساب في البنك', 'bank'),
                    ('بطاقة ائتمان', 'بطاقة ائتمان', 'credit_card'),
                    ('محفظة إلكترونية', 'محفظة رقمية', 'digital_wallet'),
                    ('استثمار', 'حساب استثماري', 'investment'),
                    ('حساب توفير', 'حساب توفير', 'savings'),
                    ('أخرى', 'نوع آخر', 'other')
                ]
                
                for name, desc, icon in account_types:
                    self.cursor.execute(
                        "INSERT INTO account_types (name, description, icon) VALUES (%s, %s, %s)",
                        (name, desc, icon)
                    )
                
                print("✓ تم إدراج أنواع الحسابات الأساسية")
            
            # 4. إنشاء جدول العملات إذا لم يكن موجوداً
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS currencies (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    code VARCHAR(3) UNIQUE NOT NULL,
                    name VARCHAR(50) NOT NULL,
                    symbol VARCHAR(10) NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✓ تم التأكد من وجود جدول العملات")
            
            # 5. إدراج العملات الأساسية
            self.cursor.execute("SELECT COUNT(*) as count FROM currencies")
            if self.cursor.fetchone()['count'] == 0:
                currencies = [
                    ('SAR', 'ريال سعودي', 'ر.س'),
                    ('USD', 'دولار أمريكي', '$'),
                    ('EUR', 'يورو', '€'),
                    ('YER', 'ريال يمني', 'ر.ي')
                ]
                
                for code, name, symbol in currencies:
                    self.cursor.execute(
                        "INSERT INTO currencies (code, name, symbol) VALUES (%s, %s, %s)",
                        (code, name, symbol)
                    )
                
                print("✓ تم إدراج العملات الأساسية")
            
            # 6. إنشاء جدول أرصدة الحسابات
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS account_balances (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    account_id INT NOT NULL,
                    currency_id INT NOT NULL,
                    balance DECIMAL(15, 2) DEFAULT 0.00,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                    FOREIGN KEY (currency_id) REFERENCES currencies(id),
                    UNIQUE KEY unique_account_currency (account_id, currency_id)
                )
            """)
            print("✓ تم التأكد من وجود جدول أرصدة الحسابات")
            
            self.connection.commit()
            print("\n✓ تم إصلاح جميع مشاكل إنشاء الحسابات")
            
            return True
            
        except Exception as e:
            print(f"✗ خطأ في الإصلاح: {e}")
            self.connection.rollback()
            return False
    
    def close_connection(self):
        """إغلاق الاتصال"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            print("تم إغلاق الاتصال بقاعدة البيانات")
        except Exception:
            pass

def main():
    """الدالة الرئيسية"""
    print("إصلاح مشكلة إضافة الحسابات الجديدة")
    print("=" * 50)
    
    fixer = AccountCreationFixer()
    
    try:
        # الاتصال بقاعدة البيانات
        if not fixer.connect_to_database():
            print("فشل في الاتصال بقاعدة البيانات")
            return False
        
        # تشخيص المشاكل
        issues = fixer.diagnose_account_creation_issues()
        
        if issues:
            print(f"\nتم العثور على {len(issues)} مشكلة:")
            for i, issue in enumerate(issues, 1):
                print(f"{i}. {issue}")
            
            # إصلاح المشاكل
            if fixer.fix_account_creation_issues():
                print("\n" + "=" * 50)
                print("✓ تم إصلاح جميع المشاكل بنجاح!")
                print("يمكنك الآن إضافة حسابات جديدة في التطبيق")
                return True
            else:
                print("\n✗ فشل في إصلاح بعض المشاكل")
                return False
        else:
            print("\n✓ لا توجد مشاكل في إنشاء الحسابات")
            return True
        
    except Exception as e:
        print(f"خطأ عام: {e}")
        return False
    finally:
        fixer.close_connection()

if __name__ == "__main__":
    main()
