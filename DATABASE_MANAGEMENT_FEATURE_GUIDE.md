# دليل ميزة إدارة قاعدة البيانات

## 🎯 نظرة عامة

تم إضافة ميزة شاملة لإدارة قاعدة البيانات إلى التطبيق، والتي تتيح للمديرين إدارة النسخ الاحتياطية وإعدادات الاتصال بقاعدة البيانات من خلال واجهة مستخدم سهلة الاستخدام.

## 🆕 الميزات الجديدة

### **زر إدارة قاعدة البيانات**
- ✅ زر "🗄️ إدارة قاعدة البيانات" جديد في الشريط الجانبي
- ✅ متاح للمديرين فقط (admin role)
- ✅ واجهة شاملة لإدارة قاعدة البيانات

### **القسم الأول: إدارة النسخ الاحتياطية**
- ✅ إنشاء نسخة احتياطية فورية
- ✅ استعادة نسخة احتياطية مع اختيار الملف
- ✅ إعدادات النسخ الاحتياطي التلقائي
- ✅ اختيار الفترة الزمنية (كل ساعة، يومياً، أسبوعياً، شهرياً)
- ✅ تحديد عدد النسخ الاحتياطية المحفوظة
- ✅ تفعيل/إلغاء النسخ التلقائي

### **القسم الثاني: إعدادات الاتصال بقاعدة البيانات**
- ✅ عرض إعدادات الاتصال الحالية
- ✅ تعديل إعدادات الاتصال (Host, Port, Database, User, Password)
- ✅ اختبار الاتصال للإعدادات الحالية والجديدة
- ✅ حفظ الإعدادات الجديدة

## 🔧 التغييرات التقنية

### الملفات المحدثة:

#### 1. `gui/main_window.py`

##### إضافة زر إدارة قاعدة البيانات:
```python
# إضافة زر إدارة قاعدة البيانات للمديرين فقط
if auth_manager.is_admin():
    menu_items.append(("database", "🗄️ إدارة قاعدة البيانات", self.show_database_management))
```

##### الدوال الجديدة المضافة:
- `show_database_management()` - عرض صفحة إدارة قاعدة البيانات
- `load_database_management_sections()` - تحميل أقسام الصفحة
- `create_backup_management_section()` - إنشاء قسم إدارة النسخ الاحتياطية
- `create_database_connection_section()` - إنشاء قسم إعدادات الاتصال
- `create_instant_backup()` - إنشاء نسخة احتياطية فورية
- `restore_backup_dialog()` - حوار استعادة نسخة احتياطية
- `toggle_auto_backup()` - تفعيل/إلغاء النسخ التلقائي
- `update_backup_interval()` - تحديث فترة النسخ الاحتياطي
- `save_backup_settings()` - حفظ إعدادات النسخ الاحتياطي
- `test_database_connection()` - اختبار الاتصال الحالي
- `test_new_database_settings()` - اختبار الإعدادات الجديدة
- `save_database_settings()` - حفظ إعدادات قاعدة البيانات

#### 2. `config/settings.py`

##### إعدادات النسخ الاحتياطي المحسنة:
```python
# إعدادات النسخ الاحتياطي المحسنة
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_interval_hours': 24,
    'max_backup_files': 30,
    'backup_location': PATHS['backup_dir'],
    'include_uploads': True,
    'include_logs': False,
    'compression_level': 6,
    'backup_on_startup': False,
    'backup_on_shutdown': True,
    'notification_enabled': True,
    'cleanup_enabled': True
}

# خيارات فترات النسخ الاحتياطي
BACKUP_INTERVALS = {
    'hourly': {'hours': 1, 'name': 'كل ساعة'},
    'daily': {'hours': 24, 'name': 'يومياً'},
    'weekly': {'hours': 168, 'name': 'أسبوعياً'},
    'monthly': {'hours': 720, 'name': 'شهرياً'}
}

# إعدادات اتصال قاعدة البيانات القابلة للتعديل
DATABASE_CONNECTION_SETTINGS = {
    'test_timeout': 10,
    'retry_attempts': 3,
    'retry_delay': 5,
    'pool_size': 5,
    'max_overflow': 10,
    'pool_timeout': 30,
    'pool_recycle': 3600
}
```

#### 3. `utils/backup.py`

الملف موجود بالفعل ويحتوي على:
- `BackupManager` class - مدير النسخ الاحتياطية
- `create_backup()` - إنشاء نسخة احتياطية
- `restore_backup()` - استعادة نسخة احتياطية
- `test_database_connection()` - اختبار الاتصال
- `get_backup_files()` - الحصول على قائمة النسخ
- `update_settings()` - تحديث الإعدادات

## 🎮 كيفية الاستخدام

### الوصول للميزة:

1. **شغل التطبيق**:
   ```bash
   python main.py
   ```

2. **سجل الدخول كمدير**:
   - اسم المستخدم: `admin`
   - كلمة المرور: `123456`

3. **اذهب إلى إدارة قاعدة البيانات**:
   - انقر على "🗄️ إدارة قاعدة البيانات" في الشريط الجانبي
   - هذا الزر متاح للمديرين فقط

### استخدام إدارة النسخ الاحتياطية:

#### **إنشاء نسخة احتياطية فورية:**
1. انقر على "📥 إنشاء نسخة احتياطية فورية"
2. انتظر حتى اكتمال العملية
3. ستظهر رسالة تأكيد عند النجاح

#### **استعادة نسخة احتياطية:**
1. انقر على "📤 استعادة نسخة احتياطية"
2. اختر ملف النسخة الاحتياطية (.sql)
3. أكد الاستعادة (تحذير: سيتم استبدال البيانات الحالية)
4. انتظر حتى اكتمال العملية

#### **إعدادات النسخ التلقائي:**
1. **تفعيل/إلغاء النسخ التلقائي**: استخدم مربع الاختيار
2. **اختيار الفترة**: اختر من القائمة المنسدلة (كل ساعة، يومياً، أسبوعياً، شهرياً)
3. **عدد النسخ المحفوظة**: أدخل الرقم المطلوب (افتراضي: 30)
4. **حفظ الإعدادات**: انقر على "💾 حفظ إعدادات النسخ الاحتياطي"

### استخدام إعدادات الاتصال:

#### **عرض الإعدادات الحالية:**
- يتم عرض الخادم، المنفذ، اسم قاعدة البيانات، اسم المستخدم، والترميز
- انقر على "🔄 اختبار الاتصال" للتحقق من الاتصال الحالي

#### **تعديل الإعدادات:**
1. أدخل الإعدادات الجديدة في الحقول المطلوبة:
   - الخادم (Host)
   - المنفذ (Port)
   - اسم قاعدة البيانات
   - اسم المستخدم
   - كلمة المرور
2. انقر على "🔄 اختبار الإعدادات الجديدة" للتحقق من صحتها
3. إذا نجح الاختبار، انقر على "💾 حفظ إعدادات قاعدة البيانات"

## 🛡️ الأمان والحماية

### القيود الأمنية:
1. **صلاحيات المدير فقط**: الميزة متاحة للمديرين فقط
2. **تأكيد الاستعادة**: تحذير واضح قبل استعادة النسخ الاحتياطية
3. **اختبار الإعدادات**: إمكانية اختبار الإعدادات قبل الحفظ
4. **رسائل خطأ واضحة**: معالجة شاملة للأخطاء

### الحماية من الأخطاء:
- التحقق من صحة البيانات المدخلة
- معالجة استثناءات الاتصال
- رسائل تحذيرية للعمليات الحرجة
- نسخ احتياطية آمنة مع تنظيف تلقائي

## 🧪 الاختبارات

### ملفات الاختبار المنشأة:

#### `test_database_management_feature.py`
اختبار شامل يتضمن:
- اختبار الاتصال بقاعدة البيانات
- اختبار صلاحيات المدير
- اختبار استيراد الوحدات
- اختبار الإعدادات الجديدة
- اختبار وجود الدوال الجديدة
- اختبار وظائف النسخ الاحتياطي

#### `quick_database_test.py`
اختبار سريع للتحقق من:
- الاتصال الأساسي
- استيراد الوحدات
- وجود الدوال الجديدة
- الإعدادات المحدثة

### تشغيل الاختبارات:
```bash
python test_database_management_feature.py
python quick_database_test.py
```

## 📊 الإحصائيات

### الدوال المضافة:
- **12 دالة جديدة** في `gui/main_window.py`
- **1 زر جديد** في الشريط الجانبي
- **2 قسم رئيسي** في الواجهة

### الإعدادات المضافة:
- **11 إعداد جديد** في `BACKUP_CONFIG`
- **4 فترات نسخ** في `BACKUP_INTERVALS`
- **7 إعدادات اتصال** في `DATABASE_CONNECTION_SETTINGS`

### الملفات المعدلة:
- `gui/main_window.py` - إضافة الواجهة والدوال
- `config/settings.py` - إضافة الإعدادات الجديدة
- `utils/backup.py` - موجود مسبقاً ومحسن

## 🎯 الخلاصة

**تم إضافة ميزة إدارة قاعدة البيانات بنجاح:**

### ✅ المتطلبات المطبقة:
- زر إدارة قاعدة البيانات للمديرين فقط
- قسم إدارة النسخ الاحتياطية مع جميع الخيارات المطلوبة
- قسم إعدادات الاتصال مع إمكانية التعديل والاختبار
- دعم النصوص العربية مع RTL
- معالجة شاملة للأخطاء
- حفظ الإعدادات وتحديثها

### 🛡️ الأمان:
- التحقق من صلاحيات المدير
- تأكيد العمليات الحرجة
- اختبار الإعدادات قبل الحفظ
- معالجة آمنة للأخطاء

### 🎨 التصميم:
- نفس نمط التصميم المستخدم في باقي الصفحات
- واجهة سهلة الاستخدام ومنظمة
- أزرار واضحة مع أيقونات مميزة
- رسائل تحذيرية وتأكيدية مناسبة

**الميزة جاهزة للاستخدام الفوري مع جميع الوظائف المطلوبة!** 🚀
