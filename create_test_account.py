#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from database.models import Account

try:
    print("🧪 إنشاء حساب اختبار...")
    
    # إنشاء حساب جديد
    account_id = Account.create(
        user_id=1,
        name="حساب اختبار متعدد العملات",
        description="حساب للاختبار يدعم عدة عملات",
        account_type_id=1
    )
    
    if account_id > 0:
        print(f"✅ تم إنشاء الحساب - ID: {account_id}")
        
        # إضافة أرصدة بعملات مختلفة
        currencies_balances = [
            (1, 1000.0),  # SAR
            (2, 500.0),   # YER
            (3, 200.0),   # AED
            (4, 100.0)    # USD
        ]
        
        for currency_id, balance in currencies_balances:
            result = Account.add_currency_balance(account_id, currency_id, balance)
            print(f"💰 تم إضافة رصيد العملة {currency_id}: {balance}")
        
        print("\n🎉 تم إنشاء حساب اختبار بنجاح!")
        
        # اختبار استرجاع الحسابات
        print("\n🔍 اختبار استرجاع الحسابات...")
        accounts = Account.get_by_user(1)
        
        print(f"📋 عدد الحسابات: {len(accounts)}")
        for account in accounts:
            print(f"🏦 {account['name']}")
            for balance in account.get('balances', []):
                print(f"   💵 {balance['name']}: {balance['balance']} {balance['symbol']}")
    
    else:
        print("❌ فشل في إنشاء الحساب")

except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
