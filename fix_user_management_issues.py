#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل ميزة إدارة المستخدمين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import mysql.connector
from config.settings import DATABASE_CONFIG

def fix_activity_log_table():
    """إصلاح جدول سجل الأنشطة"""
    print("🔧 إصلاح جدول سجل الأنشطة...")
    try:
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()
        
        # التحقق من وجود الجدول
        cursor.execute("SHOW TABLES LIKE 'activity_log'")
        tables = cursor.fetchall()
        
        if not tables:
            print("   إنشاء جدول سجل الأنشطة...")
            cursor.execute("""
                CREATE TABLE activity_log (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    action_type VARCHAR(50) NOT NULL,
                    table_name VARCHAR(50) NOT NULL,
                    record_id INT NULL,
                    description TEXT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            """)
            print("   ✅ تم إنشاء جدول سجل الأنشطة")
        else:
            print("   ✅ جدول سجل الأنشطة موجود مسبقاً")
        
        connection.commit()
        connection.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إصلاح جدول سجل الأنشطة: {e}")
        return False

def fix_users_table_structure():
    """إصلاح بنية جدول المستخدمين"""
    print("🔧 إصلاح بنية جدول المستخدمين...")
    try:
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()
        
        # فحص بنية الجدول
        cursor.execute("DESCRIBE users")
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        
        # إضافة العمود created_by إذا لم يكن موجوداً
        if 'created_by' not in column_names:
            print("   إضافة العمود created_by...")
            cursor.execute("ALTER TABLE users ADD COLUMN created_by INT NULL")
            
            # إضافة المفتاح الخارجي
            try:
                cursor.execute("ALTER TABLE users ADD FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL")
                print("   ✅ تم إضافة العمود created_by مع المفتاح الخارجي")
            except Exception as fk_error:
                print(f"   ⚠️ تم إضافة العمود created_by بدون مفتاح خارجي: {fk_error}")
        else:
            print("   ✅ العمود created_by موجود مسبقاً")
        
        connection.commit()
        connection.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إصلاح بنية جدول المستخدمين: {e}")
        return False

def ensure_admin_user_exists():
    """التأكد من وجود المستخدم المدير"""
    print("🔧 التأكد من وجود المستخدم المدير...")
    try:
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor(dictionary=True)
        
        # البحث عن المستخدم admin2
        cursor.execute("SELECT * FROM users WHERE username = 'admin2'")
        admin_user = cursor.fetchone()
        
        if admin_user:
            print("   ✅ المستخدم admin2 موجود")
            print(f"      الاسم الكامل: {admin_user['full_name']}")
            print(f"      الدور: {admin_user['role']}")
            print(f"      نشط: {'نعم' if admin_user['is_active'] else 'لا'}")
        else:
            print("   ❌ المستخدم admin2 غير موجود - سيتم إنشاؤه...")
            
            import bcrypt
            password_hash = bcrypt.hashpw('123456'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            cursor.execute("""
                INSERT INTO users (username, password_hash, full_name, role, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, NOW())
            """, ('admin2', password_hash, 'المدير الجديد', 'admin', True))
            
            print("   ✅ تم إنشاء المستخدم admin2")
        
        connection.commit()
        connection.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في التأكد من وجود المستخدم المدير: {e}")
        return False

def test_user_management_functions():
    """اختبار وظائف إدارة المستخدمين"""
    print("🧪 اختبار وظائف إدارة المستخدمين...")
    try:
        # اختبار تسجيل الدخول
        from utils.auth import auth_manager
        success, message = auth_manager.login("admin2", "123456")
        
        if not success:
            print(f"   ❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("   ✅ تسجيل الدخول ناجح")
        
        # اختبار جلب المستخدمين
        from database.models import User
        users = User.get_all()
        
        if users is None:
            print("   ❌ فشل في جلب المستخدمين")
            return False
        
        print(f"   ✅ تم جلب {len(users)} مستخدم")
        
        # اختبار الصلاحيات
        if not auth_manager.can_manage_users():
            print("   ❌ المستخدم لا يمكنه إدارة المستخدمين")
            return False
        
        print("   ✅ صلاحيات إدارة المستخدمين متاحة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار وظائف إدارة المستخدمين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_imports():
    """اختبار استيراد مكونات واجهة المستخدم"""
    print("🧪 اختبار استيراد مكونات واجهة المستخدم...")
    try:
        # اختبار استيراد الألوان والخطوط
        from config.colors import COLORS, BUTTON_STYLES, CARD_STYLES, ARABIC_TEXT_STYLES
        from config.fonts import create_rtl_label, create_rtl_button, create_rtl_entry
        print("   ✅ تم استيراد الألوان والخطوط")
        
        # اختبار استيراد نوافذ إدارة المستخدمين
        from gui.user_management_windows import AddUserWindow, EditUserWindow, ResetPasswordWindow
        print("   ✅ تم استيراد نوافذ إدارة المستخدمين")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استيراد مكونات واجهة المستخدم: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_comprehensive_fix_summary():
    """إنشاء ملخص شامل للإصلاحات"""
    print("\n📋 ملخص الإصلاحات المطبقة:")
    print("=" * 50)
    
    fixes = [
        "✅ إصلاح جدول سجل الأنشطة (activity_log)",
        "✅ إصلاح بنية جدول المستخدمين",
        "✅ التأكد من وجود المستخدم المدير admin2",
        "✅ اختبار وظائف إدارة المستخدمين",
        "✅ اختبار مكونات واجهة المستخدم"
    ]
    
    for fix in fixes:
        print(f"  {fix}")
    
    print("\n🎯 تعليمات الاستخدام:")
    print("1. شغل التطبيق: python main.py")
    print("2. سجل الدخول باستخدام:")
    print("   - اسم المستخدم: admin2")
    print("   - كلمة المرور: 123456")
    print("3. انقر على '👥 إدارة المستخدمين' في الشريط الجانبي")
    print("4. استمتع بإدارة المستخدمين!")
    
    print("\n🔧 في حالة استمرار المشكلة:")
    print("1. تأكد من تشغيل MySQL Server")
    print("2. تحقق من صحة بيانات الاتصال في config/settings.py")
    print("3. شغل python test_user_management_gui.py للاختبار الشامل")

def main():
    """الدالة الرئيسية للإصلاح"""
    print("🚀 إصلاح شامل لميزة إدارة المستخدمين")
    print("=" * 60)
    
    fixes_applied = 0
    total_fixes = 5
    
    # 1. إصلاح جدول سجل الأنشطة
    if fix_activity_log_table():
        fixes_applied += 1
    
    # 2. إصلاح بنية جدول المستخدمين
    if fix_users_table_structure():
        fixes_applied += 1
    
    # 3. التأكد من وجود المستخدم المدير
    if ensure_admin_user_exists():
        fixes_applied += 1
    
    # 4. اختبار وظائف إدارة المستخدمين
    if test_user_management_functions():
        fixes_applied += 1
    
    # 5. اختبار مكونات واجهة المستخدم
    if test_gui_imports():
        fixes_applied += 1
    
    # النتائج
    print("\n" + "=" * 60)
    print(f"📊 نتائج الإصلاح: {fixes_applied}/{total_fixes} إصلاحات نجحت")
    
    if fixes_applied == total_fixes:
        print("🎉 تم إصلاح جميع المشاكل بنجاح!")
        create_comprehensive_fix_summary()
        return True
    else:
        print("⚠️ بعض الإصلاحات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    main()
