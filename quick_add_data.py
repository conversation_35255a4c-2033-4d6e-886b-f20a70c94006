#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات عملية سريعة للبرنامج
"""

import sys
import os
import mysql.connector
from datetime import datetime, timedelta

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def connect_to_database():
    """الاتصال بقاعدة البيانات مباشرة"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='mohdam',
            database='money_manager',
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci'
        )
        return connection
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def add_practical_data():
    """إضافة البيانات العملية"""
    print("🏦 إضافة بيانات عملية للبرنامج...")
    
    connection = connect_to_database()
    if not connection:
        return False
    
    cursor = connection.cursor()
    
    try:
        # حذف البيانات الموجودة
        print("🗑️ حذف البيانات الموجودة...")
        cursor.execute("DELETE FROM transfers WHERE user_id = 1")
        cursor.execute("DELETE FROM transactions WHERE user_id = 1")
        cursor.execute("DELETE FROM accounts WHERE user_id = 1")
        connection.commit()
        
        # إضافة الحسابات
        print("🏦 إضافة الحسابات...")
        accounts_query = """
            INSERT INTO accounts (user_id, name, account_type_id, currency_id, initial_balance, current_balance, description) 
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        accounts_data = [
            (1, 'الحساب الجاري الرئيسي', 2, 1, 15000.00, 15000.00, 'الحساب الجاري الرئيسي في بنك الراجحي - للمعاملات اليومية'),
            (1, 'المحفظة النقدية', 1, 1, 2000.00, 2000.00, 'النقد المتوفر في المحفظة للمصروفات اليومية الصغيرة')
        ]
        
        for account_data in accounts_data:
            cursor.execute(accounts_query, account_data)
        
        connection.commit()
        
        # الحصول على معرفات الحسابات
        cursor.execute("SELECT id FROM accounts WHERE user_id = 1 AND name = 'الحساب الجاري الرئيسي'")
        account1_id = cursor.fetchone()[0]
        
        cursor.execute("SELECT id FROM accounts WHERE user_id = 1 AND name = 'المحفظة النقدية'")
        account2_id = cursor.fetchone()[0]
        
        print(f"✅ تم إنشاء الحسابات: {account1_id}, {account2_id}")
        
        # إضافة الواردات
        print("💰 إضافة الواردات...")
        income_query = """
            INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_id, description, transaction_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        income_data = [
            (1, account1_id, 'income', 8500.00, 1, 1, 'راتب شهر ديسمبر 2025', '2025-06-24'),
            (1, account1_id, 'income', 2200.00, 1, 4, 'مشروع تطوير موقع إلكتروني للعميل أحمد', '2025-06-17'),
            (1, account1_id, 'income', 1500.00, 1, 1, 'مكافأة نهاية السنة', '2025-06-09'),
            (1, account1_id, 'income', 750.00, 1, 3, 'أرباح من الأسهم - الربع الرابع', '2025-06-14'),
            (1, account2_id, 'income', 450.00, 1, 2, 'بيع منتجات يدوية في المعرض', '2025-06-21'),
            (1, account1_id, 'income', 1200.00, 1, 4, 'استشارة تقنية لشركة النور', '2025-06-04')
        ]
        
        for income in income_data:
            cursor.execute(income_query, income)
        
        connection.commit()
        print(f"✅ تم إضافة {len(income_data)} وارد")
        
        # إضافة المصروفات
        print("💸 إضافة المصروفات...")
        expense_query = """
            INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_id, description, transaction_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        expense_data = [
            (1, account1_id, 'expense', 2500.00, 1, 3, 'إيجار الشقة - شهر ديسمبر', '2025-06-26'),
            (1, account1_id, 'expense', 380.00, 1, 4, 'فاتورة الكهرباء والماء', '2025-06-22'),
            (1, account1_id, 'expense', 120.00, 1, 4, 'فاتورة الإنترنت والهاتف', '2025-06-19'),
            (1, account2_id, 'expense', 85.00, 1, 1, 'تسوق من السوبر ماركت', '2025-06-28'),
            (1, account2_id, 'expense', 45.00, 1, 1, 'وجبة غداء في المطعم', '2025-06-27'),
            (1, account2_id, 'expense', 120.00, 1, 1, 'مشتريات من البقالة', '2025-06-25'),
            (1, account2_id, 'expense', 200.00, 1, 2, 'وقود السيارة', '2025-06-23'),
            (1, account2_id, 'expense', 25.00, 1, 2, 'أجرة تاكسي', '2025-06-20'),
            (1, account1_id, 'expense', 150.00, 1, 5, 'زيارة طبيب الأسنان', '2025-06-15'),
            (1, account2_id, 'expense', 65.00, 1, 5, 'أدوية من الصيدلية', '2025-06-18'),
            (1, account1_id, 'expense', 320.00, 1, 8, 'ملابس شتوية جديدة', '2025-06-11'),
            (1, account2_id, 'expense', 75.00, 1, 8, 'أدوات منزلية', '2025-06-16'),
            (1, account2_id, 'expense', 90.00, 1, 7, 'تذاكر السينما مع الأصدقاء', '2025-06-13'),
            (1, account2_id, 'expense', 55.00, 1, 7, 'ألعاب فيديو جديدة', '2025-06-07'),
            (1, account1_id, 'expense', 450.00, 1, 6, 'دورة تدريبية في البرمجة', '2025-06-01')
        ]
        
        for expense in expense_data:
            cursor.execute(expense_query, expense)
        
        connection.commit()
        print(f"✅ تم إضافة {len(expense_data)} مصروف")
        
        # إضافة التحويلات
        print("🔄 إضافة التحويلات...")
        transfer_query = """
            INSERT INTO transfers (user_id, from_account_id, to_account_id, amount, currency_id, description, transfer_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        transfer_data = [
            (1, account1_id, account2_id, 500.00, 1, 'سحب نقدي للمصروفات اليومية', '2025-06-24'),
            (1, account1_id, account2_id, 300.00, 1, 'نقد إضافي للطوارئ', '2025-06-17'),
            (1, account2_id, account1_id, 150.00, 1, 'إيداع فائض النقد في البنك', '2025-06-09'),
            (1, account1_id, account2_id, 250.00, 1, 'نقد لمصروفات نهاية الأسبوع', '2025-06-21')
        ]
        
        for transfer in transfer_data:
            cursor.execute(transfer_query, transfer)
        
        connection.commit()
        print(f"✅ تم إضافة {len(transfer_data)} تحويل")
        
        # تحديث أرصدة الحسابات
        print("🔄 تحديث الأرصدة...")
        
        # حساب الرصيد الجديد للحساب الأول
        cursor.execute("""
            SELECT 
                (SELECT COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END), 0) 
                 FROM transactions WHERE account_id = %s) +
                (SELECT COALESCE(SUM(CASE WHEN to_account_id = %s THEN amount ELSE -amount END), 0)
                 FROM transfers WHERE from_account_id = %s OR to_account_id = %s) as balance_change
        """, (account1_id, account1_id, account1_id, account1_id))
        
        balance_change1 = cursor.fetchone()[0]
        new_balance1 = 15000.00 + balance_change1
        
        cursor.execute("UPDATE accounts SET current_balance = %s WHERE id = %s", (new_balance1, account1_id))
        
        # حساب الرصيد الجديد للحساب الثاني
        cursor.execute("""
            SELECT 
                (SELECT COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END), 0) 
                 FROM transactions WHERE account_id = %s) +
                (SELECT COALESCE(SUM(CASE WHEN to_account_id = %s THEN amount ELSE -amount END), 0)
                 FROM transfers WHERE from_account_id = %s OR to_account_id = %s) as balance_change
        """, (account2_id, account2_id, account2_id, account2_id))
        
        balance_change2 = cursor.fetchone()[0]
        new_balance2 = 2000.00 + balance_change2
        
        cursor.execute("UPDATE accounts SET current_balance = %s WHERE id = %s", (new_balance2, account2_id))
        
        connection.commit()
        
        # عرض الملخص
        print("\n" + "="*60)
        print("📊 ملخص البيانات المضافة")
        print("="*60)
        
        # الحسابات
        cursor.execute("SELECT name, current_balance FROM accounts WHERE user_id = 1")
        accounts = cursor.fetchall()
        
        print("🏦 الحسابات:")
        total_balance = 0
        for account in accounts:
            print(f"   • {account[0]}: {account[1]:,.2f} ر.س")
            total_balance += account[1]
        print(f"   📊 إجمالي الأرصدة: {total_balance:,.2f} ر.س")
        
        # المعاملات
        cursor.execute("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = 1 AND transaction_type = 'income'")
        income_total = cursor.fetchone()[0]
        
        cursor.execute("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE user_id = 1 AND transaction_type = 'expense'")
        expense_total = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM transfers WHERE user_id = 1")
        transfers_count = cursor.fetchone()[0]
        
        print(f"\n💰 المعاملات:")
        print(f"   • إجمالي الواردات: {income_total:,.2f} ر.س")
        print(f"   • إجمالي المصروفات: {expense_total:,.2f} ر.س")
        print(f"   • الصافي: {income_total - expense_total:,.2f} ر.س")
        print(f"   • عدد التحويلات: {transfers_count}")
        
        print(f"\n🎉 البرنامج جاهز للاستخدام العملي!")
        print(f"📋 بيانات تسجيل الدخول:")
        print(f"   اسم المستخدم: admin")
        print(f"   كلمة المرور: 123456")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {e}")
        connection.rollback()
        return False
        
    finally:
        cursor.close()
        connection.close()

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🏦 إضافة بيانات عملية لبرنامج إدارة الأموال")
    print("="*60)
    
    if add_practical_data():
        print("\n✅ تم إضافة جميع البيانات بنجاح!")
    else:
        print("\n❌ فشل في إضافة البيانات")

if __name__ == "__main__":
    main()
    input("\n🔄 اضغط Enter للخروج...")
