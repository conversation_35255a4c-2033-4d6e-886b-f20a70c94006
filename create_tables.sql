-- إن<PERSON><PERSON><PERSON> جداول قاعدة بيانات مدير الأموال
USE money_manager;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'admin',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    profile_image VARCHAR(255) NULL
);

-- جدول العملات
CREATE TABLE IF NOT EXISTS currencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(3) UNIQUE NOT NULL,
    name VARCHAR(50) NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول أنواع الحسابات
CREATE TABLE IF NOT EXISTS account_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(10),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الحسابات
CREATE TABLE IF NOT EXISTS accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    account_type_id INT NOT NULL,
    currency_id INT NOT NULL,
    initial_balance DECIMAL(15, 2) DEFAULT 0.00,
    current_balance DECIMAL(15, 2) DEFAULT 0.00,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (account_type_id) REFERENCES account_types(id),
    FOREIGN KEY (currency_id) REFERENCES currencies(id)
);

-- جدول تصنيفات الواردات
CREATE TABLE IF NOT EXISTS income_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(10),
    color VARCHAR(7),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول تصنيفات المصروفات
CREATE TABLE IF NOT EXISTS expense_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(10),
    color VARCHAR(7),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المعاملات
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    account_id INT NOT NULL,
    transaction_type ENUM('income', 'expense') NOT NULL,
    amount DECIMAL(15, 2) NOT NULL,
    currency_id INT NOT NULL,
    category_id INT NULL,
    description TEXT,
    transaction_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (currency_id) REFERENCES currencies(id)
);

-- جدول التحويلات
CREATE TABLE IF NOT EXISTS transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    from_account_id INT NOT NULL,
    to_account_id INT NOT NULL,
    amount DECIMAL(15, 2) NOT NULL,
    currency_id INT NOT NULL,
    description TEXT,
    transfer_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (from_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (to_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (currency_id) REFERENCES currencies(id)
);

-- إدراج البيانات الأساسية

-- العملات
INSERT IGNORE INTO currencies (id, code, name, symbol, exchange_rate, is_active) VALUES
(1, 'SAR', 'ريال سعودي', 'ر.س', 1.0000, 1),
(2, 'USD', 'دولار أمريكي', '$', 3.7500, 1),
(3, 'EUR', 'يورو', '€', 4.0000, 1);

-- أنواع الحسابات
INSERT IGNORE INTO account_types (id, name, description, icon, is_active) VALUES
(1, 'صندوق نقدي', 'النقد المتوفر في اليد', '💰', 1),
(2, 'حساب بنكي', 'حساب في البنك', '🏦', 1),
(3, 'بطاقة ائتمان', 'بطاقة ائتمان', '💳', 1),
(4, 'محفظة إلكترونية', 'محفظة رقمية', '📱', 1),
(5, 'استثمار', 'حساب استثماري', '📈', 1),
(6, 'حساب توفير', 'حساب توفير', '🏛️', 1);

-- تصنيفات الواردات
INSERT IGNORE INTO income_categories (id, name, description, icon, color, is_active) VALUES
(1, 'راتب', 'الراتب الشهري', '💼', '#4CAF50', 1),
(2, 'أعمال', 'دخل من الأعمال', '🏢', '#2196F3', 1),
(3, 'استثمار', 'عوائد استثمارية', '📈', '#FF9800', 1),
(4, 'عمل حر', 'دخل من العمل الحر', '💻', '#9C27B0', 1);

-- تصنيفات المصروفات
INSERT IGNORE INTO expense_categories (id, name, description, icon, color, is_active) VALUES
(1, 'طعام وشراب', 'مصروفات الطعام والشراب', '🍽️', '#F44336', 1),
(2, 'مواصلات', 'مصروفات النقل والمواصلات', '🚗', '#FF5722', 1),
(3, 'سكن', 'مصروفات السكن والإيجار', '🏠', '#795548', 1),
(4, 'فواتير', 'الفواتير والخدمات', '📄', '#607D8B', 1),
(5, 'صحة', 'المصروفات الطبية', '🏥', '#E91E63', 1),
(6, 'تعليم', 'مصروفات التعليم', '📚', '#3F51B5', 1),
(7, 'ترفيه', 'مصروفات الترفيه', '🎮', '#9C27B0', 1),
(8, 'تسوق', 'مصروفات التسوق', '🛍️', '#FF9800', 1);

SELECT 'تم إنشاء الجداول والبيانات الأساسية بنجاح!' as message;
