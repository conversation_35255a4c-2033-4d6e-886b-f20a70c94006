# دليل إعادة تعيين البيانات التجريبية - تطبيق إدارة الأموال

## 🎯 نظرة عامة

تم إنشاء سكريبت شامل لإعادة تعيين البيانات التجريبية في تطبيق إدارة الأموال، والذي يقوم بحذف البيانات الحالية بأمان وإنشاء بيانات تجريبية واقعية ومتنوعة.

## 📁 الملفات المنشأة

### **1. السكريبت الرئيسي:**
- **`reset_demo_data.py`** - السكريبت الرئيسي لإعادة تعيين البيانات

### **2. سكريبت الاختبار:**
- **`test_demo_data_reset.py`** - اختبار شامل للتحقق من نجاح العملية

### **3. الدليل:**
- **`DEMO_DATA_RESET_GUIDE.md`** - هذا الدليل

## 🚀 كيفية الاستخدام

### **الخطوة 1: تشغيل السكريبت الرئيسي**
```bash
python reset_demo_data.py
```

### **الخطوة 2: التحقق من النتائج**
```bash
python test_demo_data_reset.py
```

### **الخطوة 3: اختبار التطبيق**
```bash
python main.py
```
- سجل الدخول: admin / 123456
- تحقق من الحسابات والمعاملات

## 📊 البيانات التي يتم إنشاؤها

### **🏦 الحسابات المالية (5 حسابات):**

1. **الحساب الجاري الرئيسي**
   - النوع: حساب جاري (checking)
   - العملة: SAR
   - الرصيد الابتدائي: 15,000 ريال

2. **حساب التوفير**
   - النوع: توفير (savings)
   - العملة: SAR
   - الرصيد الابتدائي: 50,000 ريال

3. **المحفظة النقدية**
   - النوع: نقدي (cash)
   - العملة: SAR
   - الرصيد الابتدائي: 2,500 ريال

4. **حساب بالدولار**
   - النوع: حساب جاري (checking)
   - العملة: USD
   - الرصيد الابتدائي: 3,200 دولار

5. **بطاقة ائتمانية**
   - النوع: ائتمان (credit)
   - العملة: SAR
   - الرصيد الابتدائي: -5,000 ريال (دين)

### **💰 معاملات الدخل (12 معاملة):**

| الوصف | المبلغ | الأيام الماضية |
|--------|--------|----------------|
| راتب شهر ديسمبر | 8,000 | 5 |
| مكافأة أداء | 1,500 | 10 |
| عمولة مبيعات | 500 | 15 |
| استشارة تقنية | 2,000 | 20 |
| فوائد حساب التوفير | 300 | 25 |
| عمل إضافي | 1,200 | 30 |
| بيع أجهزة قديمة | 800 | 35 |
| راتب شهر نوفمبر | 7,500 | 40 |
| إيجار عقار | 600 | 45 |
| هدية نقدية | 400 | 50 |
| مشروع جانبي | 1,000 | 55 |
| استرداد ضريبي | 250 | 60 |

**إجمالي الدخل: 24,050 ريال**

### **💸 معاملات المصروفات (20 معاملة):**

| الوصف | المبلغ | الأيام الماضية |
|--------|--------|----------------|
| إيجار الشقة | 1,200 | 3 |
| فاتورة الكهرباء والماء | 800 | 7 |
| تسوق البقالة | 600 | 2 |
| وقود السيارة | 300 | 5 |
| اشتراك الإنترنت | 150 | 10 |
| فاتورة الهاتف | 450 | 12 |
| صيانة السيارة | 200 | 15 |
| مطعم مع الأصدقاء | 350 | 8 |
| قهوة ومقهى | 120 | 4 |
| ملابس جديدة | 800 | 18 |
| أدوية وصيدلية | 250 | 22 |
| كتب ومجلات | 180 | 25 |
| هدايا العائلة | 400 | 28 |
| تأمين السيارة | 320 | 32 |
| حلاق وعناية شخصية | 150 | 35 |
| إصلاح الهاتف | 500 | 38 |
| رسوم بنكية | 280 | 42 |
| تسوق أجهزة منزلية | 650 | 45 |
| اشتراك نادي رياضي | 200 | 48 |
| فاتورة طبيب | 380 | 52 |

**إجمالي المصروفات: 7,310 ريال**

### **🔄 التحويلات (5 تحويلات):**

| الوصف | المبلغ | الأيام الماضية |
|--------|--------|----------------|
| تحويل للتوفير | 2,000 | 6 |
| سحب من التوفير للطوارئ | 500 | 14 |
| تحويل لحساب الدولار | 1,000 | 21 |
| تعبئة المحفظة النقدية | 300 | 28 |
| سداد بطاقة ائتمانية | 1,500 | 35 |

**إجمالي التحويلات: 5,300 ريال**

## 🔧 ميزات السكريبت

### **1. الحذف الآمن:**
- ✅ حذف المعاملات المالية فقط
- ✅ حذف التحويلات
- ✅ حذف الحسابات
- ✅ الاحتفاظ بالمستخدمين والعملات والفئات

### **2. البيانات الواقعية:**
- ✅ أوصاف واقعية باللغة العربية
- ✅ مبالغ منطقية ومتنوعة
- ✅ تواريخ موزعة على الشهرين الماضيين
- ✅ استخدام فئات الدخل والمصروفات الموجودة

### **3. التحديث التلقائي:**
- ✅ تحديث أرصدة الحسابات تلقائياً
- ✅ ربط المعاملات بالمستخدم الحالي
- ✅ استخدام العملات المدعومة

### **4. الاختبار الشامل:**
- ✅ التحقق من إنشاء الحسابات
- ✅ التحقق من إنشاء المعاملات
- ✅ التحقق من التحويلات
- ✅ التحقق من صحة الأرصدة

## 📋 متطلبات التشغيل

### **1. قاعدة البيانات:**
- خادم MySQL يعمل
- قاعدة البيانات `money_manager` موجودة
- الجداول الأساسية منشأة

### **2. المستخدم:**
- وجود مستخدم admin بكلمة مرور 123456
- صلاحيات كافية للوصول لقاعدة البيانات

### **3. الفئات:**
- وجود فئات دخل في جدول `income_categories`
- وجود فئات مصروفات في جدول `expense_categories`

## ⚠️ تحذيرات مهمة

### **🚨 تحذير: حذف البيانات**
- السكريبت سيحذف جميع الحسابات والمعاملات الحالية للمستخدم admin
- تأكد من إنشاء نسخة احتياطية قبل التشغيل
- لا يمكن التراجع عن هذه العملية

### **💾 إنشاء نسخة احتياطية:**
```bash
# قبل تشغيل السكريبت
python main.py
# اذهب إلى "إدارة قاعدة البيانات" > "إنشاء نسخة احتياطية"
```

## 🧪 اختبار النتائج

### **1. اختبار تلقائي:**
```bash
python test_demo_data_reset.py
```

### **2. اختبار يدوي:**
1. شغل التطبيق: `python main.py`
2. سجل الدخول: admin / 123456
3. تحقق من:
   - **الحسابات**: يجب أن تظهر 5 حسابات
   - **الواردات**: يجب أن تظهر 12 معاملة دخل
   - **المصروفات**: يجب أن تظهر 20 معاملة مصروف
   - **التحويلات**: يجب أن تظهر 5 تحويلات
   - **الأرصدة**: يجب أن تكون محدثة بناءً على المعاملات

## 📊 الإحصائيات المتوقعة

### **الملخص المالي:**
- **إجمالي الدخل**: 24,050 ريال
- **إجمالي المصروفات**: 7,310 ريال
- **صافي الدخل**: 16,740 ريال
- **إجمالي التحويلات**: 5,300 ريال

### **توزيع المعاملات:**
- **معاملات الدخل**: 12 معاملة
- **معاملات المصروفات**: 20 معاملة
- **التحويلات**: 5 تحويلات
- **إجمالي المعاملات**: 37 معاملة

## 🔄 إعادة التشغيل

### **لإعادة تعيين البيانات مرة أخرى:**
```bash
python reset_demo_data.py
```

### **لاستعادة نسخة احتياطية:**
1. شغل التطبيق
2. اذهب إلى "إدارة قاعدة البيانات"
3. انقر "استعادة نسخة احتياطية"
4. اختر النسخة المطلوبة

## 🛠️ استكشاف الأخطاء

### **مشاكل شائعة:**

**1. "فشل الاتصال بقاعدة البيانات"**
- تأكد من تشغيل خادم MySQL
- راجع إعدادات الاتصال في `config/settings.py`

**2. "فشل تسجيل الدخول"**
- تأكد من وجود مستخدم admin
- تأكد من كلمة المرور 123456

**3. "لا توجد فئات كافية"**
- شغل `python database/init_db.py` لإنشاء الفئات الافتراضية

**4. "خطأ في إنشاء المعاملات"**
- تحقق من وجود الحسابات
- تحقق من صحة الفئات

## 📞 الدعم

### **للحصول على المساعدة:**
1. احفظ رسائل الخطأ
2. شغل سكريبت الاختبار للحصول على تفاصيل إضافية
3. تحقق من ملفات السجلات
4. اطلب المساعدة التقنية

---

**💡 نصيحة:** استخدم هذا السكريبت لإنشاء بيانات تجريبية واقعية لاختبار التطبيق أو لعرض الميزات للمستخدمين الجدد!
