@echo off
echo ===================================================
echo      بناء برنامج مدير الأموال وإنشاء المثبت
echo ===================================================
echo.

REM التحقق من وجود PyInstaller
pip show pyinstaller > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo تثبيت PyInstaller...
    pip install pyinstaller
    if %ERRORLEVEL% NEQ 0 (
        echo فشل تثبيت PyInstaller. يرجى التثبيت يدويًا باستخدام: pip install pyinstaller
        pause
        exit /b 1
    )
)

REM التحقق من وجود Inno Setup Compiler
where iscc > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo لم يتم العثور على Inno Setup Compiler.
    echo يرجى تثبيت Inno Setup من https://jrsoftware.org/isdl.php
    echo وتأكد من إضافته إلى متغير PATH.
    pause
    exit /b 1
)

echo تثبيت المتطلبات...
pip install -r requirements.txt
if %ERRORLEVEL% NEQ 0 (
    echo تحذير: فشل تثبيت بعض المتطلبات. قد لا يعمل البرنامج بشكل صحيح.
    echo سنستمر في عملية البناء...
)

echo.
echo بناء الملف التنفيذي باستخدام PyInstaller...
echo.

REM محاولة استخدام ملف المواصفات الرئيسي
pyinstaller --noconfirm "Money Manager.spec"
if %ERRORLEVEL% NEQ 0 (
    echo فشل بناء الملف التنفيذي باستخدام Money Manager.spec
    echo محاولة استخدام ملف المواصفات البديل...
    
    pyinstaller --noconfirm "money_manager.spec"
    if %ERRORLEVEL% NEQ 0 (
        echo فشل بناء الملف التنفيذي. يرجى التحقق من ملفات .spec والمتطلبات.
        pause
        exit /b 1
    )
)

echo.
echo التحقق من وجود الملف التنفيذي...
if not exist "dist\Money Manager\Money Manager.exe" (
    echo لم يتم العثور على الملف التنفيذي في المسار المتوقع.
    echo يرجى التحقق من ملفات .spec والمتطلبات.
    pause
    exit /b 1
)

echo.
echo إنشاء حزمة التثبيت باستخدام Inno Setup...
echo.

REM إنشاء مجلد الإخراج إذا لم يكن موجودًا
if not exist "installer_output" mkdir "installer_output"

iscc installer.iss
if %ERRORLEVEL% NEQ 0 (
    echo فشل إنشاء حزمة التثبيت. يرجى التحقق من ملف installer.iss.
    pause
    exit /b 1
)

echo.
echo ===================================================
echo                  اكتملت العملية بنجاح!
echo ===================================================
echo.
echo الملف التنفيذي: dist\Money Manager\Money Manager.exe
echo حزمة التثبيت: installer_output\MoneyManagerSetup.exe
echo.
echo يمكنك الآن توزيع حزمة التثبيت على المستخدمين.
echo.

REM فتح مجلد الإخراج تلقائيًا
echo فتح مجلد حزمة التثبيت...
start explorer.exe "installer_output"

pause