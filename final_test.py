#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from database.models import Account, Transaction
from datetime import date

try:
    print("🧪 اختبار نهائي للمعاملات")
    print("=" * 40)
    
    # إنشاء حساب
    account_id = Account.create(
        user_id=1,
        name="حساب اختبار نهائي",
        description="اختبار نهائي للمعاملات"
    )
    print(f"✅ حساب جديد: {account_id}")
    
    # إضافة رصيد ابتدائي
    Account.add_currency_balance(account_id, 1, 1000.0)  # SAR
    Account.add_currency_balance(account_id, 4, 100.0)   # USD
    print("✅ تم إضافة أرصدة ابتدائية")
    
    # إضافة وارد SAR
    income_id = Transaction.create(
        user_id=1,
        account_id=account_id,
        currency_id=1,
        transaction_type='income',
        amount=500.0,
        description="راتب",
        transaction_date=date.today()
    )
    print(f"✅ وارد SAR: {income_id}")
    
    # إضافة مصروف USD
    expense_id = Transaction.create(
        user_id=1,
        account_id=account_id,
        currency_id=4,
        transaction_type='expense',
        amount=25.0,
        description="اشتراك",
        transaction_date=date.today()
    )
    print(f"✅ مصروف USD: {expense_id}")
    
    # عرض الأرصدة
    sar_balance = Account.get_currency_balance(account_id, 1)
    usd_balance = Account.get_currency_balance(account_id, 4)
    
    print(f"\n💰 الأرصدة النهائية:")
    print(f"   SAR: {sar_balance}")
    print(f"   USD: {usd_balance}")
    
    print("\n🎉 جميع الاختبارات نجحت!")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
