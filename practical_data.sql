-- إضافة بيانات عملية لبرنامج إدارة الأموال
USE money_manager;

-- حذف البيانات الموجودة للمستخدم الافتراضي
DELETE FROM transfers WHERE user_id = 1;
DELETE FROM transactions WHERE user_id = 1;
DELETE FROM accounts WHERE user_id = 1;

-- إضافة حسابين عمليين
INSERT INTO accounts (user_id, name, account_type_id, currency_id, initial_balance, current_balance, description) VALUES
(1, 'الحساب الجاري الرئيسي', 2, 1, 15000.00, 15000.00, 'الحساب الجاري الرئيسي في بنك الراجحي - للمعاملات اليومية'),
(1, 'المحفظة النقدية', 1, 1, 2000.00, 2000.00, 'النقد المتوفر في المحفظة للمصروفات اليومية الصغيرة');

-- الحصول على معرفات الحسابات
SET @account1_id = (SELECT id FROM accounts WHERE user_id = 1 AND name = 'الحساب الجاري الرئيسي');
SET @account2_id = (SELECT id FROM accounts WHERE user_id = 1 AND name = 'المحفظة النقدية');

-- إضافة واردات عملية متنوعة
INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_id, description, transaction_date) VALUES
-- الراتب الشهري
(1, @account1_id, 'income', 8500.00, 1, 1, 'راتب شهر ديسمبر 2025', '2025-06-24'),
-- دخل من عمل حر
(1, @account1_id, 'income', 2200.00, 1, 4, 'مشروع تطوير موقع إلكتروني للعميل أحمد', '2025-06-17'),
-- مكافأة من العمل
(1, @account1_id, 'income', 1500.00, 1, 1, 'مكافأة نهاية السنة', '2025-06-09'),
-- دخل من استثمار
(1, @account1_id, 'income', 750.00, 1, 3, 'أرباح من الأسهم - الربع الرابع', '2025-06-14'),
-- دخل من أعمال جانبية
(1, @account2_id, 'income', 450.00, 1, 2, 'بيع منتجات يدوية في المعرض', '2025-06-21'),
-- استشارة مهنية
(1, @account1_id, 'income', 1200.00, 1, 4, 'استشارة تقنية لشركة النور', '2025-06-04');

-- إضافة مصروفات عملية متنوعة
INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_id, description, transaction_date) VALUES
-- مصروفات السكن
(1, @account1_id, 'expense', 2500.00, 1, 3, 'إيجار الشقة - شهر ديسمبر', '2025-06-26'),
-- فواتير الخدمات
(1, @account1_id, 'expense', 380.00, 1, 4, 'فاتورة الكهرباء والماء', '2025-06-22'),
(1, @account1_id, 'expense', 120.00, 1, 4, 'فاتورة الإنترنت والهاتف', '2025-06-19'),
-- مصروفات الطعام
(1, @account2_id, 'expense', 85.00, 1, 1, 'تسوق من السوبر ماركت', '2025-06-28'),
(1, @account2_id, 'expense', 45.00, 1, 1, 'وجبة غداء في المطعم', '2025-06-27'),
(1, @account2_id, 'expense', 120.00, 1, 1, 'مشتريات من البقالة', '2025-06-25'),
-- مصروفات المواصلات
(1, @account2_id, 'expense', 200.00, 1, 2, 'وقود السيارة', '2025-06-23'),
(1, @account2_id, 'expense', 25.00, 1, 2, 'أجرة تاكسي', '2025-06-20'),
-- مصروفات صحية
(1, @account1_id, 'expense', 150.00, 1, 5, 'زيارة طبيب الأسنان', '2025-06-15'),
(1, @account2_id, 'expense', 65.00, 1, 5, 'أدوية من الصيدلية', '2025-06-18'),
-- مصروفات التسوق
(1, @account1_id, 'expense', 320.00, 1, 8, 'ملابس شتوية جديدة', '2025-06-11'),
(1, @account2_id, 'expense', 75.00, 1, 8, 'أدوات منزلية', '2025-06-16'),
-- مصروفات الترفيه
(1, @account2_id, 'expense', 90.00, 1, 7, 'تذاكر السينما مع الأصدقاء', '2025-06-13'),
(1, @account2_id, 'expense', 55.00, 1, 7, 'ألعاب فيديو جديدة', '2025-06-07'),
-- مصروفات التعليم
(1, @account1_id, 'expense', 450.00, 1, 6, 'دورة تدريبية في البرمجة', '2025-06-01');

-- إضافة تحويلات عملية بين الحسابين
INSERT INTO transfers (user_id, from_account_id, to_account_id, amount, currency_id, description, transfer_date) VALUES
(1, @account1_id, @account2_id, 500.00, 1, 'سحب نقدي للمصروفات اليومية', '2025-06-24'),
(1, @account1_id, @account2_id, 300.00, 1, 'نقد إضافي للطوارئ', '2025-06-17'),
(1, @account2_id, @account1_id, 150.00, 1, 'إيداع فائض النقد في البنك', '2025-06-09'),
(1, @account1_id, @account2_id, 250.00, 1, 'نقد لمصروفات نهاية الأسبوع', '2025-06-21');

-- تحديث أرصدة الحسابات بناءً على المعاملات والتحويلات
-- تحديث الحساب الجاري الرئيسي
UPDATE accounts SET current_balance = initial_balance + 
    (SELECT COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END), 0) 
     FROM transactions WHERE account_id = @account1_id) +
    (SELECT COALESCE(SUM(CASE WHEN to_account_id = @account1_id THEN amount ELSE -amount END), 0)
     FROM transfers WHERE from_account_id = @account1_id OR to_account_id = @account1_id)
WHERE id = @account1_id;

-- تحديث المحفظة النقدية
UPDATE accounts SET current_balance = initial_balance + 
    (SELECT COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END), 0) 
     FROM transactions WHERE account_id = @account2_id) +
    (SELECT COALESCE(SUM(CASE WHEN to_account_id = @account2_id THEN amount ELSE -amount END), 0)
     FROM transfers WHERE from_account_id = @account2_id OR to_account_id = @account2_id)
WHERE id = @account2_id;

-- عرض ملخص البيانات المضافة
SELECT '=== ملخص البيانات المضافة ===' as message;

SELECT CONCAT('الحسابات المضافة: ', COUNT(*)) as accounts_summary 
FROM accounts WHERE user_id = 1;

SELECT name as account_name, CONCAT(current_balance, ' ر.س') as current_balance 
FROM accounts WHERE user_id = 1;

SELECT CONCAT('إجمالي الواردات: ', COALESCE(SUM(amount), 0), ' ر.س') as income_summary
FROM transactions WHERE user_id = 1 AND transaction_type = 'income';

SELECT CONCAT('إجمالي المصروفات: ', COALESCE(SUM(amount), 0), ' ر.س') as expense_summary
FROM transactions WHERE user_id = 1 AND transaction_type = 'expense';

SELECT CONCAT('عدد التحويلات: ', COUNT(*)) as transfers_summary
FROM transfers WHERE user_id = 1;

SELECT CONCAT('إجمالي الأرصدة: ', COALESCE(SUM(current_balance), 0), ' ر.س') as total_balance
FROM accounts WHERE user_id = 1;

SELECT 'البرنامج جاهز للاستخدام العملي!' as final_message;
