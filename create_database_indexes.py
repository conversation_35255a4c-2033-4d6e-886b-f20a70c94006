#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء فهارس قاعدة البيانات لتحسين الأداء
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db

def create_performance_indexes():
    """إنشاء فهارس لتحسين أداء الاستعلامات"""
    
    print("🗄️ إنشاء فهارس قاعدة البيانات لتحسين الأداء...")
    
    if not (db.is_connected() or db.connect()):
        print("❌ فشل الاتصال بقاعدة البيانات")
        return False
    
    # قائمة الفهارس المطلوبة
    indexes = [
        # فهارس للجدول transactions
        {
            "name": "idx_transactions_user_type",
            "table": "transactions",
            "columns": "user_id, transaction_type",
            "sql": "CREATE INDEX IF NOT EXISTS idx_transactions_user_type ON transactions(user_id, transaction_type)"
        },
        {
            "name": "idx_transactions_date",
            "table": "transactions", 
            "columns": "transaction_date DESC",
            "sql": "CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date DESC)"
        },
        {
            "name": "idx_transactions_account",
            "table": "transactions",
            "columns": "account_id",
            "sql": "CREATE INDEX IF NOT EXISTS idx_transactions_account ON transactions(account_id)"
        },
        {
            "name": "idx_transactions_currency",
            "table": "transactions",
            "columns": "currency_id",
            "sql": "CREATE INDEX IF NOT EXISTS idx_transactions_currency ON transactions(currency_id)"
        },
        {
            "name": "idx_transactions_created",
            "table": "transactions",
            "columns": "created_at DESC",
            "sql": "CREATE INDEX IF NOT EXISTS idx_transactions_created ON transactions(created_at DESC)"
        },
        
        # فهارس للجدول accounts
        {
            "name": "idx_accounts_user_active",
            "table": "accounts",
            "columns": "user_id, is_active",
            "sql": "CREATE INDEX IF NOT EXISTS idx_accounts_user_active ON accounts(user_id, is_active)"
        },
        {
            "name": "idx_accounts_type",
            "table": "accounts",
            "columns": "account_type_id",
            "sql": "CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(account_type_id)"
        },
        
        # فهارس للجدول account_balances
        {
            "name": "idx_account_balances_account",
            "table": "account_balances",
            "columns": "account_id",
            "sql": "CREATE INDEX IF NOT EXISTS idx_account_balances_account ON account_balances(account_id)"
        },
        {
            "name": "idx_account_balances_currency",
            "table": "account_balances",
            "columns": "currency_id",
            "sql": "CREATE INDEX IF NOT EXISTS idx_account_balances_currency ON account_balances(currency_id)"
        },
        
        # فهارس للجدول currencies
        {
            "name": "idx_currencies_active",
            "table": "currencies",
            "columns": "is_active",
            "sql": "CREATE INDEX IF NOT EXISTS idx_currencies_active ON currencies(is_active)"
        },
        {
            "name": "idx_currencies_code",
            "table": "currencies",
            "columns": "code",
            "sql": "CREATE INDEX IF NOT EXISTS idx_currencies_code ON currencies(code)"
        },
        
        # فهارس للجدول transfers
        {
            "name": "idx_transfers_from_account",
            "table": "transfers",
            "columns": "from_account_id",
            "sql": "CREATE INDEX IF NOT EXISTS idx_transfers_from_account ON transfers(from_account_id)"
        },
        {
            "name": "idx_transfers_to_account",
            "table": "transfers",
            "columns": "to_account_id", 
            "sql": "CREATE INDEX IF NOT EXISTS idx_transfers_to_account ON transfers(to_account_id)"
        },
        {
            "name": "idx_transfers_date",
            "table": "transfers",
            "columns": "transfer_date DESC",
            "sql": "CREATE INDEX IF NOT EXISTS idx_transfers_date ON transfers(transfer_date DESC)"
        }
    ]
    
    created_count = 0
    failed_count = 0
    
    for index in indexes:
        try:
            print(f"📊 إنشاء فهرس: {index['name']} على {index['table']}({index['columns']})")
            
            # تنفيذ استعلام إنشاء الفهرس
            db.execute_update(index['sql'])
            
            print(f"   ✅ تم إنشاء الفهرس بنجاح")
            created_count += 1
            
        except Exception as e:
            print(f"   ❌ فشل في إنشاء الفهرس: {e}")
            failed_count += 1
    
    print(f"\n📊 ملخص إنشاء الفهارس:")
    print(f"   ✅ تم إنشاء: {created_count} فهرس")
    print(f"   ❌ فشل: {failed_count} فهرس")
    print(f"   📈 إجمالي: {len(indexes)} فهرس")
    
    return failed_count == 0

def analyze_query_performance():
    """تحليل أداء الاستعلامات بعد إنشاء الفهارس"""
    
    print("\n🔍 تحليل أداء الاستعلامات...")
    
    # استعلامات للاختبار
    test_queries = [
        {
            "name": "استعلام الواردات",
            "sql": """
                EXPLAIN SELECT t.*, a.name as account_name, c.symbol as currency_symbol
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = 4 AND t.transaction_type = 'income'
                ORDER BY t.transaction_date DESC, t.created_at DESC
                LIMIT 20
            """
        },
        {
            "name": "استعلام المصروفات", 
            "sql": """
                EXPLAIN SELECT t.*, a.name as account_name, c.symbol as currency_symbol
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = 4 AND t.transaction_type = 'expense'
                ORDER BY t.transaction_date DESC, t.created_at DESC
                LIMIT 20
            """
        },
        {
            "name": "استعلام أرصدة العملات",
            "sql": """
                EXPLAIN SELECT
                    c.code, c.name, c.symbol,
                    COALESCE(SUM(ab.balance), 0) as total_balance,
                    COUNT(DISTINCT a.id) as accounts_count
                FROM currencies c
                LEFT JOIN account_balances ab ON c.id = ab.currency_id
                INNER JOIN accounts a ON ab.account_id = a.id
                WHERE c.is_active = TRUE
                AND a.user_id = 4
                AND a.is_active = TRUE
                GROUP BY c.id, c.code, c.name, c.symbol
                HAVING total_balance > 0 OR accounts_count > 0
                ORDER BY total_balance DESC
                LIMIT 10
            """
        }
    ]
    
    for query in test_queries:
        try:
            print(f"\n📊 تحليل: {query['name']}")
            
            results = db.execute_query(query['sql'])
            
            if results:
                print("   📋 خطة التنفيذ:")
                for row in results:
                    # عرض معلومات خطة التنفيذ
                    if 'key' in row and row['key']:
                        print(f"      🔑 استخدام فهرس: {row['key']}")
                    if 'rows' in row:
                        print(f"      📄 عدد الصفوف المفحوصة: {row['rows']}")
                    if 'Extra' in row and 'Using index' in str(row['Extra']):
                        print(f"      ⚡ استخدام فهرس محسن")
            
        except Exception as e:
            print(f"   ❌ خطأ في تحليل الاستعلام: {e}")

def check_existing_indexes():
    """فحص الفهارس الموجودة"""
    
    print("\n📋 فحص الفهارس الموجودة...")
    
    try:
        # الحصول على قائمة الفهارس
        indexes_query = """
            SELECT 
                TABLE_NAME,
                INDEX_NAME,
                COLUMN_NAME,
                NON_UNIQUE
            FROM INFORMATION_SCHEMA.STATISTICS 
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME IN ('transactions', 'accounts', 'account_balances', 'currencies', 'transfers')
            ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX
        """
        
        indexes = db.execute_query(indexes_query)
        
        if indexes:
            current_table = None
            current_index = None
            
            for index in indexes:
                if index['TABLE_NAME'] != current_table:
                    current_table = index['TABLE_NAME']
                    print(f"\n📊 جدول: {current_table}")
                
                if index['INDEX_NAME'] != current_index:
                    current_index = index['INDEX_NAME']
                    index_type = "فريد" if index['NON_UNIQUE'] == 0 else "عادي"
                    print(f"   🔑 {current_index} ({index_type})")
                
                print(f"      - {index['COLUMN_NAME']}")
        
    except Exception as e:
        print(f"❌ خطأ في فحص الفهارس: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 أداة تحسين أداء قاعدة البيانات")
    print("=" * 45)
    
    # فحص الفهارس الموجودة
    check_existing_indexes()
    
    # إنشاء فهارس جديدة
    if create_performance_indexes():
        print("\n✅ تم إنشاء جميع الفهارس بنجاح!")
        
        # تحليل أداء الاستعلامات
        analyze_query_performance()
        
        print("\n🎯 النتائج المتوقعة:")
        print("   ⚡ تحسين سرعة تحميل الصفحات")
        print("   📈 تقليل وقت تنفيذ الاستعلامات")
        print("   🚀 تحسين تجربة المستخدم")
        
        print("\n📋 الخطوات التالية:")
        print("1. إعادة تشغيل التطبيق")
        print("2. اختبار سرعة التنقل بين الصفحات")
        print("3. مراقبة أداء الاستعلامات")
        
    else:
        print("\n⚠️ فشل في إنشاء بعض الفهارس")
        print("💡 تحقق من:")
        print("   - صلاحيات قاعدة البيانات")
        print("   - وجود الجداول المطلوبة")
        print("   - صحة أسماء الأعمدة")

if __name__ == "__main__":
    main()
