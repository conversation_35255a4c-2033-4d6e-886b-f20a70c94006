#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيف البيانات المتضاربة والمكررة
"""

import os
import shutil
import json
from datetime import datetime

def create_cleanup_backup():
    """إنشاء نسخة احتياطية قبل التنظيف"""
    print("💾 إنشاء نسخة احتياطية قبل التنظيف...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"cleanup_backup_{timestamp}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        # نسخ الملفات المهمة
        files_to_backup = [
            "money_manager.db",
            "accounts.json",
            "users.json", 
            "transactions.json"
        ]
        
        for file_path in files_to_backup:
            if os.path.exists(file_path):
                shutil.copy2(file_path, backup_dir)
                print(f"✅ تم نسخ {file_path}")
        
        # نسخ مجلد النسخ الاحتياطية القديمة
        if os.path.exists("backups"):
            backup_backups_dir = os.path.join(backup_dir, "old_backups")
            shutil.copytree("backups", backup_backups_dir)
            print("✅ تم نسخ النسخ الاحتياطية القديمة")
        
        print(f"✅ تم إنشاء نسخة احتياطية في: {backup_dir}")
        return backup_dir
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def analyze_files_for_cleanup():
    """تحليل الملفات المطلوب تنظيفها"""
    print("🔍 تحليل الملفات المطلوب تنظيفها...")
    
    files_analysis = {
        'sqlite_files': [],
        'json_files': [],
        'backup_files': [],
        'old_backups': [],
        'temp_files': []
    }
    
    # فحص ملفات SQLite
    for file in os.listdir('.'):
        if file.endswith('.db') and file != 'money_manager.db':
            files_analysis['sqlite_files'].append(file)
        elif file.endswith('.json') and file in ['accounts.json', 'users.json', 'transactions.json']:
            files_analysis['json_files'].append(file)
        elif file.startswith('backup_') or file.startswith('unification_backup_'):
            files_analysis['backup_files'].append(file)
        elif file.endswith('.tmp') or file.endswith('.temp'):
            files_analysis['temp_files'].append(file)
    
    # فحص النسخ الاحتياطية القديمة
    if os.path.exists('backups'):
        backup_files = os.listdir('backups')
        # الاحتفاظ بآخر 5 نسخ فقط
        backup_files.sort(reverse=True)
        if len(backup_files) > 5:
            files_analysis['old_backups'] = backup_files[5:]
    
    # طباعة التحليل
    print(f"📄 ملفات SQLite إضافية: {len(files_analysis['sqlite_files'])}")
    for file in files_analysis['sqlite_files']:
        print(f"   - {file}")
    
    print(f"📄 ملفات JSON: {len(files_analysis['json_files'])}")
    for file in files_analysis['json_files']:
        print(f"   - {file}")
    
    print(f"💾 ملفات النسخ الاحتياطية: {len(files_analysis['backup_files'])}")
    for file in files_analysis['backup_files'][:3]:  # أول 3 فقط
        print(f"   - {file}")
    
    print(f"🗑️ نسخ احتياطية قديمة: {len(files_analysis['old_backups'])}")
    
    return files_analysis

def cleanup_sqlite_files(files_analysis, backup_dir):
    """تنظيف ملفات SQLite الإضافية"""
    print("\n🗄️ تنظيف ملفات SQLite...")
    
    sqlite_files = files_analysis['sqlite_files']
    
    if not sqlite_files:
        print("✅ لا توجد ملفات SQLite إضافية للحذف")
        return
    
    for sqlite_file in sqlite_files:
        try:
            # نسخ إلى النسخة الاحتياطية أولاً
            shutil.copy2(sqlite_file, backup_dir)
            
            # حذف الملف
            os.remove(sqlite_file)
            print(f"🗑️ تم حذف {sqlite_file}")
            
        except Exception as e:
            print(f"❌ خطأ في حذف {sqlite_file}: {e}")

def cleanup_json_files(files_analysis, backup_dir):
    """تنظيف ملفات JSON"""
    print("\n📄 تنظيف ملفات JSON...")
    
    json_files = files_analysis['json_files']
    
    if not json_files:
        print("✅ لا توجد ملفات JSON للحذف")
        return
    
    for json_file in json_files:
        try:
            # نسخ إلى النسخة الاحتياطية أولاً
            shutil.copy2(json_file, backup_dir)
            
            # حذف الملف
            os.remove(json_file)
            print(f"🗑️ تم حذف {json_file}")
            
        except Exception as e:
            print(f"❌ خطأ في حذف {json_file}: {e}")

def cleanup_old_backups(files_analysis):
    """تنظيف النسخ الاحتياطية القديمة"""
    print("\n💾 تنظيف النسخ الاحتياطية القديمة...")
    
    old_backups = files_analysis['old_backups']
    
    if not old_backups:
        print("✅ لا توجد نسخ احتياطية قديمة للحذف")
        return
    
    for backup_file in old_backups:
        try:
            backup_path = os.path.join('backups', backup_file)
            os.remove(backup_path)
            print(f"🗑️ تم حذف النسخة الاحتياطية القديمة: {backup_file}")
            
        except Exception as e:
            print(f"❌ خطأ في حذف {backup_file}: {e}")

def cleanup_main_sqlite():
    """حذف ملف SQLite الرئيسي بعد التأكد من نقل البيانات"""
    print("\n🗄️ حذف ملف SQLite الرئيسي...")
    
    if not os.path.exists("money_manager.db"):
        print("✅ ملف SQLite الرئيسي غير موجود")
        return
    
    try:
        # التحقق من حجم الملف
        file_size = os.path.getsize("money_manager.db")
        print(f"📊 حجم ملف SQLite: {file_size} bytes")
        
        # حذف الملف
        os.remove("money_manager.db")
        print("🗑️ تم حذف ملف SQLite الرئيسي")
        
    except Exception as e:
        print(f"❌ خطأ في حذف ملف SQLite: {e}")

def update_application_config():
    """تحديث إعدادات التطبيق لاستخدام MySQL فقط"""
    print("\n⚙️ تحديث إعدادات التطبيق...")
    
    # التحقق من ملف database_config.py
    config_file = "config/database_config.py"
    if os.path.exists(config_file):
        print(f"✅ ملف التكوين موجود: {config_file}")
        # يمكن إضافة تعديلات على الملف هنا إذا لزم الأمر
    
    # التحقق من ملف settings.py
    settings_file = "config/settings.py"
    if os.path.exists(settings_file):
        print(f"✅ ملف الإعدادات موجود: {settings_file}")
    
    print("✅ إعدادات التطبيق محدثة لاستخدام MySQL فقط")

def generate_cleanup_report(backup_dir, files_analysis):
    """إنشاء تقرير التنظيف"""
    print("\n📋 إنشاء تقرير التنظيف...")
    
    report = {
        'cleanup_timestamp': datetime.now().isoformat(),
        'backup_directory': backup_dir,
        'files_cleaned': {
            'sqlite_files': files_analysis['sqlite_files'],
            'json_files': files_analysis['json_files'],
            'old_backups': files_analysis['old_backups']
        },
        'main_sqlite_removed': os.path.exists("money_manager.db") == False,
        'status': 'completed'
    }
    
    report_file = f"cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"✅ تم إنشاء تقرير التنظيف: {report_file}")
    return report_file

def main():
    """الدالة الرئيسية"""
    print("🧹 بدء عملية تنظيف البيانات المتضاربة")
    print("=" * 50)
    
    # 1. إنشاء نسخة احتياطية
    backup_dir = create_cleanup_backup()
    if not backup_dir:
        print("❌ فشل في إنشاء النسخة الاحتياطية - توقف العملية")
        return
    
    # 2. تحليل الملفات
    files_analysis = analyze_files_for_cleanup()
    
    # 3. تنظيف الملفات
    cleanup_sqlite_files(files_analysis, backup_dir)
    cleanup_json_files(files_analysis, backup_dir)
    cleanup_old_backups(files_analysis)
    
    # 4. حذف ملف SQLite الرئيسي (بعد التأكد من نقل البيانات)
    cleanup_main_sqlite()
    
    # 5. تحديث إعدادات التطبيق
    update_application_config()
    
    # 6. إنشاء تقرير التنظيف
    report_file = generate_cleanup_report(backup_dir, files_analysis)
    
    print("\n🎉 تم تنظيف البيانات المتضاربة بنجاح!")
    print(f"💾 النسخة الاحتياطية: {backup_dir}")
    print(f"📋 تقرير التنظيف: {report_file}")

if __name__ == "__main__":
    main()
