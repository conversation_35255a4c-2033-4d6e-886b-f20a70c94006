# برنامج مدير الأموال - دليل البناء والتوزيع

## مقدمة

هذا الدليل يشرح كيفية تحويل برنامج مدير الأموال إلى ملف تنفيذي (exe) وإنشاء حزمة تثبيت قابلة للتوزيع على أجهزة أخرى. تم إعداد مجموعة من الملفات لتسهيل هذه العملية.

## الملفات المتاحة

1. **build_installer.bat**
   - ملف دفعي يقوم تلقائيًا ببناء الملف التنفيذي وحزمة التثبيت
   - يتحقق من وجود الأدوات المطلوبة ويقوم بتثبيتها إذا لزم الأمر
   - ينسخ ملفات الدليل إلى مجلد الإخراج

2. **تعليمات_البناء_والتثبيت.md**
   - دليل تفصيلي لعملية البناء والتثبيت
   - يشرح المتطلبات الأساسية والخطوات اليدوية
   - يقدم نصائح لاستكشاف الأخطاء وإصلاحها

3. **دليل_المستخدم_المختصر.md**
   - دليل مختصر للمستخدم النهائي
   - يشرح كيفية تثبيت واستخدام البرنامج
   - يوضح الميزات الرئيسية والإعدادات

4. **README_DISTRIBUTION.md**
   - دليل شامل لتوزيع البرنامج
   - يشرح الملفات المهمة وخطوات البناء
   - يقدم نصائح للتخصيص والتوزيع

## الطريقة السريعة للبناء والتوزيع

### 1. بناء البرنامج التنفيذي وحزمة التثبيت

1. انقر بزر الماوس الأيمن على ملف `build_installer.bat`
2. اختر "تشغيل كمسؤول" (Run as administrator)
3. انتظر حتى تكتمل العملية

### 2. الملفات الناتجة

- **الملف التنفيذي**: `dist\Money Manager\Money Manager.exe`
- **حزمة التثبيت**: `installer_output\MoneyManagerSetup.exe`
- **ملفات الدليل**: نسخ من ملفات الدليل في مجلد `installer_output`

### 3. التوزيع

قم بتوزيع محتويات مجلد `installer_output` على المستخدمين، والذي يتضمن:
- ملف التثبيت `MoneyManagerSetup.exe`
- دليل المستخدم المختصر `دليل_المستخدم_المختصر.md`
- تعليمات البناء والتثبيت `تعليمات_البناء_والتثبيت.md` (اختياري)

## المتطلبات الأساسية

### للمطورين (لبناء البرنامج)

1. **بايثون (Python)**: الإصدار 3.8 أو أحدث
2. **PyInstaller**: لتحويل برامج بايثون إلى ملفات تنفيذية
   - يمكن تثبيته باستخدام: `pip install pyinstaller`
3. **Inno Setup**: لإنشاء حزم التثبيت لنظام ويندوز
   - يمكن تحميله من: https://jrsoftware.org/isdl.php

### للمستخدمين النهائيين

- نظام التشغيل: Windows 10 أو أحدث (32 أو 64 بت)
- المساحة المطلوبة: 100 ميجابايت على الأقل
- الذاكرة: 2 جيجابايت RAM على الأقل
- لا يتطلب تثبيت Python أو أي مكتبات إضافية

## تخصيص عملية البناء

### تعديل ملف PyInstaller (.spec)

يمكنك تعديل ملف `Money Manager.spec` أو `money_manager.spec` لتغيير إعدادات بناء الملف التنفيذي:

- تغيير أيقونة البرنامج
- إضافة ملفات أو مجلدات إضافية
- تعديل اسم الملف التنفيذي
- إضافة مكتبات مخفية إضافية

### تعديل ملف Inno Setup (.iss)

يمكنك تعديل ملف `installer.iss` لتخصيص حزمة التثبيت:

- تغيير اسم البرنامج أو الإصدار
- تعديل مسار التثبيت الافتراضي
- إضافة خطوات تثبيت مخصصة
- تغيير لغة واجهة التثبيت

## استكشاف الأخطاء وإصلاحها

### مشاكل PyInstaller

- تأكد من تثبيت جميع المكتبات المطلوبة في ملف `requirements.txt`
- تحقق من وجود ملف `.spec` في المجلد الرئيسي
- إذا فشل البناء باستخدام `Money Manager.spec`، سيحاول الملف الدفعي استخدام `money_manager.spec`

### مشاكل Inno Setup

- تأكد من إضافة مسار تثبيت Inno Setup إلى متغير PATH في النظام
- تحقق من صحة ملف `installer.iss`
- تأكد من وجود الملفات المشار إليها في ملف `installer.iss`

### مشاكل التشغيل

- إذا واجه المستخدمون مشاكل في تشغيل البرنامج، تأكد من تضمين جميع المكتبات المطلوبة في ملف `.spec`
- قد تحتاج إلى إضافة مكتبات إضافية إلى قائمة `hiddenimports` في ملف `.spec`

## ملاحظات هامة

1. **اختبار قبل التوزيع**: تأكد دائمًا من اختبار الملف التنفيذي وحزمة التثبيت على جهاز نظيف قبل التوزيع

2. **النسخ الاحتياطي**: قم بالاحتفاظ بنسخة من الكود المصدري وملفات البناء

3. **التوثيق**: قم بتحديث ملفات الدليل والتوثيق مع كل إصدار جديد

4. **الإصدارات**: قم بتحديث رقم الإصدار في ملف `installer.iss` مع كل إصدار جديد

5. **الأمان**: تأكد من عدم تضمين كلمات مرور أو بيانات حساسة في الملف التنفيذي

---

للمزيد من المعلومات التفصيلية، يرجى الرجوع إلى الملفات المذكورة أعلاه.