#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع للبرنامج مع البيانات التجريبية
"""

import sys
import os
import subprocess
from pathlib import Path

def main():
    """تشغيل البرنامج مع البيانات التجريبية"""
    
    print("=" * 60)
    print("🏦 تشغيل سريع لبرنامج إدارة الأموال")
    print("=" * 60)
    
    # التحقق من وجود قاعدة البيانات
    print("🔍 فحص قاعدة البيانات...")
    
    try:
        import mysql.connector
        
        # محاولة الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='mohdam',
            database='money_manager'
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        user_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM accounts WHERE user_id = 1")
        accounts_count = cursor.fetchone()[0]
        
        cursor.close()
        connection.close()
        
        if user_count == 0:
            print("❌ قاعدة البيانات غير مُعدة")
            setup_choice = input("هل تريد إعداد قاعدة البيانات الآن؟ (y/n): ")
            if setup_choice.lower() == 'y':
                print("🔄 إعداد قاعدة البيانات...")
                result = subprocess.run([sys.executable, "setup_database.py"], 
                                      capture_output=True, text=True)
                if result.returncode != 0:
                    print("❌ فشل في إعداد قاعدة البيانات")
                    print(result.stderr)
                    return
                print("✅ تم إعداد قاعدة البيانات")
            else:
                print("تم إلغاء العملية")
                return
        
        if accounts_count < 2:
            print("📊 قاعدة البيانات تحتاج لبيانات تجريبية")
            demo_choice = input("هل تريد إضافة البيانات التجريبية؟ (y/n): ")
            if demo_choice.lower() == 'y':
                print("🔄 إضافة البيانات التجريبية...")
                result = subprocess.run([sys.executable, "add_demo_accounts.py"], 
                                      capture_output=True, text=True)
                if result.returncode != 0:
                    print("❌ فشل في إضافة البيانات التجريبية")
                    print(result.stderr)
                    return
                print("✅ تم إضافة البيانات التجريبية")
        
        print("✅ قاعدة البيانات جاهزة")
        
    except ImportError:
        print("❌ مكتبة mysql-connector-python غير مثبتة")
        install_choice = input("هل تريد تثبيت المتطلبات؟ (y/n): ")
        if install_choice.lower() == 'y':
            print("🔄 تثبيت المتطلبات...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            if result.returncode != 0:
                print("❌ فشل في تثبيت المتطلبات")
                return
            print("✅ تم تثبيت المتطلبات")
        else:
            print("تم إلغاء العملية")
            return
    
    except mysql.connector.Error as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        print("💡 تأكد من:")
        print("   - تشغيل MySQL Server")
        print("   - صحة بيانات الاتصال")
        return
    
    # تشغيل البرنامج الرئيسي
    print("\n🚀 تشغيل البرنامج...")
    print("\n📋 بيانات تسجيل الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: 123456")
    print("\n💡 الميزات المتاحة:")
    print("   • إدارة 5 حسابات مختلفة")
    print("   • أكثر من 25 معاملة تجريبية")
    print("   • 5 تحويلات بين الحسابات")
    print("   • تقارير مالية شاملة")
    print("   • واجهة سهلة الاستخدام")
    
    try:
        # تشغيل البرنامج الرئيسي
        subprocess.run([sys.executable, "main.py"])
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البرنامج: {e}")

if __name__ == "__main__":
    main()
