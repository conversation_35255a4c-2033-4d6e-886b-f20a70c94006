# دليل توزيع برنامج مدير الأموال

هذا الدليل يشرح كيفية بناء وتوزيع برنامج مدير الأموال على أجهزة أخرى.

## الملفات المهمة

- `build_installer.bat`: ملف دفعي لبناء البرنامج التنفيذي وحزمة التثبيت تلقائيًا
- `Money Manager.spec`: ملف إعدادات PyInstaller لبناء الملف التنفيذي
- `installer.iss`: ملف إعدادات Inno Setup لإنشاء حزمة التثبيت
- `تعليمات_البناء_والتثبيت.md`: دليل تفصيلي لعملية البناء والتثبيت
- `دليل_المستخدم_المختصر.md`: دليل مختصر للمستخدم النهائي

## خطوات البناء السريعة

1. تأكد من تثبيت Python وجميع المكتبات المطلوبة (راجع ملف `requirements.txt`)
2. قم بتثبيت PyInstaller: `pip install pyinstaller`
3. قم بتثبيت Inno Setup من الموقع الرسمي: https://jrsoftware.org/isdl.php
4. قم بتشغيل ملف `build_installer.bat` كمسؤول
5. انتظر حتى تكتمل العملية
6. ستجد الملف التنفيذي في مجلد `dist\Money Manager`
7. ستجد حزمة التثبيت في مجلد `installer_output`

## الملفات الناتجة للتوزيع

- **الملف التنفيذي**: `dist\Money Manager\Money Manager.exe`
  - يمكن تشغيله مباشرة على أي جهاز ويندوز متوافق
  - يحتوي على جميع المكتبات والموارد المطلوبة
  
- **حزمة التثبيت**: `installer_output\MoneyManagerSetup.exe`
  - الطريقة المفضلة للتوزيع
  - تقوم بتثبيت البرنامج بشكل صحيح مع إنشاء اختصارات
  - تسمح بإلغاء التثبيت بسهولة

## طريقة التوزيع الموصى بها

1. قم بتوزيع ملف `MoneyManagerSetup.exe` مع ملف `دليل_المستخدم_المختصر.md`
2. اطلب من المستخدمين تشغيل ملف التثبيت واتباع التعليمات
3. يمكن للمستخدمين الرجوع إلى دليل المستخدم المختصر للبدء باستخدام البرنامج

## متطلبات النظام للمستخدمين

- نظام التشغيل: Windows 10 أو أحدث (32 أو 64 بت)
- المساحة المطلوبة: 100 ميجابايت على الأقل
- الذاكرة: 2 جيجابايت RAM على الأقل
- لا يتطلب تثبيت Python أو أي مكتبات إضافية

## تخصيص عملية البناء

### تعديل ملف PyInstaller (.spec)

يمكنك تعديل ملف `Money Manager.spec` لتغيير إعدادات بناء الملف التنفيذي:

- تغيير أيقونة البرنامج
- إضافة ملفات أو مجلدات إضافية
- تعديل اسم الملف التنفيذي
- إضافة مكتبات مخفية إضافية

### تعديل ملف Inno Setup (.iss)

يمكنك تعديل ملف `installer.iss` لتخصيص حزمة التثبيت:

- تغيير اسم البرنامج أو الإصدار
- تعديل مسار التثبيت الافتراضي
- إضافة خطوات تثبيت مخصصة
- تغيير لغة واجهة التثبيت
- إضافة اتفاقية ترخيص

## ملاحظات هامة

1. **اختبار قبل التوزيع**: تأكد دائمًا من اختبار الملف التنفيذي وحزمة التثبيت على جهاز نظيف قبل التوزيع

2. **النسخ الاحتياطي**: قم بالاحتفاظ بنسخة من الكود المصدري وملفات البناء

3. **التوثيق**: قم بتحديث ملفات الدليل والتوثيق مع كل إصدار جديد

4. **الإصدارات**: قم بتحديث رقم الإصدار في ملف `installer.iss` مع كل إصدار جديد

5. **الأمان**: تأكد من عدم تضمين كلمات مرور أو بيانات حساسة في الملف التنفيذي

## استكشاف الأخطاء وإصلاحها

### مشاكل PyInstaller

- إذا فشل بناء الملف التنفيذي، جرب استخدام ملف `.spec` آخر مثل `money_manager.spec`
- تأكد من تثبيت جميع المكتبات المطلوبة: `pip install -r requirements.txt`

### مشاكل Inno Setup

- تأكد من إضافة مسار تثبيت Inno Setup إلى متغير PATH في النظام
- تحقق من صحة ملف `installer.iss`

### مشاكل التشغيل

- إذا واجه المستخدمون مشاكل في تشغيل البرنامج، تأكد من تضمين جميع المكتبات المطلوبة في ملف `.spec`
- قد تحتاج إلى إضافة مكتبات إضافية إلى قائمة `hiddenimports` في ملف `.spec`

---

للمزيد من المعلومات، راجع ملف `تعليمات_البناء_والتثبيت.md`