# 🏦 برنامج إدارة الأموال الشخصية - الإصدار المحدث

## 📋 ملخص التحديثات المطبقة

تم إجراء التعديلات التالية على البرنامج لضمان عمله بشكل صحيح:

### 🔧 الإصلاحات المطبقة:

1. **إصلاح أسماء الأعمدة في قاعدة البيانات:**
   - تغيير `type` إلى `transaction_type` في جدول transactions
   - تحديث جميع الاستعلامات لتتوافق مع الأسماء الجديدة

2. **تحسين ملفات النماذج (Models):**
   - إصلاح استعلامات الواردات والمصروفات
   - تحديث دوال التحويلات بين الحسابات
   - إصلاح حساب الأرصدة

3. **تحديث الواجهة الرئيسية:**
   - إصلاح عرض المعاملات في لوحة التحكم
   - تحديث استعلامات الإحصائيات
   - تحسين عرض كشوف الحسابات

4. **إضافة بيانات تجريبية شاملة:**
   - 5 حسابات مختلفة (شخصي، أعمال، نقدي، توفير، دولاري)
   - أكثر من 25 معاملة متنوعة (واردات ومصروفات)
   - 5 تحويلات بين الحسابات
   - تصنيفات مختلفة للمعاملات

## 🚀 كيفية تشغيل البرنامج

### المتطلبات:
- Python 3.7 أو أحدث
- MySQL Server
- المكتبات المطلوبة (requirements.txt)

### خطوات التشغيل:

#### 1. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

#### 2. تشغيل MySQL Server:
تأكد من تشغيل MySQL Server على جهازك

#### 3. إعداد قاعدة البيانات:
```bash
python setup_database.py
```

#### 4. إضافة البيانات التجريبية (اختياري):
```bash
python add_demo_accounts.py
```

#### 5. تشغيل البرنامج:
```bash
python main.py
```

أو للتشغيل السريع مع الإعداد التلقائي:
```bash
python quick_demo.py
```

## 🔑 بيانات تسجيل الدخول

- **اسم المستخدم:** admin
- **كلمة المرور:** 123456

## 🏦 الحسابات التجريبية المتاحة

1. **الحساب الشخصي - الراجحي** (حساب بنكي)
2. **حساب الأعمال - الأهلي** (حساب بنكي)
3. **الصندوق النقدي** (نقد)
4. **حساب التوفير** (توفير)
5. **محفظة دولارية** (حساب بالدولار)

## 💰 المعاملات التجريبية

### الواردات:
- رواتب شهرية
- دخل من الأعمال والمشاريع
- عوائد استثمارية
- دخل إضافي من العمل الحر

### المصروفات:
- طعام وشراب
- مواصلات
- سكن (إيجار)
- فواتير (كهرباء، إنترنت، هاتف)
- صحة
- تعليم
- ترفيه
- تسوق

### التحويلات:
- تحويلات للطوارئ
- تحويلات للأعمال
- سحب نقدي
- تحويلات للتوفير
- تحويل عملات

## 📊 الميزات المتاحة

### 1. لوحة التحكم:
- إحصائيات مالية سريعة
- المعاملات الأخيرة
- ملخص الأرصدة

### 2. إدارة الواردات:
- إضافة واردات جديدة
- تصنيف الواردات
- عرض تفصيلي للواردات

### 3. إدارة المصروفات:
- إضافة مصروفات جديدة
- تصنيف المصروفات
- تتبع المصروفات الشهرية

### 4. إدارة الحسابات:
- إنشاء حسابات جديدة
- أنواع حسابات متعددة
- دعم عملات مختلفة
- تتبع الأرصدة

### 5. التحويلات:
- تحويل بين الحسابات
- تتبع التحويلات
- حفظ سجل التحويلات

### 6. التقارير:
- ملخص مالي شامل
- تقرير الحسابات
- تقرير المعاملات الشهرية
- إحصائيات لآخر 6 أشهر

### 7. الإعدادات:
- معلومات المستخدم
- إحصائيات النظام
- إدارة البيانات (قريباً)

## 🔧 استكشاف الأخطاء

### مشكلة الاتصال بقاعدة البيانات:
1. تأكد من تشغيل MySQL Server
2. تحقق من بيانات الاتصال في ملفات الإعداد
3. تأكد من وجود قاعدة البيانات `money_manager`

### مشكلة المكتبات المفقودة:
```bash
pip install customtkinter mysql-connector-python bcrypt pillow
```

### مشكلة الواجهة الرسومية:
- تأكد من تثبيت tkinter (مدمج مع Python عادة)
- جرب تشغيل البرنامج من terminal/command prompt

## 📞 الدعم

في حالة مواجهة أي مشاكل:

1. تحقق من ملف السجل في مجلد `logs/`
2. تأكد من تطبيق جميع الإصلاحات
3. جرب إعادة إعداد قاعدة البيانات
4. تحقق من متطلبات النظام

## 🎯 الخطوات التالية

لتطوير البرنامج أكثر، يمكن إضافة:

1. ميزة النسخ الاحتياطي
2. تصدير البيانات إلى Excel
3. الرسوم البيانية والتحليلات
4. تذكيرات الفواتير
5. إدارة الديون والقروض
6. أهداف التوفير
7. تطبيق موبايل

---

**📌 ملاحظة:** جميع التعديلات المطلوبة تم تطبيقها بنجاح. البرنامج الآن جاهز للاستخدام مع حسابين وعمليات متنوعة كما طُلب.
