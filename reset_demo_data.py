#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إعادة تعيين البيانات التجريبية لتطبيق إدارة الأموال
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import random
from datetime import datetime, timedelta
from decimal import Decimal
from database.connection import db
from utils.auth import auth_manager

class DemoDataReset:
    """فئة إعادة تعيين البيانات التجريبية"""
    
    def __init__(self):
        self.user_id = None
        self.accounts = []
        self.income_categories = []
        self.expense_categories = []
        
    def connect_and_login(self):
        """الاتصال بقاعدة البيانات وتسجيل الدخول"""
        print("🔗 الاتصال بقاعدة البيانات...")
        
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # تسجيل الدخول كمدير
        print("👤 تسجيل الدخول كمدير...")
        success, message = auth_manager.login("admin", "123456")
        
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        self.user_id = auth_manager.current_user['id']
        print(f"✅ تم تسجيل الدخول بنجاح (المستخدم ID: {self.user_id})")
        return True
    
    def clear_existing_data(self):
        """حذف البيانات الحالية بأمان"""
        print("\n🧹 حذف البيانات الحالية...")
        
        try:
            # تعطيل فحص المفاتيح الخارجية مؤقتاً
            db.execute_update("SET FOREIGN_KEY_CHECKS = 0")
            
            # حذف المعاملات والتحويلات
            print("   - حذف المعاملات المالية...")
            db.execute_update("DELETE FROM transactions WHERE account_id IN (SELECT id FROM accounts WHERE user_id = %s)", (self.user_id,))
            
            print("   - حذف التحويلات...")
            db.execute_update("DELETE FROM transfers WHERE from_account_id IN (SELECT id FROM accounts WHERE user_id = %s) OR to_account_id IN (SELECT id FROM accounts WHERE user_id = %s)", (self.user_id, self.user_id))

            print("   - حذف المرفقات...")
            try:
                db.execute_update("DELETE FROM attachments WHERE transaction_id IN (SELECT id FROM transactions WHERE account_id IN (SELECT id FROM accounts WHERE user_id = %s))", (self.user_id,))
            except Exception as e:
                print(f"      ⚠️ تخطي حذف المرفقات (الجدول غير موجود): {e}")
            
            # حذف الحسابات
            print("   - حذف الحسابات المالية...")
            db.execute_update("DELETE FROM accounts WHERE user_id = %s", (self.user_id,))
            
            # إعادة تفعيل فحص المفاتيح الخارجية
            db.execute_update("SET FOREIGN_KEY_CHECKS = 1")
            
            print("✅ تم حذف البيانات الحالية بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حذف البيانات: {e}")
            # إعادة تفعيل فحص المفاتيح الخارجية في حالة الخطأ
            db.execute_update("SET FOREIGN_KEY_CHECKS = 1")
            return False
    
    def load_categories(self):
        """تحميل فئات الدخل والمصروفات"""
        print("\n📋 تحميل الفئات...")

        try:
            # تحميل فئات الدخل
            self.income_categories = db.execute_query("SELECT * FROM income_categories")
            if self.income_categories:
                print(f"   - فئات الدخل: {len(self.income_categories)}")
            else:
                print("   - فئات الدخل: 0")
                self.income_categories = []
        except Exception as e:
            print(f"   ⚠️ خطأ في تحميل فئات الدخل: {e}")
            self.income_categories = []

        try:
            # تحميل فئات المصروفات
            self.expense_categories = db.execute_query("SELECT * FROM expense_categories")
            if self.expense_categories:
                print(f"   - فئات المصروفات: {len(self.expense_categories)}")
            else:
                print("   - فئات المصروفات: 0")
                self.expense_categories = []
        except Exception as e:
            print(f"   ⚠️ خطأ في تحميل فئات المصروفات: {e}")
            self.expense_categories = []

        if not self.income_categories or not self.expense_categories:
            print("⚠️ لا توجد فئات كافية، سيتم إنشاء فئات افتراضية")
            return self.create_default_categories()

        return True

    def create_default_categories(self):
        """إنشاء فئات افتراضية"""
        print("   🔧 إنشاء فئات افتراضية...")

        try:
            # إنشاء فئات الدخل الافتراضية
            default_income_categories = [
                {'name': 'راتب', 'description': 'الراتب الشهري'},
                {'name': 'مكافآت', 'description': 'المكافآت والحوافز'},
                {'name': 'استثمارات', 'description': 'عوائد الاستثمارات'},
                {'name': 'أعمال جانبية', 'description': 'دخل من أعمال إضافية'},
                {'name': 'أخرى', 'description': 'مصادر دخل أخرى'}
            ]

            for category in default_income_categories:
                try:
                    query = "INSERT INTO income_categories (name, description, created_at) VALUES (%s, %s, %s)"
                    category_id = db.execute_insert(query, (category['name'], category['description'], datetime.now()))
                    if category_id:
                        category['id'] = category_id
                        self.income_categories.append(category)
                except Exception as e:
                    print(f"      ⚠️ خطأ في إنشاء فئة دخل {category['name']}: {e}")

            # إنشاء فئات المصروفات الافتراضية
            default_expense_categories = [
                {'name': 'طعام وشراب', 'description': 'مصروفات الطعام والمشروبات'},
                {'name': 'مواصلات', 'description': 'مصروفات النقل والمواصلات'},
                {'name': 'سكن', 'description': 'إيجار ومصروفات السكن'},
                {'name': 'فواتير', 'description': 'فواتير الخدمات'},
                {'name': 'ترفيه', 'description': 'مصروفات الترفيه والتسلية'},
                {'name': 'صحة', 'description': 'مصروفات طبية وصحية'},
                {'name': 'تسوق', 'description': 'مشتريات عامة'},
                {'name': 'أخرى', 'description': 'مصروفات أخرى'}
            ]

            for category in default_expense_categories:
                try:
                    query = "INSERT INTO expense_categories (name, description, created_at) VALUES (%s, %s, %s)"
                    category_id = db.execute_insert(query, (category['name'], category['description'], datetime.now()))
                    if category_id:
                        category['id'] = category_id
                        self.expense_categories.append(category)
                except Exception as e:
                    print(f"      ⚠️ خطأ في إنشاء فئة مصروف {category['name']}: {e}")

            print(f"   ✅ تم إنشاء {len(self.income_categories)} فئة دخل و {len(self.expense_categories)} فئة مصروف")
            return len(self.income_categories) > 0 and len(self.expense_categories) > 0

        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الفئات الافتراضية: {e}")
            return False
    
    def create_demo_accounts(self):
        """إنشاء حسابات تجريبية جديدة"""
        print("\n💳 إنشاء حسابات تجريبية...")
        
        demo_accounts = [
            {
                'name': 'الحساب الجاري الرئيسي',
                'type': 'checking',
                'currency': 'SAR',
                'balance': Decimal('15000.00'),
                'description': 'الحساب الجاري الأساسي للمعاملات اليومية'
            },
            {
                'name': 'حساب التوفير',
                'type': 'savings',
                'currency': 'SAR',
                'balance': Decimal('50000.00'),
                'description': 'حساب التوفير طويل المدى'
            },
            {
                'name': 'المحفظة النقدية',
                'type': 'cash',
                'currency': 'SAR',
                'balance': Decimal('2500.00'),
                'description': 'النقد المتاح في المحفظة'
            },
            {
                'name': 'حساب بالدولار',
                'type': 'checking',
                'currency': 'USD',
                'balance': Decimal('3200.00'),
                'description': 'حساب بالعملة الأمريكية'
            },
            {
                'name': 'بطاقة ائتمانية',
                'type': 'credit',
                'currency': 'SAR',
                'balance': Decimal('-5000.00'),
                'description': 'بطاقة ائتمانية مع حد ائتماني'
            }
        ]
        
        created_accounts = []
        
        for account_data in demo_accounts:
            try:
                query = """
                    INSERT INTO accounts (user_id, name, type, currency, balance, description, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                
                account_id = db.execute_insert(query, (
                    self.user_id,
                    account_data['name'],
                    account_data['type'],
                    account_data['currency'],
                    account_data['balance'],
                    account_data['description'],
                    datetime.now()
                ))
                
                if account_id:
                    account_data['id'] = account_id
                    created_accounts.append(account_data)
                    print(f"   ✅ تم إنشاء: {account_data['name']} (الرصيد: {account_data['balance']} {account_data['currency']})")
                
            except Exception as e:
                print(f"   ❌ خطأ في إنشاء حساب {account_data['name']}: {e}")
        
        self.accounts = created_accounts
        print(f"✅ تم إنشاء {len(created_accounts)} حساب بنجاح")
        return len(created_accounts) > 0
    
    def create_demo_transactions(self):
        """إنشاء معاملات تجريبية متنوعة"""
        print("\n💰 إنشاء معاملات تجريبية...")
        
        if not self.accounts or not self.income_categories or not self.expense_categories:
            print("❌ لا توجد حسابات أو فئات كافية")
            return False
        
        # إنشاء معاملات الدخل
        self.create_income_transactions()
        
        # إنشاء معاملات المصروفات
        self.create_expense_transactions()
        
        # إنشاء التحويلات
        self.create_transfers()
        
        return True
    
    def create_income_transactions(self):
        """إنشاء معاملات الدخل"""
        print("   📈 إنشاء معاملات الدخل...")
        
        income_data = [
            {'amount': 8000, 'description': 'راتب شهر ديسمبر', 'days_ago': 5},
            {'amount': 1500, 'description': 'مكافأة أداء', 'days_ago': 10},
            {'amount': 500, 'description': 'عمولة مبيعات', 'days_ago': 15},
            {'amount': 2000, 'description': 'استشارة تقنية', 'days_ago': 20},
            {'amount': 300, 'description': 'فوائد حساب التوفير', 'days_ago': 25},
            {'amount': 1200, 'description': 'عمل إضافي', 'days_ago': 30},
            {'amount': 800, 'description': 'بيع أجهزة قديمة', 'days_ago': 35},
            {'amount': 7500, 'description': 'راتب شهر نوفمبر', 'days_ago': 40},
            {'amount': 600, 'description': 'إيجار عقار', 'days_ago': 45},
            {'amount': 400, 'description': 'هدية نقدية', 'days_ago': 50},
            {'amount': 1000, 'description': 'مشروع جانبي', 'days_ago': 55},
            {'amount': 250, 'description': 'استرداد ضريبي', 'days_ago': 60}
        ]
        
        created_count = 0
        
        for income in income_data:
            try:
                # اختيار حساب عشوائي (تجنب بطاقة الائتمان للدخل)
                available_accounts = [acc for acc in self.accounts if acc['type'] != 'credit']
                account = random.choice(available_accounts)
                
                # اختيار فئة دخل عشوائية
                category = random.choice(self.income_categories)
                
                # حساب التاريخ
                transaction_date = datetime.now() - timedelta(days=income['days_ago'])
                
                query = """
                    INSERT INTO transactions (account_id, type, amount, description, category_id, transaction_date, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                
                transaction_id = db.execute_insert(query, (
                    account['id'],
                    'income',
                    Decimal(str(income['amount'])),
                    income['description'],
                    category['id'],
                    transaction_date,
                    datetime.now()
                ))
                
                if transaction_id:
                    # تحديث رصيد الحساب
                    new_balance = account['balance'] + Decimal(str(income['amount']))
                    db.execute_update("UPDATE accounts SET balance = %s WHERE id = %s", (new_balance, account['id']))
                    account['balance'] = new_balance
                    
                    created_count += 1
                    print(f"      ✅ {income['description']}: {income['amount']} {account['currency']}")
                
            except Exception as e:
                print(f"      ❌ خطأ في إنشاء معاملة دخل: {e}")
        
        print(f"   ✅ تم إنشاء {created_count} معاملة دخل")
    
    def create_expense_transactions(self):
        """إنشاء معاملات المصروفات"""
        print("   📉 إنشاء معاملات المصروفات...")
        
        expense_data = [
            {'amount': 1200, 'description': 'إيجار الشقة', 'days_ago': 3},
            {'amount': 800, 'description': 'فاتورة الكهرباء والماء', 'days_ago': 7},
            {'amount': 600, 'description': 'تسوق البقالة', 'days_ago': 2},
            {'amount': 300, 'description': 'وقود السيارة', 'days_ago': 5},
            {'amount': 150, 'description': 'اشتراك الإنترنت', 'days_ago': 10},
            {'amount': 450, 'description': 'فاتورة الهاتف', 'days_ago': 12},
            {'amount': 200, 'description': 'صيانة السيارة', 'days_ago': 15},
            {'amount': 350, 'description': 'مطعم مع الأصدقاء', 'days_ago': 8},
            {'amount': 120, 'description': 'قهوة ومقهى', 'days_ago': 4},
            {'amount': 800, 'description': 'ملابس جديدة', 'days_ago': 18},
            {'amount': 250, 'description': 'أدوية وصيدلية', 'days_ago': 22},
            {'amount': 180, 'description': 'كتب ومجلات', 'days_ago': 25},
            {'amount': 400, 'description': 'هدايا العائلة', 'days_ago': 28},
            {'amount': 320, 'description': 'تأمين السيارة', 'days_ago': 32},
            {'amount': 150, 'description': 'حلاق وعناية شخصية', 'days_ago': 35},
            {'amount': 500, 'description': 'إصلاح الهاتف', 'days_ago': 38},
            {'amount': 280, 'description': 'رسوم بنكية', 'days_ago': 42},
            {'amount': 650, 'description': 'تسوق أجهزة منزلية', 'days_ago': 45},
            {'amount': 200, 'description': 'اشتراك نادي رياضي', 'days_ago': 48},
            {'amount': 380, 'description': 'فاتورة طبيب', 'days_ago': 52}
        ]
        
        created_count = 0
        
        for expense in expense_data:
            try:
                # اختيار حساب عشوائي
                account = random.choice(self.accounts)
                
                # اختيار فئة مصروف عشوائية
                category = random.choice(self.expense_categories)
                
                # حساب التاريخ
                transaction_date = datetime.now() - timedelta(days=expense['days_ago'])
                
                query = """
                    INSERT INTO transactions (account_id, type, amount, description, category_id, transaction_date, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                
                transaction_id = db.execute_insert(query, (
                    account['id'],
                    'expense',
                    Decimal(str(expense['amount'])),
                    expense['description'],
                    category['id'],
                    transaction_date,
                    datetime.now()
                ))
                
                if transaction_id:
                    # تحديث رصيد الحساب
                    new_balance = account['balance'] - Decimal(str(expense['amount']))
                    db.execute_update("UPDATE accounts SET balance = %s WHERE id = %s", (new_balance, account['id']))
                    account['balance'] = new_balance
                    
                    created_count += 1
                    print(f"      ✅ {expense['description']}: {expense['amount']} {account['currency']}")
                
            except Exception as e:
                print(f"      ❌ خطأ في إنشاء معاملة مصروف: {e}")
        
        print(f"   ✅ تم إنشاء {created_count} معاملة مصروف")

    def create_transfers(self):
        """إنشاء تحويلات بين الحسابات"""
        print("   🔄 إنشاء التحويلات...")

        if len(self.accounts) < 2:
            print("      ⚠️ لا توجد حسابات كافية للتحويل")
            return

        transfer_data = [
            {'amount': 2000, 'description': 'تحويل للتوفير', 'days_ago': 6},
            {'amount': 500, 'description': 'سحب من التوفير للطوارئ', 'days_ago': 14},
            {'amount': 1000, 'description': 'تحويل لحساب الدولار', 'days_ago': 21},
            {'amount': 300, 'description': 'تعبئة المحفظة النقدية', 'days_ago': 28},
            {'amount': 1500, 'description': 'سداد بطاقة ائتمانية', 'days_ago': 35}
        ]

        created_count = 0

        for transfer in transfer_data:
            try:
                # اختيار حسابين مختلفين
                from_account = random.choice(self.accounts)
                to_accounts = [acc for acc in self.accounts if acc['id'] != from_account['id']]
                to_account = random.choice(to_accounts)

                # التأكد من وجود رصيد كافي
                if from_account['balance'] < Decimal(str(transfer['amount'])):
                    continue

                # حساب التاريخ
                transfer_date = datetime.now() - timedelta(days=transfer['days_ago'])

                query = """
                    INSERT INTO transfers (from_account_id, to_account_id, amount, description, transfer_date, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """

                transfer_id = db.execute_insert(query, (
                    from_account['id'],
                    to_account['id'],
                    Decimal(str(transfer['amount'])),
                    transfer['description'],
                    transfer_date,
                    datetime.now()
                ))

                if transfer_id:
                    # تحديث أرصدة الحسابات
                    from_new_balance = from_account['balance'] - Decimal(str(transfer['amount']))
                    to_new_balance = to_account['balance'] + Decimal(str(transfer['amount']))

                    db.execute_update("UPDATE accounts SET balance = %s WHERE id = %s", (from_new_balance, from_account['id']))
                    db.execute_update("UPDATE accounts SET balance = %s WHERE id = %s", (to_new_balance, to_account['id']))

                    from_account['balance'] = from_new_balance
                    to_account['balance'] = to_new_balance

                    created_count += 1
                    print(f"      ✅ {transfer['description']}: {transfer['amount']} من {from_account['name']} إلى {to_account['name']}")

            except Exception as e:
                print(f"      ❌ خطأ في إنشاء تحويل: {e}")

        print(f"   ✅ تم إنشاء {created_count} تحويل")

    def test_results(self):
        """اختبار النتائج والتحقق من صحة البيانات"""
        print("\n🧪 اختبار النتائج...")

        try:
            # فحص الحسابات
            accounts_count = db.execute_query("SELECT COUNT(*) as count FROM accounts WHERE user_id = %s", (self.user_id,))
            accounts_total = accounts_count[0]['count'] if accounts_count else 0
            print(f"   📊 إجمالي الحسابات: {accounts_total}")

            # فحص المعاملات
            transactions_count = db.execute_query("""
                SELECT COUNT(*) as count FROM transactions
                WHERE account_id IN (SELECT id FROM accounts WHERE user_id = %s)
            """, (self.user_id,))
            transactions_total = transactions_count[0]['count'] if transactions_count else 0
            print(f"   📊 إجمالي المعاملات: {transactions_total}")

            # فحص التحويلات
            transfers_count = db.execute_query("""
                SELECT COUNT(*) as count FROM transfers
                WHERE from_account_id IN (SELECT id FROM accounts WHERE user_id = %s)
            """, (self.user_id,))
            transfers_total = transfers_count[0]['count'] if transfers_count else 0
            print(f"   📊 إجمالي التحويلات: {transfers_total}")

            # عرض أرصدة الحسابات النهائية
            print("\n   💰 أرصدة الحسابات النهائية:")
            final_accounts = db.execute_query("SELECT name, balance, currency FROM accounts WHERE user_id = %s", (self.user_id,))

            for account in final_accounts:
                print(f"      - {account['name']}: {account['balance']} {account['currency']}")

            # فحص آخر المعاملات
            print("\n   📋 آخر 5 معاملات:")
            recent_transactions = db.execute_query("""
                SELECT t.description, t.amount, t.type, a.name as account_name, t.transaction_date
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                WHERE a.user_id = %s
                ORDER BY t.transaction_date DESC
                LIMIT 5
            """, (self.user_id,))

            for trans in recent_transactions:
                trans_type = "دخل" if trans['type'] == 'income' else "مصروف"
                print(f"      - {trans['description']}: {trans['amount']} ({trans_type}) - {trans['account_name']}")

            print("\n✅ تم إنشاء البيانات التجريبية بنجاح!")
            return True

        except Exception as e:
            print(f"❌ خطأ في اختبار النتائج: {e}")
            return False

    def run(self):
        """تشغيل عملية إعادة تعيين البيانات التجريبية"""
        print("🚀 بدء إعادة تعيين البيانات التجريبية")
        print("=" * 50)

        # الاتصال وتسجيل الدخول
        if not self.connect_and_login():
            return False

        # حذف البيانات الحالية
        if not self.clear_existing_data():
            return False

        # تحميل الفئات
        if not self.load_categories():
            print("⚠️ سيتم المتابعة بدون فئات محددة")

        # إنشاء الحسابات التجريبية
        if not self.create_demo_accounts():
            return False

        # إنشاء المعاملات التجريبية
        if not self.create_demo_transactions():
            return False

        # اختبار النتائج
        if not self.test_results():
            return False

        # تسجيل الخروج
        auth_manager.logout()

        print("\n🎉 تم إكمال إعادة تعيين البيانات التجريبية بنجاح!")
        print("\n📋 للتحقق من النتائج:")
        print("   1. شغل التطبيق: python main.py")
        print("   2. سجل الدخول: admin / 123456")
        print("   3. تحقق من الحسابات والمعاملات")

        return True

def main():
    """الدالة الرئيسية"""
    try:
        demo_reset = DemoDataReset()
        success = demo_reset.run()

        if success:
            print("\n✅ تمت العملية بنجاح!")
        else:
            print("\n❌ فشلت العملية!")

    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
