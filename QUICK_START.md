# 🚀 دليل البدء السريع - برنامج إدارة الأموال

## ⚡ التشغيل السريع (5 دقائق)

### 1️⃣ التحقق من المتطلبات
```bash
# فحص شامل للمتطلبات
python check_requirements.py

# أو فحص سريع
python --version  # يجب أن يكون 3.8+
mysql --version   # يجب أن يكون 8.0+
```

### 2️⃣ تثبيت المتطلبات
```bash
# تثبيت تلقائي
python install_requirements.py

# أو تثبيت يدوي
pip install -r requirements.txt
```

### 3️⃣ تشغيل البرنامج
```bash
# Windows
run.bat

# Linux/Mac
./run.sh

# أو مباشرة
python main.py
```

## 🎯 الإعداد الأولي

### عند التشغيل الأول:

1. **كلمة مرور MySQL:**
   - أدخل كلمة مرور MySQL root
   - اتركها فارغة إذا لم تكن محددة

2. **إنشاء المدير الأول:**
   - الاسم الكامل
   - اسم المستخدم
   - البريد الإلكتروني
   - كلمة مرور قوية

3. **بدء الاستخدام:**
   - ستظهر لوحة التحكم
   - يمكنك البدء بإضافة الحسابات والمعاملات

## 📊 البيانات التجريبية (اختياري)

لتجربة البرنامج مع بيانات جاهزة:

```bash
python create_sample_data.py
```

سيتم إنشاء:
- 5 حسابات مختلفة
- 10+ معاملات (واردات ومصروفات)
- تحويلات بين الحسابات

## 🏦 الميزات الأساسية

### إدارة الحسابات
- **إضافة حساب جديد:** القائمة → الحسابات → إضافة
- **أنواع الحسابات:** صندوق، بنك، محفظة إلكترونية، بطاقة ائتمان
- **العملات المدعومة:** 11 عملة (SAR, USD, EUR, YER, AED, KWD, QAR, BHD, OMR, JOD, EGP)

### إدارة المعاملات
- **إضافة وارد:** القائمة → الواردات → إضافة
- **إضافة مصروف:** القائمة → المصروفات → إضافة
- **التصنيفات:** تصنيفات جاهزة لكل نوع معاملة

### التحويلات
- **بين الحسابات:** القائمة → التحويلات
- **تحويل العملات:** تلقائي حسب أسعار الصرف

### التقارير
- **لوحة التحكم:** إحصائيات فورية
- **التقارير:** القائمة → التقارير
- **التصدير:** PDF, Excel, CSV

## 🔧 استكشاف الأخطاء السريع

### مشاكل شائعة:

#### "Python is not recognized"
```bash
# تأكد من تثبيت Python وإضافته لـ PATH
# Windows: أعد تثبيت Python مع تحديد "Add to PATH"
# Linux: sudo apt install python3
# Mac: brew install python3
```

#### "No module named 'customtkinter'"
```bash
pip install customtkinter
# أو
python install_requirements.py
```

#### "Can't connect to MySQL"
```bash
# تأكد من تشغيل MySQL
# Windows: net start mysql
# Linux: sudo systemctl start mysql
# Mac: brew services start mysql
```

#### واجهة لا تظهر
```bash
# Linux: تثبيت tkinter
sudo apt install python3-tk

# تحديث customtkinter
pip install --upgrade customtkinter
```

## 📁 هيكل المشروع

```
money-manager/
├── 🚀 main.py                 # تشغيل البرنامج
├── 📋 requirements.txt        # المتطلبات
├── 🔍 check_requirements.py   # فحص المتطلبات
├── 📦 install_requirements.py # تثبيت المتطلبات
├── 🎯 create_sample_data.py   # بيانات تجريبية
├── 🖥️  run.bat / run.sh       # تشغيل سريع
├── 📖 README.md              # الدليل الشامل
├── 🛠️  INSTALLATION_GUIDE.md  # دليل التثبيت
└── ⚡ QUICK_START.md         # هذا الملف
```

## 🎨 واجهة المستخدم

### الألوان والتصميم:
- **اللون الأساسي:** بنفسجي متدرج (#8B5CF6)
- **التصميم:** حديث وأنيق
- **اللغة:** عربي بالكامل
- **الخطوط:** واضحة ومريحة للعين

### التنقل:
- **القائمة الجانبية:** جميع الوظائف الرئيسية
- **لوحة التحكم:** نظرة عامة سريعة
- **الأزرار:** واضحة ومفهومة

## 💡 نصائح للاستخدام

### للمبتدئين:
1. ابدأ بإنشاء حساب واحد (مثل: الصندوق النقدي)
2. أضف بعض المعاملات البسيطة
3. استكشف لوحة التحكم
4. جرب إنشاء تقرير بسيط

### للمستخدمين المتقدمين:
1. استخدم عدة عملات
2. أنشئ حسابات متعددة
3. استفد من التحويلات بين الحسابات
4. استخدم النسخ الاحتياطي التلقائي

## 🔐 الأمان

### كلمات المرور:
- **تشفير:** bcrypt للحماية القصوى
- **المتطلبات:** 8 أحرف، أرقام، رموز خاصة
- **التغيير:** من الإعدادات

### النسخ الاحتياطي:
- **تلقائي:** كل 24 ساعة
- **يدوي:** من الإعدادات
- **الموقع:** مجلد backups/

## 📞 الحصول على المساعدة

### الملفات المرجعية:
- `README.md` - الدليل الشامل
- `INSTALLATION_GUIDE.md` - دليل التثبيت المفصل
- `logs/` - ملفات السجلات للأخطاء

### الأدوات المساعدة:
- `check_requirements.py` - فحص شامل
- `install_requirements.py` - تثبيت تلقائي
- `create_sample_data.py` - بيانات تجريبية

### خطوات استكشاف الأخطاء:
1. راجع ملفات السجل
2. شغل فحص المتطلبات
3. تأكد من تشغيل MySQL
4. راجع الأدلة المرجعية

## 🎉 مبروك!

أنت الآن جاهز لاستخدام برنامج إدارة الأموال!

### الخطوات التالية:
1. 🏦 أنشئ حساباتك المالية
2. 💰 أضف معاملاتك اليومية
3. 📊 راجع التقارير والإحصائيات
4. 🔄 استفد من التحويلات بين الحسابات
5. ⚙️ خصص الإعدادات حسب احتياجاتك

---

**استمتع بإدارة أموالك بطريقة احترافية! 💰✨**

> 💡 **نصيحة:** احفظ هذا الملف كمرجع سريع للعودة إليه عند الحاجة.
