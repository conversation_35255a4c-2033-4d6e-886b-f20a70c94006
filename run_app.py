#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل البرنامج مباشرة
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """تشغيل البرنامج مباشرة"""
    print("🏦 تشغيل برنامج خزانة الدائرة المالية...")
    print("📋 بيانات تسجيل الدخول: admin / 123456")
    print()
    
    try:
        from gui.login_window import LoginWindow
        app = LoginWindow()
        app.run()
        print("✅ تم إغلاق البرنامج بنجاح")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
