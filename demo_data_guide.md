# 🏦 دليل إضافة البيانات العملية يدوياً

## 📋 البيانات المطلوب إضافتها للاستخدام العملي

### 🏦 الحسابات المطلوبة:

#### 1. الحساب الجاري الرئيسي
- **الاسم:** الحساب الجاري الرئيسي
- **النوع:** حساب بنكي
- **العملة:** ريال سعودي (ر.س)
- **الرصيد الابتدائي:** 15,000.00 ر.س
- **الوصف:** الحساب الجاري الرئيسي في بنك الراجحي - للمعاملات اليومية

#### 2. المحفظة النقدية
- **الاسم:** المحفظة النقدية
- **النوع:** صندوق نقدي
- **العملة:** ريال سعودي (ر.س)
- **الرصيد الابتدائي:** 2,000.00 ر.س
- **الوصف:** النقد المتوفر في المحفظة للمصروفات اليومية الصغيرة

---

### 💰 الواردات المطلوبة:

#### 1. راتب شهر ديسمبر 2025
- **المبلغ:** 8,500.00 ر.س
- **الحساب:** الحساب الجاري الرئيسي
- **التصنيف:** راتب
- **التاريخ:** 2025-06-24
- **الوصف:** راتب شهر ديسمبر 2025

#### 2. مشروع تطوير موقع إلكتروني
- **المبلغ:** 2,200.00 ر.س
- **الحساب:** الحساب الجاري الرئيسي
- **التصنيف:** عمل حر
- **التاريخ:** 2025-06-17
- **الوصف:** مشروع تطوير موقع إلكتروني للعميل أحمد

#### 3. مكافأة نهاية السنة
- **المبلغ:** 1,500.00 ر.س
- **الحساب:** الحساب الجاري الرئيسي
- **التصنيف:** راتب
- **التاريخ:** 2025-06-09
- **الوصف:** مكافأة نهاية السنة

#### 4. أرباح من الأسهم
- **المبلغ:** 750.00 ر.س
- **الحساب:** الحساب الجاري الرئيسي
- **التصنيف:** استثمار
- **التاريخ:** 2025-06-14
- **الوصف:** أرباح من الأسهم - الربع الرابع

#### 5. بيع منتجات يدوية
- **المبلغ:** 450.00 ر.س
- **الحساب:** المحفظة النقدية
- **التصنيف:** أعمال
- **التاريخ:** 2025-06-21
- **الوصف:** بيع منتجات يدوية في المعرض

#### 6. استشارة تقنية
- **المبلغ:** 1,200.00 ر.س
- **الحساب:** الحساب الجاري الرئيسي
- **التصنيف:** عمل حر
- **التاريخ:** 2025-06-04
- **الوصف:** استشارة تقنية لشركة النور

---

### 💸 المصروفات المطلوبة:

#### مصروفات السكن والفواتير:
1. **إيجار الشقة** - 2,500.00 ر.س - الحساب الجاري - سكن - 2025-06-26
2. **فاتورة الكهرباء والماء** - 380.00 ر.س - الحساب الجاري - فواتير - 2025-06-22
3. **فاتورة الإنترنت والهاتف** - 120.00 ر.س - الحساب الجاري - فواتير - 2025-06-19

#### مصروفات الطعام:
4. **تسوق من السوبر ماركت** - 85.00 ر.س - المحفظة النقدية - طعام وشراب - 2025-06-28
5. **وجبة غداء في المطعم** - 45.00 ر.س - المحفظة النقدية - طعام وشراب - 2025-06-27
6. **مشتريات من البقالة** - 120.00 ر.س - المحفظة النقدية - طعام وشراب - 2025-06-25

#### مصروفات المواصلات:
7. **وقود السيارة** - 200.00 ر.س - المحفظة النقدية - مواصلات - 2025-06-23
8. **أجرة تاكسي** - 25.00 ر.س - المحفظة النقدية - مواصلات - 2025-06-20

#### مصروفات صحية:
9. **زيارة طبيب الأسنان** - 150.00 ر.س - الحساب الجاري - صحة - 2025-06-15
10. **أدوية من الصيدلية** - 65.00 ر.س - المحفظة النقدية - صحة - 2025-06-18

#### مصروفات التسوق:
11. **ملابس شتوية جديدة** - 320.00 ر.س - الحساب الجاري - تسوق - 2025-06-11
12. **أدوات منزلية** - 75.00 ر.س - المحفظة النقدية - تسوق - 2025-06-16

#### مصروفات الترفيه:
13. **تذاكر السينما مع الأصدقاء** - 90.00 ر.س - المحفظة النقدية - ترفيه - 2025-06-13
14. **ألعاب فيديو جديدة** - 55.00 ر.س - المحفظة النقدية - ترفيه - 2025-06-07

#### مصروفات التعليم:
15. **دورة تدريبية في البرمجة** - 450.00 ر.س - الحساب الجاري - تعليم - 2025-06-01

---

### 🔄 التحويلات المطلوبة:

#### 1. سحب نقدي للمصروفات اليومية
- **المبلغ:** 500.00 ر.س
- **من:** الحساب الجاري الرئيسي
- **إلى:** المحفظة النقدية
- **التاريخ:** 2025-06-24
- **الوصف:** سحب نقدي للمصروفات اليومية

#### 2. نقد إضافي للطوارئ
- **المبلغ:** 300.00 ر.س
- **من:** الحساب الجاري الرئيسي
- **إلى:** المحفظة النقدية
- **التاريخ:** 2025-06-17
- **الوصف:** نقد إضافي للطوارئ

#### 3. إيداع فائض النقد في البنك
- **المبلغ:** 150.00 ر.س
- **من:** المحفظة النقدية
- **إلى:** الحساب الجاري الرئيسي
- **التاريخ:** 2025-06-09
- **الوصف:** إيداع فائض النقد في البنك

#### 4. نقد لمصروفات نهاية الأسبوع
- **المبلغ:** 250.00 ر.س
- **من:** الحساب الجاري الرئيسي
- **إلى:** المحفظة النقدية
- **التاريخ:** 2025-06-21
- **الوصف:** نقد لمصروفات نهاية الأسبوع

---

## 📊 الملخص المتوقع بعد إضافة البيانات:

### 🏦 الأرصدة النهائية:
- **الحساب الجاري الرئيسي:** حوالي 21,105.00 ر.س
- **المحفظة النقدية:** حوالي 2,335.00 ر.س
- **إجمالي الأرصدة:** حوالي 23,440.00 ر.س

### 💰 إجمالي المعاملات:
- **إجمالي الواردات:** 14,600.00 ر.س
- **إجمالي المصروفات:** 4,680.00 ر.س
- **الصافي:** 9,920.00 ر.س
- **عدد التحويلات:** 4

---

## 🚀 خطوات الإضافة:

1. **شغل البرنامج:** `python main.py`
2. **سجل دخول:** admin / 123456
3. **اذهب لقسم الحسابات** وأضف الحسابين
4. **اذهب لقسم الواردات** وأضف جميع الواردات
5. **اذهب لقسم المصروفات** وأضف جميع المصروفات
6. **اذهب لقسم التحويلات** وأضف جميع التحويلات
7. **راجع لوحة التحكم** لرؤية الإحصائيات
8. **اذهب لقسم التقارير** لرؤية التقارير التفصيلية

---

## 🎉 النتيجة النهائية:

بعد إضافة هذه البيانات، ستحصل على:
- ✅ برنامج مليء بالبيانات العملية
- ✅ إحصائيات حقيقية في لوحة التحكم
- ✅ تقارير مفصلة ومفيدة
- ✅ تجربة مستخدم كاملة لجميع الأقسام
- ✅ أمثلة واقعية لكيفية استخدام البرنامج

البرنامج سيصبح جاهز للاستخدام العملي مع بيانات واقعية تُظهر جميع إمكانياته!
