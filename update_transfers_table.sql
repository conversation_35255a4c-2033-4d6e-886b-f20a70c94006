
-- تحديث جدول "transfers" لدعم التحويلات متعددة العملات بشكل كامل

-- الخطوة 1: إعادة تسمية الأعمدة القديمة (إذا لم يتم ذلك بالفعل)
ALTER TABLE transfers CHANGE COLUMN amount from_amount DECIMAL(15,2) NOT NULL;
ALTER TABLE transfers CHANGE COLUMN currency_id from_currency_id INT NOT NULL;

-- الخطوة 2: إضافة أعمدة جديدة (إذا لم تكن موجودة بالفعل)
ALTER TABLE transfers ADD COLUMN to_amount DECIMAL(15, 2) NOT NULL;
ALTER TABLE transfers ADD COLUMN to_currency_id INT NOT NULL;

-- الخطوة 3: تحديث البيانات الموجودة لضمان صلاحيتها
UPDATE transfers SET to_amount = from_amount, to_currency_id = from_currency_id WHERE to_currency_id = 0 OR to_currency_id IS NULL;
UPDATE transfers SET to_currency_id = 1 WHERE to_currency_id NOT IN (SELECT id FROM currencies);

-- الخطوة 4: إضافة المفتاح الخارجي (سيفشل إذا كان موجودًا بالفعل، وهو أمر مقبول)
ALTER TABLE transfers ADD CONSTRAINT fk_transfers_to_currency FOREIGN KEY (to_currency_id) REFERENCES currencies(id);

SELECT 'تم تحديث جدول transfers بنجاح.' AS message;
