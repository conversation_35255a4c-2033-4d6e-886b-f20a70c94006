#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 اختبار سريع لميزة إدارة قاعدة البيانات...")

try:
    # 1. اختبار الاتصال
    print("1. اختبار الاتصال...")
    from database.connection import db
    if db.is_connected() or db.connect():
        print("✅ الاتصال ناجح")
    else:
        print("❌ فشل الاتصال")
        exit(1)
    
    # 2. اختبار تسجيل الدخول كمدير
    print("2. اختبار تسجيل الدخول كمدير...")
    from utils.auth import auth_manager
    success, message = auth_manager.login("admin", "123456")
    if success and auth_manager.is_admin():
        print("✅ تسجيل الدخول كمدير ناجح")
    else:
        print(f"❌ فشل تسجيل الدخول أو المستخدم ليس مديراً: {message}")
        exit(1)
    
    # 3. اختبار استيراد وحدة النسخ الاحتياطي
    print("3. اختبار استيراد وحدة النسخ الاحتياطي...")
    try:
        from utils.backup import backup_manager
        print("✅ تم استيراد وحدة النسخ الاحتياطي")
    except Exception as e:
        print(f"❌ فشل استيراد وحدة النسخ الاحتياطي: {e}")
        exit(1)
    
    # 4. اختبار الإعدادات الجديدة
    print("4. اختبار الإعدادات الجديدة...")
    try:
        from config.settings import BACKUP_CONFIG, BACKUP_INTERVALS, DATABASE_CONNECTION_SETTINGS
        print("✅ إعدادات النسخ الاحتياطي متاحة")
        print(f"   - فترات النسخ: {list(BACKUP_INTERVALS.keys())}")
        print(f"   - مهلة الاختبار: {DATABASE_CONNECTION_SETTINGS.get('test_timeout')} ثانية")
    except Exception as e:
        print(f"❌ خطأ في الإعدادات: {e}")
        exit(1)
    
    # 5. فحص ملف main_window.py
    print("5. فحص ملف main_window.py...")
    with open('gui/main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود زر إدارة قاعدة البيانات
    if '"🗄️ إدارة قاعدة البيانات"' in content and 'show_database_management' in content:
        print("✅ زر إدارة قاعدة البيانات موجود")
    else:
        print("❌ زر إدارة قاعدة البيانات مفقود")
    
    # التحقق من وجود الدوال الجديدة
    required_functions = [
        'def show_database_management(',
        'def create_backup_management_section(',
        'def create_database_connection_section(',
        'def create_instant_backup(',
        'def test_database_connection('
    ]
    
    for func in required_functions:
        if func in content:
            print(f"✅ الدالة {func} موجودة")
        else:
            print(f"❌ الدالة {func} مفقودة")
    
    # 6. اختبار استيراد MainWindow
    print("6. اختبار استيراد MainWindow...")
    from gui.main_window import MainWindow
    print("✅ تم استيراد MainWindow بنجاح")
    
    # 7. اختبار وظائف النسخ الاحتياطي
    print("7. اختبار وظائف النسخ الاحتياطي...")
    try:
        # اختبار تحميل الإعدادات
        backup_manager.load_backup_settings()
        print("✅ تم تحميل إعدادات النسخ الاحتياطي")
        
        # اختبار الحصول على قائمة النسخ
        backup_files = backup_manager.get_backup_files()
        print(f"✅ تم العثور على {len(backup_files)} نسخة احتياطية")
        
        # اختبار اتصال قاعدة البيانات
        success, message = backup_manager.test_database_connection()
        if success:
            print("✅ اختبار اتصال قاعدة البيانات نجح")
        else:
            print(f"⚠️ اختبار اتصال قاعدة البيانات فشل: {message}")
            
    except Exception as e:
        print(f"⚠️ خطأ في وظائف النسخ الاحتياطي: {e}")
    
    auth_manager.logout()
    
    print("\n🎉 جميع الاختبارات الأساسية نجحت!")
    print("\n✨ الميزات الجديدة المضافة:")
    print("   🗄️ زر إدارة قاعدة البيانات (للمديرين فقط)")
    print("   📥 إنشاء نسخة احتياطية فورية")
    print("   📤 استعادة نسخة احتياطية")
    print("   ⚙️ إعدادات النسخ الاحتياطي التلقائي")
    print("   🔗 إعدادات الاتصال بقاعدة البيانات")
    print("   🔄 اختبار الاتصال")
    print("   💾 حفظ الإعدادات")
    
    print("\n🚀 للاستخدام:")
    print("1. شغل التطبيق: python main.py")
    print("2. سجل الدخول كمدير: admin / 123456")
    print("3. ستجد زر '🗄️ إدارة قاعدة البيانات' في الشريط الجانبي")
    print("4. انقر على الزر للوصول لجميع ميزات إدارة قاعدة البيانات")
    
    print("\n📋 الأقسام المتاحة:")
    print("   📥 إدارة النسخ الاحتياطية:")
    print("      - إنشاء نسخة احتياطية فورية")
    print("      - استعادة نسخة احتياطية")
    print("      - تفعيل/إلغاء النسخ التلقائي")
    print("      - اختيار فترة النسخ (كل ساعة، يومياً، أسبوعياً، شهرياً)")
    print("      - تحديد عدد النسخ المحفوظة")
    print("   🔗 إعدادات الاتصال بقاعدة البيانات:")
    print("      - عرض الإعدادات الحالية")
    print("      - تعديل إعدادات الاتصال")
    print("      - اختبار الاتصال")
    print("      - حفظ الإعدادات الجديدة")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
