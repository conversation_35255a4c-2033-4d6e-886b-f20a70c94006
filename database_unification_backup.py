#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة احتياطية شاملة قبل توحيد قاعدة البيانات
"""

import os
import shutil
import mysql.connector
import sqlite3
import json
from datetime import datetime
import zipfile

def create_full_backup():
    """إنشاء نسخة احتياطية شاملة"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"unification_backup_{timestamp}"
    
    print(f"🔄 إنشاء نسخة احتياطية شاملة في: {backup_dir}")
    
    try:
        # إنشاء مجلد النسخة الاحتياطية
        os.makedirs(backup_dir, exist_ok=True)
        
        # 1. نسخ ملف SQLite
        if os.path.exists("money_manager.db"):
            shutil.copy2("money_manager.db", f"{backup_dir}/money_manager.db")
            print("✅ تم نسخ ملف SQLite")
        
        # 2. نسخ ملفات JSON
        json_files = ["accounts.json", "users.json", "transactions.json"]
        for json_file in json_files:
            if os.path.exists(json_file):
                shutil.copy2(json_file, f"{backup_dir}/{json_file}")
                print(f"✅ تم نسخ {json_file}")
        
        # 3. نسخ مجلد النسخ الاحتياطية
        if os.path.exists("backups"):
            shutil.copytree("backups", f"{backup_dir}/backups")
            print("✅ تم نسخ مجلد النسخ الاحتياطية")
        
        # 4. تصدير بيانات MySQL
        export_mysql_data(backup_dir)
        
        # 5. نسخ ملفات التكوين
        config_files = [
            "config/database_settings.json",
            "config/settings.py",
            "database/schema.sql"
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                dest_dir = os.path.join(backup_dir, os.path.dirname(config_file))
                os.makedirs(dest_dir, exist_ok=True)
                shutil.copy2(config_file, os.path.join(backup_dir, config_file))
                print(f"✅ تم نسخ {config_file}")
        
        # 6. ضغط النسخة الاحتياطية
        zip_filename = f"{backup_dir}.zip"
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(backup_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, backup_dir)
                    zipf.write(file_path, arcname)
        
        # حذف المجلد المؤقت
        shutil.rmtree(backup_dir)
        
        print(f"✅ تم إنشاء النسخة الاحتياطية المضغوطة: {zip_filename}")
        return zip_filename
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def export_mysql_data(backup_dir):
    """تصدير بيانات MySQL"""
    try:
        # قراءة إعدادات الاتصال
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        
        # قائمة الجداول للتصدير
        tables = [
            'users', 'currencies', 'account_types', 'accounts', 
            'account_balances', 'income_categories', 'expense_categories',
            'transactions', 'transfers', 'activity_log'
        ]
        
        mysql_data = {}
        
        for table in tables:
            try:
                cursor.execute(f"SELECT * FROM {table}")
                data = cursor.fetchall()
                mysql_data[table] = data
                print(f"✅ تم تصدير جدول {table}: {len(data)} سجل")
            except Exception as e:
                print(f"⚠️ تعذر تصدير جدول {table}: {e}")
                mysql_data[table] = []
        
        # حفظ البيانات في ملف JSON
        with open(f"{backup_dir}/mysql_data.json", 'w', encoding='utf-8') as f:
            json.dump(mysql_data, f, ensure_ascii=False, indent=2, default=str)
        
        cursor.close()
        connection.close()
        
        print("✅ تم تصدير بيانات MySQL بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في تصدير بيانات MySQL: {e}")

if __name__ == "__main__":
    backup_file = create_full_backup()
    if backup_file:
        print(f"\n🎉 تم إنشاء النسخة الاحتياطية بنجاح: {backup_file}")
        print("💡 احتفظ بهذا الملف في مكان آمن قبل المتابعة")
    else:
        print("\n❌ فشل في إنشاء النسخة الاحتياطية")
