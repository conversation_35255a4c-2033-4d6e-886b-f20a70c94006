#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع لمشكلة اختفاء البيانات
"""

import os
import sys
import subprocess

def main():
    print("🚀 إصلاح سريع لمشكلة اختفاء البيانات")
    print("=" * 45)
    
    print("\n1. 🔄 إعادة تشغيل خادم MySQL...")
    try:
        # محاولة إيقاف وتشغيل MySQL
        os.system("net stop mysql > nul 2>&1")
        result = os.system("net start mysql > nul 2>&1")
        if result == 0:
            print("✅ تم تشغيل خادم MySQL بنجاح")
        else:
            print("⚠️ قد تحتاج لتشغيل MySQL يدوياً")
    except:
        print("⚠️ تحقق من تشغيل خادم MySQL يدوياً")
    
    print("\n2. 🔧 إعادة تهيئة قاعدة البيانات...")
    try:
        result = subprocess.run([sys.executable, "database/init_db.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
        else:
            print("⚠️ قد تحتاج لتهيئة قاعدة البيانات يدوياً")
            print("   تشغيل: python database/init_db.py")
    except Exception as e:
        print(f"⚠️ خطأ في التهيئة: {e}")
    
    print("\n3. 🚀 تشغيل التطبيق...")
    print("   تشغيل: python main.py")
    print("   تسجيل الدخول: admin / 123456")
    
    print("\n📋 إذا لم تظهر البيانات:")
    print("   1. اذهب إلى 'إدارة قاعدة البيانات'")
    print("   2. انقر على 'استعادة نسخة احتياطية'")
    print("   3. اختر أحدث نسخة متاحة")
    
    print("\n💡 للمساعدة الإضافية:")
    print("   - شغل: python fix_data_loss.py")
    print("   - راجع: DATA_RECOVERY_GUIDE.md")

if __name__ == "__main__":
    main()
