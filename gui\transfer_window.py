import customtkinter as ctk
from tkinter import messagebox
from decimal import Decimal, InvalidOperation
from datetime import datetime
from database.models import Account, Currency, Transfer
from utils.auth import auth_manager
from utils.exchange_calculator import exchange_calculator

class TransferWindow(ctk.CTkToplevel):
    def __init__(self, parent, on_close_callback):
        super().__init__(parent)
        self.on_close_callback = on_close_callback

        self.title("إضافة تحويل جديد")
        self.geometry("600x550")
        self.resizable(False, False)
        self.transient(parent)
        self.grab_set()

        self.load_data()
        self.create_widgets()
        self.update_from_currencies() # Initial population

    def load_data(self):
        """تحميل البيانات اللازمة من قاعدة البيانات"""
        self.accounts = Account.get_by_user(auth_manager.current_user['id'])
        self.all_currencies = Currency.get_all()
        self.account_map = {f"{acc['id']} - {acc['name']}": acc for acc in self.accounts}
        self.currency_map = {f"{c['id']} - {c['name']} ({c['symbol']})": c for c in self.all_currencies}

    def create_widgets(self):
        """إنشاء واجهة المستخدم للنافذة"""
        main_frame = ctk.CTkFrame(self, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # --- From Section ---
        from_frame = ctk.CTkFrame(main_frame)
        from_frame.pack(fill="x", pady=(0, 10))
        ctk.CTkLabel(from_frame, text="من", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)

        ctk.CTkLabel(from_frame, text="الحساب المصدر:").pack()
        self.from_account_combo = ctk.CTkComboBox(from_frame, values=list(self.account_map.keys()), command=self.update_from_currencies)
        self.from_account_combo.pack(fill="x", padx=10)

        ctk.CTkLabel(from_frame, text="العملة والمبلغ:").pack(pady=(10, 5))
        
        currency_amount_frame = ctk.CTkFrame(from_frame, fg_color="transparent")
        currency_amount_frame.pack(fill="x", padx=10)
        
        self.from_amount_entry = ctk.CTkEntry(currency_amount_frame, placeholder_text="المبلغ المرسل")
        self.from_amount_entry.pack(side="left", expand=True, padx=(0, 5))
        
        self.from_currency_combo = ctk.CTkComboBox(currency_amount_frame, values=[], width=180)
        self.from_currency_combo.pack(side="right")

        # --- To Section ---
        to_frame = ctk.CTkFrame(main_frame)
        to_frame.pack(fill="x", pady=10)
        ctk.CTkLabel(to_frame, text="إلى", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)

        ctk.CTkLabel(to_frame, text="الحساب الهدف:").pack(pady=(5, 5))
        self.to_account_combo = ctk.CTkComboBox(to_frame, values=list(self.account_map.keys()))
        self.to_account_combo.pack(fill="x", padx=10, pady=(0, 10))

        ctk.CTkLabel(to_frame, text="المبلغ المستقبل:").pack(pady=(5, 5))
        
        self.to_amount_entry = ctk.CTkEntry(to_frame, placeholder_text="المبلغ المستقبل")
        self.to_amount_entry.pack(fill="x", padx=10, pady=(0, 10))
        
        # Exchange Rate Section
        exchange_frame = ctk.CTkFrame(main_frame)
        exchange_frame.pack(fill="x", pady=10)
        ctk.CTkLabel(exchange_frame, text="حاسبة سعر الصرف", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
        
        buttons_frame = ctk.CTkFrame(exchange_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=10, pady=5)
        
        calculate_btn = ctk.CTkButton(buttons_frame, text="احسب المبلغ المحول تلقائياً", command=self.auto_calculate_amount)
        calculate_btn.pack(side="left", padx=(0, 5))
        
        self.exchange_rate_label = ctk.CTkLabel(buttons_frame, text="")
        self.exchange_rate_label.pack(side="right")
        
        # Description
        desc_frame = ctk.CTkFrame(main_frame)
        desc_frame.pack(fill="x", pady=10)
        ctk.CTkLabel(desc_frame, text="وصف التحويل:").pack(pady=(5, 5))
        self.description_entry = ctk.CTkEntry(desc_frame, placeholder_text="وصف اختياري للتحويل")
        self.description_entry.pack(fill="x", padx=10, pady=(0, 10))

        # --- Buttons --- #
        buttons_bottom_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_bottom_frame.pack(fill="x", pady=20)
        
        # إنشاء إطار للأزرار بجانب بعض
        buttons_row = ctk.CTkFrame(buttons_bottom_frame, fg_color="transparent")
        buttons_row.pack()
        
        save_button = ctk.CTkButton(buttons_row, text="✓ حفظ التحويل", command=self.save_transfer, width=150)
        save_button.pack(side="left", padx=(0, 10))
        
        cancel_button = ctk.CTkButton(buttons_row, text="✗ إلغاء", command=self.destroy, width=100, fg_color="gray")
        cancel_button.pack(side="right")

    def update_from_currencies(self, *args):
        """تحديث قائمة العملات المتاحة بناءً على الحساب المختار"""
        selected_key = self.from_account_combo.get()
        account = self.account_map.get(selected_key)
        if account:
            balances = account.get('balances', [])
            currency_options = [f"{b['currency_id']} - {b['code']} (الرصيد: {b['balance']})" for b in balances]
            self.from_currency_combo.configure(values=currency_options)
            self.from_currency_combo.set(currency_options[0] if currency_options else "")
    
    def auto_calculate_amount(self):
        """حساب المبلغ المحول تلقائياً بناءً على سعر الصرف"""
        try:
            if not self.from_amount_entry.get() or not self.from_currency_combo.get():
                messagebox.showwarning("تحذير", "يرجى إدخال المبلغ واختيار العملة أولاً", parent=self)
                return

            from_currency_id = int(self.from_currency_combo.get().split(' - ')[0])
            from_amount = Decimal(self.from_amount_entry.get())
            
            # للتبسيط، نفترض أن التحويل بنفس العملة (1:1)
            # يمكن تطوير هذا لاحقاً لاستخدام أسعار صرف حقيقية
            to_amount = from_amount
            
            self.to_amount_entry.delete(0, 'end')
            self.to_amount_entry.insert(0, str(to_amount))
            
            self.exchange_rate_label.configure(text=f"سعر الصرف: 1:1")
            
        except (ValueError, InvalidOperation):
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح", parent=self)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}", parent=self)

    def save_transfer(self):
        """حفظ التحويل الجديد"""
        try:
            from_account_id = int(self.from_account_combo.get().split(' - ')[0])
            from_currency_id = int(self.from_currency_combo.get().split(' - ')[0])
            from_amount = Decimal(self.from_amount_entry.get())

            to_account_id = int(self.to_account_combo.get().split(' - ')[0])
            to_currency_id = from_currency_id  # استخدام نفس عملة المرسل
            to_amount = Decimal(self.to_amount_entry.get())

            if from_account_id == to_account_id and from_currency_id == to_currency_id:
                messagebox.showerror("خطأ", "لا يمكن التحويل إلى نفس الحساب وبنفس العملة.", parent=self)
                return

            transfer_id = Transfer.create(
                user_id=auth_manager.current_user['id'],
                from_account_id=from_account_id,
                to_account_id=to_account_id,
                from_amount=from_amount,
                from_currency_id=from_currency_id,
                to_amount=to_amount,
                to_currency_id=to_currency_id,
                description="تحويل",
                transfer_date=datetime.now().date()
            )

            if transfer_id > 0:
                print(f"✅ TransferWindow: تم إنشاء التحويل بنجاح! ID: {transfer_id}")
                messagebox.showinfo("نجح", "تم إجراء التحويل بنجاح", parent=self)
                print("🔄 TransferWindow: استدعاء callback وإغلاق النافذة...")
                self.on_close_callback()
                self.destroy()
                print("✅ TransferWindow: تم إغلاق النافذة")
            else:
                print("❌ TransferWindow: فشل في إنشاء التحويل")
                messagebox.showerror("خطأ", "فشل في إجراء التحويل", parent=self)

        except (ValueError, InvalidOperation):
            messagebox.showerror("خطأ", "يرجى إدخال مبالغ صحيحة.", parent=self)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {e}", parent=self)
