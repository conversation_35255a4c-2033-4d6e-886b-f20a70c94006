#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة فشل حفظ الحساب الجديد
فحص شامل لجميع مكونات عملية إضافة الحساب
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from database.models import Account, Currency
from utils.auth import auth_manager

class SaveAccountDiagnostic:
    """أداة تشخيص مشاكل حفظ الحساب"""
    
    def __init__(self):
        self.issues_found = []
        self.test_data = {
            'name': 'حساب اختبار التشخيص',
            'currency_id': 1,
            'initial_balance': 1000.0,
            'description': 'حساب للاختبار والتشخيص'
        }
    
    def run_full_diagnostic(self):
        """تشغيل تشخيص شامل"""
        print("🔍 بدء تشخيص مشكلة فشل حفظ الحساب...")
        print("=" * 70)
        
        # 1. فحص المستخدم الحالي
        self.check_current_user()
        
        # 2. فحص قاعدة البيانات
        self.check_database_connection()
        
        # 3. فحص جدول الحسابات
        self.check_accounts_table()
        
        # 4. فحص جدول العملات
        self.check_currencies_table()
        
        # 5. فحص جدول أرصدة الحسابات
        self.check_account_balances_table()
        
        # 6. اختبار دالة Account.create()
        self.test_account_create_function()
        
        # 7. اختبار دالة update_balance_for_currency()
        self.test_update_balance_function()
        
        # 8. محاكاة عملية حفظ الحساب الكاملة
        self.simulate_full_save_process()
        
        # 9. عرض النتائج والتوصيات
        self.display_results()
    
    def check_current_user(self):
        """فحص المستخدم الحالي"""
        print("1️⃣ فحص المستخدم الحالي...")
        
        # محاولة تسجيل دخول تلقائي للاختبار
        if not auth_manager.current_user:
            users = db.execute_query("SELECT * FROM users LIMIT 1")
            if users:
                auth_manager.current_user = users[0]
                print(f"   ✅ تم تسجيل دخول تلقائي: {users[0].get('username')}")
            else:
                print("   ❌ لا يوجد مستخدمين في قاعدة البيانات")
                self.issues_found.append("لا يوجد مستخدمين")
                return
        
        user = auth_manager.current_user
        print(f"   ✅ المستخدم الحالي: {user.get('username')} (ID: {user.get('id')})")
    
    def check_database_connection(self):
        """فحص اتصال قاعدة البيانات"""
        print("\n2️⃣ فحص اتصال قاعدة البيانات...")
        
        try:
            result = db.execute_query("SELECT 1 as test")
            if result and result[0]['test'] == 1:
                print("   ✅ اتصال قاعدة البيانات يعمل بشكل صحيح")
            else:
                print("   ❌ مشكلة في اتصال قاعدة البيانات")
                self.issues_found.append("مشكلة في اتصال قاعدة البيانات")
        except Exception as e:
            print(f"   ❌ خطأ في اتصال قاعدة البيانات: {e}")
            self.issues_found.append(f"خطأ في الاتصال: {e}")
    
    def check_accounts_table(self):
        """فحص جدول الحسابات"""
        print("\n3️⃣ فحص جدول الحسابات...")
        
        try:
            # فحص وجود الجدول
            result = db.execute_query("SHOW TABLES LIKE 'accounts'")
            if not result:
                print("   ❌ جدول accounts غير موجود")
                self.issues_found.append("جدول accounts غير موجود")
                return
            
            print("   ✅ جدول accounts موجود")
            
            # فحص هيكل الجدول
            columns = db.execute_query("DESCRIBE accounts")
            required_columns = ['id', 'user_id', 'name', 'account_type_id', 'description', 'is_active']
            
            existing_columns = [col['Field'] for col in columns]
            print(f"   📋 أعمدة الجدول: {', '.join(existing_columns)}")
            
            for col in required_columns:
                if col in existing_columns:
                    print(f"   ✅ عمود {col} موجود")
                else:
                    print(f"   ❌ عمود {col} غير موجود")
                    self.issues_found.append(f"عمود {col} غير موجود في جدول accounts")
            
            # فحص البيانات الموجودة
            accounts_count = db.execute_query("SELECT COUNT(*) as count FROM accounts")[0]['count']
            print(f"   📊 عدد الحسابات الموجودة: {accounts_count}")
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص جدول الحسابات: {e}")
            self.issues_found.append(f"خطأ في فحص جدول accounts: {e}")
    
    def check_currencies_table(self):
        """فحص جدول العملات"""
        print("\n4️⃣ فحص جدول العملات...")
        
        try:
            # فحص وجود الجدول
            result = db.execute_query("SHOW TABLES LIKE 'currencies'")
            if not result:
                print("   ❌ جدول currencies غير موجود")
                self.issues_found.append("جدول currencies غير موجود")
                return
            
            print("   ✅ جدول currencies موجود")
            
            # فحص العملات المتاحة
            currencies = db.execute_query("SELECT * FROM currencies WHERE is_active = TRUE")
            print(f"   📊 عدد العملات النشطة: {len(currencies) if currencies else 0}")
            
            if currencies:
                for currency in currencies[:3]:  # عرض أول 3 عملات
                    print(f"   💱 {currency['name']} ({currency['code']}) - {currency['symbol']}")
            else:
                print("   ❌ لا توجد عملات نشطة")
                self.issues_found.append("لا توجد عملات نشطة")
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص جدول العملات: {e}")
            self.issues_found.append(f"خطأ في فحص جدول currencies: {e}")
    
    def check_account_balances_table(self):
        """فحص جدول أرصدة الحسابات"""
        print("\n5️⃣ فحص جدول أرصدة الحسابات...")
        
        try:
            # فحص وجود الجدول
            result = db.execute_query("SHOW TABLES LIKE 'account_balances'")
            if not result:
                print("   ❌ جدول account_balances غير موجود")
                self.issues_found.append("جدول account_balances غير موجود")
                return
            
            print("   ✅ جدول account_balances موجود")
            
            # فحص هيكل الجدول
            columns = db.execute_query("DESCRIBE account_balances")
            required_columns = ['id', 'account_id', 'currency_id', 'balance']
            
            existing_columns = [col['Field'] for col in columns]
            print(f"   📋 أعمدة الجدول: {', '.join(existing_columns)}")
            
            for col in required_columns:
                if col in existing_columns:
                    print(f"   ✅ عمود {col} موجود")
                else:
                    print(f"   ❌ عمود {col} غير موجود")
                    self.issues_found.append(f"عمود {col} غير موجود في جدول account_balances")
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص جدول أرصدة الحسابات: {e}")
            self.issues_found.append(f"خطأ في فحص جدول account_balances: {e}")
    
    def test_account_create_function(self):
        """اختبار دالة Account.create()"""
        print("\n6️⃣ اختبار دالة Account.create()...")
        
        if not auth_manager.current_user:
            print("   ⚠️ تخطي الاختبار - لا يوجد مستخدم مسجل دخول")
            return
        
        try:
            user_id = auth_manager.current_user['id']
            test_name = "حساب اختبار دالة Create"
            test_description = "اختبار دالة إنشاء الحساب"
            
            print(f"   🔍 اختبار إنشاء حساب: {test_name}")
            
            # محاولة إنشاء حساب
            result = Account.create(
                user_id=user_id,
                name=test_name,
                description=test_description
            )
            
            print(f"   📊 نتيجة Account.create(): {result}")
            
            if result and result > 0:
                print("   ✅ دالة Account.create() تعمل بشكل صحيح")
                
                # التحقق من وجود الحساب في قاعدة البيانات
                check_query = "SELECT * FROM accounts WHERE id = %s"
                created_account = db.execute_query(check_query, (result,))
                
                if created_account:
                    print("   ✅ الحساب تم إنشاؤه في قاعدة البيانات")
                    account = created_account[0]
                    print(f"      الاسم: {account['name']}")
                    print(f"      المستخدم: {account['user_id']}")
                    print(f"      نشط: {account['is_active']}")
                    
                    # حذف الحساب التجريبي
                    db.execute_update("DELETE FROM accounts WHERE id = %s", (result,))
                    print("   🗑️ تم حذف الحساب التجريبي")
                else:
                    print("   ❌ الحساب لم يتم إنشاؤه في قاعدة البيانات")
                    self.issues_found.append("دالة Account.create() لا تحفظ في قاعدة البيانات")
            else:
                print("   ❌ دالة Account.create() فشلت")
                self.issues_found.append("دالة Account.create() تُرجع قيمة خاطئة")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار دالة Account.create(): {e}")
            self.issues_found.append(f"خطأ في دالة Account.create(): {e}")
    
    def test_update_balance_function(self):
        """اختبار دالة update_balance_for_currency()"""
        print("\n7️⃣ اختبار دالة update_balance_for_currency()...")
        
        if not auth_manager.current_user:
            print("   ⚠️ تخطي الاختبار - لا يوجد مستخدم مسجل دخول")
            return
        
        try:
            # إنشاء حساب تجريبي أولاً
            user_id = auth_manager.current_user['id']
            test_account_id = Account.create(
                user_id=user_id,
                name="حساب اختبار الرصيد",
                description="اختبار دالة تحديث الرصيد"
            )
            
            if not test_account_id or test_account_id <= 0:
                print("   ❌ فشل في إنشاء حساب تجريبي للاختبار")
                self.issues_found.append("فشل في إنشاء حساب تجريبي")
                return
            
            print(f"   🔍 اختبار تحديث رصيد الحساب {test_account_id}")
            
            # اختبار إضافة رصيد
            currency_id = 1  # الريال السعودي
            test_amount = 500.0
            
            result = Account.update_balance_for_currency(test_account_id, currency_id, test_amount)
            print(f"   📊 نتيجة update_balance_for_currency(): {result}")
            
            if result and result > 0:
                print("   ✅ دالة update_balance_for_currency() تعمل بشكل صحيح")
                
                # التحقق من الرصيد في قاعدة البيانات
                balance_query = "SELECT * FROM account_balances WHERE account_id = %s AND currency_id = %s"
                balance_result = db.execute_query(balance_query, (test_account_id, currency_id))
                
                if balance_result:
                    balance = balance_result[0]['balance']
                    print(f"   💰 الرصيد المحفوظ: {balance}")
                    if float(balance) == test_amount:
                        print("   ✅ الرصيد محفوظ بشكل صحيح")
                    else:
                        print(f"   ❌ الرصيد غير صحيح. متوقع: {test_amount}, فعلي: {balance}")
                        self.issues_found.append("رصيد الحساب غير صحيح")
                else:
                    print("   ❌ لم يتم حفظ الرصيد في قاعدة البيانات")
                    self.issues_found.append("دالة update_balance_for_currency() لا تحفظ الرصيد")
            else:
                print("   ❌ دالة update_balance_for_currency() فشلت")
                self.issues_found.append("دالة update_balance_for_currency() تُرجع قيمة خاطئة")
            
            # حذف الحساب التجريبي
            db.execute_update("DELETE FROM account_balances WHERE account_id = %s", (test_account_id,))
            db.execute_update("DELETE FROM accounts WHERE id = %s", (test_account_id,))
            print("   🗑️ تم حذف الحساب التجريبي")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار دالة update_balance_for_currency(): {e}")
            self.issues_found.append(f"خطأ في دالة update_balance_for_currency(): {e}")
    
    def simulate_full_save_process(self):
        """محاكاة عملية حفظ الحساب الكاملة"""
        print("\n8️⃣ محاكاة عملية حفظ الحساب الكاملة...")
        
        if not auth_manager.current_user:
            print("   ⚠️ تخطي المحاكاة - لا يوجد مستخدم مسجل دخول")
            return
        
        try:
            print("   🎭 محاكاة خطوات المستخدم...")
            
            # البيانات التجريبية
            name = self.test_data['name']
            currency_id = self.test_data['currency_id']
            initial_balance = self.test_data['initial_balance']
            description = self.test_data['description']
            user_id = auth_manager.current_user['id']
            
            print(f"      الاسم: {name}")
            print(f"      العملة: {currency_id}")
            print(f"      المبلغ: {initial_balance}")
            print(f"      الوصف: {description}")
            
            # الخطوة 1: إنشاء الحساب
            print("   📝 الخطوة 1: إنشاء الحساب...")
            result = Account.create(
                user_id=user_id,
                name=name,
                description=description
            )
            
            if not result or result <= 0:
                print("   ❌ فشل في الخطوة 1: إنشاء الحساب")
                self.issues_found.append("فشل في إنشاء الحساب في المحاكاة الكاملة")
                return
            
            print(f"   ✅ تم إنشاء الحساب بنجاح (ID: {result})")
            
            # الخطوة 2: إضافة الرصيد الابتدائي
            if initial_balance > 0:
                print("   💰 الخطوة 2: إضافة الرصيد الابتدائي...")
                balance_result = Account.update_balance_for_currency(result, currency_id, initial_balance)
                
                if balance_result and balance_result > 0:
                    print(f"   ✅ تم إضافة الرصيد الابتدائي بنجاح")
                else:
                    print("   ❌ فشل في إضافة الرصيد الابتدائي")
                    self.issues_found.append("فشل في إضافة الرصيد الابتدائي")
            
            # التحقق النهائي
            print("   🔍 التحقق النهائي...")
            final_account = Account.get_by_id(result)
            
            if final_account:
                print("   ✅ الحساب موجود ويمكن استرجاعه")
                print(f"      الاسم: {final_account['name']}")
                print(f"      الوصف: {final_account.get('description', 'لا يوجد')}")
                
                balances = final_account.get('balances', [])
                if balances:
                    for balance in balances:
                        print(f"      الرصيد: {balance['balance']} {balance['symbol']}")
                else:
                    print("      الرصيد: 0 (لا توجد أرصدة)")
                
                print("   🎉 المحاكاة الكاملة نجحت!")
            else:
                print("   ❌ فشل في استرجاع الحساب المُنشأ")
                self.issues_found.append("فشل في استرجاع الحساب بعد الإنشاء")
            
            # حذف الحساب التجريبي
            db.execute_update("DELETE FROM account_balances WHERE account_id = %s", (result,))
            db.execute_update("DELETE FROM accounts WHERE id = %s", (result,))
            print("   🗑️ تم حذف الحساب التجريبي")
            
        except Exception as e:
            print(f"   ❌ خطأ في المحاكاة الكاملة: {e}")
            self.issues_found.append(f"خطأ في المحاكاة الكاملة: {e}")
    
    def display_results(self):
        """عرض النتائج والتوصيات"""
        print("\n" + "=" * 70)
        print("📊 نتائج التشخيص")
        print("=" * 70)
        
        if not self.issues_found:
            print("✅ لم يتم العثور على مشاكل! عملية حفظ الحساب يجب أن تعمل بشكل صحيح.")
            print("\n💡 إذا كانت المشكلة مستمرة، تحقق من:")
            print("   1. رسائل الخطأ في وحدة التحكم")
            print("   2. صحة البيانات المدخلة")
            print("   3. صلاحيات قاعدة البيانات")
        else:
            print(f"❌ تم العثور على {len(self.issues_found)} مشكلة:")
            for i, issue in enumerate(self.issues_found, 1):
                print(f"   {i}. {issue}")
            
            print(f"\n🔧 خطوات الإصلاح المقترحة:")
            print("   1. إصلاح المشاكل المذكورة أعلاه")
            print("   2. إعادة تشغيل التطبيق")
            print("   3. اختبار إضافة حساب جديد")
            print("   4. مراجعة سجلات الأخطاء")

def main():
    """الدالة الرئيسية"""
    try:
        diagnostic = SaveAccountDiagnostic()
        diagnostic.run_full_diagnostic()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التشخيص: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
