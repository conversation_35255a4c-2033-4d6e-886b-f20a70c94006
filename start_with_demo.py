#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل البرنامج مع إرشادات إضافة البيانات العملية
"""

import sys
import os
import subprocess
import time

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_welcome():
    """طباعة رسالة الترحيب"""
    print("="*70)
    print("🏦 مرحباً بك في برنامج خزانة الدائرة المالية")
    print("="*70)
    print()
    print("📋 بيانات تسجيل الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: 123456")
    print()
    print("🎯 لتجربة البرنامج بشكل عملي، يُنصح بإضافة البيانات التالية:")
    print()

def print_quick_data_guide():
    """طباعة دليل سريع للبيانات"""
    print("🏦 الحسابات المطلوبة:")
    print("   1. الحساب الجاري الرئيسي (حساب بنكي) - 15,000 ر.س")
    print("   2. المحفظة النقدية (صندوق نقدي) - 2,000 ر.س")
    print()
    
    print("💰 أمثلة على الواردات:")
    print("   • راتب شهر ديسمبر: 8,500 ر.س")
    print("   • مشروع تطوير موقع: 2,200 ر.س")
    print("   • مكافأة نهاية السنة: 1,500 ر.س")
    print("   • أرباح استثمار: 750 ر.س")
    print()
    
    print("💸 أمثلة على المصروفات:")
    print("   • إيجار الشقة: 2,500 ر.س")
    print("   • فاتورة الكهرباء: 380 ر.س")
    print("   • تسوق طعام: 250 ر.س")
    print("   • وقود السيارة: 200 ر.س")
    print()
    
    print("🔄 أمثلة على التحويلات:")
    print("   • سحب نقدي: 500 ر.س (من البنك للمحفظة)")
    print("   • إيداع فائض: 150 ر.س (من المحفظة للبنك)")
    print()

def start_mysql():
    """محاولة تشغيل MySQL"""
    print("🔄 محاولة تشغيل MySQL...")
    try:
        # محاولة تشغيل MySQL عبر XAMPP
        mysql_paths = [
            "C:\\xampp\\mysql\\bin\\mysqld.exe",
            "C:\\xampp\\mysql_start.bat",
            "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqld.exe"
        ]
        
        for path in mysql_paths:
            if os.path.exists(path):
                print(f"✅ تم العثور على MySQL في: {path}")
                if "mysqld.exe" in path:
                    subprocess.Popen([path, "--defaults-file=C:\\xampp\\mysql\\bin\\my.ini", "--standalone"])
                    time.sleep(3)
                    print("✅ تم تشغيل MySQL")
                    return True
                break
        
        print("⚠️ لم يتم العثور على MySQL. يرجى تشغيله يدوياً.")
        return False
        
    except Exception as e:
        print(f"⚠️ خطأ في تشغيل MySQL: {e}")
        return False

def start_application():
    """تشغيل البرنامج"""
    print("🚀 تشغيل البرنامج...")
    try:
        from gui.login_window import LoginWindow
        app = LoginWindow()
        app.run()
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_welcome()
    print_quick_data_guide()
    
    print("📖 للحصول على دليل مفصل للبيانات، راجع ملف: demo_data_guide.md")
    print()
    
    # السؤال عن تشغيل MySQL
    mysql_choice = input("🔄 هل تريد محاولة تشغيل MySQL تلقائياً؟ (y/n): ").lower().strip()
    if mysql_choice in ['y', 'yes', 'نعم', '']:
        start_mysql()
    
    print()
    print("🎯 نصائح للاستخدام:")
    print("   1. ابدأ بإضافة الحسابين المذكورين أعلاه")
    print("   2. أضف بعض الواردات والمصروفات")
    print("   3. جرب التحويلات بين الحسابات")
    print("   4. راجع التقارير لرؤية الإحصائيات")
    print()
    
    # السؤال عن تشغيل البرنامج
    app_choice = input("🚀 هل تريد تشغيل البرنامج الآن؟ (y/n): ").lower().strip()
    if app_choice in ['y', 'yes', 'نعم', '']:
        print()
        if start_application():
            print("✅ تم إغلاق البرنامج بنجاح")
        else:
            print("❌ فشل في تشغيل البرنامج")
    
    print()
    print("📚 ملفات مفيدة:")
    print("   • تعليمات_التشغيل.md - تعليمات شاملة")
    print("   • demo_data_guide.md - دليل البيانات العملية")
    print("   • README_FEATURES.md - دليل الميزات")
    print("   • SYSTEM_INFO.md - معلومات النظام")
    print()
    print("🎉 شكراً لاستخدام برنامج خزانة الدائرة المالية!")

if __name__ == "__main__":
    main()
    input("\n🔄 اضغط Enter للخروج...")
