#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إزالة الاعتماد على نوع الحساب من جدول الحسابات
تعديل هيكل قاعدة البيانات لتبسيط إنشاء الحسابات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db

def modify_accounts_table():
    """تعديل جدول الحسابات لإزالة الاعتماد على نوع الحساب"""
    print("🔧 تعديل جدول الحسابات...")
    
    try:
        # التحقق من وجود عمود account_type_id
        check_column_query = """
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'money_manager' 
        AND TABLE_NAME = 'accounts' 
        AND COLUMN_NAME = 'account_type_id'
        """
        
        column_exists = db.execute_query(check_column_query)
        
        if column_exists:
            print("   📋 إزالة قيد المفتاح الخارجي لنوع الحساب...")
            
            # إزالة قيد المفتاح الخارجي أولاً
            try:
                # الحصول على اسم قيد المفتاح الخارجي
                fk_query = """
                SELECT CONSTRAINT_NAME 
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = 'money_manager' 
                AND TABLE_NAME = 'accounts' 
                AND COLUMN_NAME = 'account_type_id' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
                """
                
                fk_result = db.execute_query(fk_query)
                if fk_result:
                    constraint_name = fk_result[0]['CONSTRAINT_NAME']
                    drop_fk_query = f"ALTER TABLE accounts DROP FOREIGN KEY {constraint_name}"
                    db.execute_update(drop_fk_query)
                    print(f"   ✅ تم إزالة قيد المفتاح الخارجي: {constraint_name}")
            except Exception as e:
                print(f"   ⚠️ تحذير: لم يتم العثور على قيد المفتاح الخارجي أو تم إزالته مسبقاً: {e}")
            
            # تعديل العمود ليصبح اختيارياً مع قيمة افتراضية
            print("   📋 تعديل عمود account_type_id ليصبح اختيارياً...")
            modify_column_query = """
            ALTER TABLE accounts 
            MODIFY COLUMN account_type_id INT DEFAULT 1
            """
            db.execute_update(modify_column_query)
            print("   ✅ تم تعديل عمود account_type_id")
            
            # تحديث الحسابات الموجودة التي لا تحتوي على نوع حساب
            print("   📋 تحديث الحسابات الموجودة...")
            update_query = """
            UPDATE accounts 
            SET account_type_id = 1 
            WHERE account_type_id IS NULL
            """
            db.execute_update(update_query)
            print("   ✅ تم تحديث الحسابات الموجودة")
        
        print("✅ تم تعديل جدول الحسابات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تعديل جدول الحسابات: {e}")
        return False

def create_default_account_type():
    """إنشاء نوع حساب افتراضي"""
    print("\n📋 إنشاء نوع حساب افتراضي...")
    
    try:
        # التحقق من وجود نوع حساب افتراضي
        check_query = "SELECT id FROM account_types WHERE id = 1"
        existing = db.execute_query(check_query)
        
        if not existing:
            # إنشاء نوع حساب افتراضي
            insert_query = """
            INSERT INTO account_types (id, name, description) 
            VALUES (1, 'حساب عام', 'نوع حساب افتراضي')
            """
            db.execute_insert(insert_query)
            print("   ✅ تم إنشاء نوع الحساب الافتراضي")
        else:
            print("   ✅ نوع الحساب الافتراضي موجود مسبقاً")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء نوع الحساب الافتراضي: {e}")
        return False

def test_accounts_functionality():
    """اختبار وظائف الحسابات بعد التعديل"""
    print("\n🧪 اختبار وظائف الحسابات...")
    
    try:
        # اختبار الحصول على الحسابات
        accounts = db.execute_query("SELECT * FROM accounts")
        print(f"   📊 عدد الحسابات: {len(accounts) if accounts else 0}")
        
        # اختبار إنشاء حساب جديد (محاكاة)
        print("   🔍 اختبار إمكانية إنشاء حساب جديد...")
        
        # محاكاة البيانات
        test_data = {
            'user_id': 1,
            'name': 'حساب اختبار',
            'currency_id': 1,
            'description': 'حساب للاختبار'
        }
        
        # اختبار الاستعلام (بدون تنفيذ فعلي)
        test_query = """
        SELECT 1 FROM accounts 
        WHERE user_id = %s AND name = %s
        """
        
        # التحقق من أن الاستعلام يعمل
        result = db.execute_query(test_query, (test_data['user_id'], test_data['name']))
        print("   ✅ اختبار الاستعلام نجح")
        
        print("✅ جميع الاختبارات نجحت")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إزالة الاعتماد على نوع الحساب")
    print("=" * 50)
    
    # تعديل جدول الحسابات
    if not modify_accounts_table():
        return
    
    # إنشاء نوع حساب افتراضي
    if not create_default_account_type():
        return
    
    # اختبار الوظائف
    if not test_accounts_functionality():
        return
    
    print("\n" + "=" * 50)
    print("🎉 تم تعديل قاعدة البيانات بنجاح!")
    print("✅ يمكن الآن إنشاء حسابات بدون تحديد نوع الحساب")

if __name__ == "__main__":
    main()
