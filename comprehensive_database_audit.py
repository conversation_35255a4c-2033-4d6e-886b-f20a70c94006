#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص شامل لقواعد البيانات في مشروع إدارة الأموال
"""

import os
import sys
import json
import mysql.connector
from mysql.connector import Error
import sqlite3
from datetime import datetime
from decimal import Decimal
import subprocess

class DatabaseAuditor:
    """فئة فحص قاعدة البيانات"""
    
    def __init__(self):
        self.backup_dir = "cleanup_backup_20250720_021829"
        self.connection = None
        self.cursor = None
        self.config = self.load_config()
        self.audit_report = {
            'timestamp': datetime.now().isoformat(),
            'mysql_status': {},
            'database_structure': {},
            'data_integrity': {},
            'backup_analysis': {},
            'recommendations': []
        }
        
    def load_config(self):
        """تحميل إعدادات قاعدة البيانات"""
        config_file = "config/database_settings.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return {
                "host": "localhost",
                "port": 3306,
                "user": "root",
                "password": "mohdam",
                "database": "money_manager"
            }
    
    def check_mysql_service(self):
        """فحص خدمة MySQL"""
        print("=" * 50)
        print("1. فحص خدمة MySQL")
        print("=" * 50)
        
        service_status = {
            'service_installed': False,
            'service_running': False,
            'connection_successful': False,
            'database_exists': False,
            'error_message': None
        }
        
        try:
            # فحص خدمة MySQL
            result = subprocess.run(['sc', 'query', 'mysql'], 
                                  capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                service_status['service_installed'] = True
                print("✓ خدمة MySQL مثبتة")
                
                if "RUNNING" in result.stdout:
                    service_status['service_running'] = True
                    print("✓ خدمة MySQL تعمل")
                else:
                    print("✗ خدمة MySQL متوقفة")
                    service_status['error_message'] = "خدمة MySQL متوقفة"
            else:
                print("✗ خدمة MySQL غير مثبتة")
                service_status['error_message'] = "خدمة MySQL غير مثبتة"
                
        except Exception as e:
            print(f"✗ خطأ في فحص خدمة MySQL: {e}")
            service_status['error_message'] = str(e)
        
        # اختبار الاتصال
        if service_status['service_running']:
            connection_result = self.test_database_connection()
            service_status.update(connection_result)
        
        self.audit_report['mysql_status'] = service_status
        return service_status
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        print("\nاختبار الاتصال بقاعدة البيانات...")
        
        connection_info = {
            'connection_successful': False,
            'database_exists': False,
            'tables_count': 0,
            'error_message': None
        }
        
        try:
            # محاولة الاتصال بدون قاعدة بيانات أولاً
            temp_config = self.config.copy()
            db_name = temp_config.pop('database')
            
            connection = mysql.connector.connect(**temp_config)
            
            if connection.is_connected():
                connection_info['connection_successful'] = True
                print("✓ تم الاتصال بخادم MySQL بنجاح")
                
                cursor = connection.cursor()
                
                # فحص وجود قاعدة البيانات
                cursor.execute("SHOW DATABASES")
                databases = [db[0] for db in cursor.fetchall()]
                
                if db_name in databases:
                    connection_info['database_exists'] = True
                    print(f"✓ قاعدة البيانات '{db_name}' موجودة")
                    
                    # الاتصال بقاعدة البيانات وفحص الجداول
                    cursor.execute(f"USE {db_name}")
                    cursor.execute("SHOW TABLES")
                    tables = cursor.fetchall()
                    connection_info['tables_count'] = len(tables)
                    
                    print(f"✓ عدد الجداول: {len(tables)}")
                    
                else:
                    print(f"✗ قاعدة البيانات '{db_name}' غير موجودة")
                    connection_info['error_message'] = f"قاعدة البيانات '{db_name}' غير موجودة"
                
                cursor.close()
                connection.close()
                
        except Error as e:
            print(f"✗ خطأ في الاتصال بـ MySQL: {e}")
            connection_info['error_message'] = str(e)
        except Exception as e:
            print(f"✗ خطأ غير متوقع: {e}")
            connection_info['error_message'] = str(e)
        
        return connection_info
    
    def connect_to_database(self):
        """الاتصال بقاعدة البيانات للفحص التفصيلي"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            self.cursor = self.connection.cursor(dictionary=True)
            return True
        except Exception as e:
            print(f"خطأ في الاتصال: {e}")
            return False
    
    def analyze_database_structure(self):
        """تحليل بنية قاعدة البيانات"""
        print("\n" + "=" * 50)
        print("2. تحليل بنية قاعدة البيانات")
        print("=" * 50)
        
        if not self.connect_to_database():
            print("✗ فشل في الاتصال بقاعدة البيانات")
            return False
        
        structure_info = {}
        
        try:
            # الحصول على قائمة الجداول
            self.cursor.execute("SHOW TABLES")
            tables = [table[f'Tables_in_{self.config["database"]}'] for table in self.cursor.fetchall()]
            
            print(f"الجداول الموجودة ({len(tables)}):")
            
            for table in tables:
                print(f"\n--- جدول {table} ---")
                
                # عدد السجلات
                self.cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                count = self.cursor.fetchone()['count']
                
                # بنية الجدول
                self.cursor.execute(f"DESCRIBE {table}")
                columns = self.cursor.fetchall()
                
                # معلومات الجدول
                self.cursor.execute(f"""
                    SELECT 
                        table_rows,
                        data_length,
                        index_length,
                        (data_length + index_length) as total_size
                    FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name = %s
                """, (self.config['database'], table))
                
                table_info = self.cursor.fetchone()
                
                structure_info[table] = {
                    'record_count': count,
                    'columns': len(columns),
                    'column_details': columns,
                    'size_bytes': table_info['total_size'] if table_info['total_size'] else 0
                }
                
                print(f"  السجلات: {count}")
                print(f"  الأعمدة: {len(columns)}")
                print(f"  الحجم: {self.format_bytes(table_info['total_size'] if table_info['total_size'] else 0)}")
            
            self.audit_report['database_structure'] = structure_info
            return True
            
        except Exception as e:
            print(f"✗ خطأ في تحليل بنية قاعدة البيانات: {e}")
            return False
    
    def check_data_integrity(self):
        """فحص سلامة البيانات"""
        print("\n" + "=" * 50)
        print("3. فحص سلامة البيانات")
        print("=" * 50)
        
        integrity_issues = []
        
        try:
            # فحص المفاتيح الخارجية
            print("فحص المفاتيح الخارجية...")
            
            # فحص الحسابات اليتيمة (بدون مستخدمين)
            self.cursor.execute("""
                SELECT COUNT(*) as count 
                FROM accounts a 
                LEFT JOIN users u ON a.user_id = u.id 
                WHERE u.id IS NULL
            """)
            orphaned_accounts = self.cursor.fetchone()['count']
            
            if orphaned_accounts > 0:
                integrity_issues.append(f"توجد {orphaned_accounts} حسابات يتيمة بدون مستخدمين")
                print(f"✗ توجد {orphaned_accounts} حسابات يتيمة")
            else:
                print("✓ جميع الحسابات مرتبطة بمستخدمين")
            
            # فحص المعاملات اليتيمة (بدون حسابات)
            self.cursor.execute("""
                SELECT COUNT(*) as count 
                FROM transactions t 
                LEFT JOIN accounts a ON t.account_id = a.id 
                WHERE a.id IS NULL
            """)
            orphaned_transactions = self.cursor.fetchone()['count']
            
            if orphaned_transactions > 0:
                integrity_issues.append(f"توجد {orphaned_transactions} معاملات يتيمة بدون حسابات")
                print(f"✗ توجد {orphaned_transactions} معاملات يتيمة")
            else:
                print("✓ جميع المعاملات مرتبطة بحسابات")
            
            # فحص أرصدة الحسابات اليتيمة
            self.cursor.execute("""
                SELECT COUNT(*) as count 
                FROM account_balances ab 
                LEFT JOIN accounts a ON ab.account_id = a.id 
                WHERE a.id IS NULL
            """)
            orphaned_balances = self.cursor.fetchone()['count']
            
            if orphaned_balances > 0:
                integrity_issues.append(f"توجد {orphaned_balances} أرصدة يتيمة بدون حسابات")
                print(f"✗ توجد {orphaned_balances} أرصدة يتيمة")
            else:
                print("✓ جميع الأرصدة مرتبطة بحسابات")
            
            # فحص تطابق الأرصدة مع المعاملات
            print("\nفحص تطابق الأرصدة مع المعاملات...")
            self.cursor.execute("""
                SELECT 
                    a.id,
                    a.name,
                    ab.currency_id,
                    ab.balance as recorded_balance,
                    COALESCE(
                        SUM(CASE 
                            WHEN t.type = 'income' THEN t.amount 
                            WHEN t.type = 'expense' THEN -t.amount 
                            ELSE 0 
                        END), 0
                    ) as calculated_balance
                FROM accounts a
                LEFT JOIN account_balances ab ON a.id = ab.account_id
                LEFT JOIN transactions t ON a.id = t.account_id AND t.currency_id = ab.currency_id
                WHERE ab.currency_id IS NOT NULL
                GROUP BY a.id, a.name, ab.currency_id, ab.balance
                HAVING ABS(recorded_balance - calculated_balance) > 0.01
            """)
            
            balance_mismatches = self.cursor.fetchall()
            
            if balance_mismatches:
                print(f"✗ توجد {len(balance_mismatches)} حسابات بأرصدة غير متطابقة:")
                for mismatch in balance_mismatches:
                    print(f"  - {mismatch['name']}: مسجل {mismatch['recorded_balance']}, محسوب {mismatch['calculated_balance']}")
                    integrity_issues.append(f"رصيد غير متطابق في حساب {mismatch['name']}")
            else:
                print("✓ جميع أرصدة الحسابات متطابقة مع المعاملات")
            
            # فحص القيم الفارغة في الحقول المطلوبة
            print("\nفحص القيم الفارغة...")
            
            required_fields = {
                'users': ['username', 'password_hash', 'full_name'],
                'accounts': ['user_id', 'name'],
                'transactions': ['user_id', 'account_id', 'type', 'amount', 'currency_id'],
                'account_balances': ['account_id', 'currency_id']
            }
            
            for table, fields in required_fields.items():
                for field in fields:
                    self.cursor.execute(f"SELECT COUNT(*) as count FROM {table} WHERE {field} IS NULL OR {field} = ''")
                    null_count = self.cursor.fetchone()['count']
                    
                    if null_count > 0:
                        integrity_issues.append(f"توجد {null_count} قيم فارغة في {table}.{field}")
                        print(f"✗ {null_count} قيم فارغة في {table}.{field}")
            
            self.audit_report['data_integrity'] = {
                'issues_found': len(integrity_issues),
                'issues': integrity_issues,
                'orphaned_accounts': orphaned_accounts,
                'orphaned_transactions': orphaned_transactions,
                'orphaned_balances': orphaned_balances,
                'balance_mismatches': len(balance_mismatches)
            }
            
            return True
            
        except Exception as e:
            print(f"✗ خطأ في فحص سلامة البيانات: {e}")
            return False
    
    def analyze_backup_data(self):
        """تحليل البيانات المحفوظة"""
        print("\n" + "=" * 50)
        print("4. تحليل البيانات المحفوظة")
        print("=" * 50)
        
        backup_analysis = {
            'backup_exists': False,
            'files_found': [],
            'data_summary': {},
            'comparison_with_current': {}
        }
        
        if not os.path.exists(self.backup_dir):
            print(f"✗ مجلد النسخ الاحتياطية غير موجود: {self.backup_dir}")
            self.audit_report['backup_analysis'] = backup_analysis
            return False
        
        backup_analysis['backup_exists'] = True
        print(f"✓ مجلد النسخ الاحتياطية موجود: {self.backup_dir}")
        
        # فحص الملفات الموجودة
        backup_files = ['users.json', 'accounts.json', 'transactions.json', 'money_manager.db']
        
        for file in backup_files:
            file_path = os.path.join(self.backup_dir, file)
            if os.path.exists(file_path):
                backup_analysis['files_found'].append(file)
                print(f"✓ ملف موجود: {file}")
                
                # تحليل محتوى ملفات JSON
                if file.endswith('.json'):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        if isinstance(data, list):
                            backup_analysis['data_summary'][file] = len(data)
                            print(f"  - عدد السجلات: {len(data)}")
                        elif isinstance(data, dict):
                            backup_analysis['data_summary'][file] = len(data)
                            print(f"  - عدد العناصر: {len(data)}")
                            
                    except Exception as e:
                        print(f"  - خطأ في قراءة الملف: {e}")
                
                # تحليل قاعدة بيانات SQLite
                elif file.endswith('.db'):
                    try:
                        conn = sqlite3.connect(file_path)
                        cursor = conn.cursor()
                        
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                        tables = [table[0] for table in cursor.fetchall()]
                        
                        sqlite_data = {}
                        for table in tables:
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            sqlite_data[table] = count
                        
                        backup_analysis['data_summary'][file] = sqlite_data
                        print(f"  - جداول SQLite: {sqlite_data}")
                        
                        conn.close()
                        
                    except Exception as e:
                        print(f"  - خطأ في قراءة قاعدة SQLite: {e}")
            else:
                print(f"✗ ملف مفقود: {file}")
        
        self.audit_report['backup_analysis'] = backup_analysis
        return True
    
    def format_bytes(self, bytes_value):
        """تنسيق حجم البيانات"""
        if bytes_value == 0:
            return "0 B"
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        
        return f"{bytes_value:.1f} TB"

    def generate_recommendations(self):
        """إنشاء التوصيات بناءً على نتائج الفحص"""
        print("\n" + "=" * 50)
        print("5. التوصيات والحلول")
        print("=" * 50)

        recommendations = []

        # توصيات خدمة MySQL
        mysql_status = self.audit_report['mysql_status']
        if not mysql_status.get('service_running', False):
            recommendations.append({
                'priority': 'عالية',
                'category': 'خدمة MySQL',
                'issue': 'خدمة MySQL لا تعمل',
                'solution': 'تشغيل خدمة MySQL باستخدام الأمر: sc start mysql'
            })

        if not mysql_status.get('database_exists', False):
            recommendations.append({
                'priority': 'عالية',
                'category': 'قاعدة البيانات',
                'issue': 'قاعدة البيانات غير موجودة',
                'solution': 'إنشاء قاعدة البيانات وتشغيل سكريپت comprehensive_database_fix.py'
            })

        # توصيات سلامة البيانات
        integrity = self.audit_report.get('data_integrity', {})
        if integrity.get('issues_found', 0) > 0:
            for issue in integrity.get('issues', []):
                recommendations.append({
                    'priority': 'متوسطة',
                    'category': 'سلامة البيانات',
                    'issue': issue,
                    'solution': 'تنظيف البيانات وإصلاح المراجع'
                })

        # توصيات النسخ الاحتياطية
        backup = self.audit_report.get('backup_analysis', {})
        if backup.get('backup_exists', False) and len(backup.get('files_found', [])) > 0:
            recommendations.append({
                'priority': 'متوسطة',
                'category': 'استعادة البيانات',
                'issue': 'توجد نسخ احتياطية يمكن استعادتها',
                'solution': 'تشغيل سكريپت comprehensive_database_fix.py لاستعادة البيانات'
            })

        # طباعة التوصيات
        for i, rec in enumerate(recommendations, 1):
            print(f"\n{i}. {rec['category']} - أولوية {rec['priority']}")
            print(f"   المشكلة: {rec['issue']}")
            print(f"   الحل: {rec['solution']}")

        self.audit_report['recommendations'] = recommendations
        return recommendations

    def save_audit_report(self):
        """حفظ تقرير الفحص"""
        report_file = f"database_audit_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.audit_report, f, indent=2, ensure_ascii=False, default=str)

            print(f"\n✓ تم حفظ تقرير الفحص في: {report_file}")
            return report_file

        except Exception as e:
            print(f"✗ خطأ في حفظ التقرير: {e}")
            return None

    def print_summary(self):
        """طباعة ملخص الفحص"""
        print("\n" + "=" * 60)
        print("ملخص فحص قاعدة البيانات")
        print("=" * 60)

        # حالة MySQL
        mysql_status = self.audit_report['mysql_status']
        print(f"خدمة MySQL: {'✓ تعمل' if mysql_status.get('service_running') else '✗ متوقفة'}")
        print(f"الاتصال: {'✓ نجح' if mysql_status.get('connection_successful') else '✗ فشل'}")
        print(f"قاعدة البيانات: {'✓ موجودة' if mysql_status.get('database_exists') else '✗ غير موجودة'}")

        # بنية قاعدة البيانات
        structure = self.audit_report.get('database_structure', {})
        if structure:
            total_records = sum(table.get('record_count', 0) for table in structure.values())
            print(f"عدد الجداول: {len(structure)}")
            print(f"إجمالي السجلات: {total_records}")

        # سلامة البيانات
        integrity = self.audit_report.get('data_integrity', {})
        issues_count = integrity.get('issues_found', 0)
        print(f"مشاكل سلامة البيانات: {issues_count}")

        # النسخ الاحتياطية
        backup = self.audit_report.get('backup_analysis', {})
        backup_files = len(backup.get('files_found', []))
        print(f"ملفات النسخ الاحتياطية: {backup_files}")

        # التوصيات
        recommendations = self.audit_report.get('recommendations', [])
        high_priority = len([r for r in recommendations if r['priority'] == 'عالية'])
        print(f"توصيات عالية الأولوية: {high_priority}")

        print("\n" + "=" * 60)

        if mysql_status.get('service_running') and mysql_status.get('database_exists') and issues_count == 0:
            print("✓ قاعدة البيانات تعمل بشكل طبيعي")
        else:
            print("✗ توجد مشاكل تحتاج إلى إصلاح")
            print("يرجى مراجعة التوصيات أعلاه")

    def close_connection(self):
        """إغلاق الاتصال"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
        except Exception:
            pass

def main():
    """الدالة الرئيسية للفحص"""
    print("فحص شامل لقواعد البيانات - تطبيق إدارة الأموال")
    print("=" * 60)
    print(f"التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    auditor = DatabaseAuditor()

    try:
        # 1. فحص خدمة MySQL
        auditor.check_mysql_service()

        # 2. تحليل بنية قاعدة البيانات
        if auditor.audit_report['mysql_status'].get('database_exists'):
            auditor.analyze_database_structure()

            # 3. فحص سلامة البيانات
            auditor.check_data_integrity()

        # 4. تحليل النسخ الاحتياطية
        auditor.analyze_backup_data()

        # 5. إنشاء التوصيات
        auditor.generate_recommendations()

        # 6. طباعة الملخص
        auditor.print_summary()

        # 7. حفظ التقرير
        auditor.save_audit_report()

        return True

    except Exception as e:
        print(f"خطأ عام في الفحص: {e}")
        return False
    finally:
        auditor.close_connection()

if __name__ == "__main__":
    main()
