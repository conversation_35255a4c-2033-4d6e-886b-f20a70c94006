-- إن<PERSON><PERSON><PERSON> قاعدة بيانات مدير الأموال
CREATE DATABASE IF NOT EXISTS money_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE money_manager;

-- جدول المستخدمين (محدث لدعم إدارة المستخدمين المتعددين)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    profile_image VARCHAR(255) NULL,
    created_by INT NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول العملات
CREATE TABLE IF NOT EXISTS currencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(3) UNIQUE NOT NULL,
    name VARCHAR(50) NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول أنواع الحسابات
CREATE TABLE IF NOT EXISTS account_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الحسابات (متعدد العملات)
CREATE TABLE IF NOT EXISTS accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    account_type_id INT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (account_type_id) REFERENCES account_types(id)
);

-- جدول أرصدة الحسابات متعددة العملات
CREATE TABLE IF NOT EXISTS account_balances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    account_id INT NOT NULL,
    currency_id INT NOT NULL,
    balance DECIMAL(15, 2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (currency_id) REFERENCES currencies(id),
    UNIQUE KEY unique_account_currency (account_id, currency_id)
);

-- جدول تصنيفات الواردات
CREATE TABLE IF NOT EXISTS income_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(7),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول تصنيفات المصروفات
CREATE TABLE IF NOT EXISTS expense_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(7),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المعاملات (الواردات والمصروفات)
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    account_id INT NOT NULL,
    type ENUM('income', 'expense', 'transfer') NOT NULL,
    amount DECIMAL(15, 2) NOT NULL,
    currency_id INT NOT NULL,
    category_id INT NULL,
    description TEXT,
    transaction_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    reference_number VARCHAR(50) NULL,
    notes TEXT,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurring_frequency ENUM('daily', 'weekly', 'monthly', 'yearly') NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (currency_id) REFERENCES currencies(id),
    INDEX idx_user_date (user_id, transaction_date),
    INDEX idx_account_date (account_id, transaction_date),
    INDEX idx_type_date (type, transaction_date)
);

-- جدول التحويلات بين الحسابات
CREATE TABLE IF NOT EXISTS transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    from_account_id INT NOT NULL,
    to_account_id INT NOT NULL,
    amount DECIMAL(15, 2) NOT NULL,
    currency_id INT NOT NULL,
    exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
    converted_amount DECIMAL(15, 2) NOT NULL,
    description TEXT,
    transfer_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reference_number VARCHAR(50) NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (from_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (to_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (currency_id) REFERENCES currencies(id)
);

-- جدول المرفقات (صور الفواتير)
CREATE TABLE IF NOT EXISTS attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE
);

-- جدول النسخ الاحتياطية
CREATE TABLE IF NOT EXISTS backups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    backup_type ENUM('manual', 'automatic') NOT NULL,
    created_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول سجل العمليات
CREATE TABLE IF NOT EXISTS activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT NOT NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_action (user_id, action),
    INDEX idx_table_record (table_name, record_id)
);

-- إدراج البيانات الأساسية

-- إدراج العملات
INSERT INTO currencies (code, name, symbol) VALUES
('SAR', 'ريال سعودي', 'ر.س'),
('USD', 'دولار أمريكي', '$'),
('EUR', 'يورو', '€'),
('YER', 'ريال يمني', 'ر.ي'),
('AED', 'درهم إماراتي', 'د.إ'),
('KWD', 'دينار كويتي', 'د.ك'),
('QAR', 'ريال قطري', 'ر.ق'),
('BHD', 'دينار بحريني', 'د.ب'),
('OMR', 'ريال عماني', 'ر.ع'),
('JOD', 'دينار أردني', 'د.أ'),
('EGP', 'جنيه مصري', 'ج.م');

-- إدراج أنواع الحسابات
INSERT INTO account_types (name, description, icon) VALUES
('صندوق نقدي', 'نقد في اليد', 'cash'),
('حساب بنكي', 'حساب في البنك', 'bank'),
('بطاقة ائتمان', 'بطاقة ائتمان', 'credit_card'),
('محفظة إلكترونية', 'محفظة رقمية', 'digital_wallet'),
('استثمار', 'حساب استثماري', 'investment'),
('حساب توفير', 'حساب توفير', 'savings'),
('أخرى', 'نوع آخر', 'other');

-- إدراج تصنيفات الواردات
INSERT INTO income_categories (name, description, color) VALUES
('راتب', 'راتب شهري', '#10B981'),
('أعمال', 'دخل من الأعمال', '#3B82F6'),
('استثمار', 'عوائد استثمارية', '#8B5CF6'),
('عمل حر', 'دخل من العمل الحر', '#F59E0B'),
('إيجار', 'دخل من الإيجار', '#EF4444'),
('هدية', 'هدايا ومنح', '#EC4899'),
('مكافأة', 'مكافآت وحوافز', '#06B6D4'),
('استرداد', 'استرداد مبالغ', '#84CC16'),
('أخرى', 'مصادر أخرى', '#6B7280');

-- إدراج تصنيفات المصروفات
INSERT INTO expense_categories (name, description, color) VALUES
('طعام وشراب', 'مأكولات ومشروبات', '#EF4444'),
('مواصلات', 'نقل ومواصلات', '#3B82F6'),
('سكن', 'إيجار ومسكن', '#8B5CF6'),
('فواتير', 'كهرباء وماء وغاز', '#F59E0B'),
('صحة', 'علاج وأدوية', '#10B981'),
('تعليم', 'تعليم ودورات', '#06B6D4'),
('ترفيه', 'ترفيه وتسلية', '#EC4899'),
('تسوق', 'ملابس ومستلزمات', '#84CC16'),
('سفر', 'سفر وسياحة', '#F97316'),
('عائلة', 'مصروفات عائلية', '#8B5CF6'),
('صدقات', 'زكاة وصدقات', '#10B981'),
('استثمار', 'استثمارات', '#6366F1'),
('ديون', 'سداد ديون', '#EF4444'),
('أخرى', 'مصروفات أخرى', '#6B7280');
