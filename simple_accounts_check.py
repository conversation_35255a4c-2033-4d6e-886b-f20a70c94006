#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بسيط لمشكلة الحسابات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database.connection import db
    print("✅ تم تحميل اتصال قاعدة البيانات")
except Exception as e:
    print(f"❌ خطأ في تحميل قاعدة البيانات: {e}")
    sys.exit(1)

def check_database():
    """فحص قاعدة البيانات"""
    print("🔍 فحص قاعدة البيانات...")
    
    try:
        # فحص الاتصال
        result = db.execute_query("SELECT 1 as test")
        print("✅ الاتصال بقاعدة البيانات يعمل")
        
        # فحص جدول المستخدمين
        users = db.execute_query("SELECT * FROM users")
        print(f"📊 عدد المستخدمين: {len(users) if users else 0}")
        
        if users:
            for user in users:
                print(f"   - {user.get('username')} (ID: {user.get('id')})")
        
        # فحص جدول الحسابات
        accounts = db.execute_query("SELECT * FROM accounts")
        print(f"📊 إجمالي الحسابات: {len(accounts) if accounts else 0}")
        
        if accounts:
            print("📋 جميع الحسابات:")
            for account in accounts:
                status = "نشط" if account.get('is_active') else "غير نشط"
                print(f"   - {account.get('name')} (المستخدم: {account.get('user_id')}) - {status}")
        
        # فحص الحسابات النشطة
        active_accounts = db.execute_query("SELECT * FROM accounts WHERE is_active = TRUE")
        print(f"📊 الحسابات النشطة: {len(active_accounts) if active_accounts else 0}")
        
        # فحص أنواع الحسابات
        account_types = db.execute_query("SELECT * FROM account_types")
        print(f"📊 أنواع الحسابات: {len(account_types) if account_types else 0}")
        
        # فحص العملات
        currencies = db.execute_query("SELECT * FROM currencies")
        print(f"📊 العملات: {len(currencies) if currencies else 0}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def check_specific_user_accounts(user_id):
    """فحص حسابات مستخدم محدد"""
    print(f"\n🔍 فحص حسابات المستخدم {user_id}...")
    
    try:
        # الحسابات الخاصة بالمستخدم
        user_accounts = db.execute_query(
            "SELECT * FROM accounts WHERE user_id = %s", 
            (user_id,)
        )
        print(f"📊 حسابات المستخدم {user_id}: {len(user_accounts) if user_accounts else 0}")
        
        if user_accounts:
            for account in user_accounts:
                status = "نشط" if account.get('is_active') else "غير نشط"
                print(f"   - {account.get('name')} - {status}")
                print(f"     المعرف: {account.get('id')}")
                print(f"     النوع: {account.get('account_type_id')}")
                print(f"     العملة: {account.get('currency_id')}")
        
        # الحسابات النشطة للمستخدم
        active_user_accounts = db.execute_query(
            "SELECT * FROM accounts WHERE user_id = %s AND is_active = TRUE", 
            (user_id,)
        )
        print(f"📊 الحسابات النشطة للمستخدم {user_id}: {len(active_user_accounts) if active_user_accounts else 0}")
        
        return user_accounts
        
    except Exception as e:
        print(f"❌ خطأ في فحص حسابات المستخدم: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("🧪 فحص بسيط لمشكلة الحسابات")
    print("=" * 50)
    
    # فحص قاعدة البيانات
    if not check_database():
        return
    
    # فحص حسابات كل مستخدم
    try:
        users = db.execute_query("SELECT * FROM users")
        if users:
            for user in users:
                check_specific_user_accounts(user['id'])
        else:
            print("❌ لا توجد مستخدمين")
    except Exception as e:
        print(f"❌ خطأ في فحص المستخدمين: {e}")
    
    print("\n" + "=" * 50)
    print("✅ انتهى الفحص")

if __name__ == "__main__":
    main()
