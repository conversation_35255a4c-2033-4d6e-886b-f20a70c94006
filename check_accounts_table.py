#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_accounts_table():
    """فحص هيكل جدول الحسابات"""
    try:
        conn = sqlite3.connect('money_manager.db')
        cursor = conn.cursor()
        
        print("🔍 فحص جدول الحسابات:")
        cursor.execute("PRAGMA table_info(accounts)")
        columns = cursor.fetchall()
        
        print("الأعمدة الموجودة:")
        for col in columns:
            print(f"  - {col[1]}: {col[2]}")
        
        # فحص البيانات الموجودة
        cursor.execute("SELECT COUNT(*) FROM accounts")
        count = cursor.fetchone()[0]
        print(f"\nعدد الحسابات: {count}")
        
        if count > 0:
            cursor.execute("SELECT * FROM accounts LIMIT 3")
            accounts = cursor.fetchall()
            print("\nعينة من البيانات:")
            for account in accounts:
                print(f"  {account}")
        
        conn.close()
        
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    check_accounts_table()
