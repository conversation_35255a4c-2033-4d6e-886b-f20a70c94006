#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشاكل ميزة إدارة المستخدمين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔍 اختبار الاتصال بقاعدة البيانات...")
    try:
        # اختبار الاتصال المباشر أولاً
        import mysql.connector
        from config.settings import DATABASE_CONFIG

        connection = mysql.connector.connect(**DATABASE_CONFIG)
        if connection.is_connected():
            print("✅ الاتصال المباشر بقاعدة البيانات ناجح")
            connection.close()

            # الآن اختبار db object
            from database.connection import db
            if db.is_connected() or db.connect():
                print("✅ اتصال db object ناجح")
                return True
            else:
                print("❌ فشل اتصال db object")
                return False
        else:
            print("❌ فشل الاتصال المباشر بقاعدة البيانات")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_users_table_structure():
    """فحص بنية جدول المستخدمين"""
    print("\n🔍 فحص بنية جدول المستخدمين...")
    try:
        from database.connection import db
        
        # فحص وجود الجدول
        tables = db.execute_query("SHOW TABLES LIKE 'users'")
        if not tables:
            print("❌ جدول المستخدمين غير موجود!")
            return False
        
        print("✅ جدول المستخدمين موجود")
        
        # فحص بنية الجدول
        structure = db.execute_query("DESCRIBE users")
        print("📋 بنية جدول المستخدمين:")
        for column in structure:
            print(f"   - {column['Field']}: {column['Type']} ({'NULL' if column['Null'] == 'YES' else 'NOT NULL'})")
        
        # التحقق من وجود العمود created_by
        columns = [col['Field'] for col in structure]
        if 'created_by' not in columns:
            print("⚠️ العمود 'created_by' مفقود - سيتم إضافته")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص بنية الجدول: {e}")
        return False

def check_users_data():
    """فحص بيانات المستخدمين"""
    print("\n🔍 فحص بيانات المستخدمين...")
    try:
        from database.connection import db
        
        # عد المستخدمين
        count_result = db.execute_query("SELECT COUNT(*) as count FROM users")
        user_count = count_result[0]['count'] if count_result else 0
        print(f"📊 عدد المستخدمين في قاعدة البيانات: {user_count}")
        
        if user_count == 0:
            print("⚠️ لا توجد مستخدمين في قاعدة البيانات")
            return False
        
        # جلب المستخدمين
        users = db.execute_query("SELECT id, username, full_name, role, is_active FROM users")
        print("👥 المستخدمين الموجودين:")
        for user in users:
            status = "نشط" if user['is_active'] else "معطل"
            print(f"   - {user['username']} ({user['full_name']}) - {user['role']} - {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص بيانات المستخدمين: {e}")
        return False

def test_user_model():
    """اختبار نموذج المستخدم"""
    print("\n🔍 اختبار نموذج المستخدم...")
    try:
        from database.models import User
        
        # اختبار get_all()
        print("   اختبار User.get_all()...")
        users = User.get_all()
        
        if users is None:
            print("❌ User.get_all() أرجع None")
            return False
        
        if isinstance(users, list):
            print(f"✅ User.get_all() أرجع قائمة بـ {len(users)} مستخدم")
            
            # طباعة تفاصيل المستخدمين
            for user in users:
                print(f"   - المستخدم: {user}")
            
            return True
        else:
            print(f"❌ User.get_all() أرجع نوع غير متوقع: {type(users)}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نموذج المستخدم: {e}")
        print(f"   تفاصيل الخطأ: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_auth_system():
    """اختبار نظام المصادقة"""
    print("\n🔍 اختبار نظام المصادقة...")
    try:
        from utils.auth import auth_manager
        
        # محاولة تسجيل الدخول
        success, message = auth_manager.login("admin2", "123456")
        
        if success:
            print("✅ تسجيل الدخول ناجح")
            print(f"   المستخدم الحالي: {auth_manager.current_user}")
            
            # اختبار الصلاحيات
            if auth_manager.can_manage_users():
                print("✅ المستخدم يمكنه إدارة المستخدمين")
                return True
            else:
                print("❌ المستخدم لا يمكنه إدارة المستخدمين")
                return False
        else:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام المصادقة: {e}")
        return False

def test_gui_imports():
    """اختبار استيراد وحدات واجهة المستخدم"""
    print("\n🔍 اختبار استيراد وحدات واجهة المستخدم...")
    try:
        # اختبار استيراد الوحدات الأساسية
        print("   اختبار استيراد config.colors...")
        from config.colors import COLORS, BUTTON_STYLES, CARD_STYLES, ARABIC_TEXT_STYLES
        print("   ✅ تم استيراد config.colors بنجاح")
        
        print("   اختبار استيراد config.fonts...")
        from config.fonts import create_rtl_label, create_rtl_button, create_rtl_entry
        print("   ✅ تم استيراد config.fonts بنجاح")
        
        print("   اختبار استيراد user_management_windows...")
        from gui.user_management_windows import AddUserWindow, EditUserWindow, ResetPasswordWindow
        print("   ✅ تم استيراد user_management_windows بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_missing_created_by_column():
    """إصلاح العمود المفقود created_by"""
    print("\n🔧 إصلاح العمود المفقود created_by...")
    try:
        from database.connection import db
        
        # التحقق من وجود العمود
        structure = db.execute_query("DESCRIBE users")
        columns = [col['Field'] for col in structure]
        
        if 'created_by' not in columns:
            print("   إضافة العمود created_by...")
            db.execute_update("ALTER TABLE users ADD COLUMN created_by INT NULL")
            print("   ✅ تم إضافة العمود created_by")
            
            # إضافة المفتاح الخارجي
            try:
                db.execute_update("ALTER TABLE users ADD FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL")
                print("   ✅ تم إضافة المفتاح الخارجي")
            except Exception as fk_error:
                print(f"   ⚠️ تحذير: لم يتم إضافة المفتاح الخارجي: {fk_error}")
            
            return True
        else:
            print("   ✅ العمود created_by موجود مسبقاً")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح العمود: {e}")
        return False

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🚀 تشخيص مشاكل ميزة إدارة المستخدمين")
    print("=" * 60)
    
    issues_found = []
    fixes_applied = []
    
    # 1. اختبار الاتصال بقاعدة البيانات
    if not test_database_connection():
        issues_found.append("مشكلة في الاتصال بقاعدة البيانات")
        return
    
    # 2. فحص بنية جدول المستخدمين
    if not check_users_table_structure():
        issues_found.append("مشكلة في بنية جدول المستخدمين")
        if fix_missing_created_by_column():
            fixes_applied.append("إصلاح العمود المفقود created_by")
    
    # 3. فحص بيانات المستخدمين
    if not check_users_data():
        issues_found.append("مشكلة في بيانات المستخدمين")
    
    # 4. اختبار نموذج المستخدم
    if not test_user_model():
        issues_found.append("مشكلة في نموذج المستخدم")
    
    # 5. اختبار نظام المصادقة
    if not test_auth_system():
        issues_found.append("مشكلة في نظام المصادقة")
    
    # 6. اختبار استيراد الوحدات
    if not test_gui_imports():
        issues_found.append("مشكلة في استيراد وحدات واجهة المستخدم")
    
    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج التشخيص:")
    
    if issues_found:
        print(f"❌ تم العثور على {len(issues_found)} مشكلة:")
        for issue in issues_found:
            print(f"   - {issue}")
    else:
        print("✅ لم يتم العثور على مشاكل!")
    
    if fixes_applied:
        print(f"\n🔧 تم تطبيق {len(fixes_applied)} إصلاح:")
        for fix in fixes_applied:
            print(f"   - {fix}")
    
    return len(issues_found) == 0

if __name__ == "__main__":
    main()
