#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector
from config.settings import DATABASE_CONFIG

def create_all_tables():
    """إنشاء جميع الجداول المطلوبة"""
    try:
        print("🚀 إنشاء جميع الجداول...")
        
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = connection.cursor()
        
        # 1. جدول المستخدمين
        print("👤 إنشاء جدول users...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        """)
        
        # 2. جدول العملات
        print("💰 إنشاء جدول currencies...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS currencies (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(3) UNIQUE NOT NULL,
                name VARCHAR(50) NOT NULL,
                symbol VARCHAR(10) NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 3. جدول أنواع الحسابات
        print("🏦 إنشاء جدول account_types...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_types (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                description TEXT,
                icon VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 4. جدول الحسابات
        print("📊 إنشاء جدول accounts...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                name VARCHAR(100) NOT NULL,
                account_type_id INT NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (account_type_id) REFERENCES account_types(id)
            )
        """)
        
        # 5. جدول أرصدة الحسابات متعددة العملات
        print("💵 إنشاء جدول account_balances...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_balances (
                id INT AUTO_INCREMENT PRIMARY KEY,
                account_id INT NOT NULL,
                currency_id INT NOT NULL,
                balance DECIMAL(15, 2) DEFAULT 0.00,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (currency_id) REFERENCES currencies(id),
                UNIQUE KEY unique_account_currency (account_id, currency_id)
            )
        """)
        
        # 6. جدول المعاملات
        print("📝 إنشاء جدول transactions...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                account_id INT NOT NULL,
                type ENUM('income', 'expense', 'transfer') NOT NULL,
                amount DECIMAL(15, 2) NOT NULL,
                currency_id INT NOT NULL,
                description TEXT,
                transaction_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reference_number VARCHAR(50),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (currency_id) REFERENCES currencies(id)
            )
        """)
        
        # 7. جدول التحويلات
        print("🔄 إنشاء جدول transfers...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transfers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                from_account_id INT NOT NULL,
                to_account_id INT NOT NULL,
                from_amount DECIMAL(15, 2) NOT NULL,
                to_amount DECIMAL(15, 2) NOT NULL,
                from_currency_id INT NOT NULL,
                to_currency_id INT NOT NULL,
                exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
                description TEXT,
                transfer_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reference_number VARCHAR(50),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (from_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (to_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (from_currency_id) REFERENCES currencies(id),
                FOREIGN KEY (to_currency_id) REFERENCES currencies(id)
            )
        """)
        
        # 8. جدول سجل الأنشطة
        print("📋 إنشاء جدول activity_log...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                action VARCHAR(100) NOT NULL,
                table_name VARCHAR(50) NOT NULL,
                record_id INT,
                details TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        """)
        
        # إدراج البيانات الأساسية
        print("📥 إدراج البيانات الأساسية...")
        
        # العملات
        currencies = [
            (1, 'SAR', 'ريال سعودي', 'ر.س'),
            (2, 'YER', 'ريال يمني', 'ر.ي'),
            (3, 'AED', 'درهم إماراتي', 'د.إ'),
            (4, 'USD', 'دولار أمريكي', '$')
        ]
        
        for currency_id, code, name, symbol in currencies:
            cursor.execute("""
                INSERT IGNORE INTO currencies (id, code, name, symbol, is_active, exchange_rate)
                VALUES (%s, %s, %s, %s, 1, 1.0)
            """, (currency_id, code, name, symbol))
        
        # أنواع الحسابات
        account_types = [
            (1, 'حساب عام', 'حساب عام للاستخدام المتنوع'),
            (2, 'نقدي', 'صندوق نقدي'),
            (3, 'بنكي', 'حساب بنكي'),
            (4, 'محفظة إلكترونية', 'محفظة إلكترونية')
        ]
        
        for type_id, name, description in account_types:
            cursor.execute("""
                INSERT IGNORE INTO account_types (id, name, description)
                VALUES (%s, %s, %s)
            """, (type_id, name, description))
        
        # مستخدم افتراضي
        cursor.execute("""
            INSERT IGNORE INTO users (id, username, email, password_hash, full_name)
            VALUES (1, 'admin', '<EMAIL>', 'hashed_password', 'المدير')
        """)
        
        connection.commit()
        
        print("\n✅ تم إنشاء جميع الجداول بنجاح!")
        print("🎉 قاعدة البيانات جاهزة للاستخدام")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    create_all_tables()
