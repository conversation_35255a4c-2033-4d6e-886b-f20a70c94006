#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اتصال قاعدة بيانات SQLite البديل
"""

import sqlite3
import logging
import os
import json
from datetime import datetime

class SQLiteConnection:
    """فئة إدارة الاتصال بقاعدة بيانات SQLite"""

    def __init__(self, db_path="money_manager.db"):
        self.db_path = db_path
        self.connection = None
        self.cursor = None

    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            self.cursor = self.connection.cursor()
            
            # إنشاء الجداول إذا لم تكن موجودة
            self._create_tables()
            
            logging.info("تم الاتصال بقاعدة البيانات SQLite بنجاح")
            return True
            
        except Exception as e:
            logging.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False

    def _create_tables(self):
        """إنشاء الجداول الأساسية"""
        try:
            # جدول المستخدمين
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    profile_image TEXT
                )
            ''')

            # جدول العملات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS currencies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    exchange_rate REAL DEFAULT 1.0,
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول أنواع الحسابات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS account_types (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    icon TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول الحسابات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS accounts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    account_type_id INTEGER NOT NULL,
                    currency_id INTEGER NOT NULL,
                    initial_balance REAL DEFAULT 0.0,
                    current_balance REAL DEFAULT 0.0,
                    description TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (account_type_id) REFERENCES account_types(id),
                    FOREIGN KEY (currency_id) REFERENCES currencies(id)
                )
            ''')

            # جدول تصنيفات الواردات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS income_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    icon TEXT,
                    color TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول تصنيفات المصروفات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS expense_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    icon TEXT,
                    color TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول المعاملات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    account_id INTEGER NOT NULL,
                    transaction_type TEXT NOT NULL,
                    amount REAL NOT NULL,
                    currency_id INTEGER NOT NULL,
                    category_id INTEGER,
                    description TEXT,
                    transaction_date DATE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (account_id) REFERENCES accounts(id),
                    FOREIGN KEY (currency_id) REFERENCES currencies(id)
                )
            ''')

            # جدول التحويلات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS transfers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    from_account_id INTEGER NOT NULL,
                    to_account_id INTEGER NOT NULL,
                    amount REAL NOT NULL,
                    currency_id INTEGER NOT NULL,
                    description TEXT,
                    transfer_date DATE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (from_account_id) REFERENCES accounts(id),
                    FOREIGN KEY (to_account_id) REFERENCES accounts(id),
                    FOREIGN KEY (currency_id) REFERENCES currencies(id)
                )
            ''')

            # إدراج البيانات الأساسية
            self._insert_default_data()
            
            self.connection.commit()
            logging.info("تم إنشاء الجداول بنجاح")
            
        except Exception as e:
            logging.error(f"خطأ في إنشاء الجداول: {e}")
            raise

    def _insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        try:
            # العملات
            currencies = [
                (1, 'SAR', 'ريال سعودي', 'ر.س', 1.0, 1),
                (2, 'USD', 'دولار أمريكي', '$', 3.75, 1),
                (3, 'EUR', 'يورو', '€', 4.0, 1)
            ]
            
            self.cursor.execute("SELECT COUNT(*) FROM currencies")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.executemany(
                    "INSERT OR IGNORE INTO currencies (id, code, name, symbol, exchange_rate, is_active) VALUES (?, ?, ?, ?, ?, ?)",
                    currencies
                )

            # أنواع الحسابات
            account_types = [
                (1, 'صندوق نقدي', 'النقد المتوفر في اليد', '💰', 1),
                (2, 'حساب بنكي', 'حساب في البنك', '🏦', 1),
                (3, 'بطاقة ائتمان', 'بطاقة ائتمان', '💳', 1),
                (4, 'محفظة إلكترونية', 'محفظة رقمية', '📱', 1),
                (5, 'استثمار', 'حساب استثماري', '📈', 1),
                (6, 'حساب توفير', 'حساب توفير', '🏛️', 1)
            ]
            
            self.cursor.execute("SELECT COUNT(*) FROM account_types")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.executemany(
                    "INSERT OR IGNORE INTO account_types (id, name, description, icon, is_active) VALUES (?, ?, ?, ?, ?)",
                    account_types
                )

            # تصنيفات الواردات
            income_categories = [
                (1, 'راتب', 'الراتب الشهري', '💼', '#4CAF50', 1),
                (2, 'أعمال', 'دخل من الأعمال', '🏢', '#2196F3', 1),
                (3, 'استثمار', 'عوائد استثمارية', '📈', '#FF9800', 1),
                (4, 'عمل حر', 'دخل من العمل الحر', '💻', '#9C27B0', 1)
            ]
            
            self.cursor.execute("SELECT COUNT(*) FROM income_categories")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.executemany(
                    "INSERT OR IGNORE INTO income_categories (id, name, description, icon, color, is_active) VALUES (?, ?, ?, ?, ?, ?)",
                    income_categories
                )

            # تصنيفات المصروفات
            expense_categories = [
                (1, 'طعام وشراب', 'مصروفات الطعام والشراب', '🍽️', '#F44336', 1),
                (2, 'مواصلات', 'مصروفات النقل والمواصلات', '🚗', '#FF5722', 1),
                (3, 'سكن', 'مصروفات السكن والإيجار', '🏠', '#795548', 1),
                (4, 'فواتير', 'الفواتير والخدمات', '📄', '#607D8B', 1),
                (5, 'صحة', 'المصروفات الطبية', '🏥', '#E91E63', 1),
                (6, 'تعليم', 'مصروفات التعليم', '📚', '#3F51B5', 1),
                (7, 'ترفيه', 'مصروفات الترفيه', '🎮', '#9C27B0', 1),
                (8, 'تسوق', 'مصروفات التسوق', '🛍️', '#FF9800', 1)
            ]
            
            self.cursor.execute("SELECT COUNT(*) FROM expense_categories")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.executemany(
                    "INSERT OR IGNORE INTO expense_categories (id, name, description, icon, color, is_active) VALUES (?, ?, ?, ?, ?, ?)",
                    expense_categories
                )
                
        except Exception as e:
            logging.error(f"خطأ في إدراج البيانات الافتراضية: {e}")

    def execute_query(self, query, params=None):
        """تنفيذ استعلام قراءة"""
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            rows = self.cursor.fetchall()
            # تحويل النتائج إلى قائمة من القواميس
            return [dict(row) for row in rows] if rows else []
            
        except Exception as e:
            logging.error(f"خطأ في تنفيذ الاستعلام: {e}")
            return None

    def execute_update(self, query, params=None):
        """تنفيذ استعلام تحديث/إدراج/حذف"""
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            self.connection.commit()
            return self.cursor.rowcount
        except Exception as e:
            logging.error(f"خطأ في تنفيذ التحديث: {e}")
            self.connection.rollback()
            return -1

    def execute_insert(self, query, params=None):
        """تنفيذ استعلام إدراج وإرجاع ID الجديد"""
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            self.connection.commit()
            return self.cursor.lastrowid
        except Exception as e:
            logging.error(f"خطأ في تنفيذ الإدراج: {e}")
            self.connection.rollback()
            return -1

    def close(self):
        """إغلاق الاتصال"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
                logging.info("تم إغلاق الاتصال بقاعدة البيانات")
        except Exception as e:
            logging.error(f"خطأ في إغلاق الاتصال: {e}")

    def is_connected(self):
        """فحص حالة الاتصال"""
        return self.connection is not None

# إنشاء مثيل عام لقاعدة البيانات
sqlite_db = SQLiteConnection()
