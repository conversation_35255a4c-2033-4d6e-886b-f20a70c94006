import mysql.connector

try:
    connection = mysql.connector.connect(
        host='localhost',
        user='root',
        password='mohdam',
        database='money_manager'
    )
    cursor = connection.cursor()
    
    print("✅ متصل بقاعدة البيانات")
    
    # فحص هيكل الجدول الحالي
    print("🔍 فحص هيكل جدول transactions...")
    cursor.execute("DESCRIBE transactions")
    columns = cursor.fetchall()
    
    print("الأعمدة الحالية:")
    column_names = []
    for col in columns:
        column_names.append(col[0])
        print(f"   - {col[0]}: {col[1]}")
    
    # إضافة عمود type إذا لم يكن موجوداً
    if 'type' not in column_names:
        print("\n📝 إضافة عمود type...")
        cursor.execute("""
            ALTER TABLE transactions 
            ADD COLUMN type ENUM('income', 'expense', 'transfer') NOT NULL DEFAULT 'income'
        """)
        print("✅ تم إضافة عمود type")
    else:
        print("✅ عمود type موجود بالفعل")
    
    # إضافة عمود transaction_type إذا لم يكن موجوداً (للتوافق مع الكود القديم)
    if 'transaction_type' not in column_names:
        print("\n📝 إضافة عمود transaction_type...")
        cursor.execute("""
            ALTER TABLE transactions 
            ADD COLUMN transaction_type ENUM('income', 'expense', 'transfer') NOT NULL DEFAULT 'income'
        """)
        print("✅ تم إضافة عمود transaction_type")
    else:
        print("✅ عمود transaction_type موجود بالفعل")
    
    # تحديث البيانات الموجودة إذا لزم الأمر
    print("\n🔄 تحديث البيانات الموجودة...")
    cursor.execute("UPDATE transactions SET type = 'income' WHERE type IS NULL OR type = ''")
    cursor.execute("UPDATE transactions SET transaction_type = type WHERE transaction_type IS NULL OR transaction_type = ''")
    
    connection.commit()
    
    print("\n📊 هيكل الجدول بعد التحديث:")
    cursor.execute("DESCRIBE transactions")
    columns = cursor.fetchall()
    
    for col in columns:
        print(f"   - {col[0]}: {col[1]} {col[2]} {col[3]} {col[4]} {col[5]}")
    
    cursor.close()
    connection.close()
    
    print("\n✅ تم إصلاح جدول transactions بنجاح!")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
