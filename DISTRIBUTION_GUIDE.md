# دليل توزيع برنامج إدارة الأموال

هذا الدليل يشرح كيفية توزيع برنامج إدارة الأموال بعد تحويله إلى حزمة تثبيت.

## الملفات القابلة للتوزيع

1. **حزمة التثبيت**: `installer_output\MoneyManagerSetup.exe`
   - هذا هو الملف الرئيسي الذي يجب توزيعه على المستخدمين
   - يحتوي على جميع الملفات والمكتبات اللازمة لتشغيل البرنامج

2. **دليل التثبيت**: `INSTALLATION_GUIDE.md`
   - يمكن تحويله إلى PDF وتوزيعه مع حزمة التثبيت
   - يحتوي على تعليمات مفصلة للمستخدم النهائي

## طرق التوزيع

### 1. التوزيع المباشر

- نسخ ملف `MoneyManagerSetup.exe` على وسيط تخزين (USB، قرص مضغوط)
- إرسال الملف عبر البريد الإلكتروني (إذا كان حجم الملف مناسباً)
- مشاركة الملف عبر خدمات مشاركة الملفات (Google Drive، Dropbox، إلخ)

### 2. التوزيع عبر موقع ويب

- رفع ملف `MoneyManagerSetup.exe` إلى موقع الويب الخاص بك
- إنشاء صفحة تنزيل تحتوي على رابط التنزيل وتعليمات التثبيت
- توفير معلومات حول متطلبات النظام والميزات

### 3. التوزيع عبر منصات التوزيع

- نشر البرنامج على منصات توزيع البرامج مثل:
  - SourceForge
  - GitHub Releases
  - Microsoft Store (يتطلب حزمة MSIX)

## إعداد حزمة التثبيت للتوزيع

### 1. اختبار حزمة التثبيت

قبل التوزيع، تأكد من اختبار حزمة التثبيت على أنظمة مختلفة للتأكد من:

- عمل عملية التثبيت بشكل صحيح
- تشغيل البرنامج بدون أخطاء
- عمل جميع الميزات كما هو متوقع

### 2. إضافة معلومات الإصدار

- تحديث رقم الإصدار في ملف `installer.iss`
- إضافة ملف `CHANGELOG.md` يوضح التغييرات في كل إصدار

### 3. توقيع حزمة التثبيت (اختياري ولكن موصى به)

توقيع حزمة التثبيت بشهادة رقمية يساعد في:

- تجنب تحذيرات الأمان من Windows
- التحقق من هوية الناشر
- ضمان سلامة الملف

#### خطوات توقيع حزمة التثبيت:

1. الحصول على شهادة رقمية من مزود موثوق
2. استخدام أداة مثل `signtool.exe` لتوقيع الملف

```
signtool sign /f certificate.pfx /p password /t http://timestamp.digicert.com MoneyManagerSetup.exe
```

## تحديث البرنامج

### 1. إنشاء نظام تحديث

يمكنك إضافة نظام تحديث تلقائي للبرنامج عن طريق:

- إنشاء ملف XML أو JSON يحتوي على معلومات أحدث إصدار
- إضافة وظيفة في البرنامج للتحقق من التحديثات
- توفير رابط لتنزيل أحدث إصدار

### 2. توثيق التغييرات

- الاحتفاظ بسجل التغييرات (CHANGELOG)
- توثيق الميزات الجديدة والإصلاحات
- توضيح متطلبات النظام الجديدة إن وجدت

## الدعم الفني

### 1. إنشاء وثائق المستخدم

- دليل المستخدم
- الأسئلة الشائعة (FAQ)
- فيديوهات تعليمية

### 2. توفير قنوات الدعم

- عنوان بريد إلكتروني للدعم
- نموذج اتصال على موقع الويب
- منتدى للمستخدمين

## الاعتبارات القانونية

### 1. اتفاقية ترخيص المستخدم النهائي (EULA)

- إنشاء اتفاقية ترخيص واضحة
- تضمينها في عملية التثبيت
- توضيح حقوق والتزامات المستخدم

### 2. سياسة الخصوصية

- توضيح البيانات التي يجمعها البرنامج (إن وجدت)
- كيفية استخدام هذه البيانات
- إجراءات حماية البيانات

## نصائح إضافية

1. قم بإنشاء موقع ويب بسيط للبرنامج
2. استخدم وسائل التواصل الاجتماعي للترويج
3. اجمع ملاحظات المستخدمين لتحسين البرنامج
4. قدم إصدارات تجريبية قبل إطلاق الإصدارات الرئيسية