#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد قاعدة البيانات بطريقة مبسطة
"""

import mysql.connector
from mysql.connector import Error
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_database_and_user():
    """إنشاء قاعدة البيانات والمستخدم الافتراضي"""
    
    print("🔄 إعداد قاعدة البيانات...")
    
    try:
        # الاتصال بـ MySQL
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='mohdam',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # إنشاء قاعدة البيانات
        print("📋 إنشاء قاعدة البيانات...")
        cursor.execute("DROP DATABASE IF EXISTS money_manager")
        cursor.execute("CREATE DATABASE money_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        cursor.execute("USE money_manager")
        
        # إنشاء جدول المستخدمين
        print("👥 إنشاء جدول المستخدمين...")
        cursor.execute("""
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role ENUM('admin', 'user') DEFAULT 'user',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL,
                profile_image VARCHAR(255) NULL
            )
        """)
        
        # إنشاء جدول العملات
        print("💱 إنشاء جدول العملات...")
        cursor.execute("""
            CREATE TABLE currencies (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(3) UNIQUE NOT NULL,
                name VARCHAR(50) NOT NULL,
                symbol VARCHAR(10) NOT NULL,
                exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول أنواع الحسابات
        print("🏦 إنشاء جدول أنواع الحسابات...")
        cursor.execute("""
            CREATE TABLE account_types (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                description TEXT,
                icon VARCHAR(10),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول الحسابات
        print("💰 إنشاء جدول الحسابات...")
        cursor.execute("""
            CREATE TABLE accounts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                name VARCHAR(100) NOT NULL,
                account_type_id INT NOT NULL,
                currency_id INT NOT NULL,
                initial_balance DECIMAL(15, 2) DEFAULT 0.00,
                current_balance DECIMAL(15, 2) DEFAULT 0.00,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (account_type_id) REFERENCES account_types(id),
                FOREIGN KEY (currency_id) REFERENCES currencies(id)
            )
        """)
        
        # إنشاء جدول تصنيفات الواردات
        print("📈 إنشاء جدول تصنيفات الواردات...")
        cursor.execute("""
            CREATE TABLE income_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                description TEXT,
                icon VARCHAR(10),
                color VARCHAR(7),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول تصنيفات المصروفات
        print("📉 إنشاء جدول تصنيفات المصروفات...")
        cursor.execute("""
            CREATE TABLE expense_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                description TEXT,
                icon VARCHAR(10),
                color VARCHAR(7),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول المعاملات
        print("💸 إنشاء جدول المعاملات...")
        cursor.execute("""
            CREATE TABLE transactions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                account_id INT NOT NULL,
                transaction_type ENUM('income', 'expense') NOT NULL,
                amount DECIMAL(15, 2) NOT NULL,
                currency_id INT NOT NULL,
                category_id INT NULL,
                description TEXT,
                transaction_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (currency_id) REFERENCES currencies(id)
            )
        """)
        
        # إنشاء جدول التحويلات
        print("🔄 إنشاء جدول التحويلات...")
        cursor.execute("""
            CREATE TABLE transfers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                from_account_id INT NOT NULL,
                to_account_id INT NOT NULL,
                amount DECIMAL(15, 2) NOT NULL,
                currency_id INT NOT NULL,
                description TEXT,
                transfer_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (from_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (to_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (currency_id) REFERENCES currencies(id)
            )
        """)
        
        # إدراج البيانات الأساسية
        print("📊 إدراج البيانات الأساسية...")
        
        # العملات
        cursor.executemany("""
            INSERT INTO currencies (id, code, name, symbol, exchange_rate, is_active) 
            VALUES (%s, %s, %s, %s, %s, %s)
        """, [
            (1, 'SAR', 'ريال سعودي', 'ر.س', 1.0000, 1),
            (2, 'USD', 'دولار أمريكي', '$', 3.7500, 1),
            (3, 'EUR', 'يورو', '€', 4.0000, 1)
        ])
        
        # أنواع الحسابات
        cursor.executemany("""
            INSERT INTO account_types (id, name, description, icon, is_active) 
            VALUES (%s, %s, %s, %s, %s)
        """, [
            (1, 'صندوق نقدي', 'النقد المتوفر في اليد', '💰', 1),
            (2, 'حساب بنكي', 'حساب في البنك', '🏦', 1),
            (3, 'بطاقة ائتمان', 'بطاقة ائتمان', '💳', 1),
            (4, 'محفظة إلكترونية', 'محفظة رقمية', '📱', 1),
            (5, 'استثمار', 'حساب استثماري', '📈', 1),
            (6, 'حساب توفير', 'حساب توفير', '🏛️', 1)
        ])
        
        # تصنيفات الواردات
        cursor.executemany("""
            INSERT INTO income_categories (id, name, description, icon, color, is_active) 
            VALUES (%s, %s, %s, %s, %s, %s)
        """, [
            (1, 'راتب', 'الراتب الشهري', '💼', '#4CAF50', 1),
            (2, 'أعمال', 'دخل من الأعمال', '🏢', '#2196F3', 1),
            (3, 'استثمار', 'عوائد استثمارية', '📈', '#FF9800', 1),
            (4, 'عمل حر', 'دخل من العمل الحر', '💻', '#9C27B0', 1)
        ])
        
        # تصنيفات المصروفات
        cursor.executemany("""
            INSERT INTO expense_categories (id, name, description, icon, color, is_active) 
            VALUES (%s, %s, %s, %s, %s, %s)
        """, [
            (1, 'طعام وشراب', 'مصروفات الطعام والشراب', '🍽️', '#F44336', 1),
            (2, 'مواصلات', 'مصروفات النقل والمواصلات', '🚗', '#FF5722', 1),
            (3, 'سكن', 'مصروفات السكن والإيجار', '🏠', '#795548', 1),
            (4, 'فواتير', 'الفواتير والخدمات', '📄', '#607D8B', 1),
            (5, 'صحة', 'المصروفات الطبية', '🏥', '#E91E63', 1),
            (6, 'تعليم', 'مصروفات التعليم', '📚', '#3F51B5', 1),
            (7, 'ترفيه', 'مصروفات الترفيه', '🎮', '#9C27B0', 1),
            (8, 'تسوق', 'مصروفات التسوق', '🛍️', '#FF9800', 1)
        ])
        
        # إنشاء المستخدم الافتراضي
        print("👤 إنشاء المستخدم الافتراضي...")
        import bcrypt
        password_hash = bcrypt.hashpw('123456'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        cursor.execute("""
            INSERT INTO users (username, password_hash, full_name, role, is_active)
            VALUES (%s, %s, %s, %s, %s)
        """, ('admin', password_hash, 'المدير الافتراضي', 'admin', 1))
        
        user_id = cursor.lastrowid
        
        # إنشاء حسابات تجريبية
        print("🏦 إنشاء حسابات تجريبية...")
        accounts_data = [
            (user_id, 'الصندوق النقدي', 1, 1, 5000.00, 5000.00, 'النقد المتوفر في اليد'),
            (user_id, 'حساب الراجحي', 2, 1, 25000.00, 25000.00, 'الحساب الجاري في بنك الراجحي'),
            (user_id, 'حساب التوفير', 6, 1, 50000.00, 50000.00, 'حساب التوفير طويل المدى')
        ]
        
        cursor.executemany("""
            INSERT INTO accounts (user_id, name, account_type_id, currency_id, initial_balance, current_balance, description) 
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, accounts_data)
        
        # إنشاء معاملات تجريبية
        print("💰 إنشاء معاملات تجريبية...")
        transactions_data = [
            (user_id, 2, 'income', 8000.00, 1, 1, 'راتب شهر ديسمبر', '2025-06-25'),
            (user_id, 1, 'expense', 300.00, 1, 1, 'تسوق من السوبر ماركت', '2025-06-28'),
            (user_id, 2, 'expense', 1200.00, 1, 3, 'إيجار الشقة', '2025-06-26'),
            (user_id, 1, 'income', 1500.00, 1, 2, 'دخل من مشروع جانبي', '2025-06-20')
        ]
        
        cursor.executemany("""
            INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_id, description, transaction_date) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, transactions_data)
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("\n🎉 تم إعداد قاعدة البيانات بنجاح!")
        print("\n📋 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: 123456")
        print("   الاسم الكامل: المدير الافتراضي")
        print("   البريد الإلكتروني: <EMAIL>")
        
        return True
        
    except Error as e:
        print(f"❌ خطأ في MySQL: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🏦 إعداد قاعدة بيانات مدير الأموال")
    print("=" * 60)
    
    if create_database_and_user():
        print("\n💡 يمكنك الآن تشغيل البرنامج الرئيسي:")
        print("   python main.py")
    else:
        print("\n❌ فشل في إعداد قاعدة البيانات")
        print("💡 تأكد من تشغيل MySQL Server")
    
    input("\n🔄 اضغط Enter للخروج...")
