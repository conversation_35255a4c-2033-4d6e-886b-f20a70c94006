#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector
from mysql.connector import Error

print("🔍 فحص مباشر لقاعدة البيانات...")

# إعدادات الاتصال
config = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'mohdam',
    'database': 'money_manager',
    'charset': 'utf8mb4'
}

try:
    # محاولة الاتصال
    print("1. محاولة الاتصال بقاعدة البيانات...")
    connection = mysql.connector.connect(**config)
    
    if connection.is_connected():
        print("✅ تم الاتصال بنجاح")
        cursor = connection.cursor(dictionary=True)
        
        # فحص الجداول
        print("\n2. فحص الجداول الموجودة...")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        if tables:
            print(f"✅ تم العثور على {len(tables)} جدول:")
            for table in tables:
                table_name = list(table.values())[0]
                print(f"   - {table_name}")
        else:
            print("❌ لا توجد جداول في قاعدة البيانات")
        
        # فحص البيانات في الجداول الأساسية
        essential_tables = ['users', 'accounts', 'transactions', 'currencies']
        
        print("\n3. فحص البيانات في الجداول الأساسية...")
        for table in essential_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                result = cursor.fetchone()
                count = result['count'] if result else 0
                
                if count > 0:
                    print(f"✅ {table}: {count} سجل")
                else:
                    print(f"⚠️ {table}: فارغ")
            except Error as e:
                print(f"❌ {table}: خطأ - {e}")
        
        # فحص المستخدمين
        print("\n4. فحص المستخدمين...")
        try:
            cursor.execute("SELECT id, username, role FROM users")
            users = cursor.fetchall()
            
            if users:
                print(f"✅ المستخدمين ({len(users)}):")
                for user in users:
                    print(f"   - {user['username']} (ID: {user['id']}, Role: {user['role']})")
            else:
                print("❌ لا يوجد مستخدمين")
        except Error as e:
            print(f"❌ خطأ في فحص المستخدمين: {e}")
        
        # فحص الحسابات
        print("\n5. فحص الحسابات...")
        try:
            cursor.execute("SELECT * FROM accounts")
            accounts = cursor.fetchall()
            
            if accounts:
                print(f"✅ الحسابات ({len(accounts)}):")
                for account in accounts:
                    print(f"   - {account['name']}: {account['balance']} {account['currency']} (المستخدم: {account['user_id']})")
            else:
                print("❌ لا توجد حسابات")
        except Error as e:
            print(f"❌ خطأ في فحص الحسابات: {e}")
        
        # فحص المعاملات
        print("\n6. فحص المعاملات...")
        try:
            cursor.execute("SELECT COUNT(*) as count FROM transactions")
            result = cursor.fetchone()
            trans_count = result['count'] if result else 0
            
            if trans_count > 0:
                print(f"✅ المعاملات: {trans_count}")
                
                # عرض آخر 3 معاملات
                cursor.execute("""
                    SELECT t.*, a.name as account_name 
                    FROM transactions t 
                    JOIN accounts a ON t.account_id = a.id 
                    ORDER BY t.created_at DESC 
                    LIMIT 3
                """)
                recent_trans = cursor.fetchall()
                
                if recent_trans:
                    print("   آخر المعاملات:")
                    for trans in recent_trans:
                        trans_type = "دخل" if trans['type'] == 'income' else "مصروف"
                        print(f"   - {trans['description']}: {trans['amount']} ({trans_type}) - {trans['account_name']}")
            else:
                print("❌ لا توجد معاملات")
        except Error as e:
            print(f"❌ خطأ في فحص المعاملات: {e}")
        
        # إغلاق الاتصال
        cursor.close()
        connection.close()
        
        print("\n" + "="*50)
        print("📊 ملخص التشخيص:")
        
        # تحديد المشكلة بناءً على النتائج
        if not tables:
            print("❌ مشكلة خطيرة: قاعدة البيانات فارغة تماماً")
            print("💡 الحل: تشغيل python database/init_db.py")
        elif 'users' not in [list(t.values())[0] for t in tables]:
            print("❌ مشكلة خطيرة: جدول المستخدمين مفقود")
            print("💡 الحل: إعادة تهيئة قاعدة البيانات")
        else:
            print("✅ قاعدة البيانات والجداول موجودة")
            print("💡 المشكلة قد تكون في:")
            print("   - عرض البيانات في الواجهة")
            print("   - تسجيل الدخول بمستخدم خاطئ")
            print("   - مشكلة في استعلامات البيانات")
        
        print("\n🚀 خطوات الإصلاح المقترحة:")
        print("1. أعد تشغيل التطبيق: python main.py")
        print("2. سجل الدخول بحساب admin / 123456")
        print("3. تحقق من وجود البيانات في الواجهة")
        print("4. إذا لم تظهر، استعد نسخة احتياطية")
        print("5. كحل أخير: python database/init_db.py")
        
    else:
        print("❌ فشل الاتصال بقاعدة البيانات")

except Error as e:
    print(f"❌ خطأ في الاتصال: {e}")
    
    if "Unknown database" in str(e):
        print("💡 قاعدة البيانات غير موجودة")
        print("   الحل: تشغيل python database/init_db.py")
    elif "Access denied" in str(e):
        print("💡 خطأ في كلمة المرور أو اسم المستخدم")
        print("   تحقق من إعدادات قاعدة البيانات في config/settings.py")
    elif "Can't connect to MySQL server" in str(e):
        print("💡 خادم MySQL غير متاح")
        print("   تأكد من تشغيل خادم MySQL")
    else:
        print("💡 خطأ غير معروف في الاتصال")

except Exception as e:
    print(f"❌ خطأ عام: {e}")

print("\n📞 إذا استمرت المشكلة:")
print("1. احفظ نتائج هذا الفحص")
print("2. تحقق من تشغيل خادم MySQL")
print("3. راجع إعدادات قاعدة البيانات")
print("4. اطلب المساعدة التقنية")
