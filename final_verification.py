#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق النهائي من توحيد قاعدة البيانات
"""

import os
import mysql.connector
import json
from datetime import datetime

def check_file_cleanup():
    """فحص تنظيف الملفات"""
    print("📁 فحص تنظيف الملفات...")
    
    # الملفات التي يجب أن تكون محذوفة
    files_should_be_removed = [
        "money_manager.db",
        "accounts.json",
        "users.json", 
        "transactions.json"
    ]
    
    cleanup_status = {
        'removed_files': [],
        'remaining_files': [],
        'cleanup_complete': True
    }
    
    for file_path in files_should_be_removed:
        if os.path.exists(file_path):
            print(f"⚠️ {file_path} لا يزال موجوداً")
            cleanup_status['remaining_files'].append(file_path)
            cleanup_status['cleanup_complete'] = False
        else:
            print(f"✅ {file_path} تم حذفه")
            cleanup_status['removed_files'].append(file_path)
    
    # فحص الملفات المؤقتة
    temp_files = []
    for file in os.listdir('.'):
        if (file.startswith('test_') or 
            file.endswith('.tmp') or 
            file.endswith('.temp')):
            temp_files.append(file)
    
    if temp_files:
        print(f"⚠️ ملفات مؤقتة متبقية: {len(temp_files)}")
        cleanup_status['cleanup_complete'] = False
    else:
        print("✅ تم تنظيف جميع الملفات المؤقتة")
    
    return cleanup_status

def check_mysql_connection():
    """فحص الاتصال بـ MySQL"""
    print("\n🔗 فحص الاتصال بـ MySQL...")
    
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        
        print("✅ الاتصال بـ MySQL ناجح")
        
        # فحص الجداول
        cursor.execute("SHOW TABLES")
        tables = [row[list(row.keys())[0]] for row in cursor.fetchall()]
        print(f"📋 الجداول الموجودة: {len(tables)}")
        
        required_tables = [
            'users', 'currencies', 'account_types', 'accounts',
            'account_balances', 'transactions', 'transfers'
        ]
        
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"⚠️ جداول مفقودة: {missing_tables}")
        else:
            print("✅ جميع الجداول المطلوبة موجودة")
        
        # فحص البيانات الأساسية
        data_status = {}
        
        for table in ['users', 'currencies', 'accounts', 'account_balances']:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                count = cursor.fetchone()['count']
                data_status[table] = count
                print(f"   {table}: {count} سجل")
        
        cursor.close()
        connection.close()
        
        return True, data_status
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ MySQL: {e}")
        return False, {}

def check_application_config():
    """فحص إعدادات التطبيق"""
    print("\n⚙️ فحص إعدادات التطبيق...")
    
    config_status = {
        'database_settings': False,
        'main_settings': False,
        'connection_file': False
    }
    
    # فحص ملف إعدادات قاعدة البيانات
    db_settings_file = "config/database_settings.json"
    if os.path.exists(db_settings_file):
        try:
            with open(db_settings_file, 'r', encoding='utf-8') as f:
                db_config = json.load(f)
                
            if db_config.get('database') == 'money_manager':
                print("✅ إعدادات قاعدة البيانات صحيحة")
                config_status['database_settings'] = True
            else:
                print("⚠️ إعدادات قاعدة البيانات غير صحيحة")
        except Exception as e:
            print(f"❌ خطأ في قراءة إعدادات قاعدة البيانات: {e}")
    else:
        print("❌ ملف إعدادات قاعدة البيانات غير موجود")
    
    # فحص ملف الإعدادات الرئيسي
    main_settings_file = "config/settings.py"
    if os.path.exists(main_settings_file):
        print("✅ ملف الإعدادات الرئيسي موجود")
        config_status['main_settings'] = True
    else:
        print("❌ ملف الإعدادات الرئيسي غير موجود")
    
    # فحص ملف الاتصال
    connection_file = "database/connection.py"
    if os.path.exists(connection_file):
        print("✅ ملف الاتصال موجود")
        config_status['connection_file'] = True
    else:
        print("❌ ملف الاتصال غير موجود")
    
    return config_status

def check_backup_files():
    """فحص ملفات النسخ الاحتياطية"""
    print("\n💾 فحص ملفات النسخ الاحتياطية...")
    
    backup_status = {
        'backup_directory_exists': False,
        'backup_count': 0,
        'cleanup_backups': []
    }
    
    # فحص مجلد النسخ الاحتياطية
    if os.path.exists("backups"):
        backup_files = os.listdir("backups")
        backup_status['backup_directory_exists'] = True
        backup_status['backup_count'] = len(backup_files)
        print(f"📦 مجلد النسخ الاحتياطية موجود: {len(backup_files)} ملف")
    else:
        print("⚠️ مجلد النسخ الاحتياطية غير موجود")
    
    # فحص نسخ التنظيف الاحتياطية
    cleanup_backups = []
    for file in os.listdir('.'):
        if (file.startswith('final_cleanup_backup_') or 
            file.startswith('unification_backup_') or
            file.startswith('cleanup_backup_')):
            cleanup_backups.append(file)
    
    backup_status['cleanup_backups'] = cleanup_backups
    
    if cleanup_backups:
        print(f"✅ نسخ احتياطية للتنظيف: {len(cleanup_backups)}")
        for backup in cleanup_backups:
            print(f"   - {backup}")
    else:
        print("⚠️ لا توجد نسخ احتياطية للتنظيف")
    
    return backup_status

def generate_final_report(cleanup_status, mysql_status, config_status, backup_status):
    """إنشاء التقرير النهائي"""
    print("\n📋 إنشاء التقرير النهائي...")
    
    mysql_connected, mysql_data = mysql_status
    
    final_report = {
        'verification_timestamp': datetime.now().isoformat(),
        'database_unification_status': 'completed',
        'cleanup_status': cleanup_status,
        'mysql_connection': mysql_connected,
        'mysql_data': mysql_data,
        'application_config': config_status,
        'backup_status': backup_status,
        'overall_status': 'success' if (
            cleanup_status['cleanup_complete'] and 
            mysql_connected and 
            config_status['database_settings']
        ) else 'partial_success',
        'recommendations': []
    }
    
    # إضافة التوصيات
    if not cleanup_status['cleanup_complete']:
        final_report['recommendations'].append("إكمال تنظيف الملفات المتبقية")
    
    if not mysql_connected:
        final_report['recommendations'].append("إصلاح مشكلة الاتصال بـ MySQL")
    
    if not config_status['database_settings']:
        final_report['recommendations'].append("تحديث إعدادات قاعدة البيانات")
    
    # حفظ التقرير
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"final_verification_report_{timestamp}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم إنشاء التقرير النهائي: {report_file}")
        return report_file, final_report
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {e}")
        return None, final_report

def main():
    """الدالة الرئيسية"""
    print("🔍 التحقق النهائي من توحيد قاعدة البيانات")
    print("=" * 60)
    
    # 1. فحص تنظيف الملفات
    cleanup_status = check_file_cleanup()
    
    # 2. فحص الاتصال بـ MySQL
    mysql_status = check_mysql_connection()
    
    # 3. فحص إعدادات التطبيق
    config_status = check_application_config()
    
    # 4. فحص ملفات النسخ الاحتياطية
    backup_status = check_backup_files()
    
    # 5. إنشاء التقرير النهائي
    report_file, final_report = generate_final_report(
        cleanup_status, mysql_status, config_status, backup_status
    )
    
    # 6. عرض النتائج النهائية
    print("\n🎯 النتائج النهائية:")
    print("=" * 40)
    
    overall_status = final_report['overall_status']
    
    if overall_status == 'success':
        print("🎉 تم توحيد قاعدة البيانات بنجاح!")
        print("✅ التطبيق يستخدم MySQL فقط")
        print("✅ تم تنظيف جميع البيانات المتضاربة")
        print("✅ إعدادات التطبيق صحيحة")
    elif overall_status == 'partial_success':
        print("⚠️ تم توحيد قاعدة البيانات جزئياً")
        print("💡 التوصيات:")
        for recommendation in final_report['recommendations']:
            print(f"   - {recommendation}")
    else:
        print("❌ فشل في توحيد قاعدة البيانات")
    
    if report_file:
        print(f"\n📋 التقرير النهائي: {report_file}")

if __name__ == "__main__":
    main()
