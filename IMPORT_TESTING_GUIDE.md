# 📊 دليل اختبار وظيفة استيراد المعاملات - تقرير شامل

## 🎯 نظرة عامة

تم إنشاء ملفات Excel نموذجية لاختبار وظيفة استيراد المعاملات في تطبيق إدارة الأموال. هذا الدليل يوضح كيفية اختبار الوظيفة بالتفصيل والتحقق من صحة العمليات.

## 📁 الملفات النموذجية المنشأة

### **1. ملف الواردات (`sample_income.xlsx`)**
```
📊 محتويات ملف الواردات:
┌─────────────┬──────────────────┬──────────────┬────────┬─────────────────┐
│ التاريخ      │ الوصف            │ المبلغ        │ العملة │ الحساب          │
├─────────────┼──────────────────┼──────────────┼────────┼─────────────────┤
│ 2024-01-15  │ راتب شهر يناير   │ 15,000.00    │ SAR    │ البنك الأهلي    │
│ 2024-01-20  │ مكافأة أداء      │ 3,000.00     │ SAR    │ البنك الأهلي    │
│ 2024-01-25  │ عمولة مبيعات     │ 2,500.00     │ USD    │ حساب الدولار    │
│ 2024-02-01  │ راتب شهر فبراير  │ 15,000.00    │ SAR    │ البنك الأهلي    │
│ 2024-02-05  │ إيجار عقار       │ 8,000.00     │ AED    │ بنك الإمارات    │
│ 2024-02-10  │ استثمار أرباح    │ 1,200.00     │ USD    │ حساب الدولار    │
│ 2024-02-15  │ مشروع تجاري      │ 450,000.00   │ YER    │ البنك اليمني    │
│ 2024-02-20  │ هدية نقدية      │ 5,000.00     │ SAR    │ محفظة نقدية     │
└─────────────┴──────────────────┴──────────────┴────────┴─────────────────┘

📈 إجمالي المعاملات: 8
💰 إجمالي المبلغ: 499,700.00 (بعملات مختلفة)
🌍 العملات المستخدمة: SAR, USD, AED, YER
```

### **2. ملف المصروفات (`sample_expenses.xlsx`)**
```
📉 محتويات ملف المصروفات:
┌─────────────┬──────────────────┬──────────────┬────────┬─────────────────┐
│ التاريخ      │ الوصف            │ المبلغ        │ العملة │ الحساب          │
├─────────────┼──────────────────┼──────────────┼────────┼─────────────────┤
│ 2024-01-16  │ فاتورة كهرباء    │ 450.00       │ SAR    │ البنك الأهلي    │
│ 2024-01-18  │ تسوق بقالة       │ 320.00       │ SAR    │ محفظة نقدية     │
│ 2024-01-22  │ وقود السيارة     │ 180.00       │ SAR    │ البنك الأهلي    │
│ 2024-01-28  │ مطعم وعشاء       │ 150.00       │ USD    │ حساب الدولار    │
│ 2024-02-02  │ فاتورة إنترنت    │ 200.00       │ SAR    │ البنك الأهلي    │
│ 2024-02-08  │ صيانة السيارة    │ 800.00       │ AED    │ بنك الإمارات    │
│ 2024-02-12  │ أدوية وعلاج      │ 25,000.00    │ YER    │ البنك اليمني    │
│ 2024-02-18  │ ملابس وأحذية     │ 1,200.00     │ SAR    │ البنك الأهلي    │
└─────────────┴──────────────────┴──────────────┴────────┴─────────────────┘

📉 إجمالي المعاملات: 8
💸 إجمالي المبلغ: 28,300.00 (بعملات مختلفة)
🌍 العملات المستخدمة: SAR, USD, AED, YER
```

## 🧪 خطوات الاختبار التفصيلية

### **المرحلة 1: التحضير للاختبار**

#### **1.1 تشغيل التطبيق:**
```bash
python main.py
```

#### **1.2 تسجيل الدخول:**
- استخدم بيانات المستخدم الموجودة
- تأكد من الوصول إلى الواجهة الرئيسية

#### **1.3 التنقل إلى صفحة الاستيراد:**
- انقر على "📊 استيراد المعاملات" في الشريط الجانبي
- تأكد من ظهور واجهة الاستيراد بشكل صحيح

### **المرحلة 2: اختبار استيراد الواردات**

#### **2.1 اختيار ملف الواردات:**
- انقر على زر "📁 اختيار ملف Excel"
- اختر ملف `sample_income.xlsx`
- تحقق من ظهور اسم الملف في الواجهة

#### **2.2 اختيار نوع المعاملة:**
- اختر "وارد" من قائمة نوع المعاملة
- تأكد من أن الخيار محدد بشكل صحيح

#### **2.3 معاينة البيانات:**
- انقر على زر "👁️ معاينة البيانات"
- تحقق من النتائج المتوقعة:
  - ✅ عرض 8 صفوف من البيانات
  - ✅ عرض الأعمدة: التاريخ، الوصف، المبلغ، العملة، الحساب
  - ✅ النصوص العربية تظهر بالاتجاه الصحيح (RTL)
  - ✅ التواريخ بالتنسيق الصحيح
  - ✅ المبالغ بالتنسيق الصحيح
  - ✅ العملات المدعومة (SAR, USD, AED, YER)

#### **2.4 تنفيذ الاستيراد:**
- انقر على زر "📥 استيراد المعاملات"
- تحقق من رسالة النجاح
- تأكد من عدم ظهور رسائل خطأ

#### **2.5 التحقق من النتائج:**
- انتقل إلى لوحة التحكم
- تحقق من ظهور المعاملات الجديدة في قسم "المعاملات الأخيرة"
- تأكد من تحديث الأرصدة في بطاقات العملات

### **المرحلة 3: اختبار استيراد المصروفات**

#### **3.1 اختيار ملف المصروفات:**
- عد إلى صفحة الاستيراد
- انقر على زر "📁 اختيار ملف Excel"
- اختر ملف `sample_expenses.xlsx`

#### **3.2 اختيار نوع المعاملة:**
- اختر "مصروف" من قائمة نوع المعاملة

#### **3.3 معاينة البيانات:**
- انقر على زر "👁️ معاينة البيانات"
- تحقق من النتائج المتوقعة:
  - ✅ عرض 8 صفوف من بيانات المصروفات
  - ✅ جميع الأعمدة تظهر بشكل صحيح
  - ✅ النصوص العربية بالاتجاه الصحيح

#### **3.4 تنفيذ الاستيراد:**
- انقر على زر "📥 استيراد المعاملات"
- تحقق من رسالة النجاح

#### **3.5 التحقق من النتائج:**
- انتقل إلى لوحة التحكم
- تحقق من ظهور المصروفات الجديدة
- تأكد من تحديث الأرصدة (انخفاض بسبب المصروفات)

### **المرحلة 4: التحقق الشامل من النتائج**

#### **4.1 فحص لوحة التحكم:**
- **ملخص الأرصدة بجميع العملات:**
  - تحقق من ظهور الأرصدة المحدثة لكل عملة
  - تأكد من صحة الحسابات الرياضية

- **تفاصيل الحسابات حسب العملة:**
  - تحقق من تحديث أرصدة الحسابات الفردية
  - تأكد من ظهور المعاملات في الحسابات الصحيحة

- **الإحصائيات المالية الشهرية:**
  - تحقق من تحديث إحصائيات الواردات والمصروفات
  - تأكد من صحة الأرقام لكل عملة

- **المعاملات الأخيرة:**
  - تحقق من ظهور المعاملات المستوردة
  - تأكد من عرض النصوص بالعربية ("وارد" و "مصروف")
  - تحقق من صحة التواريخ والمبالغ

#### **4.2 فحص قاعدة البيانات:**
- تحقق من إدراج المعاملات في جدول `transactions`
- تأكد من تحديث أرصدة الحسابات في جدول `account_balances`
- تحقق من ربط المعاملات بالعملات والحسابات الصحيحة

### **المرحلة 5: اختبار حالات الخطأ**

#### **5.1 اختبار ملف بتنسيق خاطئ:**
- أنشئ ملف Excel بأعمدة مختلفة (مثل Date بدلاً من التاريخ)
- حاول استيراده
- تحقق من ظهور رسالة خطأ واضحة

#### **5.2 اختبار ملف بعملة غير مدعومة:**
- أنشئ ملف يحتوي على عملة غير مدعومة (مثل EUR)
- حاول استيراده
- تحقق من رفض الاستيراد أو تصفية الصفوف غير الصحيحة

#### **5.3 اختبار ملف بمبالغ غير صحيحة:**
- أنشئ ملف يحتوي على نصوص في عمود المبلغ
- حاول استيراده
- تحقق من معالجة الخطأ بشكل صحيح

## ✅ معايير النجاح

### **1. وظائف أساسية:**
- ✅ قراءة ملفات Excel بنجاح
- ✅ معاينة البيانات قبل الاستيراد
- ✅ استيراد المعاملات إلى قاعدة البيانات
- ✅ تحديث أرصدة الحسابات

### **2. دعم العملات المتعددة:**
- ✅ دعم العملات الأربع (SAR, YER, AED, USD)
- ✅ ربط المعاملات بالعملات الصحيحة
- ✅ تحديث الأرصدة حسب العملة

### **3. واجهة المستخدم:**
- ✅ النصوص العربية تظهر بالاتجاه الصحيح (RTL)
- ✅ رسائل واضحة للنجاح والخطأ
- ✅ معاينة بيانات سهلة القراءة

### **4. معالجة الأخطاء:**
- ✅ رسائل خطأ واضحة للملفات غير الصحيحة
- ✅ التحقق من صحة البيانات قبل الاستيراد
- ✅ عدم إدراج بيانات خاطئة في قاعدة البيانات

## 🚨 مشاكل محتملة وحلولها

### **مشكلة 1: عدم ظهور النصوص العربية بشكل صحيح**
- **الحل**: تأكد من استخدام مكونات RTL المحسنة
- **التحقق**: فحص استخدام `create_rtl_label` في كود الاستيراد

### **مشكلة 2: عدم تحديث الأرصدة**
- **الحل**: تحقق من دوال تحديث `account_balances`
- **التحقق**: فحص قاعدة البيانات مباشرة

### **مشكلة 3: مشاكل في قراءة ملفات Excel**
- **الحل**: تأكد من تثبيت مكتبة `openpyxl`
- **التحقق**: `pip install openpyxl pandas`

## 📋 تقرير الاختبار النهائي

بعد إكمال جميع مراحل الاختبار، يجب توثيق النتائج:

### **النتائج المتوقعة:**
- ✅ استيراد 8 معاملات واردة بنجاح
- ✅ استيراد 8 معاملات مصروفات بنجاح
- ✅ تحديث أرصدة جميع الحسابات
- ✅ عرض المعاملات في لوحة التحكم
- ✅ دعم كامل للعملات الأربع
- ✅ واجهة مستخدم عربية صحيحة

---

**تاريخ الاختبار**: 2025-07-16  
**الإصدار المختبر**: 1.0.4  
**المختبر**: Augment Agent  
**الحالة**: ✅ جاهز للاختبار
