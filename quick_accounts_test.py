#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from utils.auth import auth_manager

print("🔍 فحص سريع للحسابات")

# تسجيل دخول
users = db.execute_query("SELECT * FROM users LIMIT 1")
if users:
    auth_manager.current_user = users[0]
    user_id = users[0]['id']
    print(f"المستخدم: {users[0]['username']}")
    
    # فحص الحسابات
    accounts = db.execute_query("SELECT * FROM accounts WHERE user_id = %s", (user_id,))
    print(f"عدد الحسابات: {len(accounts) if accounts else 0}")
    
    if accounts:
        for account in accounts:
            print(f"- {account['name']} (ID: {account['id']})")
    
    # اختبار النموذج
    try:
        from database.models import Account
        model_accounts = Account.get_by_user(user_id)
        print(f"النموذج يُرجع: {len(model_accounts) if model_accounts else 0} حساب")
        
        if model_accounts:
            for account in model_accounts:
                print(f"- {account['name']}")
                balances = account.get('balances', [])
                print(f"  أرصدة: {len(balances)}")
                for balance in balances:
                    print(f"    {balance['balance']} {balance['symbol']}")
    except Exception as e:
        print(f"خطأ في النموذج: {e}")
        import traceback
        traceback.print_exc()

else:
    print("لا يوجد مستخدمين")
