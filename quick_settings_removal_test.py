#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 اختبار سريع لإزالة زر الإعدادات...")

try:
    # 1. اختبار الاتصال
    print("1. اختبار الاتصال...")
    from database.connection import db
    if db.is_connected() or db.connect():
        print("✅ الاتصال ناجح")
    else:
        print("❌ فشل الاتصال")
        exit(1)
    
    # 2. اختبار تسجيل الدخول
    print("2. اختبار تسجيل الدخول...")
    from utils.auth import auth_manager
    success, message = auth_manager.login("admin", "123456")
    if success:
        print("✅ تسجيل الدخول ناجح")
    else:
        print(f"❌ فشل تسجيل الدخول: {message}")
        exit(1)
    
    # 3. فحص ملف main_window.py
    print("3. فحص ملف main_window.py...")
    with open('gui/main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من إزالة زر الإعدادات
    if '("settings"' in content and '"⚙️ الإعدادات"' in content:
        print("❌ زر الإعدادات لا يزال موجوداً")
    else:
        print("✅ تم إزالة زر الإعدادات من الشريط الجانبي")
    
    # التحقق من إزالة دالة show_settings
    if 'def show_settings(' in content:
        print("❌ دالة show_settings لا تزال موجودة")
    else:
        print("✅ تم إزالة دالة show_settings")
    
    # التحقق من إزالة دوال الإعدادات الأخرى
    removed_functions = [
        'def load_settings(',
        'def create_user_info_section(',
        'def create_data_management_section(',
        'def create_system_settings_section('
    ]
    
    for func in removed_functions:
        if func in content:
            print(f"❌ الدالة {func} لا تزال موجودة")
        else:
            print(f"✅ تم إزالة الدالة {func}")
    
    # 4. اختبار استيراد MainWindow
    print("4. اختبار استيراد MainWindow...")
    from gui.main_window import MainWindow
    print("✅ تم استيراد MainWindow بنجاح")
    
    # 5. اختبار مكونات واجهة المستخدم
    print("5. اختبار مكونات واجهة المستخدم...")
    from config.colors import COLORS, BUTTON_STYLES
    from config.fonts import create_rtl_label, create_rtl_button
    print("✅ مكونات واجهة المستخدم متاحة")
    
    auth_manager.logout()
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("\n✨ ما تم إزالته:")
    print("   🗑️ زر الإعدادات من الشريط الجانبي")
    print("   🗑️ دالة show_settings")
    print("   🗑️ دالة load_settings")
    print("   🗑️ دالة create_user_info_section")
    print("   🗑️ دالة create_data_management_section")
    print("   🗑️ دالة create_system_settings_section")
    print("   🗑️ دالة create_backup")
    print("   🗑️ دالة restore_backup")
    print("   🗑️ دالة export_to_excel")
    
    print("\n✅ ما تم الحفاظ عليه:")
    print("   🏠 لوحة التحكم")
    print("   💰 الواردات")
    print("   💸 المصروفات")
    print("   🏦 الحسابات")
    print("   🔄 التحويلات")
    print("   🔍 البحث")
    print("   📊 التقارير")
    print("   👥 إدارة المستخدمين")
    
    print("\n🚀 للاستخدام:")
    print("1. شغل التطبيق: python main.py")
    print("2. سجل الدخول: admin / 123456")
    print("3. لن تجد زر الإعدادات في الشريط الجانبي")
    print("4. جميع الوظائف الأخرى تعمل بشكل طبيعي")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
