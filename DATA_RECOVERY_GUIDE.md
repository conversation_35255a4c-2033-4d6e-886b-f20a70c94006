# دليل استعادة البيانات المفقودة - تطبيق إدارة الأموال

## 🚨 المشكلة المبلغ عنها
**اختفاء جميع البيانات من التطبيق:**
- لا توجد حسابات مالية معروضة
- لا توجد معاملات مالية (واردات/مصروفات)
- لا توجد أي بيانات أخرى

## 🔍 التشخيص المحتمل

### **الأسباب المحتملة:**
1. **مشكلة في الاتصال بقاعدة البيانات**
2. **قاعدة البيانات محذوفة أو تالفة**
3. **مشكلة في عرض البيانات في الواجهة**
4. **تسجيل الدخول بمستخدم خاطئ**
5. **مشكلة في استعلامات البيانات**

## 🛠️ خطوات الإصلاح (بالترتيب)

### **الخطوة 1: فحص أساسي سريع**

#### **أ. تحقق من تشغيل خادم MySQL:**
```bash
# في Windows
services.msc
# ابحث عن MySQL وتأكد أنه يعمل

# أو من Command Prompt
net start mysql
```

#### **ب. تحقق من إعدادات قاعدة البيانات:**
افتح ملف `config/settings.py` وتأكد من:
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'mohdam',  # تأكد من صحة كلمة المرور
    'database': 'money_manager',
    'charset': 'utf8mb4'
}
```

### **الخطوة 2: تشغيل أداة التشخيص**

```bash
python fix_data_loss.py
```

هذه الأداة ستفحص:
- ✅ الاتصال بخادم MySQL
- ✅ وجود قاعدة البيانات
- ✅ وجود الجداول الأساسية
- ✅ وجود البيانات

### **الخطوة 3: الحلول حسب نتيجة التشخيص**

#### **إذا كان خادم MySQL لا يعمل:**
```bash
# تشغيل خادم MySQL
net start mysql

# أو إعادة تشغيل الخدمة
net stop mysql
net start mysql
```

#### **إذا كانت قاعدة البيانات مفقودة:**
```bash
# إنشاء قاعدة البيانات والجداول
python database/init_db.py
```

#### **إذا كانت الجداول مفقودة:**
```bash
# إعادة إنشاء الجداول
python database/init_db.py
```

#### **إذا كانت البيانات مفقودة:**
**الخيار 1: استعادة نسخة احتياطية**
1. شغل التطبيق: `python main.py`
2. سجل الدخول كمدير: admin / 123456
3. اذهب إلى "🗄️ إدارة قاعدة البيانات"
4. انقر على "📤 استعادة نسخة احتياطية"
5. اختر أحدث نسخة احتياطية متاحة

**الخيار 2: إعادة إدخال البيانات**
1. شغل التطبيق: `python main.py`
2. سجل الدخول كمدير: admin / 123456
3. أضف الحسابات من "إدارة الحسابات"
4. أضف المعاملات من "الواردات" و "المصروفات"

### **الخطوة 4: فحص عرض البيانات**

#### **إذا كانت البيانات موجودة لكن لا تظهر:**

**أ. تحقق من تسجيل الدخول:**
- تأكد من تسجيل الدخول بالمستخدم الصحيح
- جرب تسجيل الدخول بحساب admin / 123456

**ب. إعادة تشغيل التطبيق:**
```bash
# أغلق التطبيق تماماً ثم شغله مرة أخرى
python main.py
```

**ج. فحص ملفات السجلات:**
ابحث عن ملفات السجلات في مجلد `logs/` للبحث عن أخطاء

### **الخطوة 5: الحلول المتقدمة**

#### **إعادة تهيئة كاملة لقاعدة البيانات:**
```bash
# احذف قاعدة البيانات الحالية (احتياط!)
# ثم أعد إنشاؤها
python database/init_db.py
```

#### **استيراد البيانات من Excel:**
1. شغل التطبيق
2. اذهب إلى "استيراد البيانات"
3. اختر ملف Excel يحتوي على بياناتك
4. اتبع خطوات الاستيراد

## 🔧 أدوات التشخيص المتاحة

### **1. أداة الإصلاح الشاملة:**
```bash
python fix_data_loss.py
```

### **2. فحص مباشر لقاعدة البيانات:**
```bash
python direct_db_check.py
```

### **3. تشخيص شامل:**
```bash
python diagnose_data_loss.py
```

## 🛡️ الوقاية من تكرار المشكلة

### **1. تفعيل النسخ الاحتياطي التلقائي:**
1. اذهب إلى "إدارة قاعدة البيانات"
2. فعل "النسخ الاحتياطي التلقائي"
3. اختر فترة مناسبة (يومياً مثلاً)

### **2. إنشاء نسخ احتياطية يدوية:**
- قم بإنشاء نسخة احتياطية أسبوعياً على الأقل
- احفظ النسخ في مكان آمن خارج مجلد التطبيق

### **3. مراقبة حالة قاعدة البيانات:**
- تحقق دورياً من عمل خادم MySQL
- راقب مساحة القرص الصلب
- تأكد من عدم وجود أخطاء في السجلات

## 📞 طلب المساعدة

### **إذا لم تنجح الحلول أعلاه:**

**معلومات مطلوبة للدعم:**
1. نتائج تشغيل `python fix_data_loss.py`
2. رسائل الخطأ (إن وجدت)
3. آخر إجراء تم قبل اختفاء البيانات
4. هل تم تشغيل أي تحديثات أو تغييرات مؤخراً؟

**خطوات الاتصال:**
1. احفظ جميع نتائج التشخيص
2. اكتب وصف مفصل للمشكلة
3. أرفق ملفات السجلات إن أمكن
4. اطلب المساعدة التقنية

## ⚡ حلول سريعة

### **للمشاكل الشائعة:**

**1. "لا يمكن الاتصال بقاعدة البيانات":**
```bash
net start mysql
python main.py
```

**2. "قاعدة البيانات فارغة":**
```bash
python database/init_db.py
python main.py
```

**3. "البيانات لا تظهر":**
- أعد تشغيل التطبيق
- سجل الدخول بحساب admin
- تحقق من وجود البيانات في "إدارة قاعدة البيانات"

**4. "فقدان البيانات بعد تحديث":**
- استعد أحدث نسخة احتياطية
- أو أعد إدخال البيانات يدوياً

## 📊 ملخص سريع

| المشكلة | الحل السريع |
|---------|-------------|
| خادم MySQL لا يعمل | `net start mysql` |
| قاعدة البيانات مفقودة | `python database/init_db.py` |
| البيانات مفقودة | استعادة نسخة احتياطية |
| البيانات لا تظهر | إعادة تشغيل التطبيق |
| خطأ في الاتصال | فحص إعدادات `config/settings.py` |

---

**💡 نصيحة مهمة:** احتفظ دائماً بنسخ احتياطية منتظمة لتجنب فقدان البيانات في المستقبل!
