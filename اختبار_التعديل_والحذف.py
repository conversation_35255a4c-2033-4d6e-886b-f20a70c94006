#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لميزات التعديل والحذف الجديدة
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_all_edit_delete_features():
    """اختبار جميع ميزات التعديل والحذف"""
    print("🧪 اختبار شامل لميزات التعديل والحذف")
    print("=" * 70)
    
    try:
        from database.connection import db
        from database.models import Transaction, Account, Transfer, Category
        
        # التحقق من الاتصال
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return False
        
        print("✅ الاتصال بقاعدة البيانات نشط")
        
        # 1. اختبار دوال المعاملات
        print("\n💰 1. اختبار دوال المعاملات:")
        
        # إنشاء معاملة تجريبية
        test_transaction_id = Transaction.create(
            user_id=1,
            account_id=1,
            transaction_type="income",
            amount=1000.0,
            currency_id=1,
            category_id=1,
            description="معاملة تجريبية للاختبار"
        )
        
        if test_transaction_id > 0:
            print(f"   ✅ إنشاء معاملة تجريبية: {test_transaction_id}")
            
            # اختبار التعديل
            success = Transaction.update(
                transaction_id=test_transaction_id,
                amount=1500.0,
                description="معاملة محدثة"
            )
            print(f"   {'✅' if success else '❌'} تعديل المعاملة")
            
            # اختبار الحذف
            success = Transaction.delete(test_transaction_id)
            print(f"   {'✅' if success else '❌'} حذف المعاملة")
        else:
            print("   ❌ فشل في إنشاء معاملة تجريبية")
        
        # 2. اختبار دوال الحسابات
        print("\n🏦 2. اختبار دوال الحسابات:")
        
        # إنشاء حساب تجريبي
        test_account_id = db.execute_insert("""
            INSERT INTO accounts (user_id, name, account_type_id, currency_id, initial_balance, current_balance)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (1, "حساب تجريبي", 1, 1, 0.0, 0.0))
        
        if test_account_id > 0:
            print(f"   ✅ إنشاء حساب تجريبي: {test_account_id}")
            
            # اختبار التعديل
            success = Account.update(
                account_id=test_account_id,
                name="حساب محدث",
                description="وصف محدث"
            )
            print(f"   {'✅' if success else '❌'} تعديل الحساب")
            
            # اختبار إلغاء التفعيل
            success = Account.deactivate(test_account_id)
            print(f"   {'✅' if success else '❌'} إلغاء تفعيل الحساب")
            
            # اختبار إعادة التفعيل
            success = Account.activate(test_account_id)
            print(f"   {'✅' if success else '❌'} إعادة تفعيل الحساب")
            
            # اختبار الحذف
            success, message = Account.delete(test_account_id)
            print(f"   {'✅' if success else '❌'} حذف الحساب: {message}")
        else:
            print("   ❌ فشل في إنشاء حساب تجريبي")
        
        # 3. اختبار دوال التحويلات
        print("\n🔄 3. اختبار دوال التحويلات:")
        
        # إنشاء تحويل تجريبي
        test_transfer_id = Transfer.create(
            user_id=1,
            from_account_id=1,
            to_account_id=2,
            amount=500.0,
            currency_id=1,
            description="تحويل تجريبي"
        )
        
        if test_transfer_id > 0:
            print(f"   ✅ إنشاء تحويل تجريبي: {test_transfer_id}")
            
            # اختبار التعديل
            success = Transfer.update(
                transfer_id=test_transfer_id,
                amount=750.0,
                description="تحويل محدث"
            )
            print(f"   {'✅' if success else '❌'} تعديل التحويل")
            
            # اختبار الحذف
            success = Transfer.delete(test_transfer_id)
            print(f"   {'✅' if success else '❌'} حذف التحويل")
        else:
            print("   ❌ فشل في إنشاء تحويل تجريبي")
        
        # 4. اختبار دوال الأقسام (تم اختبارها مسبقاً)
        print("\n📂 4. اختبار دوال الأقسام:")
        
        # إنشاء قسم تجريبي
        test_category_id = Category.create_income_category(
            name="قسم تجريبي للاختبار",
            description="قسم للاختبار الشامل",
            icon="🧪",
            color="#FF5722"
        )
        
        if test_category_id > 0:
            print(f"   ✅ إنشاء قسم تجريبي: {test_category_id}")
            
            # اختبار التعديل
            success = Category.update_income_category(
                test_category_id,
                name="قسم محدث",
                description="قسم محدث للاختبار"
            )
            print(f"   {'✅' if success else '❌'} تعديل القسم")
            
            # اختبار إلغاء التفعيل
            success = Category.deactivate_income_category(test_category_id)
            print(f"   {'✅' if success else '❌'} إلغاء تفعيل القسم")
            
            # اختبار إعادة التفعيل
            success = Category.activate_income_category(test_category_id)
            print(f"   {'✅' if success else '❌'} إعادة تفعيل القسم")
            
            # اختبار الحذف
            success, message = Category.delete_income_category(test_category_id)
            print(f"   {'✅' if success else '❌'} حذف القسم: {message}")
        else:
            print("   ❌ فشل في إنشاء قسم تجريبي")
        
        # 5. اختبار الواجهة
        print("\n🖥️ 5. اختبار مكونات الواجهة:")
        
        try:
            from gui.main_window import MainWindow
            
            # التحقق من وجود الدوال الجديدة
            main_window = MainWindow()
            
            new_methods = [
                'show_edit_transaction_dialog',
                'save_transaction_changes',
                'delete_transaction',
                'show_edit_transfer_dialog',
                'save_transfer_changes',
                'delete_transfer',
                'show_edit_account_dialog',
                'save_account_changes',
                'toggle_account_status',
                'delete_account'
            ]
            
            for method in new_methods:
                if hasattr(main_window, method):
                    print(f"   ✅ دالة {method} موجودة")
                else:
                    print(f"   ❌ دالة {method} غير موجودة")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الواجهة: {e}")
        
        print("\n" + "=" * 70)
        print("🎉 انتهى الاختبار الشامل!")
        print("\n💡 الميزات المضافة:")
        print("   ✅ تعديل وحذف المعاملات (الواردات والمصروفات)")
        print("   ✅ تعديل وحذف التحويلات")
        print("   ✅ تعديل وحذف الحسابات")
        print("   ✅ تعديل وحذف الأقسام")
        print("   ✅ واجهات مستخدم شاملة للتعديل")
        print("   ✅ حماية من الحذف العرضي")
        print("   ✅ تحديث الأرصدة تلقائياً")
        print("   ✅ تسجيل جميع العمليات")
        
        print("\n🚀 لاستخدام الميزات:")
        print("   1. شغل التطبيق: python main.py")
        print("   2. سجل دخولك")
        print("   3. ستجد أزرار التعديل والحذف في جميع البطاقات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_all_edit_delete_features()
