#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من توافق ملفات Excel مع قاعدة البيانات
"""

import pandas as pd
import os

def verify_excel_compatibility():
    """التحقق من توافق ملفات Excel مع قاعدة البيانات"""
    
    print("🔍 التحقق من توافق ملفات Excel...")
    print("=" * 50)
    
    # 1. التحقق من الاتصال بقاعدة البيانات
    try:
        from database.connection import db
        from utils.auth import auth_manager
        
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return False
        
        # تسجيل دخول تجريبي
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل في تسجيل الدخول: {message}")
            return False
        
        user_id = auth_manager.current_user['id']
        print("✅ تم الاتصال وتسجيل الدخول بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False
    
    # 2. جلب الحسابات والعملات من قاعدة البيانات
    try:
        # جلب الحسابات
        accounts_query = "SELECT id, name FROM accounts WHERE user_id = %s AND is_active = 1"
        accounts = db.execute_query(accounts_query, (user_id,))
        accounts_dict = {acc['name']: acc['id'] for acc in accounts}
        
        # جلب العملات
        currencies_query = "SELECT id, code, symbol FROM currencies WHERE is_active = 1"
        currencies = db.execute_query(currencies_query)
        currencies_dict = {}
        for curr in currencies:
            currencies_dict[curr['code']] = curr['id']
            currencies_dict[curr['symbol']] = curr['id']
        
        print(f"📊 الحسابات المتاحة: {list(accounts_dict.keys())}")
        print(f"💱 العملات المدعومة: {list(set([k for k in currencies_dict.keys() if len(k) <= 3]))}")
        
    except Exception as e:
        print(f"❌ خطأ في جلب البيانات: {e}")
        return False
    
    # 3. فحص ملفات Excel
    excel_files = [
        'sample_excel_files/نموذج_واردات.xlsx',
        'sample_excel_files/نموذج_مصروفات.xlsx'
    ]
    
    all_compatible = True
    
    for file_path in excel_files:
        print(f"\n📄 فحص الملف: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"❌ الملف غير موجود")
            all_compatible = False
            continue
        
        try:
            df = pd.read_excel(file_path)
            print(f"✅ تم قراءة الملف - {len(df)} صف")
            
            # فحص كل صف
            compatible_rows = 0
            incompatible_rows = 0
            
            for index, row in df.iterrows():
                row_compatible = True
                issues = []
                
                # فحص المبلغ
                try:
                    amount = float(str(row.get('المبلغ', 0)).replace(',', '').strip())
                    if amount <= 0:
                        issues.append("مبلغ غير صحيح")
                        row_compatible = False
                except:
                    issues.append("مبلغ غير صحيح")
                    row_compatible = False
                
                # فحص الحساب
                account_name = str(row.get('الحساب', '')).strip()
                if account_name not in accounts_dict:
                    issues.append(f"حساب غير موجود: {account_name}")
                    row_compatible = False
                
                # فحص العملة
                currency_code = str(row.get('العملة', '')).strip()
                if currency_code not in currencies_dict:
                    issues.append(f"عملة غير مدعومة: {currency_code}")
                    row_compatible = False
                
                # فحص التاريخ
                date_str = str(row.get('التاريخ', ''))
                if not date_str or date_str == 'nan':
                    issues.append("تاريخ مفقود")
                    row_compatible = False
                
                if row_compatible:
                    compatible_rows += 1
                else:
                    incompatible_rows += 1
                    print(f"   ❌ الصف {index + 1}: {', '.join(issues)}")
            
            print(f"   ✅ صفوف متوافقة: {compatible_rows}")
            print(f"   ❌ صفوف غير متوافقة: {incompatible_rows}")
            
            if incompatible_rows > 0:
                all_compatible = False
                
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {e}")
            all_compatible = False
    
    # 4. النتيجة النهائية
    print(f"\n{'='*50}")
    if all_compatible:
        print("🎉 جميع ملفات Excel متوافقة مع قاعدة البيانات!")
        print("✅ يمكن استيراد جميع البيانات بنجاح")
    else:
        print("⚠️ هناك مشاكل في التوافق")
        print("💡 تحتاج لإصلاح البيانات قبل الاستيراد")
    
    return all_compatible

if __name__ == "__main__":
    verify_excel_compatibility()
