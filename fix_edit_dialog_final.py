#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح نهائي لمشاكل نافذة التعديل
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_xampp_status():
    """فحص حالة XAMPP"""
    print("🔌 فحص حالة XAMPP...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        # محاولة الاتصال
        if db.connect():
            print("✅ XAMPP يعمل بشكل صحيح")
            
            # اختبار قاعدة البيانات
            result = db.execute_query("SELECT DATABASE() as db_name")
            if result:
                print(f"   قاعدة البيانات: {result[0]['db_name']}")
            
            return True
        else:
            print("❌ XAMPP غير متصل!")
            print("\n🔧 خطوات الإصلاح:")
            print("1. افتح XAMPP Control Panel")
            print("2. اضغط 'Start' بجانب Apache")
            print("3. اضغط 'Start' بجانب MySQL")
            print("4. تأكد من ظهور اللون الأخضر")
            print("5. أعد تشغيل هذا الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def fix_database_schema():
    """إصلاح مخطط قاعدة البيانات"""
    print("\n📋 إصلاح مخطط قاعدة البيانات...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        # فحص جدول transactions
        columns = db.execute_query("DESCRIBE transactions")
        column_names = [col['Field'] for col in columns]
        
        # إضافة عمود category_name إذا لم يكن موجوداً
        if 'category_name' not in column_names:
            print("🔧 إضافة عمود category_name...")
            db.execute_update("""
                ALTER TABLE transactions 
                ADD COLUMN category_name VARCHAR(100) NULL 
                COMMENT 'اسم التصنيف المدخل يدوياً'
            """)
            print("✅ تم إضافة عمود category_name")
        else:
            print("✅ عمود category_name موجود")
        
        # إضافة عمود updated_at إذا لم يكن موجوداً
        if 'updated_at' not in column_names:
            print("🔧 إضافة عمود updated_at...")
            db.execute_update("""
                ALTER TABLE transactions 
                ADD COLUMN updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                COMMENT 'تاريخ آخر تحديث'
            """)
            print("✅ تم إضافة عمود updated_at")
        else:
            print("✅ عمود updated_at موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح المخطط: {e}")
        return False

def test_transaction_update_function():
    """اختبار دالة تحديث المعاملة"""
    print("\n🧪 اختبار دالة تحديث المعاملة...")
    print("=" * 50)
    
    try:
        from database.models import Transaction
        from database.connection import db
        
        # البحث عن معاملة للاختبار
        transactions = db.execute_query("""
            SELECT id, amount, description, transaction_type 
            FROM transactions 
            LIMIT 1
        """)
        
        if not transactions:
            print("⚠️ لا توجد معاملات للاختبار")
            print("   أضف بعض المعاملات أولاً ثم أعد الاختبار")
            return True
        
        test_transaction = transactions[0]
        transaction_id = test_transaction['id']
        original_description = test_transaction['description']
        
        print(f"📝 اختبار المعاملة رقم: {transaction_id}")
        
        # اختبار 1: تحديث الوصف فقط
        test_description = f"اختبار - {original_description}"
        print("   اختبار 1: تحديث الوصف...")
        
        success = Transaction.update(
            transaction_id=transaction_id,
            description=test_description
        )
        
        if success:
            print("   ✅ نجح تحديث الوصف")
            
            # التحقق من التحديث
            updated = db.execute_query(
                "SELECT description FROM transactions WHERE id = %s",
                (transaction_id,)
            )
            
            if updated and updated[0]['description'] == test_description:
                print("   ✅ تم حفظ التحديث في قاعدة البيانات")
                
                # إعادة الوصف الأصلي
                Transaction.update(
                    transaction_id=transaction_id,
                    description=original_description
                )
                print("   ✅ تم إعادة الوصف الأصلي")
                
            else:
                print("   ❌ لم يتم حفظ التحديث في قاعدة البيانات")
                return False
        else:
            print("   ❌ فشل في تحديث الوصف")
            return False
        
        # اختبار 2: تحديث متعدد الحقول
        print("   اختبار 2: تحديث متعدد الحقول...")
        
        success = Transaction.update(
            transaction_id=transaction_id,
            category_name="اختبار تصنيف",
            description="اختبار وصف متعدد"
        )
        
        if success:
            print("   ✅ نجح التحديث متعدد الحقول")
            
            # إعادة القيم الأصلية
            Transaction.update(
                transaction_id=transaction_id,
                category_name=None,
                description=original_description
            )
            print("   ✅ تم إعادة القيم الأصلية")
        else:
            print("   ❌ فشل في التحديث متعدد الحقول")
            return False
        
        print("🎉 جميع اختبارات التحديث نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحديث: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_data():
    """إنشاء بيانات تجريبية للاختبار"""
    print("\n📊 إنشاء بيانات تجريبية...")
    print("=" * 50)
    
    try:
        from database.connection import db
        from utils.auth import auth_manager
        
        # التحقق من وجود مستخدم
        if not auth_manager.current_user:
            print("⚠️ لا يوجد مستخدم مسجل دخول")
            print("   سيتم إنشاء مستخدم تجريبي...")
            
            # إنشاء مستخدم تجريبي
            user_result = db.execute_query(
                "SELECT id FROM users LIMIT 1"
            )
            
            if user_result:
                auth_manager.current_user = {'id': user_result[0]['id'], 'full_name': 'مستخدم تجريبي'}
                print(f"   ✅ تم تعيين المستخدم: {user_result[0]['id']}")
            else:
                print("   ❌ لا يوجد مستخدمون في قاعدة البيانات")
                return False
        
        user_id = auth_manager.current_user['id']
        
        # التحقق من وجود حسابات
        accounts = db.execute_query(
            "SELECT COUNT(*) as count FROM accounts WHERE user_id = %s",
            (user_id,)
        )
        
        if accounts[0]['count'] == 0:
            print("   إنشاء حساب تجريبي...")
            
            # إنشاء حساب تجريبي
            db.execute_update("""
                INSERT INTO accounts (user_id, name, account_type, currency_id, balance, is_active)
                VALUES (%s, 'حساب تجريبي', 'cash', 1, 1000.00, TRUE)
            """, (user_id,))
            print("   ✅ تم إنشاء حساب تجريبي")
        
        # التحقق من وجود معاملات
        transactions = db.execute_query(
            "SELECT COUNT(*) as count FROM transactions WHERE user_id = %s",
            (user_id,)
        )
        
        if transactions[0]['count'] == 0:
            print("   إنشاء معاملة تجريبية...")
            
            # الحصول على معرف الحساب
            account = db.execute_query(
                "SELECT id FROM accounts WHERE user_id = %s LIMIT 1",
                (user_id,)
            )
            
            if account:
                # إنشاء معاملة تجريبية
                db.execute_update("""
                    INSERT INTO transactions (user_id, account_id, currency_id, transaction_type, amount, description, transaction_date)
                    VALUES (%s, %s, 1, 'income', 500.00, 'معاملة تجريبية للاختبار', CURDATE())
                """, (user_id, account[0]['id']))
                print("   ✅ تم إنشاء معاملة تجريبية")
        
        print("✅ البيانات التجريبية جاهزة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح نهائي لمشاكل نافذة التعديل")
    print("=" * 60)
    
    # 1. فحص XAMPP
    if not check_xampp_status():
        input("\nاضغط Enter بعد تشغيل XAMPP...")
        return
    
    # 2. إصلاح مخطط قاعدة البيانات
    if not fix_database_schema():
        print("❌ فشل في إصلاح مخطط قاعدة البيانات")
        input("\nاضغط Enter للخروج...")
        return
    
    # 3. إنشاء بيانات تجريبية إذا لزم الأمر
    create_test_data()
    
    # 4. اختبار دالة التحديث
    if not test_transaction_update_function():
        print("❌ فشل في اختبار دالة التحديث")
        input("\nاضغط Enter للخروج...")
        return
    
    print("\n" + "=" * 60)
    print("🎉 تم إصلاح جميع المشاكل بنجاح!")
    print("\n✅ المشاكل التي تم إصلاحها:")
    print("1. ✅ الاتصال بـ XAMPP")
    print("2. ✅ مخطط قاعدة البيانات")
    print("3. ✅ دالة تحديث المعاملات")
    print("4. ✅ البيانات التجريبية")
    
    print("\n🚀 يمكنك الآن:")
    print("1. تشغيل التطبيق: python main.py")
    print("2. تسجيل الدخول")
    print("3. الذهاب لقسم الواردات أو المصروفات")
    print("4. اضغط على زر 'تعديل' بجانب أي معاملة")
    print("5. عدّل أي حقل واضغط 'حفظ التغييرات'")
    print("6. يجب أن يتم حفظ التعديلات بنجاح!")
    
    print("\n💡 ملاحظات:")
    print("- ستظهر رسائل تطوير في وحدة التحكم")
    print("- إذا ظهرت مشاكل، راجع وحدة التحكم")
    print("- تأكد من بقاء XAMPP يعمل")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
