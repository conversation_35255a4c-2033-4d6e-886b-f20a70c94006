#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية لبرنامج إدارة الأموال
"""

import sys
import os
from datetime import datetime, date, timedelta
import random

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from database.models import Account, Transaction, Transfer
from utils.auth import auth_manager

def create_sample_accounts(user_id):
    """إنشاء حسابات تجريبية"""
    print("إنشاء حسابات تجريبية...")

    sample_accounts = [
        {
            'name': 'الصندوق النقدي',
            'account_type_id': 1,  # صندوق نقدي
            'currency_id': 1,      # ريال سعودي
            'initial_balance': 5000.00,
            'description': 'النقد المتوفر في اليد'
        },
        {
            'name': 'حساب الراجحي',
            'account_type_id': 2,  # حساب بنكي
            'currency_id': 1,      # ريال سعودي
            'initial_balance': 25000.00,
            'description': 'الحساب الجاري في بنك الراجحي'
        },
        {
            'name': 'حساب التوفير',
            'account_type_id': 6,  # حساب توفير
            'currency_id': 1,      # ريال سعودي
            'initial_balance': 50000.00,
            'description': 'حساب التوفير طويل المدى'
        },
        {
            'name': 'محفظة PayPal',
            'account_type_id': 4,  # محفظة إلكترونية
            'currency_id': 2,      # دولار أمريكي
            'initial_balance': 1200.00,
            'description': 'محفظة PayPal للمعاملات الدولية'
        },
        {
            'name': 'بطاقة فيزا',
            'account_type_id': 3,  # بطاقة ائتمان
            'currency_id': 1,      # ريال سعودي
            'initial_balance': -2500.00,
            'description': 'بطاقة ائتمان فيزا'
        }
    ]

    created_accounts = []
    for account_data in sample_accounts:
        account_id = Account.create(
            user_id=user_id,
            name=account_data['name'],
            account_type_id=account_data['account_type_id'],
            currency_id=account_data['currency_id'],
            initial_balance=account_data['initial_balance'],
            description=account_data['description']
        )

        if account_id > 0:
            created_accounts.append(account_id)
            print(f"✅ تم إنشاء الحساب: {account_data['name']}")
        else:
            print(f"❌ فشل في إنشاء الحساب: {account_data['name']}")

    return created_accounts

def create_sample_transactions(user_id, account_ids):
    """إنشاء معاملات تجريبية"""
    print("إنشاء معاملات تجريبية...")

    # معاملات الواردات
    income_transactions = [
        {
            'account_id': account_ids[1],  # حساب الراجحي
            'amount': 8000.00,
            'currency_id': 1,
            'category_id': 1,  # راتب
            'description': 'راتب شهر ديسمبر',
            'days_ago': 5
        },
        {
            'account_id': account_ids[0],  # الصندوق النقدي
            'amount': 1500.00,
            'currency_id': 1,
            'category_id': 2,  # أعمال
            'description': 'دخل من مشروع جانبي',
            'days_ago': 10
        },
        {
            'account_id': account_ids[3],  # محفظة PayPal
            'amount': 500.00,
            'currency_id': 2,
            'category_id': 4,  # عمل حر
            'description': 'مشروع تطوير موقع',
            'days_ago': 15
        },
        {
            'account_id': account_ids[2],  # حساب التوفير
            'amount': 200.00,
            'currency_id': 1,
            'category_id': 3,  # استثمار
            'description': 'أرباح استثمارية',
            'days_ago': 20
        }
    ]

    # معاملات المصروفات
    expense_transactions = [
        {
            'account_id': account_ids[0],  # الصندوق النقدي
            'amount': 300.00,
            'currency_id': 1,
            'category_id': 1,  # طعام وشراب
            'description': 'تسوق من السوبر ماركت',
            'days_ago': 1
        },
        {
            'account_id': account_ids[1],  # حساب الراجحي
            'amount': 1200.00,
            'currency_id': 1,
            'category_id': 3,  # سكن
            'description': 'إيجار الشقة',
            'days_ago': 3
        },
        {
            'account_id': account_ids[4],  # بطاقة فيزا
            'amount': 150.00,
            'currency_id': 1,
            'category_id': 2,  # مواصلات
            'description': 'وقود السيارة',
            'days_ago': 2
        },
        {
            'account_id': account_ids[1],  # حساب الراجحي
            'amount': 400.00,
            'currency_id': 1,
            'category_id': 4,  # فواتير
            'description': 'فاتورة الكهرباء والماء',
            'days_ago': 7
        },
        {
            'account_id': account_ids[0],  # الصندوق النقدي
            'amount': 80.00,
            'currency_id': 1,
            'category_id': 7,  # ترفيه
            'description': 'تذاكر السينما',
            'days_ago': 4
        },
        {
            'account_id': account_ids[1],  # حساب الراجحي
            'amount': 250.00,
            'currency_id': 1,
            'category_id': 8,  # تسوق
            'description': 'ملابس جديدة',
            'days_ago': 6
        }
    ]

    created_transactions = []

    # إنشاء الواردات
    for income in income_transactions:
        transaction_date = date.today() - timedelta(days=income['days_ago'])
        transaction_id = Transaction.create(
            user_id=user_id,
            account_id=income['account_id'],
            transaction_type='income',
            amount=income['amount'],
            currency_id=income['currency_id'],
            category_id=income['category_id'],
            description=income['description'],
            transaction_date=transaction_date
        )

        if transaction_id > 0:
            created_transactions.append(transaction_id)
            print(f"✅ تم إنشاء وارد: {income['description']}")
        else:
            print(f"❌ فشل في إنشاء وارد: {income['description']}")

    # إنشاء المصروفات
    for expense in expense_transactions:
        transaction_date = date.today() - timedelta(days=expense['days_ago'])
        transaction_id = Transaction.create(
            user_id=user_id,
            account_id=expense['account_id'],
            transaction_type='expense',
            amount=expense['amount'],
            currency_id=expense['currency_id'],
            category_id=expense['category_id'],
            description=expense['description'],
            transaction_date=transaction_date
        )

        if transaction_id > 0:
            created_transactions.append(transaction_id)
            print(f"✅ تم إنشاء مصروف: {expense['description']}")
        else:
            print(f"❌ فشل في إنشاء مصروف: {expense['description']}")

    return created_transactions

def create_sample_transfers(user_id, account_ids):
    """إنشاء تحويلات تجريبية"""
    print("إنشاء تحويلات تجريبية...")

    sample_transfers = [
        {
            'from_account_id': account_ids[1],  # من حساب الراجحي
            'to_account_id': account_ids[0],    # إلى الصندوق النقدي
            'amount': 1000.00,
            'currency_id': 1,
            'description': 'سحب نقدي من البنك',
            'days_ago': 8
        },
        {
            'from_account_id': account_ids[1],  # من حساب الراجحي
            'to_account_id': account_ids[2],    # إلى حساب التوفير
            'amount': 2000.00,
            'currency_id': 1,
            'description': 'تحويل للتوفير',
            'days_ago': 12
        }
    ]

    created_transfers = []
    for transfer in sample_transfers:
        transfer_date = date.today() - timedelta(days=transfer['days_ago'])
        transfer_id = Transfer.create(
            user_id=user_id,
            from_account_id=transfer['from_account_id'],
            to_account_id=transfer['to_account_id'],
            amount=transfer['amount'],
            currency_id=transfer['currency_id'],
            description=transfer['description'],
            transfer_date=transfer_date
        )

        if transfer_id > 0:
            created_transfers.append(transfer_id)
            print(f"✅ تم إنشاء تحويل: {transfer['description']}")
        else:
            print(f"❌ فشل في إنشاء تحويل: {transfer['description']}")

    return created_transfers

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        print("🔄 اختبار الاتصال بقاعدة البيانات...")

        if db.connect():
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")

            # اختبار استعلام بسيط
            result = db.execute_query("SELECT 1 as test")
            if result:
                print("✅ قاعدة البيانات تعمل بشكل صحيح")
                return True
            else:
                print("❌ مشكلة في تنفيذ الاستعلامات")
                return False
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False

    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية لتشغيل البرنامج النصي"""
    print("=" * 60)
    print("🏦 إنشاء البيانات التجريبية لمدير الأموال")
    print("=" * 60)

    # اختبار الاتصال أولاً
    if not test_database_connection():
        print("\n💡 تأكد من:")
        print("   • تشغيل MySQL Server")
        print("   • صحة إعدادات قاعدة البيانات في config/settings.py")
        print("   • وجود قاعدة البيانات money_manager")
        print("\n🔧 يمكنك تشغيل reset_database.py لإعادة إنشاء قاعدة البيانات")
        return False

    # معلومات المستخدم التجريبي
    username = "admin"
    password = "123456"
    full_name = "المدير الافتراضي"
    email = "<EMAIL>"

    try:
        # تسجيل المستخدم التجريبي
        if not auth_manager.user_exists(username, email):
            print(f"📝 إنشاء المستخدم '{username}'...")
            success, message = auth_manager.register_user(username, password, full_name, email=email)
            if not success:
                print(f"❌ فشل تسجيل المستخدم التجريبي: {message}")
                db.close()
                return False
            print(f"✅ تم تسجيل المستخدم '{username}' بنجاح")
        else:
            print(f"✅ المستخدم '{username}' موجود بالفعل")

        # الحصول على معرّف المستخدم
        user = db.execute_query("SELECT id FROM users WHERE username = %s", (username,))
        if not user:
            print("❌ لم يتم العثور على المستخدم بعد التسجيل/التحقق")
            db.close()
            return False

        user_id = user[0]['id']

        # إنشاء الحسابات التجريبية
        account_ids = create_sample_accounts(user_id)
        if not account_ids:
            print("❌ لم يتم إنشاء أي حسابات، لا يمكن المتابعة")
            db.close()
            return False

        # إنشاء المعاملات التجريبية
        create_sample_transactions(user_id, account_ids)

        # إنشاء التحويلات التجريبية
        create_sample_transfers(user_id, account_ids)

        print("\n🎉 اكتمل إنشاء البيانات التجريبية بنجاح!")
        print("\n📋 بيانات تسجيل الدخول:")
        print(f"   اسم المستخدم: {username}")
        print(f"   كلمة المرور: {password}")
        print(f"   الاسم الكامل: {full_name}")
        print(f"   البريد الإلكتروني: {email}")

        # إغلاق الاتصال بقاعدة البيانات
        db.close()
        return True

    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        db.close()
        return False

if __name__ == '__main__':
    main()
