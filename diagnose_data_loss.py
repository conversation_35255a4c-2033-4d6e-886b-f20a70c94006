#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة اختفاء البيانات من تطبيق إدارة الأموال
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_data_loss():
    """تشخيص شامل لمشكلة اختفاء البيانات"""
    print("🔍 تشخيص مشكلة اختفاء البيانات من تطبيق إدارة الأموال")
    print("=" * 65)
    
    try:
        # 1. اختبار الاتصال بقاعدة البيانات
        print("1. اختبار الاتصال بقاعدة البيانات...")
        from database.connection import db
        
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            print("   السبب المحتمل: مشكلة في الاتصال بقاعدة البيانات")
            return False
        print("✅ الاتصال بقاعدة البيانات ناجح")
        
        # 2. فحص وجود الجداول الأساسية
        print("\n2. فحص وجود الجداول الأساسية...")
        essential_tables = ['users', 'accounts', 'transactions', 'income_categories', 'expense_categories', 'currencies']
        
        for table in essential_tables:
            try:
                result = db.execute_query(f"SHOW TABLES LIKE '{table}'")
                if result:
                    print(f"✅ جدول {table} موجود")
                else:
                    print(f"❌ جدول {table} مفقود")
                    return False
            except Exception as e:
                print(f"❌ خطأ في فحص جدول {table}: {e}")
                return False
        
        # 3. فحص عدد السجلات في كل جدول
        print("\n3. فحص عدد السجلات في الجداول الأساسية...")
        
        tables_data = {}
        for table in essential_tables:
            try:
                result = db.execute_query(f"SELECT COUNT(*) as count FROM {table}")
                count = result[0]['count'] if result else 0
                tables_data[table] = count
                
                if count > 0:
                    print(f"✅ جدول {table}: {count} سجل")
                else:
                    print(f"⚠️ جدول {table}: فارغ (0 سجل)")
            except Exception as e:
                print(f"❌ خطأ في فحص جدول {table}: {e}")
                tables_data[table] = "خطأ"
        
        # 4. فحص المستخدمين
        print("\n4. فحص المستخدمين...")
        try:
            users = db.execute_query("SELECT id, username, role FROM users")
            if users:
                print(f"✅ تم العثور على {len(users)} مستخدم:")
                for user in users:
                    print(f"   - {user['username']} (ID: {user['id']}, Role: {user['role']})")
            else:
                print("❌ لا يوجد مستخدمين في قاعدة البيانات")
                return False
        except Exception as e:
            print(f"❌ خطأ في فحص المستخدمين: {e}")
            return False
        
        # 5. فحص الحسابات لكل مستخدم
        print("\n5. فحص الحسابات لكل مستخدم...")
        try:
            for user in users:
                user_id = user['id']
                username = user['username']
                
                accounts = db.execute_query("SELECT * FROM accounts WHERE user_id = %s", (user_id,))
                if accounts:
                    print(f"✅ المستخدم {username}: {len(accounts)} حساب")
                    for account in accounts:
                        print(f"   - {account['name']} (ID: {account['id']}, الرصيد: {account['balance']} {account['currency']})")
                else:
                    print(f"⚠️ المستخدم {username}: لا يوجد حسابات")
        except Exception as e:
            print(f"❌ خطأ في فحص الحسابات: {e}")
        
        # 6. فحص المعاملات المالية
        print("\n6. فحص المعاملات المالية...")
        try:
            # فحص المعاملات العامة
            transactions = db.execute_query("SELECT COUNT(*) as count FROM transactions")
            trans_count = transactions[0]['count'] if transactions else 0
            print(f"📊 إجمالي المعاملات: {trans_count}")
            
            if trans_count > 0:
                # فحص آخر المعاملات
                recent_transactions = db.execute_query("""
                    SELECT t.*, a.name as account_name, u.username 
                    FROM transactions t 
                    JOIN accounts a ON t.account_id = a.id 
                    JOIN users u ON a.user_id = u.id 
                    ORDER BY t.created_at DESC 
                    LIMIT 5
                """)
                
                if recent_transactions:
                    print("📋 آخر 5 معاملات:")
                    for trans in recent_transactions:
                        trans_type = "دخل" if trans['type'] == 'income' else "مصروف"
                        print(f"   - {trans['description']}: {trans['amount']} ({trans_type}) - {trans['username']}")
            else:
                print("⚠️ لا توجد معاملات مالية")
        except Exception as e:
            print(f"❌ خطأ في فحص المعاملات: {e}")
        
        # 7. فحص العملات
        print("\n7. فحص العملات...")
        try:
            currencies = db.execute_query("SELECT * FROM currencies")
            if currencies:
                print(f"✅ تم العثور على {len(currencies)} عملة:")
                for currency in currencies:
                    print(f"   - {currency['name']} ({currency['code']})")
            else:
                print("⚠️ لا توجد عملات محددة")
        except Exception as e:
            print(f"❌ خطأ في فحص العملات: {e}")
        
        # 8. فحص الفئات
        print("\n8. فحص فئات الدخل والمصروفات...")
        try:
            income_cats = db.execute_query("SELECT COUNT(*) as count FROM income_categories")
            expense_cats = db.execute_query("SELECT COUNT(*) as count FROM expense_categories")
            
            income_count = income_cats[0]['count'] if income_cats else 0
            expense_count = expense_cats[0]['count'] if expense_cats else 0
            
            print(f"📊 فئات الدخل: {income_count}")
            print(f"📊 فئات المصروفات: {expense_count}")
        except Exception as e:
            print(f"❌ خطأ في فحص الفئات: {e}")
        
        # 9. اختبار تسجيل الدخول
        print("\n9. اختبار تسجيل الدخول...")
        try:
            from utils.auth import auth_manager
            
            # محاولة تسجيل الدخول بالمدير
            success, message = auth_manager.login("admin", "123456")
            if success:
                current_user = auth_manager.current_user
                print(f"✅ تسجيل الدخول ناجح: {current_user['username']} (ID: {current_user['id']})")
                
                # فحص حسابات المستخدم الحالي
                user_accounts = db.execute_query("SELECT * FROM accounts WHERE user_id = %s", (current_user['id'],))
                if user_accounts:
                    print(f"✅ المستخدم الحالي لديه {len(user_accounts)} حساب")
                else:
                    print("⚠️ المستخدم الحالي ليس لديه حسابات")
                
                auth_manager.logout()
            else:
                print(f"❌ فشل تسجيل الدخول: {message}")
        except Exception as e:
            print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        
        # 10. تحليل النتائج وتقديم التوصيات
        print("\n" + "=" * 65)
        print("📊 تحليل النتائج:")
        
        # تحديد نوع المشكلة
        total_users = tables_data.get('users', 0)
        total_accounts = tables_data.get('accounts', 0)
        total_transactions = tables_data.get('transactions', 0)
        
        if total_users == 0:
            print("❌ مشكلة خطيرة: لا يوجد مستخدمين في قاعدة البيانات")
            print("   التوصية: استعادة نسخة احتياطية أو إعادة تهيئة قاعدة البيانات")
        elif total_accounts == 0:
            print("⚠️ مشكلة: لا توجد حسابات مالية")
            print("   التوصية: إنشاء حسابات جديدة أو استعادة نسخة احتياطية")
        elif total_transactions == 0:
            print("⚠️ مشكلة: لا توجد معاملات مالية")
            print("   التوصية: إضافة معاملات جديدة أو استعادة نسخة احتياطية")
        else:
            print("✅ البيانات موجودة في قاعدة البيانات")
            print("   المشكلة قد تكون في عرض البيانات في الواجهة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_backup_files():
    """فحص ملفات النسخ الاحتياطية المتاحة"""
    print("\n🔍 فحص ملفات النسخ الاحتياطية المتاحة...")
    
    try:
        from utils.backup import backup_manager
        
        backup_files = backup_manager.get_backup_files()
        if backup_files:
            print(f"✅ تم العثور على {len(backup_files)} نسخة احتياطية:")
            for backup in backup_files:
                print(f"   - {backup['name']} ({backup['size_mb']} MB) - {backup['created']}")
            
            print("\n💡 يمكنك استعادة إحدى هذه النسخ من خلال:")
            print("   1. تسجيل الدخول كمدير")
            print("   2. الذهاب إلى 'إدارة قاعدة البيانات'")
            print("   3. النقر على 'استعادة نسخة احتياطية'")
        else:
            print("⚠️ لا توجد نسخ احتياطية متاحة")
            
    except Exception as e:
        print(f"❌ خطأ في فحص النسخ الاحتياطية: {e}")

def suggest_solutions():
    """اقتراح حلول للمشكلة"""
    print("\n💡 الحلول المقترحة:")
    print("=" * 30)
    
    print("1. 🔄 إعادة تشغيل التطبيق:")
    print("   - أغلق التطبيق تماماً")
    print("   - شغله مرة أخرى: python main.py")
    
    print("\n2. 👤 تسجيل الدخول بمستخدم مختلف:")
    print("   - جرب تسجيل الدخول بحساب admin")
    print("   - أو أي مستخدم آخر لديه بيانات")
    
    print("\n3. 📥 استعادة نسخة احتياطية:")
    print("   - استخدم ميزة 'إدارة قاعدة البيانات'")
    print("   - اختر 'استعادة نسخة احتياطية'")
    print("   - اختر أحدث نسخة احتياطية متاحة")
    
    print("\n4. 🔧 إعادة تهيئة قاعدة البيانات:")
    print("   - شغل: python database/init_db.py")
    print("   - سيتم إنشاء البيانات الأساسية")
    
    print("\n5. 📞 طلب المساعدة:")
    print("   - احفظ نتائج هذا التشخيص")
    print("   - اتصل بالدعم التقني")

def main():
    """الدالة الرئيسية"""
    print("🚨 تشخيص مشكلة اختفاء البيانات من تطبيق إدارة الأموال")
    print("=" * 70)
    
    # تشخيص المشكلة
    diagnosis_success = diagnose_data_loss()
    
    # فحص النسخ الاحتياطية
    check_backup_files()
    
    # اقتراح الحلول
    suggest_solutions()
    
    if diagnosis_success:
        print("\n✅ تم إكمال التشخيص بنجاح")
    else:
        print("\n❌ فشل في إكمال التشخيص")
    
    print("\n📋 ملخص التوصيات:")
    print("1. راجع نتائج التشخيص أعلاه")
    print("2. جرب الحلول المقترحة بالترتيب")
    print("3. إذا لم تنجح، استعد نسخة احتياطية")
    print("4. كحل أخير، أعد تهيئة قاعدة البيانات")

if __name__ == "__main__":
    main()
