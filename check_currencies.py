#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص سريع للعملات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        from database.connection import db
        
        print("💱 فحص العملات المتاحة")
        print("=" * 30)
        
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال")
                return
        
        print("✅ متصل بقاعدة البيانات")
        
        # الحصول على العملات النشطة
        currencies = db.execute_query("""
            SELECT code, name, symbol, is_active 
            FROM currencies 
            ORDER BY is_active DESC, name
        """)
        
        print(f"\n📊 إجمالي العملات: {len(currencies)}")
        
        active_count = 0
        print("\n💱 العملات:")
        for curr in currencies:
            status = "✅ نشط" if curr['is_active'] else "🚫 غير نشط"
            print(f"   {curr['name']} ({curr['code']}) - {curr['symbol']} - {status}")
            if curr['is_active']:
                active_count += 1
        
        print(f"\n📈 العملات النشطة: {active_count}")
        
        if active_count == 4:
            print("🎉 العدد صحيح!")
        else:
            print("⚠️ العدد غير صحيح")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
