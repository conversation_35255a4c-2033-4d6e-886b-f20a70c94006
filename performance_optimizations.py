
# تحسينات الأداء لـ main_window.py

class PerformanceCache:
    '''نظام تخزين مؤقت للبيانات'''
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 30  # 30 ثانية
        self.last_update = {}
    
    def get(self, key):
        '''الحصول على البيانات من التخزين المؤقت'''
        import time
        
        if key in self.cache:
            if time.time() - self.last_update.get(key, 0) < self.cache_timeout:
                return self.cache[key]
        return None
    
    def set(self, key, value):
        '''حفظ البيانات في التخزين المؤقت'''
        import time
        
        self.cache[key] = value
        self.last_update[key] = time.time()
    
    def clear(self, key=None):
        '''مسح التخزين المؤقت'''
        if key:
            self.cache.pop(key, None)
            self.last_update.pop(key, None)
        else:
            self.cache.clear()
            self.last_update.clear()

# إضافة التخزين المؤقت إلى MainWindow
performance_cache = PerformanceCache()

# تحسين دالة get_currency_balances_summary
def get_currency_balances_summary_optimized(self):
    '''نسخة محسنة من get_currency_balances_summary'''
    
    # التحقق من التخزين المؤقت أولاً
    cached_data = performance_cache.get('currency_balances')
    if cached_data is not None:
        return cached_data
    
    try:
        user_id = auth_manager.current_user['id']
        
        # استعلام محسن مع LIMIT
        query = """
            SELECT
                c.code, c.name, c.symbol,
                COALESCE(SUM(ab.balance), 0) as total_balance,
                COUNT(DISTINCT a.id) as accounts_count
            FROM currencies c
            LEFT JOIN account_balances ab ON c.id = ab.currency_id
            INNER JOIN accounts a ON ab.account_id = a.id
            WHERE c.is_active = TRUE
            AND a.user_id = %s
            AND a.is_active = TRUE
            GROUP BY c.id, c.code, c.name, c.symbol
            HAVING total_balance > 0 OR accounts_count > 0
            ORDER BY total_balance DESC
            LIMIT 10
        """
        
        results = db.execute_query(query, (user_id,))
        
        # حفظ في التخزين المؤقت
        performance_cache.set('currency_balances', results)
        
        return results if results else []
        
    except Exception as e:
        print(f"خطأ في الحصول على ملخص أرصدة العملات: {e}")
        return []

# تحسين دالة load_income_list
def load_income_list_optimized(self, parent):
    '''نسخة محسنة من load_income_list'''
    
    # التحقق من التخزين المؤقت
    cached_data = performance_cache.get('income_list')
    if cached_data is not None:
        self._render_income_list(parent, cached_data)
        return
    
    try:
        user_id = auth_manager.current_user['id']
        
        # استعلام محسن مع LIMIT للصفحة الأولى
        query = """
            SELECT t.*, a.name as account_name, c.symbol as currency_symbol
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s AND t.transaction_type = 'income'
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT 20
        """
        
        incomes = db.execute_query(query, (user_id,))
        
        # حفظ في التخزين المؤقت
        performance_cache.set('income_list', incomes)
        
        self._render_income_list(parent, incomes)
        
    except Exception as e:
        print(f"خطأ في تحميل الواردات: {e}")

def _render_income_list(self, parent, incomes):
    '''عرض قائمة الواردات'''
    if not incomes:
        no_data_label = create_rtl_label(
            parent,
            text="لا توجد واردات حتى الآن\nاضغط على 'إضافة وارد جديد' لإضافة وارد",
            font_size='header',
            text_color=COLORS['text_muted'],
            **ARABIC_TEXT_STYLES['title']
        )
        no_data_label.pack(pady=50)
    else:
        # عرض أول 10 عناصر فقط للسرعة
        for income in incomes[:10]:
            self.create_transaction_card(parent, income, "income")
        
        # إضافة زر "عرض المزيد" إذا كان هناك المزيد
        if len(incomes) > 10:
            load_more_btn = ctk.CTkButton(
                parent,
                text="عرض المزيد",
                command=lambda: self._load_more_incomes(parent, incomes[10:])
            )
            load_more_btn.pack(pady=10)

# تحسين التنقل بين الصفحات
def show_page_optimized(self, page_name, load_func):
    '''دالة محسنة للتنقل بين الصفحات'''
    
    # إظهار مؤشر التحميل
    self.show_loading_indicator()
    
    # تنفيذ التحميل في خيط منفصل
    import threading
    
    def load_in_background():
        try:
            self.current_page = page_name
            self.set_active_button(page_name)
            load_func()
        finally:
            self.hide_loading_indicator()
    
    thread = threading.Thread(target=load_in_background, daemon=True)
    thread.start()

def show_loading_indicator(self):
    '''إظهار مؤشر التحميل'''
    self.clear_content()
    
    loading_frame = ctk.CTkFrame(self.content_frame)
    loading_frame.pack(expand=True, fill="both")
    
    loading_label = create_rtl_label(
        loading_frame,
        text="جاري التحميل...",
        font_size='header'
    )
    loading_label.pack(expand=True)

def hide_loading_indicator(self):
    '''إخفاء مؤشر التحميل'''
    # سيتم استبداله بالمحتوى الفعلي
    pass

# تحسين إنشاء البطاقات
def create_transaction_card_optimized(self, parent, transaction, transaction_type):
    '''نسخة محسنة من create_transaction_card'''
    
    # استخدام إعدادات مبسطة للسرعة
    card = ctk.CTkFrame(parent, corner_radius=8)
    card.pack(fill="x", padx=20, pady=5)  # تقليل المسافات
    
    # معلومات أساسية فقط
    info_frame = ctk.CTkFrame(card, fg_color="transparent")
    info_frame.pack(fill="x", padx=15, pady=10)
    
    # العنوان والمبلغ في سطر واحد
    title_amount_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
    title_amount_frame.pack(fill="x")
    
    # العنوان
    title_label = create_rtl_label(
        title_amount_frame,
        text=transaction.get('description', 'بدون وصف'),
        font_size='body'
    )
    title_label.pack(side="right")
    
    # المبلغ
    amount_color = COLORS['success'] if transaction_type == 'income' else COLORS['error']
    amount_label = create_rtl_label(
        title_amount_frame,
        text=f"{transaction['amount']} {transaction.get('currency_symbol', '')}",
        font_size='body',
        text_color=amount_color
    )
    amount_label.pack(side="left")
