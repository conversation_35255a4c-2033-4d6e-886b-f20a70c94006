#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جداول قاعدة البيانات المفقودة
إنشاء الجداول المطلوبة للحسابات والعملات وأنواع الحسابات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db

def create_missing_tables():
    """إنشاء الجداول المفقودة"""
    print("🔧 إنشاء الجداول المفقودة...")
    
    # جدول أنواع الحسابات
    account_types_sql = """
    CREATE TABLE IF NOT EXISTS account_types (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );
    """
    
    # جدول العملات
    currencies_sql = """
    CREATE TABLE IF NOT EXISTS currencies (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(10) NOT NULL UNIQUE,
        name VARCHAR(100) NOT NULL,
        symbol VARCHAR(10) NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );
    """
    
    # جدول الحسابات
    accounts_sql = """
    CREATE TABLE IF NOT EXISTS accounts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        account_type_id INT NOT NULL,
        currency_id INT NOT NULL,
        initial_balance DECIMAL(15, 2) DEFAULT 0.00,
        current_balance DECIMAL(15, 2) DEFAULT 0.00,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (account_type_id) REFERENCES account_types(id),
        FOREIGN KEY (currency_id) REFERENCES currencies(id)
    );
    """
    
    # جدول أرصدة الحسابات (متعدد العملات)
    account_balances_sql = """
    CREATE TABLE IF NOT EXISTS account_balances (
        id INT AUTO_INCREMENT PRIMARY KEY,
        account_id INT NOT NULL,
        currency_id INT NOT NULL,
        balance DECIMAL(15, 2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
        FOREIGN KEY (currency_id) REFERENCES currencies(id),
        UNIQUE KEY unique_account_currency (account_id, currency_id)
    );
    """
    
    # جدول المعاملات
    transactions_sql = """
    CREATE TABLE IF NOT EXISTS transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        account_id INT NOT NULL,
        currency_id INT NOT NULL,
        transaction_type ENUM('income', 'expense') NOT NULL,
        amount DECIMAL(15, 2) NOT NULL,
        description TEXT,
        transaction_date DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (account_id) REFERENCES accounts(id),
        FOREIGN KEY (currency_id) REFERENCES currencies(id)
    );
    """
    
    # جدول التحويلات
    transfers_sql = """
    CREATE TABLE IF NOT EXISTS transfers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        from_account_id INT NOT NULL,
        to_account_id INT NOT NULL,
        from_currency_id INT NOT NULL,
        to_currency_id INT NOT NULL,
        from_amount DECIMAL(15, 2) NOT NULL,
        to_amount DECIMAL(15, 2) NOT NULL,
        exchange_rate DECIMAL(10, 6) DEFAULT 1.000000,
        description TEXT,
        transfer_date DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (from_account_id) REFERENCES accounts(id),
        FOREIGN KEY (to_account_id) REFERENCES accounts(id),
        FOREIGN KEY (from_currency_id) REFERENCES currencies(id),
        FOREIGN KEY (to_currency_id) REFERENCES currencies(id)
    );
    """
    
    tables = [
        ("account_types", account_types_sql),
        ("currencies", currencies_sql),
        ("accounts", accounts_sql),
        ("account_balances", account_balances_sql),
        ("transactions", transactions_sql),
        ("transfers", transfers_sql)
    ]
    
    try:
        for table_name, sql in tables:
            print(f"   📋 إنشاء جدول {table_name}...")
            db.execute_update(sql)
            print(f"   ✅ تم إنشاء جدول {table_name}")
        
        print("✅ تم إنشاء جميع الجداول بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def insert_default_data():
    """إدراج البيانات الافتراضية"""
    print("\n📊 إدراج البيانات الافتراضية...")
    
    try:
        # أنواع الحسابات الافتراضية
        account_types_data = [
            ("حساب جاري", "حساب بنكي للمعاملات اليومية"),
            ("حساب توفير", "حساب للادخار والاستثمار"),
            ("محفظة نقدية", "نقود في المحفظة أو الخزنة"),
            ("بطاقة ائتمان", "بطاقة ائتمان أو دين"),
            ("استثمار", "حسابات الاستثمار والأسهم")
        ]
        
        for name, description in account_types_data:
            existing = db.execute_query("SELECT id FROM account_types WHERE name = %s", (name,))
            if not existing:
                db.execute_insert(
                    "INSERT INTO account_types (name, description) VALUES (%s, %s)",
                    (name, description)
                )
                print(f"   ✅ تم إضافة نوع حساب: {name}")
        
        # العملات الافتراضية
        currencies_data = [
            ("SAR", "ريال سعودي", "ر.س"),
            ("USD", "دولار أمريكي", "$"),
            ("EUR", "يورو", "€"),
            ("GBP", "جنيه إسترليني", "£"),
            ("AED", "درهم إماراتي", "د.إ")
        ]
        
        for code, name, symbol in currencies_data:
            existing = db.execute_query("SELECT id FROM currencies WHERE code = %s", (code,))
            if not existing:
                db.execute_insert(
                    "INSERT INTO currencies (code, name, symbol) VALUES (%s, %s, %s)",
                    (code, name, symbol)
                )
                print(f"   ✅ تم إضافة عملة: {name} ({code})")
        
        print("✅ تم إدراج البيانات الافتراضية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدراج البيانات الافتراضية: {e}")
        return False

def create_sample_accounts():
    """إنشاء حسابات تجريبية للمستخدم الحالي"""
    print("\n🏦 إنشاء حسابات تجريبية...")
    
    try:
        # الحصول على المستخدم الأول
        users = db.execute_query("SELECT * FROM users LIMIT 1")
        if not users:
            print("❌ لا يوجد مستخدمين")
            return False
        
        user_id = users[0]['id']
        print(f"   👤 المستخدم: {users[0].get('username')} (ID: {user_id})")
        
        # الحصول على أنواع الحسابات والعملات
        account_types = db.execute_query("SELECT * FROM account_types LIMIT 3")
        currencies = db.execute_query("SELECT * FROM currencies WHERE code IN ('SAR', 'USD')")
        
        if not account_types or not currencies:
            print("❌ لا توجد أنواع حسابات أو عملات")
            return False
        
        # إنشاء حسابات تجريبية
        sample_accounts = [
            ("البنك الأهلي السعودي", account_types[0]['id'], currencies[0]['id'], 5000.00),
            ("محفظة نقدية", account_types[1]['id'] if len(account_types) > 1 else account_types[0]['id'], currencies[0]['id'], 1500.00),
            ("حساب التوفير", account_types[2]['id'] if len(account_types) > 2 else account_types[0]['id'], currencies[0]['id'], 10000.00)
        ]
        
        for name, type_id, currency_id, balance in sample_accounts:
            # التحقق من عدم وجود الحساب
            existing = db.execute_query(
                "SELECT id FROM accounts WHERE user_id = %s AND name = %s", 
                (user_id, name)
            )
            
            if not existing:
                account_id = db.execute_insert(
                    """INSERT INTO accounts (user_id, name, account_type_id, currency_id, 
                       initial_balance, current_balance, is_active) 
                       VALUES (%s, %s, %s, %s, %s, %s, TRUE)""",
                    (user_id, name, type_id, currency_id, balance, balance)
                )
                
                # إضافة رصيد في جدول account_balances
                db.execute_insert(
                    "INSERT INTO account_balances (account_id, currency_id, balance) VALUES (%s, %s, %s)",
                    (account_id, currency_id, balance)
                )
                
                print(f"   ✅ تم إنشاء حساب: {name} - {balance} {currencies[0]['symbol']}")
        
        print("✅ تم إنشاء الحسابات التجريبية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحسابات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح جداول قاعدة البيانات")
    print("=" * 50)
    
    # إنشاء الجداول
    if not create_missing_tables():
        return
    
    # إدراج البيانات الافتراضية
    if not insert_default_data():
        return
    
    # إنشاء حسابات تجريبية
    if not create_sample_accounts():
        return
    
    print("\n" + "=" * 50)
    print("🎉 تم إصلاح قاعدة البيانات بنجاح!")
    print("✅ يمكنك الآن تشغيل التطبيق ومشاهدة الحسابات")

if __name__ == "__main__":
    main()
