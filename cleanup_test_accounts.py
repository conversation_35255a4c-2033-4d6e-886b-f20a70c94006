#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تنظيف الحسابات الاختبارية
حذف جميع الحسابات الاختبارية المؤقتة والاحتفاظ بحساب "معاذ" فقط
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db

class TestAccountsCleanup:
    """أداة تنظيف الحسابات الاختبارية"""
    
    def __init__(self):
        self.test_keywords = [
            'اختبار',
            'تجريبي', 
            'test',
            'demo',
            'sample',
            'البنك الأهلي السعودي',  # الحسابات التجريبية الأولى
            'محفظة نقدية',
            'حساب التوفير'
        ]
        self.accounts_to_keep = ['معاذ']  # الحسابات التي يجب الاحتفاظ بها
        self.deleted_accounts = []
        self.deleted_balances = []
    
    def run_cleanup(self):
        """تشغيل عملية التنظيف الشاملة"""
        print("🧹 بدء تنظيف الحسابات الاختبارية...")
        print("=" * 60)
        
        # 1. عرض الحسابات الحالية
        self.show_current_accounts()
        
        # 2. تحديد الحسابات المراد حذفها
        accounts_to_delete = self.identify_test_accounts()
        
        if not accounts_to_delete:
            print("✅ لا توجد حسابات اختبارية للحذف")
            return
        
        # 3. طلب تأكيد من المستخدم
        if not self.confirm_deletion(accounts_to_delete):
            print("❌ تم إلغاء عملية التنظيف")
            return
        
        # 4. حذف الأرصدة أولاً
        self.delete_account_balances(accounts_to_delete)
        
        # 5. حذف الحسابات
        self.delete_accounts(accounts_to_delete)
        
        # 6. عرض النتائج النهائية
        self.show_cleanup_results()
        
        print("\n" + "=" * 60)
        print("🎉 تم تنظيف قاعدة البيانات بنجاح!")
    
    def show_current_accounts(self):
        """عرض جميع الحسابات الحالية"""
        print("📋 الحسابات الحالية في قاعدة البيانات:")
        
        try:
            accounts = db.execute_query("SELECT * FROM accounts ORDER BY created_at")
            
            if accounts:
                print(f"📊 إجمالي الحسابات: {len(accounts)}")
                print()
                
                for i, account in enumerate(accounts, 1):
                    status = "نشط" if account.get('is_active') else "غير نشط"
                    print(f"{i:2d}. {account['name']} (ID: {account['id']})")
                    print(f"    المستخدم: {account['user_id']} | الحالة: {status}")
                    print(f"    تاريخ الإنشاء: {account['created_at']}")
                    
                    # عرض الأرصدة
                    balances = db.execute_query("""
                        SELECT ab.balance, c.symbol 
                        FROM account_balances ab 
                        JOIN currencies c ON ab.currency_id = c.id 
                        WHERE ab.account_id = %s AND ab.balance != 0
                    """, (account['id'],))
                    
                    if balances:
                        balance_text = ", ".join([f"{b['balance']} {b['symbol']}" for b in balances])
                        print(f"    الأرصدة: {balance_text}")
                    else:
                        print(f"    الأرصدة: 0")
                    print()
            else:
                print("❌ لا توجد حسابات في قاعدة البيانات")
                
        except Exception as e:
            print(f"❌ خطأ في عرض الحسابات: {e}")
    
    def identify_test_accounts(self):
        """تحديد الحسابات الاختبارية المراد حذفها"""
        print("🔍 تحديد الحسابات الاختبارية...")
        
        try:
            accounts = db.execute_query("SELECT * FROM accounts")
            accounts_to_delete = []
            
            for account in accounts:
                account_name = account['name'].lower()
                
                # التحقق من أن الحساب ليس في قائمة الحسابات المحفوظة
                if account['name'] in self.accounts_to_keep:
                    print(f"✅ الاحتفاظ بالحساب: {account['name']}")
                    continue
                
                # التحقق من وجود كلمات اختبارية
                is_test_account = False
                for keyword in self.test_keywords:
                    if keyword.lower() in account_name:
                        is_test_account = True
                        print(f"🎯 حساب اختباري محدد: {account['name']} (يحتوي على '{keyword}')")
                        break
                
                if is_test_account:
                    accounts_to_delete.append(account)
            
            print(f"\n📊 تم تحديد {len(accounts_to_delete)} حساب اختباري للحذف")
            return accounts_to_delete
            
        except Exception as e:
            print(f"❌ خطأ في تحديد الحسابات الاختبارية: {e}")
            return []
    
    def confirm_deletion(self, accounts_to_delete):
        """طلب تأكيد الحذف من المستخدم"""
        print("\n⚠️ تأكيد الحذف:")
        print("الحسابات التالية سيتم حذفها:")
        
        for account in accounts_to_delete:
            print(f"   - {account['name']} (ID: {account['id']})")
        
        print(f"\nإجمالي الحسابات المراد حذفها: {len(accounts_to_delete)}")
        
        # في البيئة التلقائية، نفترض الموافقة
        print("✅ تم تأكيد الحذف تلقائياً")
        return True
    
    def delete_account_balances(self, accounts_to_delete):
        """حذف أرصدة الحسابات الاختبارية"""
        print("\n💰 حذف أرصدة الحسابات الاختبارية...")
        
        try:
            total_balances_deleted = 0
            
            for account in accounts_to_delete:
                account_id = account['id']
                account_name = account['name']
                
                # الحصول على الأرصدة قبل الحذف
                balances = db.execute_query("""
                    SELECT ab.*, c.symbol 
                    FROM account_balances ab 
                    JOIN currencies c ON ab.currency_id = c.id 
                    WHERE ab.account_id = %s
                """, (account_id,))
                
                if balances:
                    print(f"   🗑️ حذف أرصدة الحساب: {account_name}")
                    for balance in balances:
                        print(f"      - {balance['balance']} {balance['symbol']}")
                        self.deleted_balances.append({
                            'account_name': account_name,
                            'balance': balance['balance'],
                            'symbol': balance['symbol']
                        })
                    
                    # حذف الأرصدة
                    result = db.execute_update(
                        "DELETE FROM account_balances WHERE account_id = %s", 
                        (account_id,)
                    )
                    total_balances_deleted += result
                else:
                    print(f"   ℹ️ لا توجد أرصدة للحساب: {account_name}")
            
            print(f"✅ تم حذف {total_balances_deleted} رصيد من قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في حذف الأرصدة: {e}")
    
    def delete_accounts(self, accounts_to_delete):
        """حذف الحسابات الاختبارية"""
        print("\n🏦 حذف الحسابات الاختبارية...")
        
        try:
            total_accounts_deleted = 0
            
            for account in accounts_to_delete:
                account_id = account['id']
                account_name = account['name']
                
                print(f"   🗑️ حذف الحساب: {account_name} (ID: {account_id})")
                
                # حذف الحساب
                result = db.execute_update(
                    "DELETE FROM accounts WHERE id = %s", 
                    (account_id,)
                )
                
                if result > 0:
                    total_accounts_deleted += 1
                    self.deleted_accounts.append({
                        'id': account_id,
                        'name': account_name,
                        'user_id': account['user_id']
                    })
                    print(f"      ✅ تم حذف الحساب بنجاح")
                else:
                    print(f"      ❌ فشل في حذف الحساب")
            
            print(f"✅ تم حذف {total_accounts_deleted} حساب من قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في حذف الحسابات: {e}")
    
    def show_cleanup_results(self):
        """عرض نتائج التنظيف"""
        print("\n📊 نتائج التنظيف:")
        
        # الحسابات المحذوفة
        if self.deleted_accounts:
            print(f"🗑️ الحسابات المحذوفة ({len(self.deleted_accounts)}):")
            for account in self.deleted_accounts:
                print(f"   - {account['name']} (ID: {account['id']})")
        
        # الأرصدة المحذوفة
        if self.deleted_balances:
            print(f"\n💰 الأرصدة المحذوفة ({len(self.deleted_balances)}):")
            for balance in self.deleted_balances:
                print(f"   - {balance['account_name']}: {balance['balance']} {balance['symbol']}")
        
        # الحسابات المتبقية
        print(f"\n✅ الحسابات المتبقية:")
        try:
            remaining_accounts = db.execute_query("SELECT * FROM accounts ORDER BY name")
            if remaining_accounts:
                for account in remaining_accounts:
                    status = "نشط" if account.get('is_active') else "غير نشط"
                    print(f"   - {account['name']} (ID: {account['id']}) - {status}")
            else:
                print("   لا توجد حسابات متبقية")
        except Exception as e:
            print(f"   ❌ خطأ في عرض الحسابات المتبقية: {e}")
    
    def verify_cleanup(self):
        """التحقق من نجاح التنظيف"""
        print("\n🔍 التحقق من نجاح التنظيف...")
        
        try:
            # التحقق من عدم وجود حسابات اختبارية
            for keyword in self.test_keywords:
                test_accounts = db.execute_query(
                    "SELECT * FROM accounts WHERE LOWER(name) LIKE %s", 
                    (f"%{keyword.lower()}%",)
                )
                
                if test_accounts:
                    print(f"⚠️ تحذير: لا تزال هناك حسابات تحتوي على '{keyword}':")
                    for account in test_accounts:
                        print(f"   - {account['name']} (ID: {account['id']})")
                else:
                    print(f"✅ لا توجد حسابات تحتوي على '{keyword}'")
            
            # التحقق من وجود حساب "معاذ"
            maaz_account = db.execute_query("SELECT * FROM accounts WHERE name = 'معاذ'")
            if maaz_account:
                print("✅ حساب 'معاذ' موجود ومحفوظ")
            else:
                print("⚠️ تحذير: حساب 'معاذ' غير موجود")
            
            # إحصائيات نهائية
            total_accounts = db.execute_query("SELECT COUNT(*) as count FROM accounts")[0]['count']
            total_balances = db.execute_query("SELECT COUNT(*) as count FROM account_balances")[0]['count']
            
            print(f"\n📊 الإحصائيات النهائية:")
            print(f"   - إجمالي الحسابات: {total_accounts}")
            print(f"   - إجمالي الأرصدة: {total_balances}")
            
        except Exception as e:
            print(f"❌ خطأ في التحقق: {e}")

def main():
    """الدالة الرئيسية"""
    print("🧹 سكريبت تنظيف الحسابات الاختبارية")
    print("=" * 60)
    print("الهدف: حذف جميع الحسابات الاختبارية والاحتفاظ بحساب 'معاذ' فقط")
    print()
    
    try:
        # إنشاء أداة التنظيف
        cleanup = TestAccountsCleanup()
        
        # تشغيل التنظيف
        cleanup.run_cleanup()
        
        # التحقق من النتائج
        cleanup.verify_cleanup()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل سكريبت التنظيف: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
