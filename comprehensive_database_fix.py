#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل شامل لمشاكل قاعدة البيانات في تطبيق إدارة الأموال
"""

import os
import sys
import json
import mysql.connector
from mysql.connector import Error
import sqlite3
from datetime import datetime
import hashlib

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DatabaseFixer:
    """فئة إصلاح قاعدة البيانات"""
    
    def __init__(self):
        self.backup_dir = "cleanup_backup_20250720_021829"
        self.connection = None
        self.cursor = None
        self.config = self.load_config()
        
    def load_config(self):
        """تحميل إعدادات قاعدة البيانات"""
        config_file = "config/database_settings.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # إعدادات افتراضية
            return {
                "host": "localhost",
                "port": 3306,
                "user": "root",
                "password": "mohdam",
                "database": "money_manager"
            }
    
    def test_mysql_connection(self):
        """اختبار الاتصال بـ MySQL"""
        print("اختبار الاتصال بـ MySQL...")
        
        try:
            # محاولة الاتصال بدون قاعدة بيانات أولاً
            temp_config = self.config.copy()
            db_name = temp_config.pop('database')
            
            connection = mysql.connector.connect(**temp_config)
            
            if connection.is_connected():
                print("✓ تم الاتصال بخادم MySQL بنجاح")
                
                cursor = connection.cursor()
                
                # فحص وجود قاعدة البيانات
                cursor.execute("SHOW DATABASES")
                databases = [db[0] for db in cursor.fetchall()]
                
                if db_name in databases:
                    print(f"✓ قاعدة البيانات '{db_name}' موجودة")
                else:
                    print(f"إنشاء قاعدة البيانات '{db_name}'...")
                    cursor.execute(f"CREATE DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                    print(f"✓ تم إنشاء قاعدة البيانات '{db_name}'")
                
                cursor.close()
                connection.close()
                return True
                
        except Error as e:
            print(f"✗ خطأ في الاتصال بـ MySQL: {e}")
            return False
        except Exception as e:
            print(f"✗ خطأ غير متوقع: {e}")
            return False
    
    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            self.cursor = self.connection.cursor(dictionary=True)
            print("✓ تم الاتصال بقاعدة البيانات")
            return True
        except Exception as e:
            print(f"✗ خطأ في الاتصال: {e}")
            return False
    
    def create_tables(self):
        """إنشاء الجداول المطلوبة"""
        print("إنشاء الجداول...")
        
        tables = {
            'users': """
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100) NOT NULL,
                    role ENUM('admin', 'user') DEFAULT 'user',
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    last_login TIMESTAMP NULL
                )
            """,
            'currencies': """
                CREATE TABLE IF NOT EXISTS currencies (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    code VARCHAR(3) UNIQUE NOT NULL,
                    name VARCHAR(50) NOT NULL,
                    symbol VARCHAR(10) NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    exchange_rate DECIMAL(10, 4) DEFAULT 1.0000,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            'account_types': """
                CREATE TABLE IF NOT EXISTS account_types (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(50) NOT NULL,
                    description TEXT,
                    icon VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            'accounts': """
                CREATE TABLE IF NOT EXISTS accounts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    name VARCHAR(100) NOT NULL,
                    account_type_id INT NOT NULL,
                    description TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (account_type_id) REFERENCES account_types(id)
                )
            """,
            'account_balances': """
                CREATE TABLE IF NOT EXISTS account_balances (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    account_id INT NOT NULL,
                    currency_id INT NOT NULL,
                    balance DECIMAL(15, 2) DEFAULT 0.00,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                    FOREIGN KEY (currency_id) REFERENCES currencies(id),
                    UNIQUE KEY unique_account_currency (account_id, currency_id)
                )
            """,
            'income_categories': """
                CREATE TABLE IF NOT EXISTS income_categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(50) NOT NULL,
                    description TEXT,
                    icon VARCHAR(50),
                    color VARCHAR(7),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            'expense_categories': """
                CREATE TABLE IF NOT EXISTS expense_categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(50) NOT NULL,
                    description TEXT,
                    icon VARCHAR(50),
                    color VARCHAR(7),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            'transactions': """
                CREATE TABLE IF NOT EXISTS transactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    account_id INT NOT NULL,
                    type ENUM('income', 'expense', 'transfer') NOT NULL,
                    amount DECIMAL(15, 2) NOT NULL,
                    currency_id INT NOT NULL,
                    category_id INT NULL,
                    description TEXT,
                    transaction_date DATE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
                    FOREIGN KEY (currency_id) REFERENCES currencies(id)
                )
            """
        }
        
        try:
            for table_name, create_sql in tables.items():
                self.cursor.execute(create_sql)
                print(f"✓ جدول {table_name}")
            
            self.connection.commit()
            print("✓ تم إنشاء جميع الجداول")
            return True
            
        except Exception as e:
            print(f"✗ خطأ في إنشاء الجداول: {e}")
            return False
    
    def insert_basic_data(self):
        """إدراج البيانات الأساسية"""
        print("إدراج البيانات الأساسية...")
        
        try:
            # العملات
            self.cursor.execute("SELECT COUNT(*) as count FROM currencies")
            if self.cursor.fetchone()['count'] == 0:
                currencies = [
                    ('SAR', 'ريال سعودي', 'ر.س'),
                    ('USD', 'دولار أمريكي', '$'),
                    ('EUR', 'يورو', '€'),
                    ('YER', 'ريال يمني', 'ر.ي')
                ]
                for code, name, symbol in currencies:
                    self.cursor.execute(
                        "INSERT INTO currencies (code, name, symbol) VALUES (%s, %s, %s)",
                        (code, name, symbol)
                    )
                print("✓ تم إدراج العملات")
            
            # أنواع الحسابات
            self.cursor.execute("SELECT COUNT(*) as count FROM account_types")
            if self.cursor.fetchone()['count'] == 0:
                account_types = [
                    ('صندوق نقدي', 'نقد في اليد', 'cash'),
                    ('حساب بنكي', 'حساب في البنك', 'bank'),
                    ('بطاقة ائتمان', 'بطاقة ائتمان', 'credit_card'),
                    ('محفظة إلكترونية', 'محفظة رقمية', 'digital_wallet'),
                    ('استثمار', 'حساب استثماري', 'investment'),
                    ('حساب توفير', 'حساب توفير', 'savings'),
                    ('أخرى', 'نوع آخر', 'other')
                ]
                for name, desc, icon in account_types:
                    self.cursor.execute(
                        "INSERT INTO account_types (name, description, icon) VALUES (%s, %s, %s)",
                        (name, desc, icon)
                    )
                print("✓ تم إدراج أنواع الحسابات")
            
            # تصنيفات الدخل
            self.cursor.execute("SELECT COUNT(*) as count FROM income_categories")
            if self.cursor.fetchone()['count'] == 0:
                income_categories = [
                    ('راتب', 'راتب شهري', '#10B981'),
                    ('أعمال', 'دخل من الأعمال', '#3B82F6'),
                    ('استثمار', 'عوائد استثمارية', '#8B5CF6'),
                    ('عمل حر', 'دخل من العمل الحر', '#F59E0B'),
                    ('إيجار', 'دخل من الإيجار', '#EF4444'),
                    ('هدية', 'هدايا ومنح', '#EC4899'),
                    ('مكافأة', 'مكافآت وحوافز', '#06B6D4'),
                    ('استرداد', 'استرداد مبالغ', '#84CC16'),
                    ('أخرى', 'مصادر أخرى', '#6B7280')
                ]
                for name, desc, color in income_categories:
                    self.cursor.execute(
                        "INSERT INTO income_categories (name, description, color) VALUES (%s, %s, %s)",
                        (name, desc, color)
                    )
                print("✓ تم إدراج تصنيفات الدخل")
            
            # تصنيفات المصروفات
            self.cursor.execute("SELECT COUNT(*) as count FROM expense_categories")
            if self.cursor.fetchone()['count'] == 0:
                expense_categories = [
                    ('طعام وشراب', 'مأكولات ومشروبات', '#EF4444'),
                    ('مواصلات', 'نقل ومواصلات', '#3B82F6'),
                    ('سكن', 'إيجار ومسكن', '#8B5CF6'),
                    ('فواتير', 'كهرباء وماء وغاز', '#F59E0B'),
                    ('صحة', 'علاج وأدوية', '#10B981'),
                    ('تعليم', 'تعليم ودورات', '#06B6D4'),
                    ('ترفيه', 'ترفيه وتسلية', '#EC4899'),
                    ('تسوق', 'ملابس ومستلزمات', '#84CC16'),
                    ('سفر', 'سفر وسياحة', '#F97316'),
                    ('عائلة', 'مصروفات عائلية', '#8B5CF6'),
                    ('صدقات', 'زكاة وصدقات', '#10B981'),
                    ('استثمار', 'استثمارات', '#6366F1'),
                    ('ديون', 'سداد ديون', '#EF4444'),
                    ('أخرى', 'مصروفات أخرى', '#6B7280')
                ]
                for name, desc, color in expense_categories:
                    self.cursor.execute(
                        "INSERT INTO expense_categories (name, description, color) VALUES (%s, %s, %s)",
                        (name, desc, color)
                    )
                print("✓ تم إدراج تصنيفات المصروفات")
            
            self.connection.commit()
            return True

        except Exception as e:
            print(f"✗ خطأ في إدراج البيانات الأساسية: {e}")
            return False

    def restore_backup_data(self):
        """استعادة البيانات من النسخ الاحتياطية"""
        print("\nاستعادة البيانات من النسخ الاحتياطية...")

        if not os.path.exists(self.backup_dir):
            print(f"✗ مجلد النسخ الاحتياطية غير موجود: {self.backup_dir}")
            return False

        success = True
        success &= self._restore_users()
        success &= self._restore_accounts()
        success &= self._restore_transactions()

        return success

    def _restore_users(self):
        """استعادة بيانات المستخدمين"""
        try:
            users_file = os.path.join(self.backup_dir, "users.json")
            if not os.path.exists(users_file):
                print("✗ ملف المستخدمين غير موجود")
                return False

            with open(users_file, 'r', encoding='utf-8') as f:
                users_data = json.load(f)

            print(f"استعادة {len(users_data)} مستخدم...")

            for username, user_info in users_data.items():
                # التحقق من وجود المستخدم
                self.cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
                existing_user = self.cursor.fetchone()

                if existing_user:
                    print(f"المستخدم {username} موجود بالفعل")
                    continue

                # إدراج المستخدم الجديد
                insert_query = """
                INSERT INTO users (username, password_hash, full_name, role, is_active, created_at, last_login)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """

                values = (
                    username,
                    user_info['password_hash'],
                    user_info['full_name'],
                    user_info['role'],
                    user_info['is_active'],
                    user_info['created_at'],
                    user_info.get('last_login')
                )

                self.cursor.execute(insert_query, values)
                print(f"✓ تم استعادة المستخدم: {username}")

            self.connection.commit()
            return True

        except Exception as e:
            print(f"✗ خطأ في استعادة المستخدمين: {e}")
            return False

    def _restore_accounts(self):
        """استعادة بيانات الحسابات"""
        try:
            accounts_file = os.path.join(self.backup_dir, "accounts.json")
            if not os.path.exists(accounts_file):
                print("✗ ملف الحسابات غير موجود")
                return False

            with open(accounts_file, 'r', encoding='utf-8') as f:
                accounts_data = json.load(f)

            print(f"استعادة {len(accounts_data)} حساب...")

            # الحصول على معرفات أنواع الحسابات والعملات
            self.cursor.execute("SELECT id, name FROM account_types")
            account_types = {row['name']: row['id'] for row in self.cursor.fetchall()}

            self.cursor.execute("SELECT id, name FROM currencies")
            currencies = {row['name']: row['id'] for row in self.cursor.fetchall()}

            for account in accounts_data:
                # البحث عن نوع الحساب
                account_type_id = 1  # افتراضي
                for type_name, type_id in account_types.items():
                    if account['type'] in type_name or type_name in account['type']:
                        account_type_id = type_id
                        break

                # البحث عن العملة
                currency_id = 1  # افتراضي (ريال سعودي)
                for curr_name, curr_id in currencies.items():
                    if account['currency'] in curr_name or curr_name in account['currency']:
                        currency_id = curr_id
                        break

                # إدراج الحساب
                insert_query = """
                INSERT INTO accounts (user_id, name, account_type_id, description, is_active)
                VALUES (%s, %s, %s, %s, %s)
                """

                values = (
                    account['user_id'],
                    account['name'],
                    account_type_id,
                    account.get('description', ''),
                    True
                )

                self.cursor.execute(insert_query, values)
                account_id = self.cursor.lastrowid

                # إدراج الرصيد
                balance_query = """
                INSERT INTO account_balances (account_id, currency_id, balance)
                VALUES (%s, %s, %s)
                ON DUPLICATE KEY UPDATE balance = VALUES(balance)
                """

                self.cursor.execute(balance_query, (account_id, currency_id, account['balance']))
                print(f"✓ تم استعادة الحساب: {account['name']}")

            self.connection.commit()
            return True

        except Exception as e:
            print(f"✗ خطأ في استعادة الحسابات: {e}")
            return False

    def _restore_transactions(self):
        """استعادة بيانات المعاملات"""
        try:
            transactions_file = os.path.join(self.backup_dir, "transactions.json")
            if not os.path.exists(transactions_file):
                print("✗ ملف المعاملات غير موجود")
                return False

            with open(transactions_file, 'r', encoding='utf-8') as f:
                transactions_data = json.load(f)

            print(f"استعادة {len(transactions_data)} معاملة...")

            # الحصول على معرفات التصنيفات والعملات
            self.cursor.execute("SELECT id, name FROM income_categories")
            income_categories = {row['name']: row['id'] for row in self.cursor.fetchall()}

            self.cursor.execute("SELECT id, name FROM expense_categories")
            expense_categories = {row['name']: row['id'] for row in self.cursor.fetchall()}

            for transaction in transactions_data:
                # تحديد نوع المعاملة
                trans_type = 'income' if transaction['type'] == 'دخل' else 'expense'

                # البحث عن التصنيف
                category_id = None
                categories = income_categories if trans_type == 'income' else expense_categories

                for cat_name, cat_id in categories.items():
                    if transaction['category'] in cat_name or cat_name in transaction['category']:
                        category_id = cat_id
                        break

                # البحث عن العملة (افتراضي ريال سعودي)
                currency_id = 1

                # إدراج المعاملة
                insert_query = """
                INSERT INTO transactions (user_id, account_id, type, amount, currency_id, category_id,
                                        description, transaction_date, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                values = (
                    transaction['user_id'],
                    transaction['account_id'],
                    trans_type,
                    transaction['amount'],
                    currency_id,
                    category_id,
                    transaction['description'],
                    transaction['date'],
                    transaction['created_at']
                )

                self.cursor.execute(insert_query, values)
                print(f"✓ تم استعادة المعاملة: {transaction['description'][:30]}...")

            self.connection.commit()
            return True

        except Exception as e:
            print(f"✗ خطأ في استعادة المعاملات: {e}")
            return False

    def close_connection(self):
        """إغلاق الاتصال"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            print("تم إغلاق الاتصال بقاعدة البيانات")
        except Exception as e:
            print(f"خطأ في إغلاق الاتصال: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("إصلاح شامل لقاعدة البيانات - تطبيق إدارة الأموال")
    print("=" * 60)

    fixer = DatabaseFixer()

    try:
        # اختبار الاتصال
        if not fixer.test_mysql_connection():
            print("فشل في الاتصال بـ MySQL")
            return False

        # الاتصال بقاعدة البيانات
        if not fixer.connect_to_database():
            print("فشل في الاتصال بقاعدة البيانات")
            return False

        # إنشاء الجداول
        if not fixer.create_tables():
            print("فشل في إنشاء الجداول")
            return False

        # إدراج البيانات الأساسية
        if not fixer.insert_basic_data():
            print("فشل في إدراج البيانات الأساسية")
            return False

        # استعادة البيانات من النسخ الاحتياطية
        if not fixer.restore_backup_data():
            print("فشل في استعادة البيانات")
            return False

        print("\n" + "=" * 60)
        print("✓ تم إصلاح قاعدة البيانات بنجاح!")
        print("=" * 60)

        return True

    except Exception as e:
        print(f"خطأ عام: {e}")
        return False
    finally:
        fixer.close_connection()

if __name__ == "__main__":
    main()
