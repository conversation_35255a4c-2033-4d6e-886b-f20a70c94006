#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 فحص سريع لحالة البيانات...")

try:
    # 1. اختبار الاتصال
    print("1. اختبار الاتصال بقاعدة البيانات...")
    from database.connection import db
    
    if not (db.is_connected() or db.connect()):
        print("❌ فشل الاتصال بقاعدة البيانات")
        exit(1)
    print("✅ الاتصال ناجح")
    
    # 2. فحص الجداول الأساسية
    print("\n2. فحص الجداول الأساسية...")
    tables = ['users', 'accounts', 'transactions', 'currencies']
    
    for table in tables:
        try:
            result = db.execute_query(f"SELECT COUNT(*) as count FROM {table}")
            count = result[0]['count'] if result else 0
            
            if count > 0:
                print(f"✅ {table}: {count} سجل")
            else:
                print(f"⚠️ {table}: فارغ")
        except Exception as e:
            print(f"❌ {table}: خطأ - {e}")
    
    # 3. فحص المستخدمين
    print("\n3. فحص المستخدمين...")
    users = db.execute_query("SELECT id, username, role FROM users")
    if users:
        print(f"✅ المستخدمين ({len(users)}):")
        for user in users:
            print(f"   - {user['username']} (ID: {user['id']})")
    else:
        print("❌ لا يوجد مستخدمين")
    
    # 4. فحص الحسابات
    print("\n4. فحص الحسابات...")
    accounts = db.execute_query("SELECT * FROM accounts")
    if accounts:
        print(f"✅ الحسابات ({len(accounts)}):")
        for account in accounts:
            print(f"   - {account['name']}: {account['balance']} {account['currency']}")
    else:
        print("❌ لا توجد حسابات")
    
    # 5. فحص المعاملات
    print("\n5. فحص المعاملات...")
    transactions = db.execute_query("SELECT COUNT(*) as count FROM transactions")
    trans_count = transactions[0]['count'] if transactions else 0
    
    if trans_count > 0:
        print(f"✅ المعاملات: {trans_count}")
        
        # عرض آخر معاملة
        last_trans = db.execute_query("""
            SELECT t.*, a.name as account_name 
            FROM transactions t 
            JOIN accounts a ON t.account_id = a.id 
            ORDER BY t.created_at DESC 
            LIMIT 1
        """)
        
        if last_trans:
            trans = last_trans[0]
            print(f"   آخر معاملة: {trans['description']} - {trans['amount']}")
    else:
        print("❌ لا توجد معاملات")
    
    # 6. اختبار تسجيل الدخول
    print("\n6. اختبار تسجيل الدخول...")
    from utils.auth import auth_manager
    
    success, message = auth_manager.login("admin", "123456")
    if success:
        user = auth_manager.current_user
        print(f"✅ تسجيل دخول ناجح: {user['username']}")
        
        # فحص حسابات المستخدم
        user_accounts = db.execute_query("SELECT * FROM accounts WHERE user_id = %s", (user['id'],))
        print(f"   حسابات المستخدم: {len(user_accounts) if user_accounts else 0}")
        
        auth_manager.logout()
    else:
        print(f"❌ فشل تسجيل الدخول: {message}")
    
    # 7. فحص النسخ الاحتياطية
    print("\n7. فحص النسخ الاحتياطية...")
    try:
        from utils.backup import backup_manager
        backup_files = backup_manager.get_backup_files()
        
        if backup_files:
            print(f"✅ نسخ احتياطية متاحة: {len(backup_files)}")
            for backup in backup_files[:3]:  # أول 3 نسخ
                print(f"   - {backup['name']} ({backup['size_mb']} MB)")
        else:
            print("⚠️ لا توجد نسخ احتياطية")
    except Exception as e:
        print(f"❌ خطأ في فحص النسخ الاحتياطية: {e}")
    
    print("\n" + "="*50)
    print("📊 ملخص التشخيص:")
    
    # تحديد المشكلة
    if not users:
        print("❌ مشكلة خطيرة: لا يوجد مستخدمين")
        print("💡 الحل: استعادة نسخة احتياطية أو إعادة تهيئة قاعدة البيانات")
    elif not accounts:
        print("⚠️ مشكلة: لا توجد حسابات")
        print("💡 الحل: إنشاء حسابات جديدة أو استعادة نسخة احتياطية")
    elif trans_count == 0:
        print("⚠️ مشكلة: لا توجد معاملات")
        print("💡 الحل: إضافة معاملات جديدة أو استعادة نسخة احتياطية")
    else:
        print("✅ البيانات موجودة - المشكلة قد تكون في الواجهة")
        print("💡 الحل: إعادة تشغيل التطبيق أو فحص عرض البيانات")
    
    print("\n🚀 خطوات الإصلاح المقترحة:")
    print("1. أعد تشغيل التطبيق: python main.py")
    print("2. سجل الدخول بحساب admin")
    print("3. إذا لم تظهر البيانات، استعد نسخة احتياطية")
    print("4. كحل أخير: python database/init_db.py")

except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
