# 💻 معلومات النظام - برنامج إدارة الأموال

## 📋 ملخص المشروع

**اسم البرنامج:** برنامج إدارة الأموال الشخصية  
**الإصدار:** 1.0.0  
**المطور:** Augment Agent  
**التاريخ:** ديسمبر 2024  
**اللغة:** Python 3.8+  
**قاعدة البيانات:** MySQL 8.0+  
**الواجهة:** CustomTkinter (GUI)  

## 🏗️ البنية التقنية

### اللغات والتقنيات:
- **Python 3.8+** - لغة البرمجة الأساسية
- **MySQL 8.0+** - قاعدة البيانات
- **CustomTkinter** - واجهة المستخدم الحديثة
- **SQL** - استعلامات قاعدة البيانات
- **JSON** - تخزين الإعدادات والنسخ الاحتياطية

### المكتبات الأساسية:
```
mysql-connector-python  # اتصال قاعدة البيانات
customtkinter           # واجهة المستخدم
Pillow                  # معالجة الصور
bcrypt                  # تشفير كلمات المرور
python-dateutil         # أدوات التاريخ
```

### المكتبات الإضافية:
```
reportlab               # تقارير PDF
matplotlib              # رسوم بيانية
pandas                  # تحليل البيانات
openpyxl                # ملفات Excel
hijri-converter         # التقويم الهجري
schedule                # جدولة المهام
psutil                  # معلومات النظام
```

## 📁 هيكل المشروع

```
money-manager/
├── 🚀 main.py                    # نقطة البداية الرئيسية
├── 🎯 launcher.py                # مشغل متقدم
├── 📋 requirements.txt           # قائمة المتطلبات
├── 🔍 check_requirements.py      # فحص المتطلبات
├── 📦 install_requirements.py    # تثبيت المتطلبات
├── 🎲 create_sample_data.py      # بيانات تجريبية
├── 🖥️  run.bat                   # تشغيل سريع (Windows)
├── 🐧 run.sh                     # تشغيل سريع (Linux/Mac)
│
├── 📖 README.md                  # الدليل الشامل
├── 🛠️  INSTALLATION_GUIDE.md     # دليل التثبيت
├── ⚡ QUICK_START.md             # دليل البدء السريع
├── 💻 SYSTEM_INFO.md             # معلومات النظام (هذا الملف)
│
├── ⚙️  config/                   # ملفات الإعدادات
│   ├── settings.py              # إعدادات التطبيق
│   └── colors.py                # ألوان التصميم
│
├── 🗄️  database/                 # قاعدة البيانات
│   ├── connection.py            # إدارة الاتصال
│   ├── models.py                # نماذج البيانات
│   └── schema.sql               # هيكل قاعدة البيانات
│
├── 🖼️  gui/                      # واجهة المستخدم
│   ├── login_window.py          # نافذة تسجيل الدخول
│   ├── register_window.py       # نافذة التسجيل
│   ├── main_window.py           # النافذة الرئيسية
│   ├── income_window.py         # نافذة الواردات (قريباً)
│   ├── expense_window.py        # نافذة المصروفات (قريباً)
│   ├── reports_window.py        # نافذة التقارير (قريباً)
│   └── settings_window.py       # نافذة الإعدادات (قريباً)
│
├── 🛠️  utils/                    # أدوات مساعدة
│   ├── auth.py                  # نظام المصادقة
│   ├── currency.py              # إدارة العملات
│   ├── date_utils.py            # أدوات التاريخ
│   └── backup.py                # النسخ الاحتياطي
│
└── 📂 مجلدات تُنشأ تلقائياً:
    ├── logs/                    # ملفات السجلات
    ├── backups/                 # النسخ الاحتياطية
    ├── uploads/                 # الملفات المرفوعة
    ├── reports/                 # التقارير المُصدرة
    └── assets/                  # الموارد والأيقونات
```

## 🗄️ قاعدة البيانات

### الجداول الرئيسية:

#### 👥 إدارة المستخدمين:
- `users` - بيانات المستخدمين
- `activity_log` - سجل نشاط المستخدمين

#### 💱 العملات والحسابات:
- `currencies` - العملات المدعومة
- `account_types` - أنواع الحسابات
- `accounts` - الحسابات المالية

#### 💰 المعاملات:
- `transactions` - الواردات والمصروفات
- `transfers` - التحويلات بين الحسابات
- `income_categories` - تصنيفات الواردات
- `expense_categories` - تصنيفات المصروفات

#### 📎 المرفقات والنسخ:
- `attachments` - مرفقات المعاملات
- `backups` - سجل النسخ الاحتياطية

### خصائص قاعدة البيانات:
- **الترميز:** UTF-8 (دعم كامل للعربية)
- **المحرك:** InnoDB (دعم المعاملات)
- **المفاتيح الخارجية:** مفعلة للحفاظ على سلامة البيانات
- **الفهارس:** محسنة للاستعلامات السريعة

## 🎨 واجهة المستخدم

### التصميم:
- **المكتبة:** CustomTkinter
- **النمط:** حديث ومسطح
- **الألوان:** بنفسجي متدرج (#8B5CF6 إلى #EC4899)
- **الخطوط:** واضحة ومقروءة
- **اللغة:** عربي بالكامل مع دعم RTL

### المكونات:
- **النوافذ:** مركزة وقابلة لتغيير الحجم
- **الأزرار:** تفاعلية مع تأثيرات hover
- **الحقول:** تحقق من صحة البيانات
- **القوائم:** منظمة وسهلة التنقل
- **البطاقات:** عرض أنيق للمعلومات

## 🔐 الأمان

### تشفير البيانات:
- **كلمات المرور:** bcrypt مع salt
- **الجلسات:** رموز آمنة
- **قاعدة البيانات:** اتصال آمن

### صلاحيات المستخدمين:
- **مدير:** صلاحيات كاملة
- **مستخدم:** صلاحيات محدودة
- **تسجيل النشاط:** تتبع جميع العمليات

### النسخ الاحتياطي:
- **تشفير:** ملفات مضغوطة آمنة
- **جدولة:** تلقائية كل 24 ساعة
- **تنظيف:** حذف النسخ القديمة تلقائياً

## 📊 الأداء

### التحسينات:
- **قاعدة البيانات:** فهارس محسنة
- **الذاكرة:** إدارة فعالة للموارد
- **الواجهة:** تحديث تدريجي للبيانات
- **الملفات:** ضغط للنسخ الاحتياطية

### المتطلبات:
- **المعالج:** Intel Core i3 أو أحدث
- **الذاكرة:** 4GB RAM (8GB موصى به)
- **التخزين:** 500MB (2GB موصى به)
- **الشبكة:** اختيارية للتحديثات

## 🌍 الدعم الدولي

### اللغات:
- **العربية:** اللغة الأساسية
- **الإنجليزية:** دعم جزئي في الكود

### العملات المدعومة:
- **SAR** - ريال سعودي
- **USD** - دولار أمريكي  
- **EUR** - يورو
- **YER** - ريال يمني
- **AED** - درهم إماراتي
- **KWD** - دينار كويتي
- **QAR** - ريال قطري
- **BHD** - دينار بحريني
- **OMR** - ريال عماني
- **JOD** - دينار أردني
- **EGP** - جنيه مصري

### التقويم:
- **الميلادي:** الافتراضي
- **الهجري:** اختياري مع التحويل التلقائي

## 🔄 التحديثات المستقبلية

### المخطط لها:
1. **نوافذ إدخال البيانات** - الواردات والمصروفات
2. **نظام التقارير المتقدم** - رسوم بيانية تفاعلية
3. **إعدادات شاملة** - تخصيص كامل
4. **البحث والفلاتر** - أدوات بحث متقدمة
5. **التصدير المتقدم** - تنسيقات متعددة
6. **الإشعارات** - تذكيرات وتنبيهات
7. **التزامن السحابي** - نسخ احتياطي سحابي
8. **تطبيق الجوال** - إصدار للهواتف الذكية

### التحسينات:
- **الأداء:** تحسين سرعة الاستعلامات
- **الواجهة:** تحسين تجربة المستخدم
- **الأمان:** تعزيز الحماية
- **التوافق:** دعم أنظمة تشغيل إضافية

## 📞 الدعم التقني

### الملفات المرجعية:
- `README.md` - دليل شامل
- `INSTALLATION_GUIDE.md` - تثبيت مفصل
- `QUICK_START.md` - بدء سريع

### أدوات التشخيص:
- `check_requirements.py` - فحص المتطلبات
- `install_requirements.py` - تثبيت تلقائي
- `launcher.py` - مشغل ذكي

### ملفات السجلات:
- `logs/` - سجلات مفصلة للأخطاء
- تسجيل تلقائي لجميع العمليات
- تنظيف دوري للسجلات القديمة

---

**تم تطوير هذا البرنامج بعناية لتوفير تجربة إدارة مالية شاملة وآمنة! 💰✨**
